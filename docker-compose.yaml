services:
  root:
    build:
      dockerfile: dockerfiles/root/dev.Dockerfile
    container_name: root
    ports:
      - "5000:5000"
    volumes:
      - ./dockerfiles/root/dev.server.conf:/etc/nginx/conf.d/default.conf:ro
      - ./apps/home:/usr/share/nginx/html/home:ro
    networks:
      - makroz-fe
    restart: unless-stopped

  pronet:
    build:
      dockerfile: dockerfiles/pronet/dev.Dockerfile
    container_name: pronet
    volumes:
      - ./dockerfiles/pronet/dev.server.conf:/etc/nginx/conf.d/default.conf:ro
      - ./apps/pronet:/usr/share/nginx/html/pronet:ro
    networks:
      - makroz-fe
    restart: unless-stopped

  # TODO: ebetlab app

networks:
  makroz-fe:
    driver: bridge
