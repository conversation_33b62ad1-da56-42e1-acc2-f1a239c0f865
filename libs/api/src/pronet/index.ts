export {
  CtLoginRequest,
  type CtLoginRequestOptions,
  type CtLoginResponse,
} from './requests/auth/CtLoginRequest.js';

export {
  DagurLoginRequest,
  type DagurLoginRequestOptions,
  type DagurLoginResponse,
} from './requests/auth/DagurLoginRequest.js';

export {
  FreespinBonusSearchRequest,
  type FreespinBonusSearchRequestOptions,
} from './requests/freespin-bonus/FreespinBonusSearchRequest.js';

export {
  FreespinBonusTemplateSearchRequest,
  type FreespinBonusTemplateSearchRequestOptions,
} from './requests/freespin-bonus/FreespinBonusTemplateSearchRequest.js';

export {
  LossbackBonusSearchRequest,
  type LossbackBonusSearchRequestOptions,
} from './requests/lossback-bonus/LossbackBonusSearchRequest.js';

export {
  LossbackBonusCreateRequest,
  type LossbackBonusCreateRequestOptions,
  type LossbackBonusRuleCreateOptions,
} from './requests/lossback-bonus/LossbackBonusCreateRequest.js';

export {
  LossbackBonusTemplateSearchRequest,
  type LossbackBonusTemplateSearchRequestOptions,
} from './requests/lossback-bonus/LossbackBonusTemplateSearchRequest.js';

export {
  LossbackBonusTemplateCreateRequest,
  type LossbackBonusTemplateCreateRequestOptions,
  type LossbackBonusTemplateRuleCreateOptions,
} from './requests/lossback-bonus/LossbackBonusTemplateCreateRequest.js';

export {
  CashBonusSearchRequest,
  type CashBonusSearchRequestOptions,
} from './requests/cash-bonus/CashBonusSearchRequest.js';

export {
  CashBonusCreateRequest,
  type CashBonusCreateRequestOptions,
} from './requests/cash-bonus/CashBonusCreateRequest.js';

export {
  CashBonusTemplateSearchRequest,
  type CashBonusTemplateSearchRequestOptions,
} from './requests/cash-bonus/CashBonusTemplateSearchRequest.js';

export {
  CashBonusTemplateCreateRequest,
  type CashBonusTemplateCreateRequestOptions,
} from './requests/cash-bonus/CashBonusTemplateCreateRequest.js';

export {
  CashBonusBulkAssignmentJobCreateRequest,
  type CashBonusBulkAssignmentRequestCreateOptions,
} from './requests/cash-bonus/CashBonusBulkAssignmentJobCreateRequest.js';

export {
  WeeklyLossbackBonusSearchRequest,
  type WeeklyLossbackBonusSearchRequestOptions,
} from './requests/weekly-lossback-bonus/WeeklyLossbackBonusSearchRequest.js';

export {
  WeeklyLossbackBonusCreateRequest,
  type WeeklyLossbackBonusCreateRequestOptions,
  type WeeklyLossbackBonusRuleCreateOptions,
} from './requests/weekly-lossback-bonus/WeeklyLossbackBonusCreateRequest.js';

export {
  WeeklyLossbackBonusTemplateSearchRequest,
  type WeeklyLossbackBonusTemplateSearchRequestOptions,
} from './requests/weekly-lossback-bonus/WeeklyLossbackBonusTemplateSearchRequest.js';

export {
  WeeklyLossbackBonusTemplateCreateRequest,
  type WeeklyLossbackBonusTemplateCreateRequestOptions,
  type WeeklyLossbackBonusTemplateRuleCreateOptions,
} from './requests/weekly-lossback-bonus/WeeklyLossbackBonusTemplateCreateRequest.js';

export {
  HappyHoursBonusSearchRequest,
  type HappyHoursBonusSearchRequestOptions,
} from './requests/happy-hours-bonus/HappyHoursBonusSearchRequest.js';

export {
  HappyHoursBonusCreateRequest,
  type HappyHoursBonusCreateRequestOptions,
  type HappyHoursBonusRuleCreateOptions,
} from './requests/happy-hours-bonus/HappyHoursBonusCreateRequest.js';

export {
  HappyHoursBonusTemplateSearchRequest,
  type HappyHoursBonusTemplateSearchRequestOptions,
} from './requests/happy-hours-bonus/HappyHoursBonusTemplateSearchRequest.js';

export {
  HappyHoursBonusTemplateCreateRequest,
  type HappyHoursBonusTemplateCreateRequestOptions,
  type HappyHoursBonusTemplateRuleCreateOptions,
} from './requests/happy-hours-bonus/HappyHoursBonusTemplateCreateRequest.js';

export {
  HappyHoursBonusBulkAssignmentJobCreateRequest,
  type HappyHoursBonusBulkAssignmentRequestCreateOptions,
} from './requests/happy-hours-bonus/HappyHoursBonusBulkAssignmentJobCreateRequest.js';

export {
  BonusPromocodeSearchRequest,
  type BonusPromocodeSearchRequestOptions,
} from './requests/bonus/BonusPromocodeSearchRequest.js';

export {
  BonusClaimSearchRequest,
  type BonusClaimSearchRequestOptions,
} from './requests/bonus/BonusClaimSearchRequest.js';

export {
  BonusClaimsByCustomerSearchRequest,
  type BonusClaimsByCustomerSearchRequestOptions,
} from './requests/bonus/BonusClaimsByCustomerSearchRequest.js';

export {
  BonusPromocodeActivationsByCustomerSearchRequest,
  type BonusPromocodeActivationsByCustomerSearchRequestOptions,
} from './requests/bonus/BonusPromocodeActivationsByCustomerSearchRequest.js';

export {
  BonusPromocodeCreateRequest,
  type BonusPromocodeCreateRequestOptions,
} from './requests/bonus/BonusPromocodeCreateRequest.js';

export {
  BonusSearchRequest,
  type BonusSearchRequestOptions,
} from './requests/bonus/BonusSearchRequest.js';

export {
  BonusPromocodeToggleRequest,
  type BonusPromocodeToggleRequestOptions,
} from './requests/bonus/BonusPromocodeToggleRequest.js';

export {
  BonusPromocodeSoftDeleteRequest,
  type BonusPromocodeSoftDeleteRequestOptions,
} from './requests/bonus/BonusPromocodeSoftDeleteRequest.js';

export {
  BonusToggleRequest,
  type BonusToggleRequestOptions,
} from './requests/bonus/BonusToggleRequest.js';

export {
  BonusUpdateRequest,
  type BonusUpdateRequestOptions,
} from './requests/bonus/BonusUpdateRequest.js';

export {
  BonusSoftDeleteRequest,
  type BonusSoftDeleteRequestOptions,
} from './requests/bonus/BonusSoftDeleteRequest.js';

export {
  BonusBulkAssignmentJobSearchRequest,
  type BonusBulkAssignmentJobSearchRequestOptions,
} from './requests/bonus/BonusBulkAssignmentJobSearchRequest.js';

export {
  BonusBulkAssignmentJobGetRequest,
  type BonusBulkAssignmentJobGetRequestOptions,
} from './requests/bonus/BonusBulkAssignmentJobGetRequest.js';

export {
  BonusBulkAssignmentJobCancelRequest,
  type BonusBulkAssignmentJobCancelRequestOptions,
} from './requests/bonus/BonusBulkAssignmentJobCancelRequest.js';

export {
  BonusBulkAssignmentJobRetryRequest,
  type BonusBulkAssignmentJobRetryRequestOptions,
} from './requests/bonus/BonusBulkAssignmentJobRetryRequest.js';

export {
  BonusTemplateSoftDeleteRequest,
  type BonusTemplateSoftDeleteRequestOptions,
} from './requests/bonus/BonusTemplateSoftDeleteRequest.js';

export {
  FreespinBonusBulkAssignmentJobCreateRequest,
  type FreespinBonusBulkAssignmentRequestCreateOptions,
} from './requests/freespin-bonus/FreespinBonusBulkAssignmentJobCreateRequest.js';

export {
  TrialBonusSearchRequest,
  type TrialBonusSearchRequestOptions,
} from './requests/trial-bonus/TrialBonusSearchRequest.js';

export {
  TrialBonusCreateRequest,
  type TrialBonusCreateRequestOptions,
  type TrialBonusRuleCreateOptions,
} from './requests/trial-bonus/TrialBonusCreateRequest.js';

export {
  TrialBonusTemplateSearchRequest,
  type TrialBonusTemplateSearchRequestOptions,
} from './requests/trial-bonus/TrialBonusTemplateSearchRequest.js';

export {
  TrialBonusTemplateCreateRequest,
  type TrialBonusTemplateCreateRequestOptions,
  type TrialBonusTemplateRuleCreateOptions,
} from './requests/trial-bonus/TrialBonusTemplateCreateRequest.js';

export {
  TrialBonusBulkAssignmentJobCreateRequest,
  type TrialBonusBulkAssignmentRequestCreateOptions,
} from './requests/trial-bonus/TrialBonusBulkAssignmentJobCreateRequest.js';

export {
  TrialBonusExternalListRequest,
  type TrialBonusExternalListRequestOptions,
  type TrialBonusExternal,
  type TrialBonusExternalDTO,
  mapTrialBonusExternal,
} from './requests/trial-bonus/TrialBonusExternalListRequest.js';

export { type BonusBulkAssignmentJob } from './dto/BonusBulkAssignmentJob.js';

export {
  FreespinBonusCreateRequest,
  type FreespinBonusCreateRequestOptions,
  type FreespinBonusRuleCreateOptions,
} from './requests/freespin-bonus/FreespinBonusCreateRequest.js';

export {
  FreespinBonusTemplateCreateRequest,
  type FreespinBonusTemplateCreateRequestOptions,
  type FreespinBonusTemplateRuleCreateOptions,
} from './requests/freespin-bonus/FreespinBonusTemplateCreateRequest.js';

export { FreespinBonusProviderSearchRequest } from './requests/freespin-bonus/FreespinBonusProviderSearchRequest.js';

export {
  FreespinBonusGameSearchRequest,
  type FreespinBonusGameSearchRequestOptions,
} from './requests/freespin-bonus/FreespinBonusGameSearchRequest.js';

export {
  FreespinBonusBetAmountSearchRequest,
  type FreespinBonusBetAmountSearchRequestOptions,
} from './requests/freespin-bonus/FreespinBonusBetAmountSearchRequest.js';

export {
  FreespinBonusCurrencySearchRequest,
  type FreespinBonusCurrencySearchRequestOptions,
} from './requests/freespin-bonus/FreespinBonusCurrencySearchRequest.js';

export {
  type FreespinBonusProvider,
  type FreespinBonusProviderDTO,
  mapFreespinBonusProvider,
} from './dto/FreespinBonusProvider.js';

export {
  type FreespinBonusGame,
  type FreespinBonusGameDTO,
  mapFreespinBonusGame,
} from './dto/FreespinBonusGame.js';

export {
  type FreespinBonusBetAmount,
  type FreespinBonusBetAmountDTO,
} from './dto/FreespinBonusBetAmount.js';

export {
  type Currency,
  type CurrencyDTO,
  mapCurrency,
} from './dto/Currency.js';

export {
  type FreespinBonus,
  type FreespinBonusDTO,
  mapFreespinBonus,
} from './dto/FreespinBonus.js';

export {
  type FreespinBonusTemplate,
  type FreespinBonusTemplateDTO,
  mapFreespinBonusTemplate,
} from './dto/FreespinBonusTemplate.js';

export {
  type TrialBonus,
  type TrialBonusDTO,
  mapTrialBonus,
} from './dto/TrialBonus.js';

export {
  type TrialBonusTemplate,
  type TrialBonusTemplateDTO,
  mapTrialBonusTemplate,
} from './dto/TrialBonusTemplate.js';

export {
  type LossbackBonus,
  type LossbackBonusDTO,
  mapLossbackBonus,
} from './dto/LossbackBonus.js';

export {
  type LossbackBonusTemplate,
  type LossbackBonusTemplateDTO,
  mapLossbackBonusTemplate,
} from './dto/LossbackBonusTemplate.js';

export {
  type CashBonus,
  type CashBonusDTO,
  mapCashBonus,
} from './dto/CashBonus.js';

export {
  type CashBonusTemplate,
  type CashBonusTemplateDTO,
  mapCashBonusTemplate,
} from './dto/CashBonusTemplate.js';

export {
  type WeeklyLossbackBonus,
  type WeeklyLossbackBonusDTO,
  mapWeeklyLossbackBonus,
} from './dto/WeeklyLossbackBonus.js';

export {
  type WeeklyLossbackBonusTemplate,
  type WeeklyLossbackBonusTemplateDTO,
  mapWeeklyLossbackBonusTemplate,
} from './dto/WeeklyLossbackBonusTemplate.js';

export {
  type HappyHoursBonus,
  type HappyHoursBonusDTO,
  mapHappyHoursBonus,
} from './dto/HappyHoursBonus.js';

export {
  type HappyHoursBonusTemplate,
  type HappyHoursBonusTemplateDTO,
  mapHappyHoursBonusTemplate,
} from './dto/HappyHoursBonusTemplate.js';

export {
  type BonusPromocode,
  type BonusPromocodeDTO,
  mapBonusPromocode,
} from './dto/BonusPromocode.js';

export {
  type BonusClaim,
  type BonusClaimDTO,
  mapBonusClaim,
} from './dto/BonusClaim.js';

export {
  type BonusPromocodeActivation,
  type BonusPromocodeActivationDTO,
  mapBonusPromocodeActivation,
} from './dto/BonusPromocodeActivation.js';

export { type Bonus, type BonusDTO, mapBonus } from './dto/Bonus.js';

export {
  CustomerSearchRequest,
  type CustomerSearchRequestOptions,
} from './requests/customer/CustomerSearchRequest.js';

export {
  CustomerGetRequest,
  type CustomerGetRequestOptions,
} from './requests/customer/CustomerGetRequest.js';

export {
  CustomersTotalBalancesGetRequest,
  type CustomersTotalBalancesGetRequestOptions,
} from './requests/customer/CustomersTotalBalancesGetRequest.js';

export {
  type Customer,
  type CustomerDTO,
  mapCustomer,
} from './dto/Customer.js';

export {
  type CustomersTotalBalances,
  type CustomersTotalBalancesDTO,
  mapCustomersTotalBalances,
} from './dto/CustomersTotalBalances.js';

export { SiteClaimableBonusSearchRequest } from './requests/site-claimable-bonus/SiteClaimableBonusSearchRequest.js';

export {
  SiteClaimableBonusCreateRequest,
  type SiteClaimableBonusCreateRequestOptions,
} from './requests/site-claimable-bonus/SiteClaimableBonusCreateRequest.js';

export {
  SiteClaimableBonusUpdateRequest,
  type SiteClaimableBonusUpdateRequestOptions,
} from './requests/site-claimable-bonus/SiteClaimableBonusUpdateRequest.js';

export {
  SiteClaimableBonusDeleteRequest,
  type SiteClaimableBonusDeleteRequestOptions,
} from './requests/site-claimable-bonus/SiteClaimableBonusDeleteRequest.js';

export {
  SiteClaimableBonusRemoveRequest,
  type SiteClaimableBonusRemoveRequestOptions,
} from './requests/site-claimable-bonus/SiteClaimableBonusRemoveRequest.js';

export {
  SiteClaimableBonusToggleRequest,
  type SiteClaimableBonusToggleRequestOptions,
} from './requests/site-claimable-bonus/SiteClaimableBonusToggleRequest.js';

export { SiteClaimableBonusClaimableRequest } from './requests/site-claimable-bonus/SiteClaimableBonusClaimableRequest.js';

export {
  SiteClaimableBonusCheckRequest,
  type SiteClaimableBonusCheckRequestOptions,
} from './requests/site-claimable-bonus/SiteClaimableBonusCheckRequest.js';

export {
  type SiteClaimableBonus,
  type SiteClaimableBonusDTO,
  type SiteClaimableBonusUpdateDTO,
  type SiteClaimableBonusToggleDTO,
  type ClaimableBonusCheck,
  type ClaimableBonusCheckDTO,
  mapSiteClaimableBonus,
  mapClaimableBonusCheck,
} from './dto/SiteClaimableBonus.js';
