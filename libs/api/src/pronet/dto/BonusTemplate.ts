import {
  BonusTemplateRule,
  BonusTemplateRuleDTO,
  mapBonusTemplateRule,
} from './BonusTemplateRule.js';

export type BonusTemplateDTO = {
  id: number;
  name: string;
  type: string;
  rules: BonusTemplateRuleDTO[];
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
};

export type BonusTemplate = {
  id: number;
  name: string;
  type: string;
  rules: BonusTemplateRule[];
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
};

export const mapBonusTemplate = (
  bonusTemplate: BonusTemplateDTO
): BonusTemplate => ({
  id: bonusTemplate.id,
  name: bonusTemplate.name,
  type: bonusTemplate.type,
  rules: bonusTemplate.rules.map(mapBonusTemplateRule),
  createdAt: new Date(bonusTemplate.createdAt),
  updatedAt: new Date(bonusTemplate.updatedAt),
  deletedAt: bonusTemplate.deletedAt ? new Date(bonusTemplate.deletedAt) : null,
});
