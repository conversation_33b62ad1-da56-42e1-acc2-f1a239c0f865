export type CustomersTotalBalancesDTO = {
  mainBalance: {
    TRY: number;
    GBP: number;
    USD: number;
    EUR: number;
    KZT: number;
    AZN: number;
  };
  sportBonusBalance: {
    TRY: number;
    GBP: number;
    USD: number;
    EUR: number;
    KZT: number;
    AZN: number;
  };
  sportFreebetBalance: {
    TRY: number;
    GBP: number;
    USD: number;
    EUR: number;
    KZT: number;
    AZN: number;
  };
  casinoBonusBalance: {
    TRY: number;
    GBP: number;
    USD: number;
    EUR: number;
    KZT: number;
    AZN: number;
  };
};

export type CustomersTotalBalances = {
  mainBalance: {
    TRY: number;
    GBP: number;
    USD: number;
    EUR: number;
    KZT: number;
    AZN: number;
  };
  sportBonusBalance: {
    TRY: number;
    GBP: number;
    USD: number;
    EUR: number;
    KZT: number;
    AZN: number;
  };
  sportFreebetBalance: {
    TRY: number;
    GBP: number;
    USD: number;
    EUR: number;
    KZT: number;
    AZN: number;
  };
  casinoBonusBalance: {
    TRY: number;
    GBP: number;
    USD: number;
    EUR: number;
    KZT: number;
    AZN: number;
  };
};

export const mapCustomersTotalBalances = (
  customersTotalBalance: CustomersTotalBalancesDTO
): CustomersTotalBalances => ({
  mainBalance: customersTotalBalance.mainBalance,
  sportBonusBalance: customersTotalBalance.sportBonusBalance,
  sportFreebetBalance: customersTotalBalance.sportFreebetBalance,
  casinoBonusBalance: customersTotalBalance.casinoBonusBalance,
});
