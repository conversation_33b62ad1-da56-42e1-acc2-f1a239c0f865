export type FreespinBonusGameDTO = {
  id: number;
  name: string;
  providerId: number;
  providerName: string;
};

export type FreespinBonusGame = {
  id: number;
  name: string;
  providerId: number;
  providerName: string;
};

export const mapFreespinBonusGame = (
  freespinBonusGame: FreespinBonusGameDTO
): FreespinBonusGame => ({
  id: freespinBonusGame.id,
  name: freespinBonusGame.name,
  providerId: freespinBonusGame.providerId,
  providerName: freespinBonusGame.providerName,
});
