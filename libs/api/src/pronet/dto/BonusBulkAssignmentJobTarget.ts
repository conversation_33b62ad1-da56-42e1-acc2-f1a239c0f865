import { Customer, CustomerDTO, mapCustomer } from './Customer.js';

export type BonusBulkAssignmentJobTargetDTO = {
  id: number;
  bonusBulkAssignmentJobId: number;
  externalCustomerId: number;
  customer: CustomerDTO | null;
  // @todo
  events: {
    type: string;
    message: string;
    timestamp: string;
  }[];
  // pending, processing, completed, failed
  status: string;
  errorMessage: string | null;
  processedAt: string | null;
  createdAt: string;
  updatedAt: string;
};

export type BonusBulkAssignmentJobTarget = {
  id: number;
  bonusBulkAssignmentJobId: number;
  externalCustomerId: number;
  customer: Customer | null;
  // @todo
  events: {
    type: string;
    message: string;
    timestamp: Date;
  }[];
  // pending, processing, completed, failed
  status: string;
  errorMessage: string | null;
  processedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

export const mapBonusBulkAssignmentJobTarget = (
  target: BonusBulkAssignmentJobTargetDTO
): BonusBulkAssignmentJobTarget => ({
  id: target.id,
  bonusBulkAssignmentJobId: target.bonusBulkAssignmentJobId,
  externalCustomerId: target.externalCustomerId,
  customer: target.customer ? mapCustomer(target.customer) : null,
  events: target.events.map((event) => ({
    type: event.type,
    message: event.message,
    timestamp: new Date(event.timestamp),
  })),
  status: target.status,
  errorMessage: target.errorMessage,
  processedAt: target.processedAt ? new Date(target.processedAt) : null,
  createdAt: new Date(target.createdAt),
  updatedAt: new Date(target.updatedAt),
});
