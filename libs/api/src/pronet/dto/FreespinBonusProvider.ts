export type FreespinBonusProviderDTO = {
  providerId: number;
  providerName: string;
  // steps of json schema form fields
  steps: { schema: Record<string, unknown> }[];
};

export type FreespinBonusProvider = {
  providerId: number;
  providerName: string;
  // steps of json schema form fields
  steps: { schema: Record<string, unknown> }[];
};

export const mapFreespinBonusProvider = (
  freespinBonusProvider: FreespinBonusProviderDTO
): FreespinBonusProvider => ({
  providerId: freespinBonusProvider.providerId,
  providerName: freespinBonusProvider.providerName,
  steps: freespinBonusProvider.steps,
});
