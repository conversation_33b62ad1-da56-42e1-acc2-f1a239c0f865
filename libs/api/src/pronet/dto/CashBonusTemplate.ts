import {
  BonusTemplate,
  BonusTemplateDTO,
  mapBonusTemplate,
} from './BonusTemplate.js';

export type CashBonusTemplateDTO = {
  id: number;
  cashAmount: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplateDTO;
  createdAt: string;
  updatedAt: string;
};

export type CashBonusTemplate = {
  id: number;
  cashAmount: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplate;
  createdAt: Date;
  updatedAt: Date;
};

export const mapCashBonusTemplate = (
  cashBonusTemplate: CashBonusTemplateDTO
): CashBonusTemplate => ({
  id: cashBonusTemplate.id,
  cashAmount: cashBonusTemplate.cashAmount,
  bonusTemplateId: cashBonusTemplate.bonusTemplateId,
  bonusTemplate: mapBonusTemplate(cashBonusTemplate.bonusTemplate),
  createdAt: new Date(cashBonusTemplate.createdAt),
  updatedAt: new Date(cashBonusTemplate.updatedAt),
});
