import {
  BonusTemplate,
  BonusTemplateDTO,
  mapBonusTemplate,
} from './BonusTemplate.js';

export type FreespinBonusTemplateDTO = {
  id: number;
  vendorId: number;
  vendorName: string;
  values: Record<string, unknown>;
  gameIds: number[];
  validForDays: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplateDTO;
  createdAt: string;
  updatedAt: string;
};

export type FreespinBonusTemplate = {
  id: number;
  vendorId: number;
  vendorName: string;
  values: Record<string, unknown>;
  gameIds: number[];
  validForDays: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplate;
  createdAt: Date;
  updatedAt: Date;
};

export const mapFreespinBonusTemplate = (
  freespinBonusTemplate: FreespinBonusTemplateDTO
): FreespinBonusTemplate => ({
  id: freespinBonusTemplate.id,
  vendorId: freespinBonusTemplate.vendorId,
  vendorName: freespinBonusTemplate.vendorName,
  values: freespinBonusTemplate.values,
  gameIds: freespinBonusTemplate.gameIds,
  validForDays: freespinBonusTemplate.validForDays,
  bonusTemplateId: freespinBonusTemplate.bonusTemplateId,
  bonusTemplate: mapBonusTemplate(freespinBonusTemplate.bonusTemplate),
  createdAt: new Date(freespinBonusTemplate.createdAt),
  updatedAt: new Date(freespinBonusTemplate.updatedAt),
});
