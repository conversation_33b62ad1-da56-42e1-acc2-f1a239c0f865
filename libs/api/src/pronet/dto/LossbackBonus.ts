import { Bonus, BonusDTO, mapBonus } from './Bonus.js';

export type LossbackBonusDTO = {
  id: number;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  bonusId: number;
  bonus: BonusDTO | null;
  createdAt: string;
  updatedAt: string;
};

export type LossbackBonus = {
  id: number;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  bonusId: number;
  bonus: Bonus | null;
  createdAt: Date;
  updatedAt: Date;
};

export const mapLossbackBonus = (
  lossbackBonus: LossbackBonusDTO
): LossbackBonus => ({
  id: lossbackBonus.id,
  maxBalance: lossbackBonus.maxBalance,
  lossbackPercentage: lossbackBonus.lossbackPercentage,
  happyHoursStart: lossbackBonus.happyHoursStart,
  happyHoursEnd: lossbackBonus.happyHoursEnd,
  happyHoursBoostPercentage: lossbackBonus.happyHoursBoostPercentage,
  depositWithDrawDifferenceThreshold: lossbackBonus.depositWithDrawDifferenceThreshold,
  bonusId: lossbackBonus.bonusId,
  bonus: lossbackBonus.bonus ? mapBonus(lossbackBonus.bonus) : null,
  createdAt: new Date(lossbackBonus.createdAt),
  updatedAt: new Date(lossbackBonus.updatedAt),
});
