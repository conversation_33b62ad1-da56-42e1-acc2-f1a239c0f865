import { Bonus, BonusDTO, mapBonus } from './Bonus.js';

export type TrialBonusDTO = {
  id: number;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  bonusId: number;
  bonus: BonusDTO;
  createdAt: string;
  updatedAt: string;
};

export type TrialBonus = {
  id: number;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  bonusId: number;
  bonus: Bonus;
  createdAt: Date;
  updatedAt: Date;
};

export const mapTrialBonus = (
  trialBonus: TrialBonusDTO
): TrialBonus => ({
  id: trialBonus.id,
  externalBonusName: trialBonus.externalBonusName,
  externalBonusId: trialBonus.externalBonusId,
  amount: trialBonus.amount,
  bonusId: trialBonus.bonusId,
  bonus: mapBonus(trialBonus.bonus),
  createdAt: new Date(trialBonus.createdAt),
  updatedAt: new Date(trialBonus.updatedAt),
});
