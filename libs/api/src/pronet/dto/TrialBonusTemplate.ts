import {
  BonusTemplate,
  BonusTemplateDTO,
  mapBonusTemplate,
} from './BonusTemplate.js';

export type TrialBonusTemplateDTO = {
  id: number;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  validForDays: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplateDTO;
  createdAt: string;
  updatedAt: string;
};

export type TrialBonusTemplate = {
  id: number;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  validForDays: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplate;
  createdAt: Date;
  updatedAt: Date;
};

export const mapTrialBonusTemplate = (
  trialBonusTemplate: TrialBonusTemplateDTO
): TrialBonusTemplate => ({
  id: trialBonusTemplate.id,
  externalBonusName: trialBonusTemplate.externalBonusName,
  externalBonusId: trialBonusTemplate.externalBonusId,
  amount: trialBonusTemplate.amount,
  validForDays: trialBonusTemplate.validForDays,
  bonusTemplateId: trialBonusTemplate.bonusTemplateId,
  bonusTemplate: mapBonusTemplate(trialBonusTemplate.bonusTemplate),
  createdAt: new Date(trialBonusTemplate.createdAt),
  updatedAt: new Date(trialBonusTemplate.updatedAt),
});
