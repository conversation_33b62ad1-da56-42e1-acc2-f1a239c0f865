import {
  BonusTemplate,
  BonusTemplateDTO,
  mapBonusTemplate,
} from './BonusTemplate.js';

export type LossbackBonusTemplateDTO = {
  id: number;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplateDTO;
  createdAt: string;
  updatedAt: string;
};

export type LossbackBonusTemplate = {
  id: number;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplate;
  createdAt: Date;
  updatedAt: Date;
};

export const mapLossbackBonusTemplate = (
  lossbackBonusTemplate: LossbackBonusTemplateDTO
): LossbackBonusTemplate => ({
  id: lossbackBonusTemplate.id,
  maxBalance: lossbackBonusTemplate.maxBalance,
  lossbackPercentage: lossbackBonusTemplate.lossbackPercentage,
  happyHoursStart: lossbackBonusTemplate.happyHoursStart,
  happyHoursEnd: lossbackBonusTemplate.happyHoursEnd,
  happyHoursBoostPercentage: lossbackBonusTemplate.happyHoursBoostPercentage,
  depositWithDrawDifferenceThreshold: lossbackBonusTemplate.depositWithDrawDifferenceThreshold,
  bonusTemplateId: lossbackBonusTemplate.bonusTemplateId,
  bonusTemplate: mapBonusTemplate(lossbackBonusTemplate.bonusTemplate),
  createdAt: new Date(lossbackBonusTemplate.createdAt),
  updatedAt: new Date(lossbackBonusTemplate.updatedAt),
});
