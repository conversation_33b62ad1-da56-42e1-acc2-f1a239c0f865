import { Bonus, BonusDTO, mapBonus } from './Bonus.js';

export type SiteClaimableBonusDTO = {
  id: number;
  slotName: string;
  bonusId: number | null;
  bonus: BonusDTO | null;
  isActive: boolean;
  position: number;
  createdAt: string;
  updatedAt: string;
};

export type SiteClaimableBonus = {
  id: number;
  slotName: string;
  bonusId: number | null;
  bonus: Bonus | null;
  isActive: boolean;
  position: number;
  createdAt: Date;
  updatedAt: Date;
};

export const mapSiteClaimableBonus = (dto: SiteClaimableBonusDTO): SiteClaimableBonus => ({
  id: dto.id,
  slotName: dto.slotName,
  bonusId: dto.bonusId,
  bonus: dto.bonus ? mapBonus(dto.bonus) : null,
  isActive: dto.isActive,
  position: dto.position,
  createdAt: new Date(dto.createdAt),
  updatedAt: new Date(dto.updatedAt),
});

export type SiteClaimableBonusUpdateDTO = {
  bonusId: number | null;
};

export type SiteClaimableBonusToggleDTO = {
  isActive: boolean;
};

export type ClaimableBonusCheckDTO = {
  bonusId: number;
  isClaimable: boolean;
  reason?: string;
};

export type ClaimableBonusCheck = {
  bonusId: number;
  isClaimable: boolean;
  reason?: string;
};

export const mapClaimableBonusCheck = (dto: ClaimableBonusCheckDTO): ClaimableBonusCheck => ({
  bonusId: dto.bonusId,
  isClaimable: dto.isClaimable,
  reason: dto.reason,
});
