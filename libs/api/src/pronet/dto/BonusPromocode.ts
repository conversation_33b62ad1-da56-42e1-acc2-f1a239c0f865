import { Bonus, BonusDTO, mapBonus } from './Bonus.js';

export type BonusPromocodeDTO = {
  id: number;
  bonusId: number;
  bonus?: BonusDTO; // Optional since backend may not always include it
  code: string;
  activations: number;
  maxActivations?: number; // Added based on backend response
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
};

export type BonusPromocode = {
  id: number;
  bonusId: number;
  bonus?: Bonus;
  code: string;
  activations: number;
  maxActivations?: number; // Added based on backend response
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
};

export const mapBonusPromocode = (
  promocode: BonusPromocodeDTO
): BonusPromocode => ({
  id: promocode.id,
  bonusId: promocode.bonusId,
  bonus: promocode.bonus ? mapBonus(promocode.bonus) : undefined,
  code: promocode.code,
  activations: promocode.activations,
  maxActivations: promocode.maxActivations,
  isActive: promocode.isActive,
  createdAt: new Date(promocode.createdAt),
  updatedAt: new Date(promocode.updatedAt),
  deletedAt: promocode.deletedAt ? new Date(promocode.deletedAt) : null,
});
