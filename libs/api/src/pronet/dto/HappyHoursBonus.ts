import { Bonus, BonusDTO, mapBonus } from './Bonus.js';

export type HappyHoursBonusDTO = {
  id: number;
  bonusId: number;
  bonus: BonusDTO;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  createdAt: string;
  updatedAt: string;
};

export type HappyHoursBonus = {
  id: number;
  bonusId: number;
  bonus: Bonus;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  createdAt: Date;
  updatedAt: Date;
};

export const mapHappyHoursBonus = (dto: HappyHoursBonusDTO): HappyHoursBonus => ({
  id: dto.id,
  bonusId: dto.bonusId,
  bonus: mapBonus(dto.bonus),
  externalBonusName: dto.externalBonusName,
  externalBonusId: dto.externalBonusId,
  amount: dto.amount,
  createdAt: new Date(dto.createdAt),
  updatedAt: new Date(dto.updatedAt),
});
