import {
  BonusPromocode,
  BonusPromocodeDTO,
  mapBonusPromocode,
} from './BonusPromocode.js';
import { Customer, CustomerDTO, mapCustomer } from './Customer.js';

export type BonusPromocodeActivationDTO = {
  id: number;
  promocodeId: number;
  promocode: BonusPromocodeDTO;
  customerId: number;
  customer: CustomerDTO;
  createdAt: string;
  updatedAt: string;
};

export type BonusPromocodeActivation = {
  id: number;
  promocodeId: number;
  promocode: BonusPromocode;
  customerId: number;
  customer: Customer;
  createdAt: Date;
  updatedAt: Date;
};

export const mapBonusPromocodeActivation = (
  activation: BonusPromocodeActivationDTO
): BonusPromocodeActivation => ({
  id: activation.id,
  promocodeId: activation.promocodeId,
  promocode: mapBonusPromocode(activation.promocode),
  customerId: activation.customerId,
  customer: mapCustomer(activation.customer),
  createdAt: new Date(activation.createdAt),
  updatedAt: new Date(activation.updatedAt),
});
