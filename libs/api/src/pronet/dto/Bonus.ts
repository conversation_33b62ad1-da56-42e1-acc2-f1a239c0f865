import { BonusRule, BonusRuleDTO, mapBonusRule } from './BonusRule.js';

export type BonusDTO = {
  id: number;
  name: string;
  type: string;
  description?: string;
  reward?: string;
  rules: BonusRuleDTO[];
  isActive: boolean;
  expiresAt: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
};

export type Bonus = {
  id: number;
  name: string;
  type: string;
  description?: string;
  reward?: string;
  rules: BonusRule[];
  isActive: boolean;
  expiresAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
};

export const mapBonus = (bonus: BonusDTO): Bonus => ({
  id: bonus.id,
  name: bonus.name,
  type: bonus.type,
  description: bonus.description,
  reward: bonus.reward,
  rules: bonus.rules.map(mapBonusRule),
  isActive: bonus.isActive,
  expiresAt: bonus.expiresAt ? new Date(bonus.expiresAt) : null,
  createdAt: new Date(bonus.createdAt),
  updatedAt: new Date(bonus.updatedAt),
  deletedAt: bonus.deletedAt ? new Date(bonus.deletedAt) : null,
});
