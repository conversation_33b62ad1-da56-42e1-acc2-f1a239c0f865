import {
  BonusTemplate,
  BonusTemplateDTO,
  mapBonusTemplate,
} from './BonusTemplate.js';

export type WeeklyLossbackBonusTemplateDTO = {
  id: number;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  requestWindowStartSeconds: number;
  requestWindowEndSeconds: number;
  validForDays: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplateDTO;
  createdAt: string;
  updatedAt: string;
};

export type WeeklyLossbackBonusTemplate = {
  id: number;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  requestWindowStartSeconds: number;
  requestWindowEndSeconds: number;
  validForDays: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplate;
  createdAt: Date;
  updatedAt: Date;
};

export const mapWeeklyLossbackBonusTemplate = (dto: WeeklyLossbackBonusTemplateDTO): WeeklyLossbackBonusTemplate => ({
  id: dto.id,
  maxBalance: dto.maxBalance,
  lossbackPercentage: dto.lossbackPercentage,
  happyHoursStart: dto.happyHoursStart,
  happyHoursEnd: dto.happyHoursEnd,
  happyHoursBoostPercentage: dto.happyHoursBoostPercentage,
  depositWithDrawDifferenceThreshold: dto.depositWithDrawDifferenceThreshold,
  requestWindowStartSeconds: dto.requestWindowStartSeconds,
  requestWindowEndSeconds: dto.requestWindowEndSeconds,
  validForDays: dto.validForDays,
  bonusTemplateId: dto.bonusTemplateId,
  bonusTemplate: mapBonusTemplate(dto.bonusTemplate),
  createdAt: new Date(dto.createdAt),
  updatedAt: new Date(dto.updatedAt),
});
