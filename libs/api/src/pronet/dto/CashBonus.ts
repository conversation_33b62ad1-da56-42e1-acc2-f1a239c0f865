import { Bonus, BonusDTO, mapBonus } from './Bonus.js';

export type CashBonusDTO = {
  id: number;
  cashAmount: number;
  bonusId: number;
  bonus: BonusDTO;
  createdAt: string;
  updatedAt: string;
};

export type CashBonus = {
  id: number;
  cashAmount: number;
  bonusId: number;
  bonus: Bonus;
  createdAt: Date;
  updatedAt: Date;
};

export const mapCashBonus = (cashBonus: CashBonusDTO): CashBonus => ({
  id: cashBonus.id,
  cashAmount: cashBonus.cashAmount,
  bonusId: cashBonus.bonusId,
  bonus: mapBonus(cashBonus.bonus),
  createdAt: new Date(cashBonus.createdAt),
  updatedAt: new Date(cashBonus.updatedAt),
});
