export type BonusTemplateRuleDTO = {
  id: number;
  bonusTemplateId: number;
  criterium: string;
  firstOperand: string;
  secondOperand: string | null;
  operator: string;
  startsAt: string | null;
  endsAt: string | null;
  createdAt: string;
  updatedAt: string;
};

export type BonusTemplateRule = {
  id: number;
  bonusTemplateId: number;
  criterium: string;
  firstOperand: string;
  secondOperand: string | null;
  operator: string;
  startsAt: Date | null;
  endsAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

export const mapBonusTemplateRule = (
  bonusRule: BonusTemplateRuleDTO
): BonusTemplateRule => ({
  id: bonusRule.id,
  bonusTemplateId: bonusRule.bonusTemplateId,
  criterium: bonusRule.criterium,
  firstOperand: bonusRule.firstOperand,
  secondOperand: bonusRule.secondOperand,
  operator: bonusRule.operator,
  startsAt: bonusRule.startsAt ? new Date(bonusRule.startsAt) : null,
  endsAt: bonusRule.endsAt ? new Date(bonusRule.endsAt) : null,
  createdAt: new Date(bonusRule.createdAt),
  updatedAt: new Date(bonusRule.updatedAt),
});
