import { BonusDTO } from './Bonus.js';
import { CustomerDTO } from './Customer.js';

export type BonusClaimDTO = {
  id: number;
  bonusId: number;
  customerId: number;
  customer: CustomerDTO;
  bonus?: BonusDTO; // Optional since backend may not always include it
  source: string;
  createdAt: string;
  updatedAt: string;
};

export type BonusClaim = {
  id: number;
  bonusId: number;
  customerId: number;
  customer: CustomerDTO;
  bonus?: BonusDTO; // Optional since backend may not always include it
  source: string;
  createdAt: Date;
  updatedAt: Date;
};

export const mapBonusClaim = (claim: BonusClaimDTO): BonusClaim => ({
  id: claim.id,
  bonusId: claim.bonusId,
  customerId: claim.customerId,
  customer: claim.customer,
  bonus: claim.bonus,
  source: claim.source,
  createdAt: new Date(claim.createdAt),
  updatedAt: new Date(claim.updatedAt),
});
