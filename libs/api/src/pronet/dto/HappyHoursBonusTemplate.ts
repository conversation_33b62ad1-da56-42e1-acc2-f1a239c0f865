import {
  BonusTemplate,
  BonusTemplateDTO,
  mapBonusTemplate,
} from './BonusTemplate.js';

export type HappyHoursBonusTemplateDTO = {
  id: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplateDTO;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  createdAt: string;
  updatedAt: string;
};

export type HappyHoursBonusTemplate = {
  id: number;
  bonusTemplateId: number;
  bonusTemplate: BonusTemplate;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  createdAt: Date;
  updatedAt: Date;
};

export const mapHappyHoursBonusTemplate = (dto: HappyHoursBonusTemplateDTO): HappyHoursBonusTemplate => ({
  id: dto.id,
  bonusTemplateId: dto.bonusTemplateId,
  bonusTemplate: mapBonusTemplate(dto.bonusTemplate),
  externalBonusName: dto.externalBonusName,
  externalBonusId: dto.externalBonusId,
  amount: dto.amount,
  createdAt: new Date(dto.createdAt),
  updatedAt: new Date(dto.updatedAt),
});
