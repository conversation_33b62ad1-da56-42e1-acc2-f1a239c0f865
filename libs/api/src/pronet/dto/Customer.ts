export type CustomerDTO = {
  id: number;
  externalId: number;
  code: string;
  username: string;
  email?: string;
  status?: string;
  registrationDate?: string;
  lastLoginDate?: string;
  totalDeposits?: number;
  totalWithdrawals?: number;
  totalBonuses?: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
};

export type Customer = {
  id: number;
  externalId: number;
  code: string;
  username: string;
  email?: string;
  status?: string;
  registrationDate?: Date;
  lastLoginDate?: Date;
  totalDeposits?: number;
  totalWithdrawals?: number;
  totalBonuses?: number;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
};

export const mapCustomer = (customer: CustomerDTO): Customer => ({
  id: customer.id,
  externalId: customer.externalId,
  code: customer.code,
  username: customer.username,
  email: customer.email,
  status: customer.status,
  registrationDate: customer.registrationDate ? new Date(customer.registrationDate) : undefined,
  lastLoginDate: customer.lastLoginDate ? new Date(customer.lastLoginDate) : undefined,
  totalDeposits: customer.totalDeposits,
  totalWithdrawals: customer.totalWithdrawals,
  totalBonuses: customer.totalBonuses,
  createdAt: new Date(customer.createdAt),
  updatedAt: new Date(customer.updatedAt),
  deletedAt: customer.deletedAt ? new Date(customer.deletedAt) : null,
});
