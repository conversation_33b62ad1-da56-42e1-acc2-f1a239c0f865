import { Bonus, BonusDTO, mapBonus } from './Bonus.js';

export type FreespinBonusDTO = {
  id: number;
  vendorId: number;
  vendorName: string;
  values: Record<string, unknown>;
  gameIds: number[];
  bonusId: number;
  bonus: BonusDTO;
  createdAt: string;
  updatedAt: string;
};

export type FreespinBonus = {
  id: number;
  vendorId: number;
  vendorName: string;
  values: Record<string, unknown>;
  gameIds: number[];
  bonusId: number;
  bonus: Bonus;
  createdAt: Date;
  updatedAt: Date;
};

export const mapFreespinBonus = (
  freespinBonus: FreespinBonusDTO
): FreespinBonus => ({
  id: freespinBonus.id,
  vendorId: freespinBonus.vendorId,
  vendorName: freespinBonus.vendorName,
  values: freespinBonus.values,
  gameIds: freespinBonus.gameIds,
  bonusId: freespinBonus.bonusId,
  bonus: mapBonus(freespinBonus.bonus),
  createdAt: new Date(freespinBonus.createdAt),
  updatedAt: new Date(freespinBonus.updatedAt),
});
