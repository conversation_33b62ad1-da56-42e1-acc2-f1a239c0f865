import { BonusDTO } from './Bonus.js';
import {
  BonusBulkAssignmentJobTarget,
  BonusBulkAssignmentJobTargetDTO,
  mapBonusBulkAssignmentJobTarget,
} from './BonusBulkAssignmentJobTarget.js';

export type BonusBulkAssignmentJobDTO = {
  id: number;
  bonusId: number;
  // pending, processing, completed, failed
  bonus: BonusDTO;
  targets: BonusBulkAssignmentJobTargetDTO[];
  bonusValues: Record<string, unknown>;
  bonusValuesVersion: number;
  status: string;
  createdAt: string;
  updatedAt: string;
};

export type BonusBulkAssignmentJob = {
  id: number;
  bonusId: number;
  // pending, processing, completed, failed
  bonus: BonusDTO;
  targets: BonusBulkAssignmentJobTarget[];
  bonusValues: Record<string, unknown>;
  bonusValuesVersion: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
};

export const mapBonusBulkAssignmentJob = (
  job: BonusBulkAssignmentJobDTO
): BonusBulkAssignmentJob => ({
  id: job.id,
  bonusId: job.bonusId,
  bonus: job.bonus,
  targets: job.targets.map(mapBonusBulkAssignmentJobTarget),
  bonusValues: job.bonusValues,
  bonusValuesVersion: job.bonusValuesVersion,
  status: job.status,
  createdAt: new Date(job.createdAt),
  updatedAt: new Date(job.updatedAt),
});
