import { Bonus, BonusDTO, mapBonus } from './Bonus.js';

export type WeeklyLossbackBonusDTO = {
  id: number;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  requestWindowStartSeconds: number;
  requestWindowEndSeconds: number;
  bonusId: number;
  bonus: BonusDTO | null;
  createdAt: string;
  updatedAt: string;
};

export type WeeklyLossbackBonus = {
  id: number;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  requestWindowStartSeconds: number;
  requestWindowEndSeconds: number;
  bonusId: number;
  bonus: Bonus | null;
  createdAt: Date;
  updatedAt: Date;
};

export const mapWeeklyLossbackBonus = (dto: WeeklyLossbackBonusDTO): WeeklyLossbackBonus => ({
  id: dto.id,
  maxBalance: dto.maxBalance,
  lossbackPercentage: dto.lossbackPercentage,
  happyHoursStart: dto.happyHoursStart,
  happyHoursEnd: dto.happyHoursEnd,
  happyHoursBoostPercentage: dto.happyHoursBoostPercentage,
  depositWithDrawDifferenceThreshold: dto.depositWithDrawDifferenceThreshold,
  requestWindowStartSeconds: dto.requestWindowStartSeconds,
  requestWindowEndSeconds: dto.requestWindowEndSeconds,
  bonusId: dto.bonusId,
  bonus: dto.bonus ? mapBonus(dto.bonus) : null,
  createdAt: new Date(dto.createdAt),
  updatedAt: new Date(dto.updatedAt),
});
