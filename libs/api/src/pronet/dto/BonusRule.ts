export type BonusRuleDTO = {
  id: number;
  bonusId: number;
  criterium: string;
  firstOperand: string;
  secondOperand: string | null;
  operator: string;
  startsAt: string | null;
  endsAt: string | null;
  createdAt: string;
  updatedAt: string;
};

export type BonusRule = {
  id: number;
  bonusId: number;
  criterium: string;
  firstOperand: string;
  secondOperand: string | null;
  operator: string;
  startsAt: Date | null;
  endsAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

export const mapBonusRule = (bonusRule: BonusRuleDTO): BonusRule => ({
  id: bonusRule.id,
  bonusId: bonusRule.bonusId,
  criterium: bonusRule.criterium,
  firstOperand: bonusRule.firstOperand,
  secondOperand: bonusRule.secondOperand,
  operator: bonusRule.operator,
  startsAt: bonusRule.startsAt ? new Date(bonusRule.startsAt) : null,
  endsAt: bonusRule.endsAt ? new Date(bonusRule.endsAt) : null,
  createdAt: new Date(bonusRule.createdAt),
  updatedAt: new Date(bonusRule.updatedAt),
});
