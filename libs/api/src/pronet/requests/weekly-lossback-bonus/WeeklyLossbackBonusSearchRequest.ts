import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  WeeklyLossbackBonus,
  WeeklyLossbackBonusDTO,
  mapWeeklyLossbackBonus,
} from '../../dto/WeeklyLossbackBonus.js';

export type WeeklyLossbackBonusSearchRequestOptions = {
  isActive?: boolean;
  page?: number;
  limit?: number;
};

export class WeeklyLossbackBonusSearchRequest extends ApiRequest<
  PaginatedResponse<WeeklyLossbackBonusDTO>,
  PaginatedResponse<WeeklyLossbackBonus>
> {
  constructor(private options: WeeklyLossbackBonusSearchRequestOptions = {}) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    
    if (this.options.page !== undefined) {
      params.append('page', this.options.page.toString());
    }
    
    if (this.options.limit !== undefined) {
      params.append('limit', this.options.limit.toString());
    }
    
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }

    const queryString = params.toString();
    return `/api/pronet/v1/weekly-lossback-bonuses${queryString ? `?${queryString}` : ''}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<WeeklyLossbackBonusDTO>>
  ): ApiRequestResponse<PaginatedResponse<WeeklyLossbackBonus>> {
    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapWeeklyLossbackBonus),
      },
    };
  }
}
