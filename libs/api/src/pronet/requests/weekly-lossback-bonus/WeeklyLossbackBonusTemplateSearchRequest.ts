import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  WeeklyLossbackBonusTemplate,
  WeeklyLossbackBonusTemplateDTO,
  mapWeeklyLossbackBonusTemplate,
} from '../../dto/WeeklyLossbackBonusTemplate.js';

export type WeeklyLossbackBonusTemplateSearchRequestOptions = {
  isActive?: boolean;
  page?: number;
  limit?: number;
};

export class WeeklyLossbackBonusTemplateSearchRequest extends ApiRequest<
  PaginatedResponse<WeeklyLossbackBonusTemplateDTO>,
  PaginatedResponse<WeeklyLossbackBonusTemplate>
> {
  constructor(private options: WeeklyLossbackBonusTemplateSearchRequestOptions = {}) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    
    if (this.options.page !== undefined) {
      params.append('page', this.options.page.toString());
    }
    
    if (this.options.limit !== undefined) {
      params.append('limit', this.options.limit.toString());
    }
    
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }

    const queryString = params.toString();
    return `/api/pronet/v1/weekly-lossback-bonus-templates${queryString ? `?${queryString}` : ''}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<WeeklyLossbackBonusTemplateDTO>>
  ): ApiRequestResponse<PaginatedResponse<WeeklyLossbackBonusTemplate>> {
    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapWeeklyLossbackBonusTemplate),
      },
    };
  }
}
