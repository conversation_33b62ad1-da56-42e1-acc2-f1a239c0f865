import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  WeeklyLossbackBonusTemplate,
  WeeklyLossbackBonusTemplateDTO,
  mapWeeklyLossbackBonusTemplate,
} from '../../dto/WeeklyLossbackBonusTemplate.js';

export type WeeklyLossbackBonusTemplateRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsInSeconds: number | null;
  endsInSeconds: number | null;
};

export type WeeklyLossbackBonusTemplateCreateRequestOptions = {
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  requestWindowStartSeconds: number;
  requestWindowEndSeconds: number;
  validForDays?: number;
  rules: WeeklyLossbackBonusTemplateRuleCreateOptions[];
};

export class WeeklyLossbackBonusTemplateCreateRequest extends ApiRequest<
  WeeklyLossbackBonusTemplateDTO,
  WeeklyLossbackBonusTemplate
> {
  constructor(private options: WeeklyLossbackBonusTemplateCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/weekly-lossback-bonus-templates';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<WeeklyLossbackBonusTemplateDTO>
  ): ApiRequestResponse<WeeklyLossbackBonusTemplate> {
    return {
      success: true,
      data: mapWeeklyLossbackBonusTemplate(json.data),
    };
  }
}
