import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  WeeklyLossbackBonus,
  WeeklyLossbackBonusDTO,
  mapWeeklyLossbackBonus,
} from '../../dto/WeeklyLossbackBonus.js';

export type WeeklyLossbackBonusRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsAt: Date | null;
  endsAt: Date | null;
};

export type WeeklyLossbackBonusCreateRequestOptions = {
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  requestWindowStartSeconds: number;
  requestWindowEndSeconds: number;
  expiresAt: Date;
  rules: WeeklyLossbackBonusRuleCreateOptions[];
};

export class WeeklyLossbackBonusCreateRequest extends ApiRequest<
  WeeklyLossbackBonusDTO,
  WeeklyLossbackBonus
> {
  constructor(private options: WeeklyLossbackBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/weekly-lossback-bonuses';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<WeeklyLossbackBonusDTO>
  ): ApiRequestResponse<WeeklyLossbackBonus> {
    return {
      success: true,
      data: mapWeeklyLossbackBonus(json.data),
    };
  }
}
