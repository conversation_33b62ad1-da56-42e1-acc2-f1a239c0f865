import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  Customer,
  CustomerDTO,
  mapCustomer,
} from '../../dto/Customer.js';

export type CustomerSearchRequestOptions = {
  page?: number;
  limit?: number;
  username?: string;
  code?: string;
};

// Backend response format
type CustomerSearchBackendResponse = {
  customers: CustomerDTO[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
};

export class CustomerSearchRequest extends ApiRequest<
  CustomerSearchBackendResponse,
  PaginatedResponse<Customer>
> {
  constructor(private options: CustomerSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }
    if (this.options.username) {
      params.append('username', this.options.username);
    }
    if (this.options.code) {
      params.append('code', this.options.code);
    }

    return `/api/pronet/v1/customers?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<CustomerSearchBackendResponse>
  ): ApiRequestResponse<PaginatedResponse<Customer>> {
    if (typeof json.data !== 'object' || !Array.isArray(json.data.customers)) {
      return {
        success: false,
        error: 'Invalid response format',
      };
    }

    return {
      success: true,
      data: {
        items: json.data.customers.map(mapCustomer),
        page: json.data.pagination.page,
        limit: json.data.pagination.limit,
        total: json.data.pagination.total,
        totalPages: json.data.pagination.totalPages,
      },
    };
  }
}
