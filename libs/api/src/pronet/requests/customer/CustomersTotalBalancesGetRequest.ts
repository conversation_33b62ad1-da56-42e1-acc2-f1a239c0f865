import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  CustomersTotalBalances,
  CustomersTotalBalancesDTO,
  mapCustomersTotalBalances,
} from '../../dto/CustomersTotalBalances.js';

export type CustomersTotalBalancesGetRequestOptions = {
  endDate: string;
};

export class CustomersTotalBalancesGetRequest extends ApiRequest<
  CustomersTotalBalancesDTO,
  CustomersTotalBalances
> {
  constructor(private options: CustomersTotalBalancesGetRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    params.append('endDate', this.options.endDate);

    return `/api/pronet/v1/customers/total-balances?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<CustomersTotalBalancesDTO>
  ): ApiRequestResponse<CustomersTotalBalances> {
    return {
      success: true,
      data: mapCustomersTotalBalances(json.data),
    };
  }
}
