import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  Customer,
  CustomerDTO,
  mapCustomer,
} from '../../dto/Customer.js';

export type CustomerGetRequestOptions = {
  id: number;
};

// Backend response format for individual customer
type CustomerGetBackendResponse = CustomerDTO;

export class CustomerGetRequest extends ApiRequest<
  CustomerGetBackendResponse,
  Customer
> {
  constructor(private options: CustomerGetRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/customers/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<CustomerGetBackendResponse>
  ): ApiRequestResponse<Customer> {
    if (typeof json.data !== 'object' || !json.data.id) {
      return {
        success: false,
        error: 'Invalid response format',
      };
    }

    return {
      success: true,
      data: mapCustomer(json.data),
    };
  }
}
