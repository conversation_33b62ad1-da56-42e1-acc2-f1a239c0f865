import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  HappyHoursBonusTemplate,
  HappyHoursBonusTemplateDTO,
  mapHappyHoursBonusTemplate,
} from '../../dto/HappyHoursBonusTemplate.js';

export type HappyHoursBonusTemplateRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsInSeconds: number | null;
  endsInSeconds: number | null;
};

export type HappyHoursBonusTemplateCreateRequestOptions = {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  rules: HappyHoursBonusTemplateRuleCreateOptions[];
};

export class HappyHoursBonusTemplateCreateRequest extends ApiRequest<
  HappyHoursBonusTemplateDTO,
  HappyHoursBonusTemplate
> {
  constructor(private options: HappyHoursBonusTemplateCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/happy-hours-bonus-templates';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
    };
  }

  validateResponse(
    json: ApiRequestSuccessResponse<HappyHoursBonusTemplateDTO>
  ): ApiRequestResponse<HappyHoursBonusTemplate> {
    return {
      success: true,
      data: mapHappyHoursBonusTemplate(json.data),
    };
  }
}
