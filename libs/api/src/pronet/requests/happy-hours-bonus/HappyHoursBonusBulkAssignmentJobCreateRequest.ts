import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  BonusBulkAssignmentJob,
  BonusBulkAssignmentJobDTO,
  mapBonusBulkAssignmentJob,
} from '../../dto/BonusBulkAssignmentJob.js';

export type HappyHoursBonusBulkAssignmentRequestCreateOptions = {
  happyHoursBonusId: number;
  externalCustomerIds: number[];
};

export class HappyHoursBonusBulkAssignmentJobCreateRequest extends ApiRequest<
  BonusBulkAssignmentJobDTO,
  BonusBulkAssignmentJob
> {
  constructor(private options: HappyHoursBonusBulkAssignmentRequestCreateOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/happy-hours-bonuses/${this.options.happyHoursBonusId}/bulk-assignment-jobs`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {
      externalCustomerIds: this.options.externalCustomerIds,
    };
  }

  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
    };
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusBulkAssignmentJobDTO>
  ): ApiRequestResponse<BonusBulkAssignmentJob> {
    return {
      success: true,
      data: mapBonusBulkAssignmentJob(json.data),
    };
  }
}
