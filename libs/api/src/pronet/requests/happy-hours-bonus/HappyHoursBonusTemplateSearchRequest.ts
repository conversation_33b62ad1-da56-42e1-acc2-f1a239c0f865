import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  HappyHoursBonusTemplate,
  HappyHoursBonusTemplateDTO,
  mapHappyHoursBonusTemplate,
} from '../../dto/HappyHoursBonusTemplate.js';

export type HappyHoursBonusTemplateSearchRequestOptions = {
  isActive?: boolean;
  page?: number;
  limit?: number;
};

export class HappyHoursBonusTemplateSearchRequest extends ApiRequest<
  PaginatedResponse<HappyHoursBonusTemplateDTO>,
  PaginatedResponse<HappyHoursBonusTemplate>
> {
  constructor(private options: HappyHoursBonusTemplateSearchRequestOptions = {}) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/happy-hours-bonus-templates?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<HappyHoursBonusTemplateDTO>>
  ): ApiRequestResponse<PaginatedResponse<HappyHoursBonusTemplate>> {
    return {
      success: true,
      data: {
        items: json.data.items.map(mapHappyHoursBonusTemplate),
        total: json.data.total ?? json.data.items.length,
        page: json.data.page ?? 1,
        limit: json.data.limit ?? 20,
        totalPages: json.data.totalPages ?? 1,
      },
    };
  }
}
