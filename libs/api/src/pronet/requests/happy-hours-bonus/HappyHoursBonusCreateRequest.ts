import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  HappyHoursBonus,
  HappyHoursBonusDTO,
  mapHappyHoursBonus,
} from '../../dto/HappyHoursBonus.js';

export type HappyHoursBonusRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsAt: Date | null;
  endsAt: Date | null;
};

export type HappyHoursBonusCreateRequestOptions = {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  expiresAt: Date;
  rules: HappyHoursBonusRuleCreateOptions[];
};

export class HappyHoursBonusCreateRequest extends ApiRequest<
  HappyHoursBonusDTO,
  HappyHoursBonus
> {
  constructor(private options: HappyHoursBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/happy-hours-bonuses';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {
      name: this.options.name,
      externalBonusName: this.options.externalBonusName,
      externalBonusId: this.options.externalBonusId,
      amount: this.options.amount,
      expiresAt: this.options.expiresAt.toISOString(),
      rules: this.options.rules.map((rule) => ({
        ...rule,
        startsAt: rule.startsAt?.toISOString() || null,
        endsAt: rule.endsAt?.toISOString() || null,
      })),
    };
  }

  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
    };
  }

  validateResponse(
    json: ApiRequestSuccessResponse<HappyHoursBonusDTO>
  ): ApiRequestResponse<HappyHoursBonus> {
    return {
      success: true,
      data: mapHappyHoursBonus(json.data),
    };
  }
}
