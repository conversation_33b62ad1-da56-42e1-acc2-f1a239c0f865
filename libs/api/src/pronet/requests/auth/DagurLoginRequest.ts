import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';

export type DagurLoginRequestOptions = {
  username: string;
  password: string;
  otpCode: string;
};

export type DagurLoginResponse = {
  session: string;
};

export class DagurLoginRequest extends ApiRequest<DagurLoginResponse> {
  constructor(private options: DagurLoginRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/auth/dagur/login';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<DagurLoginResponse>
  ): ApiRequestResponse<DagurLoginResponse> {
    return {
      success: true,
      data: json.data,
    };
  }
}
