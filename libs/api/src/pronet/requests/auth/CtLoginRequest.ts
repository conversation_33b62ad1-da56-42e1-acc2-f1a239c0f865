import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';

export type CtLoginRequestOptions = {
  username: string;
  password: string;
  traderCode: string;
  otpCode: string;
};

export type CtLoginResponse = {
  session: string;
};

export class CtLoginRequest extends ApiRequest<CtLoginResponse> {
  constructor(private options: CtLoginRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/auth/ct/login';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<CtLoginResponse>
  ): ApiRequestResponse<CtLoginResponse> {
    return {
      success: true,
      data: json.data,
    };
  }
}
