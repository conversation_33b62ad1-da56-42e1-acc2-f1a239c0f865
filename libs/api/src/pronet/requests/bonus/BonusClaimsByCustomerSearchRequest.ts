import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  BonusClaim,
  BonusClaimDTO,
  mapBonusClaim,
} from '../../dto/BonusClaim.js';

export type BonusClaimsByCustomerSearchRequestOptions = {
  customerId: number;
  page?: number;
  limit?: number;
};

// Backend response format
type BonusClaimsByCustomerSearchBackendResponse = {
  items: BonusClaimDTO[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export class BonusClaimsByCustomerSearchRequest extends ApiRequest<
  BonusClaimsByCustomerSearchBackendResponse,
  PaginatedResponse<BonusClaim>
> {
  constructor(private options: BonusClaimsByCustomerSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    params.append('customerId', this.options.customerId.toString());
    
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/bonus-claims?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusClaimsByCustomerSearchBackendResponse>
  ): ApiRequestResponse<PaginatedResponse<BonusClaim>> {
    if (typeof json.data !== 'object' || !Array.isArray(json.data.items)) {
      return {
        success: false,
        error: 'Invalid response format',
      };
    }

    return {
      success: true,
      data: {
        items: json.data.items.map(mapBonusClaim),
        page: json.data.page,
        limit: json.data.limit,
        total: json.data.total,
        totalPages: json.data.totalPages,
      },
    };
  }
}
