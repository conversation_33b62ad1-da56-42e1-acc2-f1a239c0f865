import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  BonusClaim,
  BonusClaimDTO,
  mapBonusClaim,
} from '../../dto/BonusClaim.js';

export type BonusClaimSearchRequestOptions = {
  bonusId?: number;
  bonusType?: string;
  username?: string;
  customerId?: number;
  externalCustomerId?: number;
  page?: number;
  limit?: number;
};

export class BonusClaimSearchRequest extends ApiRequest<
  PaginatedResponse<BonusClaimDTO>,
  PaginatedResponse<BonusClaim>
> {
  constructor(private options: BonusClaimSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.bonusId) {
      params.append('bonusId', this.options.bonusId.toString());
    }
    if (this.options.bonusType) {
      params.append('bonusType', this.options.bonusType);
    }
    if (this.options.username) {
      params.append('username', this.options.username);
    }
    if (this.options.customerId) {
      params.append('customerId', this.options.customerId.toString());
    }
    if (this.options.externalCustomerId) {
      params.append(
        'externalCustomerId',
        this.options.externalCustomerId.toString()
      );
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/bonus-claims?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<BonusClaimDTO>>
  ): ApiRequestResponse<PaginatedResponse<BonusClaim>> {
    if (typeof json.data !== 'object' || !Array.isArray(json.data.items)) {
      return {
        success: false,
        error: 'Invalid response format',
      };
    }

    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapBonusClaim),
      },
    };
  }
}
