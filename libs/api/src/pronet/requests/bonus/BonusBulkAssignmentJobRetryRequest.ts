import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  BonusBulkAssignmentJob,
  BonusBulkAssignmentJobDTO,
  mapBonusBulkAssignmentJob,
} from '../../dto/BonusBulkAssignmentJob.js';

export type BonusBulkAssignmentJobRetryRequestOptions = {
  id: number;
};

export class BonusBulkAssignmentJobRetryRequest extends ApiRequest<
  BonusBulkAssignmentJobDTO,
  BonusBulkAssignmentJob
> {
  constructor(private options: BonusBulkAssignmentJobRetryRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/bonus-bulk-assignment-jobs/${this.options.id}/retries`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusBulkAssignmentJobDTO>
  ): ApiRequestResponse<BonusBulkAssignmentJob> {
    return {
      success: true,
      data: mapBonusBulkAssignmentJob(json.data),
    };
  }
}
