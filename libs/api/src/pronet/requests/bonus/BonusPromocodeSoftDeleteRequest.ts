import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';

export type BonusPromocodeSoftDeleteRequestOptions = {
  id: number;
};

export class BonusPromocodeSoftDeleteRequest extends ApiRequest<null, null> {
  constructor(private options: BonusPromocodeSoftDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/bonus-promocodes/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'DELETE';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<null>
  ): ApiRequestResponse<null> {
    return {
      success: true,
      data: json.data,
    };
  }
}
