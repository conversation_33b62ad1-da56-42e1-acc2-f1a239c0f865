import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import { Bonus, BonusDTO, mapBonus } from '../../dto/Bonus.js';

export type BonusToggleRequestOptions = {
  id: number;
  isActive: boolean;
};

export class BonusToggleRequest extends ApiRequest<BonusDTO, Bonus> {
  constructor(private options: BonusToggleRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/bonuses/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'PATCH';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusDTO>
  ): ApiRequestResponse<Bonus> {
    return {
      success: true,
      data: mapBonus(json.data),
    };
  }
}
