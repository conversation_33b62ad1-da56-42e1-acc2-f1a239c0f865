import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';

export type BonusSoftDeleteRequestOptions = {
  id: number;
};

export class BonusSoftDeleteRequest extends ApiRequest<null, null> {
  constructor(private options: BonusSoftDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/bonuses/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'DELETE';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<null>
  ): ApiRequestResponse<null> {
    return {
      success: true,
      data: json.data,
    };
  }
}
