import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';

export type BonusTemplateSoftDeleteRequestOptions = {
  id: number;
};

export class BonusTemplateSoftDeleteRequest extends ApiRequest<null, null> {
  constructor(private options: BonusTemplateSoftDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/bonus-templates/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'DELETE';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<null>
  ): ApiRequestResponse<null> {
    return {
      success: true,
      data: json.data,
    };
  }
}
