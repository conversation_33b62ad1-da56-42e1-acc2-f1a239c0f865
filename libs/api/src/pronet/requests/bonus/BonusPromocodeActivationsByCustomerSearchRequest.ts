import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  BonusPromocodeActivation,
  BonusPromocodeActivationDTO,
  mapBonusPromocodeActivation,
} from '../../dto/BonusPromocodeActivation.js';

export type BonusPromocodeActivationsByCustomerSearchRequestOptions = {
  customerId: number;
  page?: number;
  limit?: number;
};

// Backend response format
type BonusPromocodeActivationsByCustomerSearchBackendResponse = {
  items: BonusPromocodeActivationDTO[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export class BonusPromocodeActivationsByCustomerSearchRequest extends ApiRequest<
  BonusPromocodeActivationsByCustomerSearchBackendResponse,
  PaginatedResponse<BonusPromocodeActivation>
> {
  constructor(private options: BonusPromocodeActivationsByCustomerSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    params.append('customerId', this.options.customerId.toString());
    
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/bonus-promocode-activations?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusPromocodeActivationsByCustomerSearchBackendResponse>
  ): ApiRequestResponse<PaginatedResponse<BonusPromocodeActivation>> {
    if (typeof json.data !== 'object' || !Array.isArray(json.data.items)) {
      return {
        success: false,
        error: 'Invalid response format',
      };
    }

    return {
      success: true,
      data: {
        items: json.data.items.map(mapBonusPromocodeActivation),
        page: json.data.page,
        limit: json.data.limit,
        total: json.data.total,
        totalPages: json.data.totalPages,
      },
    };
  }
}
