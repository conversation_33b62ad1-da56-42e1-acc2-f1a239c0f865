import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  BonusPromocode,
  BonusPromocodeDTO,
  mapBonusPromocode,
} from '../../dto/BonusPromocode.js';

export type BonusPromocodeSearchRequestOptions = {
  isActive?: boolean;
  code?: string;
  bonusId?: number;
  bonusType?: string;
  page?: number;
  limit?: number;
};

export class BonusPromocodeSearchRequest extends ApiRequest<
  PaginatedResponse<BonusPromocodeDTO>,
  PaginatedResponse<BonusPromocode>
> {
  constructor(private options: BonusPromocodeSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }
    if (this.options.code) {
      params.append('code', this.options.code);
    }
    if (this.options.bonusId) {
      params.append('bonusId', this.options.bonusId.toString());
    }
    if (this.options.bonusType) {
      params.append('bonusType', this.options.bonusType);
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/bonus-promocodes?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<BonusPromocodeDTO>>
  ): ApiRequestResponse<PaginatedResponse<BonusPromocode>> {
    if (typeof json.data !== 'object' || !Array.isArray(json.data.items)) {
      return {
        success: false,
        error: 'Invalid response format',
      };
    }

    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapBonusPromocode),
      },
    };
  }
}
