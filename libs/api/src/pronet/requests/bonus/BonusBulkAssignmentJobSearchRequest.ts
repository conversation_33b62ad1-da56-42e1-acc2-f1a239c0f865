import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  BonusBulkAssignmentJob,
  BonusBulkAssignmentJobDTO,
  mapBonusBulkAssignmentJob,
} from '../../dto/BonusBulkAssignmentJob.js';

export type BonusBulkAssignmentJobSearchRequestOptions = {
  bonusId?: number;
  bonusType?: string;
  status?: string;
  page?: number;
  limit?: number;
};

export class BonusBulkAssignmentJobSearchRequest extends ApiRequest<
  PaginatedResponse<BonusBulkAssignmentJobDTO>,
  PaginatedResponse<BonusBulkAssignmentJob>
> {
  constructor(private options: BonusBulkAssignmentJobSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.bonusId) {
      params.append('bonusId', this.options.bonusId.toString());
    }
    if (this.options.bonusType) {
      params.append('bonusType', this.options.bonusType);
    }
    if (this.options.status) {
      params.append('status', this.options.status);
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/bonus-bulk-assignment-jobs?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<
      PaginatedResponse<BonusBulkAssignmentJobDTO>
    >
  ): ApiRequestResponse<PaginatedResponse<BonusBulkAssignmentJob>> {
    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapBonusBulkAssignmentJob),
      },
    };
  }
}
