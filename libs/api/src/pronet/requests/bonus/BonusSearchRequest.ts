import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import { Bonus, BonusDTO, mapBonus } from '../../dto/Bonus.js';

export type BonusSearchRequestOptions = {
  isActive?: boolean;
  type?: string;
  page?: number;
  limit?: number;
};

export class BonusSearchRequest extends ApiRequest<
  PaginatedResponse<BonusDTO>,
  PaginatedResponse<Bonus>
> {
  constructor(private options: BonusSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }
    if (this.options.type) {
      params.append('type', this.options.type);
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/bonuses?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<BonusDTO>>
  ): ApiRequestResponse<PaginatedResponse<Bonus>> {
    if (typeof json.data !== 'object' || !Array.isArray(json.data.items)) {
      return {
        success: false,
        error: 'Invalid response format',
      };
    }

    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapBonus),
      },
    };
  }
}
