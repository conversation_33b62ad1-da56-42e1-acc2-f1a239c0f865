import {
  BonusPromocode,
  BonusPromocodeDTO,
  mapBonusPromocode,
} from '../../dto/BonusPromocode.js';
import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';

export type BonusPromocodeToggleRequestOptions = {
  id: number;
  isActive: boolean;
};

export class BonusPromocodeToggleRequest extends ApiRequest<
  BonusPromocodeDTO,
  BonusPromocode
> {
  constructor(private options: BonusPromocodeToggleRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/bonus-promocodes/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'PATCH';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusPromocodeDTO>
  ): ApiRequestResponse<BonusPromocode> {
    return {
      success: true,
      data: mapBonusPromocode(json.data),
    };
  }
}
