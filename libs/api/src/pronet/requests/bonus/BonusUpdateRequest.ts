import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import { Bonus, BonusDTO, mapBonus } from '../../dto/Bonus.js';

export type BonusUpdateRequestOptions = {
  id: number;
  description?: string;
  reward?: string;
};

export class BonusUpdateRequest extends ApiRequest<BonusDTO, Bonus> {
  constructor(private options: BonusUpdateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/bonuses/${this.options.id}`;
  }

  getMethod(): HttpMethod {
    return 'PATCH';
  }

  getBody() {
    const body: { description?: string; reward?: string } = {};
    
    if (this.options.description !== undefined) {
      body.description = this.options.description;
    }
    
    if (this.options.reward !== undefined) {
      body.reward = this.options.reward;
    }
    
    return body;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusDTO>
  ): ApiRequestResponse<Bonus> {
    return {
      success: true,
      data: mapBonus(json.data),
    };
  }
}
