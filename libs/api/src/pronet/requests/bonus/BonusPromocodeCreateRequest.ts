import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  BonusPromocode,
  BonusPromocodeDTO,
  mapBonusPromocode,
} from '../../dto/BonusPromocode.js';

export type BonusPromocodeCreateRequestOptions = {
  bonusId: number;
  code: string;
  maxActivations: number | null;
};

export class BonusPromocodeCreateRequest extends ApiRequest<
  BonusPromocodeDTO,
  BonusPromocode
> {
  constructor(private options: BonusPromocodeCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/bonuses/${this.options.bonusId}/promocodes`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusPromocodeDTO>
  ): ApiRequestResponse<BonusPromocode> {
    return {
      success: true,
      data: mapBonusPromocode(json.data),
    };
  }
}
