import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  BonusBulkAssignmentJob,
  BonusBulkAssignmentJobDTO,
  mapBonusBulkAssignmentJob,
} from '../../dto/BonusBulkAssignmentJob.js';

export type BonusBulkAssignmentJobCancelRequestOptions = {
  id: number;
};

export class BonusBulkAssignmentJobCancelRequest extends ApiRequest<
  BonusBulkAssignmentJobDTO,
  BonusBulkAssignmentJob
> {
  constructor(private options: BonusBulkAssignmentJobCancelRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/bonus-bulk-assignment-jobs/${this.options.id}/cancellations`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusBulkAssignmentJobDTO>
  ): ApiRequestResponse<BonusBulkAssignmentJob> {
    return {
      success: true,
      data: mapBonusBulkAssignmentJob(json.data),
    };
  }
}
