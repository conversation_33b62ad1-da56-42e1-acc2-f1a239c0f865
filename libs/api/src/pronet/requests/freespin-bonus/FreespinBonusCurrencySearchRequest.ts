import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import { Currency, CurrencyDTO, mapCurrency } from '../../dto/Currency.js';

export type FreespinBonusCurrencySearchRequestOptions = {
  providerId: number;
};

export class FreespinBonusCurrencySearchRequest extends ApiRequest<
  CurrencyDTO[],
  Currency[]
> {
  constructor(private options: FreespinBonusCurrencySearchRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/freespin-bonuses/providers/${this.options.providerId}/currencies`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<CurrencyDTO[]>
  ): ApiRequestResponse<Currency[]> {
    return {
      success: true,
      data: json.data.map(mapCurrency),
    };
  }
}
