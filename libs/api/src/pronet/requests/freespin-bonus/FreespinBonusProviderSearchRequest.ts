import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  FreespinBonusProvider,
  FreespinBonusProviderDTO,
  mapFreespinBonusProvider,
} from '../../dto/FreespinBonusProvider.js';

export class FreespinBonusProviderSearchRequest extends ApiRequest<
  FreespinBonusProviderDTO[],
  FreespinBonusProvider[]
> {
  getPath(): string {
    return `/api/pronet/v1/freespin-bonuses/providers`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<FreespinBonusProviderDTO[]>
  ): ApiRequestResponse<FreespinBonusProvider[]> {
    return {
      success: true,
      data: json.data.map(mapFreespinBonusProvider),
    };
  }
}
