import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  BonusBulkAssignmentJob,
  BonusBulkAssignmentJobDTO,
  mapBonusBulkAssignmentJob,
} from '../../dto/BonusBulkAssignmentJob.js';

export type FreespinBonusBulkAssignmentRequestCreateOptions = {
  freespinBonusId: number;
  externalCustomerIds: number[];
};

export class FreespinBonusBulkAssignmentJobCreateRequest extends ApiRequest<
  BonusBulkAssignmentJobDTO,
  BonusBulkAssignmentJob
> {
  constructor(
    private options: FreespinBonusBulkAssignmentRequestCreateOptions
  ) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/freespin-bonuses/${this.options.freespinBonusId}/bulk-assignment-jobs`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusBulkAssignmentJobDTO>
  ): ApiRequestResponse<BonusBulkAssignmentJob> {
    return {
      success: true,
      data: mapBonusBulkAssignmentJob(json.data),
    };
  }
}
