import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  FreespinBonusTemplate,
  FreespinBonusTemplateDTO,
  mapFreespinBonusTemplate,
} from '../../dto/FreespinBonusTemplate.js';

export type FreespinBonusTemplateRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsInSeconds: number | null;
  endsInSeconds: number | null;
};

export type FreespinBonusTemplateCreateRequestOptions = {
  name: string;
  providerId: number;
  vendorName: string;
  values: Record<string, unknown>;
  gameIds: number[];
  validForDays: number;
  rules: FreespinBonusTemplateRuleCreateOptions[];
};

export class FreespinBonusTemplateCreateRequest extends ApiRequest<
  FreespinBonusTemplateDTO,
  FreespinBonusTemplate
> {
  constructor(private options: FreespinBonusTemplateCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/freespin-bonus-templates';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {
      ...this.options,
      // @todo change api
      vendorId: this.options.providerId,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<FreespinBonusTemplateDTO>
  ): ApiRequestResponse<FreespinBonusTemplate> {
    return {
      success: true,
      data: mapFreespinBonusTemplate(json.data),
    };
  }
}
