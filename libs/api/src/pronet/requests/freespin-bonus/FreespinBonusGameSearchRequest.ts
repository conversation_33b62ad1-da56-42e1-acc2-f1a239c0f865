import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  FreespinBonusGame,
  FreespinBonusGameDTO,
  mapFreespinBonusGame,
} from '../../dto/FreespinBonusGame.js';

export type FreespinBonusGameSearchRequestOptions = {
  name?: string;
  providerId?: number;
  page?: number;
  limit?: number;
};

export class FreespinBonusGameSearchRequest extends ApiRequest<
  FreespinBonusGameDTO[],
  FreespinBonusGame[]
> {
  constructor(private options: FreespinBonusGameSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.name) {
      params.append('name', this.options.name);
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/freespin-bonuses/providers/${this.options.providerId}/games?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<FreespinBonusGameDTO[]>
  ): ApiRequestResponse<FreespinBonusGame[]> {
    return {
      success: true,
      data: json.data.map(mapFreespinBonusGame),
    };
  }
}
