import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  FreespinBonus,
  FreespinBonusDTO,
  mapFreespinBonus,
} from '../../dto/FreespinBonus.js';

export type FreespinBonusRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsAt: Date | null;
  endsAt: Date | null;
};

export type FreespinBonusCreateRequestOptions = {
  name: string;
  providerId: number;
  vendorName: string;
  values: Record<string, unknown>;
  gameIds: number[];
  expiresAt: Date;
  rules: FreespinBonusRuleCreateOptions[];
};

export class FreespinBonusCreateRequest extends ApiRequest<
  FreespinBonusDTO,
  FreespinBonus
> {
  constructor(private options: FreespinBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/freespin-bonuses';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {
      ...this.options,
      // @todo change api
      vendorId: this.options.providerId,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<FreespinBonusDTO>
  ): ApiRequestResponse<FreespinBonus> {
    return {
      success: true,
      data: mapFreespinBonus(json.data),
    };
  }
}
