import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  FreespinBonusTemplate,
  FreespinBonusTemplateDTO,
  mapFreespinBonusTemplate,
} from '../../dto/FreespinBonusTemplate.js';

export type FreespinBonusTemplateSearchRequestOptions = {
  isActive?: boolean;
  page?: number;
  limit?: number;
};

export class FreespinBonusTemplateSearchRequest extends ApiRequest<
  PaginatedResponse<FreespinBonusTemplateDTO>,
  PaginatedResponse<FreespinBonusTemplate>
> {
  constructor(private options: FreespinBonusTemplateSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/freespin-bonus-templates?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<FreespinBonusTemplateDTO>>
  ): ApiRequestResponse<PaginatedResponse<FreespinBonusTemplate>> {
    if (typeof json.data !== 'object' || !Array.isArray(json.data.items)) {
      return {
        success: false,
        error: 'Invalid response format',
      };
    }

    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapFreespinBonusTemplate),
      },
    };
  }
}
