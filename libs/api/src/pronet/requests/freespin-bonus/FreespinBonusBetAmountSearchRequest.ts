import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  FreespinBonusBetAmount,
  FreespinBonusBetAmountDTO,
} from '../../dto/FreespinBonusBetAmount.js';

export type FreespinBonusBetAmountSearchRequestOptions = {
  providerId: number;
  gameIds: number[];
  currencyId: number;
};

export class FreespinBonusBetAmountSearchRequest extends ApiRequest<
  FreespinBonusBetAmountDTO[],
  FreespinBonusBetAmount[]
> {
  constructor(private options: FreespinBonusBetAmountSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    this.options.gameIds.forEach((id) =>
      params.append('gameId', id.toString())
    );
    params.append('currencyId', this.options.currencyId.toString());

    return `/api/pronet/v1/freespin-bonuses/providers/${this.options.providerId}/bet-amounts?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<FreespinBonusBetAmountDTO[]>
  ): ApiRequestResponse<FreespinBonusBetAmount[]> {
    return {
      success: true,
      data: json.data,
    };
  }
}
