import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import { CashBonus, CashBonusDTO, mapCashBonus } from '../../dto/CashBonus.js';

export type CashBonusSearchRequestOptions = {
  isActive?: boolean;
  page?: number;
  limit?: number;
};

export class CashBonusSearchRequest extends ApiRequest<
  PaginatedResponse<CashBonusDTO>,
  PaginatedResponse<CashBonus>
> {
  constructor(private options: CashBonusSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/cash-bonuses?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<CashBonusDTO>>
  ): ApiRequestResponse<PaginatedResponse<CashBonus>> {
    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapCashBonus),
      },
    };
  }
}
