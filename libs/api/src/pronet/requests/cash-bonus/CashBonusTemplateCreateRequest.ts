import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  CashBonusTemplate,
  CashBonusTemplateDTO,
  mapCashBonusTemplate,
} from '../../dto/CashBonusTemplate.js';
import { BonusRuleCreateOptions } from '../../dto/BonusRule.js';

export type CashBonusTemplateCreateRequestOptions = {
  name: string;
  cashAmount: number;
  rules: BonusRuleCreateOptions[];
  expiresAt?: Date;
  isActive?: boolean;
};

export class CashBonusTemplateCreateRequest extends ApiRequest<
  CashBonusTemplateDTO,
  CashBonusTemplate
> {
  constructor(private options: CashBonusTemplateCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/cash-bonus-templates';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<CashBonusTemplateDTO>
  ): ApiRequestResponse<CashBonusTemplate> {
    return {
      success: true,
      data: mapCashBonusTemplate(json.data),
    };
  }
}
