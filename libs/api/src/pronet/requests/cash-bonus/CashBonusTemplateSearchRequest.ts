import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  CashBonusTemplate,
  CashBonusTemplateDTO,
  mapCashBonusTemplate,
} from '../../dto/CashBonusTemplate.js';

export type CashBonusTemplateSearchRequestOptions = {
  isActive?: boolean;
  page?: number;
  limit?: number;
};

export class CashBonusTemplateSearchRequest extends ApiRequest<
  PaginatedResponse<CashBonusTemplateDTO>,
  PaginatedResponse<CashBonusTemplate>
> {
  constructor(private options: CashBonusTemplateSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/cash-bonus-templates?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<CashBonusTemplateDTO>>
  ): ApiRequestResponse<PaginatedResponse<CashBonusTemplate>> {
    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapCashBonusTemplate),
      },
    };
  }
}
