import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  CashBonus,
  CashBonusDTO,
  mapCashBonus,
} from '../../dto/CashBonus.js';
import { BonusRuleCreateOptions } from '../../dto/BonusRule.js';

export type CashBonusCreateRequestOptions = {
  name: string;
  cashAmount: number;
  rules: BonusRuleCreateOptions[];
  expiresAt?: Date;
  isActive?: boolean;
};

export class CashBonusCreateRequest extends ApiRequest<
  CashBonusDTO,
  CashBonus
> {
  constructor(private options: CashBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/cash-bonuses';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<CashBonusDTO>
  ): ApiRequestResponse<CashBonus> {
    return {
      success: true,
      data: mapCashBonus(json.data),
    };
  }
}
