import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  BonusBulkAssignmentJob,
  BonusBulkAssignmentJobDTO,
  mapBonusBulkAssignmentJob,
} from '../../dto/BonusBulkAssignmentJob.js';

export type CashBonusBulkAssignmentRequestCreateOptions = {
  cashBonusId: number;
  externalCustomerIds: number[];
};

export class CashBonusBulkAssignmentJobCreateRequest extends ApiRequest<
  BonusBulkAssignmentJobDTO,
  BonusBulkAssignmentJob
> {
  constructor(private options: CashBonusBulkAssignmentRequestCreateOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/cash-bonuses/${this.options.cashBonusId}/bulk-assignment-jobs`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {
      externalCustomerIds: this.options.externalCustomerIds,
    };
  }

  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
    };
  }

  validateResponse(
    json: ApiRequestSuccessResponse<BonusBulkAssignmentJobDTO>
  ): ApiRequestResponse<BonusBulkAssignmentJob> {
    return {
      success: true,
      data: mapBonusBulkAssignmentJob(json.data),
    };
  }
}
