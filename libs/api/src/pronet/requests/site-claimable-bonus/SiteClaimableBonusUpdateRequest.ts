import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  SiteClaimableBonus,
  SiteClaimableBonusDTO,
  SiteClaimableBonusUpdateDTO,
  mapSiteClaimableBonus,
} from '../../dto/SiteClaimableBonus.js';

export type SiteClaimableBonusUpdateRequestOptions = {
  slotName: string;
  bonusId: number; // Only accept valid numbers, use remove request for null
};

export class SiteClaimableBonusUpdateRequest extends ApiRequest<
  SiteClaimableBonusDTO,
  SiteClaimableBonus
> {
  constructor(private options: SiteClaimableBonusUpdateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/site-claimable-bonuses/${this.options.slotName}`;
  }

  getMethod(): HttpMethod {
    return 'PUT';
  }

  getBody(): SiteClaimableBonusUpdateDTO {
    return {
      bonusId: this.options.bonusId,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<SiteClaimableBonusDTO>
  ): ApiRequestResponse<SiteClaimableBonus> {
    return {
      success: true,
      data: mapSiteClaimableBonus(json.data),
    };
  }
}
