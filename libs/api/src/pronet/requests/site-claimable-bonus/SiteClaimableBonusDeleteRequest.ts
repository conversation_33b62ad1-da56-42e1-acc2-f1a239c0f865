import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';

export type SiteClaimableBonusDeleteRequestOptions = {
  slotName: string;
};

export class SiteClaimableBonusDeleteRequest extends ApiRequest<null, null> {
  constructor(private options: SiteClaimableBonusDeleteRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/site-claimable-bonuses/${this.options.slotName}`;
  }

  getMethod(): HttpMethod {
    return 'DELETE';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<null>
  ): ApiRequestResponse<null> {
    return {
      success: true,
      data: json.data,
    };
  }
}
