import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  SiteClaimableBonus,
  SiteClaimableBonusDTO,
  mapSiteClaimableBonus,
} from '../../dto/SiteClaimableBonus.js';

export class SiteClaimableBonusClaimableRequest extends ApiRequest<
  SiteClaimableBonusDTO[],
  SiteClaimableBonus[]
> {
  getPath(): string {
    return `/api/pronet/v1/site-claimable-bonuses/claimable`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<SiteClaimableBonusDTO[]>
  ): ApiRequestResponse<SiteClaimableBonus[]> {
    return {
      success: true,
      data: json.data.map(mapSiteClaimableBonus),
    };
  }
}
