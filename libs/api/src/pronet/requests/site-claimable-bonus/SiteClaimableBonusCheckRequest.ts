import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  ClaimableBonusCheck,
  ClaimableBonusCheckDTO,
  mapClaimableBonusCheck,
} from '../../dto/SiteClaimableBonus.js';

export type SiteClaimableBonusCheckRequestOptions = {
  bonusId: number;
};

export class SiteClaimableBonusCheckRequest extends ApiRequest<
  ClaimableBonusCheckDTO,
  ClaimableBonusCheck
> {
  constructor(private options: SiteClaimableBonusCheckRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/site-claimable-bonuses/check/${this.options.bonusId}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return {};
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<ClaimableBonusCheckDTO>
  ): ApiRequestResponse<ClaimableBonusCheck> {
    return {
      success: true,
      data: mapClaimableBonusCheck(json.data),
    };
  }
}
