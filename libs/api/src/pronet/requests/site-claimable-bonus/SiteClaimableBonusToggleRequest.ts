import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  SiteClaimableBonus,
  SiteClaimableBonusDTO,
  SiteClaimableBonusToggleDTO,
  mapSiteClaimableBonus,
} from '../../dto/SiteClaimableBonus.js';

export type SiteClaimableBonusToggleRequestOptions = {
  slotName: string;
  isActive: boolean;
};

export class SiteClaimableBonusToggleRequest extends ApiRequest<
  SiteClaimableBonusDTO,
  SiteClaimableBonus
> {
  constructor(private options: SiteClaimableBonusToggleRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/site-claimable-bonuses/${this.options.slotName}/toggle`;
  }

  getMethod(): HttpMethod {
    return 'PATCH';
  }

  getBody(): SiteClaimableBonusToggleDTO {
    return {
      isActive: this.options.isActive,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<SiteClaimableBonusDTO>
  ): ApiRequestResponse<SiteClaimableBonus> {
    return {
      success: true,
      data: mapSiteClaimableBonus(json.data),
    };
  }
}
