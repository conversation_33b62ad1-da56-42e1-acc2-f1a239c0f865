import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  SiteClaimableBonus,
  SiteClaimableBonusDTO,
  mapSiteClaimableBonus,
} from '../../dto/SiteClaimableBonus.js';

export type SiteClaimableBonusCreateRequestOptions = {
  slotName: string;
  bonusId: number;
  isActive: boolean;
};

export class SiteClaimableBonusCreateRequest extends ApiRequest<
  SiteClaimableBonusDTO,
  SiteClaimableBonus
> {
  constructor(private options: SiteClaimableBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/site-claimable-bonuses`;
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {
      slotName: this.options.slotName,
      bonusId: this.options.bonusId,
      isActive: this.options.isActive,
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<SiteClaimableBonusDTO>
  ): ApiRequestResponse<SiteClaimableBonus> {
    return {
      success: true,
      data: mapSiteClaimableBonus(json.data),
    };
  }
}
