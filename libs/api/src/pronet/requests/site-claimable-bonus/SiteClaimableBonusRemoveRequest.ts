import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  SiteClaimableBonus,
  SiteClaimableBonusDTO,
  mapSiteClaimableBonus,
} from '../../dto/SiteClaimableBonus.js';

export type SiteClaimableBonusRemoveRequestOptions = {
  slotName: string;
};

export class SiteClaimableBonusRemoveRequest extends ApiRequest<
  SiteClaimableBonusDTO,
  SiteClaimableBonus
> {
  constructor(private options: SiteClaimableBonusRemoveRequestOptions) {
    super();
  }

  getPath(): string {
    return `/api/pronet/v1/site-claimable-bonuses/${this.options.slotName}`;
  }

  getMethod(): HttpMethod {
    return 'PUT';
  }

  getBody() {
    // Try sending bonusId: 0 to indicate removal
    // This might be what the API expects for removing assignments
    return {
      bonusId: 0
    };
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<SiteClaimableBonusDTO>
  ): ApiRequestResponse<SiteClaimableBonus> {
    return {
      success: true,
      data: mapSiteClaimableBonus(json.data),
    };
  }
}
