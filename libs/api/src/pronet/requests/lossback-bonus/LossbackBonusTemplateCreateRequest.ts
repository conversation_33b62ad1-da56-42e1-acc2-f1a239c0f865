import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  LossbackBonusTemplate,
  LossbackBonusTemplateDTO,
  mapLossbackBonusTemplate,
} from '../../dto/LossbackBonusTemplate.js';

export type LossbackBonusTemplateRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsInSeconds: number | null;
  endsInSeconds: number | null;
};

export type LossbackBonusTemplateCreateRequestOptions = {
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  validForDays?: number;
  rules: LossbackBonusTemplateRuleCreateOptions[];
};

export class LossbackBonusTemplateCreateRequest extends ApiRequest<
  LossbackBonusTemplateDTO,
  LossbackBonusTemplate
> {
  constructor(private options: LossbackBonusTemplateCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/lossback-bonus-templates';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<LossbackBonusTemplateDTO>
  ): ApiRequestResponse<LossbackBonusTemplate> {
    return {
      success: true,
      data: mapLossbackBonusTemplate(json.data),
    };
  }
}
