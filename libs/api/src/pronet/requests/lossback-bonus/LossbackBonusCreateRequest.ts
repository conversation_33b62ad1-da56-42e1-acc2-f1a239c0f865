import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  LossbackBonus,
  LossbackBonusDTO,
  mapLossbackBonus,
} from '../../dto/LossbackBonus.js';

export type LossbackBonusRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsAt: Date | null;
  endsAt: Date | null;
};

export type LossbackBonusCreateRequestOptions = {
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string; // 18:00:00
  happyHoursEnd: string; // 22:00:00
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  expiresAt: Date;
  rules: LossbackBonusRuleCreateOptions[];
};

export class LossbackBonusCreateRequest extends ApiRequest<
  LossbackBonusDTO,
  LossbackBonus
> {
  constructor(private options: LossbackBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/lossback-bonuses';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<LossbackBonusDTO>
  ): ApiRequestResponse<LossbackBonus> {
    return {
      success: true,
      data: mapLossbackBonus(json.data),
    };
  }
}
