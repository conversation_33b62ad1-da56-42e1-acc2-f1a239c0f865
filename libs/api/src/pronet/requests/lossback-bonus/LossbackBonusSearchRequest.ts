import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  LossbackBonus,
  LossbackBonusDTO,
  mapLossbackBonus,
} from '../../dto/LossbackBonus.js';

export type LossbackBonusSearchRequestOptions = {
  isActive?: boolean;
  page?: number;
  limit?: number;
};

export class LossbackBonusSearchRequest extends ApiRequest<
  PaginatedResponse<LossbackBonusDTO>,
  PaginatedResponse<LossbackBonus>
> {
  constructor(private options: LossbackBonusSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/lossback-bonuses?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<LossbackBonusDTO>>
  ): ApiRequestResponse<PaginatedResponse<LossbackBonus>> {
    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapLossbackBonus),
      },
    };
  }
}
