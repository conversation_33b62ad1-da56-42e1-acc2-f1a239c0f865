import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  LossbackBonusTemplate,
  LossbackBonusTemplateDTO,
  mapLossbackBonusTemplate,
} from '../../dto/LossbackBonusTemplate.js';

export type LossbackBonusTemplateSearchRequestOptions = {
  isActive?: boolean;
  page?: number;
  limit?: number;
};

export class LossbackBonusTemplateSearchRequest extends ApiRequest<
  PaginatedResponse<LossbackBonusTemplateDTO>,
  PaginatedResponse<LossbackBonusTemplate>
> {
  constructor(private options: LossbackBonusTemplateSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/lossback-bonus-templates?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<LossbackBonusTemplateDTO>>
  ): ApiRequestResponse<PaginatedResponse<LossbackBonusTemplate>> {
    return {
      success: true,
      data: {
        ...json.data,
        items: json.data.items.map(mapLossbackBonusTemplate),
      },
    };
  }
}
