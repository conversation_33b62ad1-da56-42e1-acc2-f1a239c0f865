import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  TrialBonusTemplate,
  TrialBonusTemplateDTO,
  mapTrialBonusTemplate,
} from '../../dto/TrialBonusTemplate.js';

export type TrialBonusTemplateRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsAt: Date | null;
  endsAt: Date | null;
};

export type TrialBonusTemplateCreateRequestOptions = {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  validForDays: number;
  rules: TrialBonusTemplateRuleCreateOptions[];
};

export class TrialBonusTemplateCreateRequest extends ApiRequest<
  TrialBonusTemplateDTO,
  TrialBonusTemplate
> {
  constructor(private options: TrialBonusTemplateCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/trial-bonus-templates';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {
      name: this.options.name,
      externalBonusName: this.options.externalBonusName,
      externalBonusId: this.options.externalBonusId,
      amount: this.options.amount,
      validForDays: this.options.validForDays,
      rules: this.options.rules.map((rule) => ({
        ...rule,
        startsAt: rule.startsAt?.toISOString() || null,
        endsAt: rule.endsAt?.toISOString() || null,
      })),
    };
  }

  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
    };
  }

  validateResponse(
    json: ApiRequestSuccessResponse<TrialBonusTemplateDTO>
  ): ApiRequestResponse<TrialBonusTemplate> {
    return {
      success: true,
      data: mapTrialBonusTemplate(json.data),
    };
  }
}
