import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  TrialBonus,
  TrialBonusDTO,
  mapTrialBonus,
} from '../../dto/TrialBonus.js';

export type TrialBonusSearchRequestOptions = {
  isActive?: boolean;
  page?: number;
  limit?: number;
};

export class TrialBonusSearchRequest extends ApiRequest<
  PaginatedResponse<TrialBonusDTO>,
  PaginatedResponse<TrialBonus>
> {
  constructor(private options: TrialBonusSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/trial-bonuses?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<TrialBonusDTO>>
  ): ApiRequestResponse<PaginatedResponse<TrialBonus>> {
    return {
      success: true,
      data: {
        items: json.data.items.map(mapTrialBonus),
        total: json.data.total ?? json.data.items.length,
        page: json.data.page ?? 1,
        limit: json.data.limit ?? 20,
        totalPages: json.data.totalPages ?? 1,
      },
    };
  }
}
