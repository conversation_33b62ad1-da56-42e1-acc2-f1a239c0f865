import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';
import {
  TrialBonusTemplate,
  TrialBonusTemplateDTO,
  mapTrialBonusTemplate,
} from '../../dto/TrialBonusTemplate.js';

export type TrialBonusTemplateSearchRequestOptions = {
  page?: number;
  limit?: number;
  isActive?: boolean;
};

export class TrialBonusTemplateSearchRequest extends ApiRequest<
  PaginatedResponse<TrialBonusTemplateDTO>,
  PaginatedResponse<TrialBonusTemplate>
> {
  constructor(private options: TrialBonusTemplateSearchRequestOptions) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }
    if (this.options.isActive !== undefined) {
      params.append('isActive', this.options.isActive.toString());
    }

    return `/api/pronet/v1/trial-bonus-templates?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<PaginatedResponse<TrialBonusTemplateDTO>>
  ): ApiRequestResponse<PaginatedResponse<TrialBonusTemplate>> {
    return {
      success: true,
      data: {
        items: json.data.items.map(mapTrialBonusTemplate),
        total: json.data.total ?? json.data.items.length,
        page: json.data.page ?? 1,
        limit: json.data.limit ?? 20,
        totalPages: json.data.totalPages ?? 1,
      },
    };
  }
}
