import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
} from '../../../ApiClient.js';
import {
  TrialBonus,
  TrialBonusDTO,
  mapTrialBonus,
} from '../../dto/TrialBonus.js';

export type TrialBonusRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsAt: Date | null;
  endsAt: Date | null;
};

export type TrialBonusCreateRequestOptions = {
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;
  expiresAt: Date;
  rules: TrialBonusRuleCreateOptions[];
};

export class TrialBonusCreateRequest extends ApiRequest<
  TrialBonusDTO,
  TrialBonus
> {
  constructor(private options: TrialBonusCreateRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/pronet/v1/trial-bonuses';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return {
      name: this.options.name,
      externalBonusName: this.options.externalBonusName,
      externalBonusId: this.options.externalBonusId,
      amount: this.options.amount,
      expiresAt: this.options.expiresAt.toISOString(),
      rules: this.options.rules.map((rule) => ({
        ...rule,
        startsAt: rule.startsAt?.toISOString() || null,
        endsAt: rule.endsAt?.toISOString() || null,
      })),
    };
  }

  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
    };
  }

  validateResponse(
    json: ApiRequestSuccessResponse<TrialBonusDTO>
  ): ApiRequestResponse<TrialBonus> {
    return {
      success: true,
      data: mapTrialBonus(json.data),
    };
  }
}
