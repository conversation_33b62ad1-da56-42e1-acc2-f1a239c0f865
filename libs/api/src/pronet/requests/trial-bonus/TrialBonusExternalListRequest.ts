import {
  ApiRequest,
  ApiRequestResponse,
  ApiRequestSuccessResponse,
  HttpMethod,
  PaginatedResponse,
} from '../../../ApiClient.js';

export type TrialBonusExternalDTO = {
  id: number;
  type: string;
  name: string;
  description: string;
  code: string;
  startDate: string;
  endDate: string;
  status: string;
};

export type TrialBonusExternal = {
  id: number;
  type: string;
  name: string;
  description: string;
  code: string;
  startDate: Date;
  endDate: Date;
  status: string;
};

export const mapTrialBonusExternal = (
  external: TrialBonusExternalDTO
): TrialBonusExternal => ({
  id: external.id,
  type: external.type,
  name: external.name,
  description: external.description,
  code: external.code,
  startDate: new Date(external.startDate),
  endDate: new Date(external.endDate),
  status: external.status,
});

export type TrialBonusExternalListRequestOptions = {
  page?: number;
  limit?: number;
};

export class TrialBonusExternalListRequest extends ApiRequest<
  TrialBonusExternalDTO[],
  PaginatedResponse<TrialBonusExternal>
> {
  constructor(private options: TrialBonusExternalListRequestOptions = {}) {
    super();
  }

  getPath(): string {
    const params = new URLSearchParams();
    if (this.options.page) {
      params.append('page', this.options.page.toString());
    }
    if (this.options.limit) {
      params.append('limit', this.options.limit.toString());
    }

    return `/api/pronet/v1/external-trial-bonuses?${params}`;
  }

  getMethod(): HttpMethod {
    return 'GET';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }

  validateResponse(
    json: ApiRequestSuccessResponse<TrialBonusExternalDTO[]>
  ): ApiRequestResponse<PaginatedResponse<TrialBonusExternal>> {
    return {
      success: true,
      data: {
        items: json.data.map(mapTrialBonusExternal),
        total: json.data.length,
        page: this.options.page ?? 1,
        limit: this.options.limit ?? 20,
        totalPages: 1,
      },
    };
  }
}
