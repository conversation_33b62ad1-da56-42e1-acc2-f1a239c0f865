import { ApiRequest, HttpMethod } from '../../ApiClient.js';

export type InternalLoginRequestOptions = {
  username: string;
  password: string;
  traderCode: string;
  otpCode: string;
};

export type InternalLoginResponse = {
  session: string;
};

export class InternalLoginRequest extends ApiRequest<InternalLoginResponse> {
  constructor(private options: InternalLoginRequestOptions) {
    super();
  }

  getPath(): string {
    return '/api/internal/v1/auth/login';
  }

  getMethod(): HttpMethod {
    return 'POST';
  }

  getBody() {
    return this.options;
  }

  getHeaders(): Record<string, string> {
    return {};
  }
}
