export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export abstract class ApiRequest<
  ResponseModel = unknown,
  Model = unknown,
  Body = unknown
> {
  abstract getPath(): string;

  abstract getMethod(): HttpMethod;

  abstract getBody(): Body;

  abstract getHeaders(): Record<string, string>;

  abstract validateResponse(
    json: ApiRequestSuccessResponse<ResponseModel>
  ): ApiRequestResponse<Model>;
}

export type PaginatedResponse<T> = {
  items: T[];
  page: number;
  limit: number;
  total: number;
  totalPages: number;
};

export type ApiRequestErrorResponse = {
  success: false;
  error: string;
};

export type ApiRequestSuccessResponse<T> = {
  success: true;
  data: T;
};

export type ApiRequestResponse<T> =
  | ApiRequestSuccessResponse<T>
  | ApiRequestErrorResponse;

// Configuration interface for the API client
export interface ApiClientConfig {
  baseUrl: string;
  timeout?: number;
  defaultHeaders?: Record<string, string>;
}

// Error classes for different types of API errors
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: unknown
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class NetworkError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'NetworkError';
  }
}

export class TimeoutError extends Error {
  constructor(message = 'Request timeout') {
    super(message);
    this.name = 'TimeoutError';
  }
}

// Main API Client class
export class ApiClient {
  private config: Required<ApiClientConfig>;

  constructor(config: ApiClientConfig) {
    this.config = {
      timeout: 30000, // 30 seconds default timeout
      defaultHeaders: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      ...config,
    };
  }

  /**
   * Makes an HTTP request using the provided ApiRequest instance
   */
  async makeRequest<R, M>(
    request: ApiRequest<R, M>,
    additionalHeaders: Record<string, string> = {}
  ): Promise<ApiRequestResponse<M>> {
    try {
      const url = this.buildUrl(request.getPath());
      const method = request.getMethod();
      const headers: Record<string, string> = {
        ...request.getHeaders(),
        ...this.config.defaultHeaders,
        ...additionalHeaders,
      };
      const body = this.prepareBody(request, method);

      const response = await this.fetchWithTimeout(url, {
        method,
        headers,
        body,
      });

      const text = await response.text();
      let json: Record<string, unknown>;
      if (response.ok) {
        json = JSON.parse(text);
      } else {
        try {
          json = await JSON.parse(text);
        } catch (e) {
          return {
            success: false,
            error: (e as Error).message ?? 'Failed to parse error response',
          };
        }
      }

      if (typeof json !== 'object' || 'success' in json === false) {
        return {
          success: false,
          error: 'Invalid response format',
        };
      }

      if (json.success) {
        if ('data' in json === false) {
          return {
            success: false,
            error: 'Invalid response format',
          };
        }

        return request.validateResponse(json as ApiRequestSuccessResponse<R>);
      } else {
        return {
          success: false,
          error: (json.error as string) ?? (json.message as string) ?? 'Something went wrong',
        };
      }
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Builds the complete URL from the base URL and path
   */
  private buildUrl(path: string): string {
    const baseUrl = this.config.baseUrl.replace(/\/$/, '');
    const cleanPath = path.replace(/^\//, '');
    return `${baseUrl}/${cleanPath}`;
  }

  /**
   * Prepares the request body based on method and content
   */
  private prepareBody(request: ApiRequest, method: HttpMethod): string | null {
    if (method === 'GET' || method === 'DELETE') {
      return null;
    }

    const body = request.getBody();
    if (body === null || body === undefined) {
      return null;
    }

    if (typeof body === 'string') {
      return body;
    }

    return JSON.stringify(body);
  }

  /**
   * Performs fetch with timeout support
   */
  private async fetchWithTimeout(
    url: string,
    options: RequestInit
  ): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new TimeoutError();
      }

      throw new NetworkError(
        `Network request failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Handles errors and converts them to ApiRequestResponse format
   */
  private handleError<T>(error: unknown): ApiRequestResponse<T> {
    if (error instanceof ApiError) {
      return {
        success: false,
        error: error.message,
      };
    }

    if (error instanceof NetworkError) {
      return {
        success: false,
        error: `Network error: ${error.message}`,
      };
    }

    if (error instanceof TimeoutError) {
      return {
        success: false,
        error: 'Request timeout. Please try again.',
      };
    }

    // Handle unknown errors
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }

  /**
   * Updates the base URL for the client
   */
  public setBaseUrl(baseUrl: string): void {
    this.config.baseUrl = baseUrl;
  }

  /**
   * Updates the timeout for requests
   */
  public setTimeout(timeout: number): void {
    this.config.timeout = timeout;
  }

  /**
   * Updates default headers
   */
  public setDefaultHeaders(headers: Record<string, string>): void {
    this.config.defaultHeaders = { ...this.config.defaultHeaders, ...headers };
  }

  /**
   * Gets the current configuration
   */
  public getConfig(): Readonly<Required<ApiClientConfig>> {
    return { ...this.config };
  }
}

export class AuthorizedApiClient extends ApiClient {
  private getSession: () => Promise<string>;
  private session: string | null = null;

  constructor(config: ApiClientConfig, getSession: () => Promise<string>) {
    super(config);

    this.getSession = getSession;
  }

  override async makeRequest<R, M>(
    request: ApiRequest<R, M>,
    additionalHeaders: Record<string, string> = {},
    retries = 3
  ): Promise<ApiRequestResponse<M>> {
    if (!this.session) {
      this.session = await this.getSession();

      if (!this.session) {
        return {
          success: false,
          error: 'Failed to get session',
        };
      }
    }

    const response = await super.makeRequest(request, {
      ...additionalHeaders,
      Authorization: this.session,
    });

    if (response.success) {
      return response;
    } else {
      if (response.error.toLowerCase().includes('Unauthorized')) {
        if (retries === 0) {
          return response;
        }

        this.session = null;
        return await this.makeRequest(request, additionalHeaders, retries - 1);
      } else {
        return response;
      }
    }
  }
}
