import React from 'react';
import { DetailsModal } from './DetailsModal';
import { ClaimDetailsModalProps } from './types';

export const ClaimDetailsModal: React.FC<ClaimDetailsModalProps> = ({
  isOpen,
  onClose,
  claim,
  children,
}) => {
  if (!claim) return null;

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Claim Details"
      size="lg"
    >
      {children}
    </DetailsModal>
  );
};
