import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { StepModalProps } from './types';

// Button component (simplified version - you might want to import from your UI library)
const Button: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'outline';
  className?: string;
}> = ({ children, onClick, disabled, variant = 'primary', className = '' }) => {
  const baseClasses = 'px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2';
  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-600',
    outline: 'border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white disabled:text-gray-500'
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {children}
    </button>
  );
};

export const StepModal = <TFormData, TFormErrors>({
  isOpen,
  onClose,
  title,
  steps,
  currentStep,
  onStepChange,
  formData,
  errors,
  onInputChange,
  isSubmitting,
  onSubmit,
  submitButtonText = 'Create',
  children,
  size = 'xl',
  canGoNext,
  canGoPrevious,
}: StepModalProps<TFormData, TFormErrors>) => {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      onStepChange(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      onStepChange(currentStep - 1);
    }
  };

  const canMoveNext = canGoNext ? canGoNext(currentStep, formData) : true;
  const canMovePrevious = canGoPrevious ? canGoPrevious(currentStep) : currentStep > 1;

  const stepProps = {
    formData,
    errors,
    onInputChange,
    onNext: handleNext,
    onPrevious: handlePrevious,
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={`w-full ${sizeClasses[size]} transform overflow-hidden rounded-lg bg-dark-800 border border-dark-600 p-6 text-left align-middle shadow-xl transition-all`}
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-300"
                  >
                    {title}
                  </Dialog.Title>
                  <Button
                    variant="outline"
                    onClick={onClose}
                    disabled={isSubmitting}
                    className="p-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Step Indicator */}
                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    {steps.map((step, index) => (
                      <div key={step.id} className="flex items-center">
                        <div
                          className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                            currentStep >= step.id
                              ? 'border-blue-500 bg-blue-500 text-white'
                              : 'border-dark-600 text-gray-400'
                          }`}
                        >
                          {step.id}
                        </div>
                        <div className="ml-2 hidden sm:block">
                          <div className="text-sm font-medium text-gray-300">
                            {step.title}
                          </div>
                          <div className="text-xs text-gray-400">
                            {step.description}
                          </div>
                        </div>
                        {index < steps.length - 1 && (
                          <div className="flex-1 h-px bg-dark-600 mx-4" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Step Content */}
                <div className="mb-6 min-h-[400px]">
                  {children(stepProps)}
                </div>

                {/* Error Display */}
                {(errors as any)?.submit && (
                  <div className="mb-4 p-3 bg-red-900/20 border border-red-700 rounded-md">
                    <p className="text-sm text-red-400">{(errors as any).submit}</p>
                  </div>
                )}

                {/* Footer */}
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={currentStep === 1 ? onClose : handlePrevious}
                    disabled={isSubmitting || (currentStep > 1 && !canMovePrevious)}
                    className="flex items-center gap-2"
                  >
                    {currentStep === 1 ? (
                      'Cancel'
                    ) : (
                      <>
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </>
                    )}
                  </Button>

                  {currentStep < steps.length ? (
                    <Button
                      onClick={handleNext}
                      disabled={isSubmitting || !canMoveNext}
                      className="flex items-center gap-2"
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  ) : (
                    <Button
                      onClick={onSubmit}
                      disabled={isSubmitting}
                      className="flex items-center gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          Creating...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4" />
                          {submitButtonText}
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
