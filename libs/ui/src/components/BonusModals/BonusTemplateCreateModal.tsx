import React from 'react';
import { StepModal } from './StepModal';
import { BonusTemplateCreateModalProps } from './types';

export const BonusTemplateCreateModal = <TFormData, TFormErrors>({
  isOpen,
  onClose,
  steps,
  currentStep,
  onStepChange,
  formData,
  errors,
  onInputChange,
  isSubmitting,
  onSubmit,
  submitButtonText = 'Create Template',
  canGoNext,
  canGoPrevious,
  bonusType = 'bonus template',
  children,
}: BonusTemplateCreateModalProps<TFormData, TFormErrors>) => {
  const stepModalProps = {
    isOpen,
    onClose,
    title: `Create ${bonusType.charAt(0).toUpperCase() + bonusType.slice(1)}`,
    steps,
    currentStep,
    onStepChange,
    formData,
    errors,
    onInputChange,
    isSubmitting,
    onSubmit,
    submitButtonText,
    canGoNext,
    canGoPrevious,
    size: 'xl' as const,
  };

  return <StepModal {...stepModalProps}>{children}</StepModal>;
};
