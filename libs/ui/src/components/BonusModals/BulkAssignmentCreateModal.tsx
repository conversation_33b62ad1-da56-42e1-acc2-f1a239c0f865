import React from 'react';
import { FormModal } from './FormModal';
import { BulkAssignmentCreateModalProps } from './types';

export const BulkAssignmentCreateModal = <TFormData, TFormErrors>({
  isOpen,
  onClose,
  formData,
  errors,
  onInputChange,
  isSubmitting,
  onSubmit,
  submitButtonText = 'Create Job',
  canSubmit,
  children,
}: BulkAssignmentCreateModalProps<TFormData, TFormErrors>) => {
  const formModalProps = {
    isOpen,
    onClose,
    title: 'Create Bulk Assignment Job',
    formData,
    errors,
    onInputChange,
    isSubmitting,
    onSubmit,
    submitButtonText,
    canSubmit,
    size: 'lg' as const,
  };

  return <FormModal {...formModalProps}>{children}</FormModal>;
};
