import React from 'react';
import { DetailsModal } from './DetailsModal';
import { BulkAssignmentDetailsModalProps } from './types';

export const BulkAssignmentDetailsModal: React.FC<BulkAssignmentDetailsModalProps> = ({
  isOpen,
  onClose,
  job,
  children,
}) => {
  if (!job) return null;

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Bulk Assignment Job Details"
      size="lg"
    >
      {children}
    </DetailsModal>
  );
};
