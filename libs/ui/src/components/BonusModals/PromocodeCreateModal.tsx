import React from 'react';
import { FormModal } from './FormModal';
import { PromocodeCreateModalProps } from './types';

export const PromocodeCreateModal: React.FC<PromocodeCreateModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  formData,
  errors,
  onInputChange,
  isSubmitting,
  submitButtonText = 'Create Promocode',
  canSubmit,
  children,
}) => {
  const formModalProps = {
    isOpen,
    onClose,
    title: 'Create Promocode',
    formData,
    errors,
    onInputChange,
    isSubmitting,
    onSubmit,
    submitButtonText,
    canSubmit,
    size: 'lg' as const,
  };

  return <FormModal {...formModalProps}>{children}</FormModal>;
};
