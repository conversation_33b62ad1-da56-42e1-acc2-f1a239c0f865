import { Plus, Trash2, Users } from 'lucide-react';

export interface RuleField {
  value: string;
  label: string;
  type: string;
  inputType?: string;
  options?: string[];
}

export interface RuleOperator {
  value: string;
  label: string;
}

export interface Rule {
  criterium: string;
  operator: string;
  firstOperand: string | null;
  secondOperand?: string | null;
  startsAt?: Date | null;
  endsAt?: Date | null;
  startsInSeconds?: number | null;
  endsInSeconds?: number | null;
}

export interface RulesStepProps {
  rules: Rule[];
  errors?: { rules?: string };
  ruleFields: RuleField[];
  ruleOperators: RuleOperator[];
  onAddRule: () => void;
  onRemoveRule: (index: number) => void;
  onUpdateRule: (index: number, field: any, value: any) => void;
  title?: string;
  description?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  useSecondsForDates?: boolean;
  variant?: 'compact' | 'full';
}

export const RulesStep = ({
  rules,
  errors,
  ruleFields,
  ruleOperators,
  onAddRule,
  onRemoveRule,
  onUpdateRule,
  title = 'Eligibility Rules',
  description = 'Define conditions that customers must meet to be eligible',
  emptyStateTitle = 'No rules defined',
  emptyStateDescription = 'Add rules to control eligibility, or leave empty to allow all users.',
  useSecondsForDates = false,
  variant = 'full',
}: RulesStepProps) => {
  const getFieldType = (criterium: string) => {
    const field = ruleFields.find((f) => f.value === criterium);
    return field?.type || 'string';
  };

  const getFieldInputType = (criterium: string) => {
    const field = ruleFields.find((f) => f.value === criterium);
    return field?.inputType || 'text';
  };

  const getFieldOptions = (criterium: string) => {
    const field = ruleFields.find((f) => f.value === criterium);
    return field?.options || [];
  };

  const getOperatorsForField = (criterium: string) => {
    const field = ruleFields.find((f) => f.value === criterium);

    // Special handling for KYC verification and binary-checkbox fields
    if (
      field?.inputType === 'binary-checkbox' ||
      criterium === 'kycVerification'
    ) {
      return [
        { value: 'eq', label: 'Exact match' },
        { value: 'gte', label: 'At least' },
      ];
    }

    return ruleOperators;
  };

  const renderOperandInput = (
    ruleIndex: number,
    operandKey: 'firstOperand' | 'secondOperand',
    criterium: string,
    value: string | null
  ) => {
    const fieldType = getFieldType(criterium);
    const inputType = getFieldInputType(criterium);

    if (inputType === 'binary-checkbox') {
      const options = getFieldOptions(criterium);
      const binaryString = value || '';
      const booleanArray = binaryString.split('').map((bit) => bit === '1');

      // Ensure array has correct length
      while (booleanArray.length < options.length) {
        booleanArray.push(false);
      }

      return (
        <div
          className={
            variant === 'compact' ? 'grid grid-cols-2 gap-2' : 'space-y-2'
          }
        >
          {options.map((option, optionIndex) => (
            <label key={option} className="flex items-center">
              <input
                type="checkbox"
                checked={booleanArray[optionIndex] || false}
                onChange={(e) => {
                  const newBooleanArray = [...booleanArray];
                  newBooleanArray[optionIndex] = e.target.checked;
                  const newBinaryString = newBooleanArray
                    .map((val) => (val ? '1' : '0'))
                    .join('');
                  onUpdateRule(ruleIndex, operandKey, newBinaryString);
                }}
                className={`${
                  variant === 'compact'
                    ? 'rounded border-dark-500 bg-dark-600 text-blue-600 focus:ring-blue-500'
                    : 'mr-2'
                }`}
              />
              <span
                className={`text-gray-300 capitalize ${
                  variant === 'compact' ? 'text-xs ml-2' : 'text-sm'
                }`}
              >
                {option}
              </span>
            </label>
          ))}
        </div>
      );
    }

    const inputClasses =
      variant === 'compact'
        ? 'w-full px-2 py-1 bg-dark-700 border border-dark-600 rounded text-gray-300 text-sm'
        : 'w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent';

    return (
      <input
        type={
          fieldType === 'date'
            ? 'date'
            : fieldType === 'number'
            ? 'number'
            : 'text'
        }
        value={value || ''}
        onChange={(e) => onUpdateRule(ruleIndex, operandKey, e.target.value)}
        placeholder={`Enter ${
          operandKey === 'firstOperand' ? 'first' : 'second'
        } value`}
        className={inputClasses}
      />
    );
  };

  const formatDateForInput = (date: Date | null, useSeconds: boolean) => {
    if (!date) return '';
    if (useSeconds && typeof date === 'number') {
      return new Date(date * 1000).toISOString().slice(0, 16);
    }
    return new Date(date).toISOString().slice(0, 16);
  };

  const parseDateFromInput = (value: string, useSeconds: boolean) => {
    if (!value) return null;
    const date = new Date(value);
    return useSeconds ? Math.floor(date.getTime() / 1000) : date;
  };

  const isCompact = variant === 'compact';
  const containerClasses = isCompact ? 'space-y-4' : 'space-y-6';
  const ruleContainerClasses = isCompact
    ? 'p-4 border border-dark-600 rounded-lg space-y-3'
    : 'p-4 bg-dark-700 border border-dark-600 rounded-lg';
  const gridClasses = isCompact
    ? 'grid grid-cols-1 md:grid-cols-3 gap-3'
    : 'grid grid-cols-1 md:grid-cols-3 gap-4';
  const labelClasses = isCompact
    ? 'block text-xs font-medium text-gray-400 mb-1'
    : 'block text-sm font-medium text-gray-400 mb-2';
  const selectClasses = isCompact
    ? 'w-full px-2 py-1 bg-dark-700 border border-dark-600 rounded text-gray-300 text-sm'
    : 'w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent';

  return (
    <div className={containerClasses}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3
            className={`font-medium text-gray-300 flex items-center ${
              isCompact ? 'text-lg' : 'text-lg'
            }`}
          >
            {!isCompact && <Users className="w-5 h-5 mr-2" />}
            {title} ({rules.length})
          </h3>
          {!isCompact && description && (
            <p className="text-sm text-gray-400 mt-1">{description}</p>
          )}
        </div>
        <button
          onClick={onAddRule}
          className={`flex items-center text-white rounded-md transition-colors ${
            isCompact
              ? 'px-2 py-1 bg-blue-600 hover:bg-blue-700 text-sm gap-1'
              : 'px-3 py-2 bg-blue-600 hover:bg-blue-700 gap-2'
          }`}
        >
          <Plus className={`${isCompact ? 'w-3 h-3' : 'w-4 h-4'}`} />
          Add Rule
        </button>
      </div>

      {/* Rules List */}
      {rules.length === 0 ? (
        <div className="text-center py-8 text-gray-400">
          {!isCompact && (
            <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
          )}
          <p
            className={`font-medium mb-2 ${
              isCompact ? 'text-base' : 'text-lg'
            }`}
          >
            {emptyStateTitle}
          </p>
          <p className="text-sm">{emptyStateDescription}</p>
        </div>
      ) : (
        <div
          className={`space-y-4 ${isCompact ? 'max-h-96 overflow-y-auto' : ''}`}
        >
          {rules.map((rule, index) => (
            <div key={index} className={ruleContainerClasses}>
              <div className="flex items-center justify-between mb-4">
                <h4
                  className={`font-medium text-gray-300 ${
                    isCompact ? 'text-sm' : 'text-md'
                  }`}
                >
                  Rule {isCompact ? index + 1 : `#${index + 1}`}
                </h4>
                <button
                  onClick={() => onRemoveRule(index)}
                  className="text-red-400 hover:text-red-300 transition-colors"
                  title="Remove rule"
                >
                  <Trash2 className={`${isCompact ? 'w-4 h-4' : 'w-4 h-4'}`} />
                </button>
              </div>

              <div className={gridClasses}>
                {/* Criterium */}
                <div>
                  <label className={labelClasses}>Field</label>
                  <select
                    value={rule.criterium}
                    onChange={(e) =>
                      onUpdateRule(index, 'criterium', e.target.value)
                    }
                    className={selectClasses}
                  >
                    <option value="">Select field...</option>
                    {ruleFields.map((field) => (
                      <option key={field.value} value={field.value}>
                        {field.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Operator */}
                <div>
                  <label className={labelClasses}>Operator</label>
                  <select
                    value={rule.operator}
                    onChange={(e) =>
                      onUpdateRule(index, 'operator', e.target.value)
                    }
                    className={selectClasses}
                  >
                    <option value="">Select operator...</option>
                    {getOperatorsForField(rule.criterium).map((op) => (
                      <option key={op.value} value={op.value}>
                        {op.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* First Operand */}
                <div>
                  <label className={labelClasses}>Value</label>
                  {renderOperandInput(
                    index,
                    'firstOperand',
                    rule.criterium,
                    rule.firstOperand
                  )}
                </div>
              </div>

              {/* Second Operand (for "between" operator) */}
              {rule.operator === 'btw' && (
                <div className="mt-4">
                  <label className={labelClasses}>
                    Second Value (for "between")
                  </label>
                  {renderOperandInput(
                    index,
                    'secondOperand',
                    rule.criterium,
                    rule.secondOperand || null
                  )}
                </div>
              )}

              {/* Date Range (optional) */}
              <div
                className={`mt-4 grid grid-cols-1 md:grid-cols-2 ${
                  isCompact ? 'gap-3' : 'gap-4'
                }`}
              >
                <div>
                  <label className={labelClasses}>
                    {isCompact
                      ? 'Starts At (Optional)'
                      : 'Valid From (Optional)'}
                  </label>
                  <input
                    type="datetime-local"
                    value={formatDateForInput(
                      useSecondsForDates
                        ? rule.startsInSeconds
                          ? new Date(rule.startsInSeconds * 1000)
                          : null
                        : rule.startsAt || null,
                      false
                    )}
                    onChange={(e) => {
                      const value = parseDateFromInput(
                        e.target.value,
                        useSecondsForDates
                      );
                      onUpdateRule(
                        index,
                        useSecondsForDates ? 'startsInSeconds' : 'startsAt',
                        value
                      );
                    }}
                    className={selectClasses}
                  />
                </div>
                <div>
                  <label className={labelClasses}>
                    {isCompact
                      ? 'Ends At (Optional)'
                      : 'Valid Until (Optional)'}
                  </label>
                  <input
                    type="datetime-local"
                    value={formatDateForInput(
                      useSecondsForDates
                        ? rule.endsInSeconds
                          ? new Date(rule.endsInSeconds * 1000)
                          : null
                        : rule.endsAt || null,
                      false
                    )}
                    onChange={(e) => {
                      const value = parseDateFromInput(
                        e.target.value,
                        useSecondsForDates
                      );
                      onUpdateRule(
                        index,
                        useSecondsForDates ? 'endsInSeconds' : 'endsAt',
                        value
                      );
                    }}
                    className={selectClasses}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {errors?.rules && <p className="text-sm text-red-400">{errors.rules}</p>}
    </div>
  );
};
