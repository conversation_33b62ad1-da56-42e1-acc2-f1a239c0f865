import React, { ReactNode } from 'react';

// Base modal props
export interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Step configuration for step-based modals
export interface StepConfig {
  id: number;
  title: string;
  description: string;
}

// Base step props that all step components should extend
export interface BaseStepProps<TFormData = any, TFormErrors = any> {
  formData: TFormData;
  errors: TFormErrors;
  onInputChange: (field: keyof TFormData, value: any) => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

// Step-based modal props
export interface StepModalProps<TFormData = any, TFormErrors = any>
  extends BaseModalProps {
  title: string;
  steps: StepConfig[];
  currentStep: number;
  onStepChange: (step: number) => void;
  formData: TFormData;
  errors: TFormErrors;
  onInputChange: (field: keyof TFormData, value: any) => void;
  isSubmitting: boolean;
  onSubmit: () => void;
  onSuccess?: () => void;
  submitButtonText?: string;
  children: (stepProps: BaseStepProps<TFormData, TFormErrors>) => ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  canGoNext?: (currentStep: number, formData: TFormData) => boolean;
  canGoPrevious?: (currentStep: number) => boolean;
}

// Details modal props
export interface DetailsModalProps extends BaseModalProps {
  title: string;
  children: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  actions?: ReactNode;
}

// Form modal props (for simple forms without steps)
export interface FormModalProps<TFormData = any, TFormErrors = any>
  extends BaseModalProps {
  title: string;
  formData: TFormData;
  errors: TFormErrors;
  onInputChange: (field: keyof TFormData, value: any) => void;
  isSubmitting: boolean;
  onSubmit: () => void;
  onSuccess?: () => void;
  submitButtonText?: string;
  children: (formProps: {
    formData: TFormData;
    errors: TFormErrors;
    onInputChange: (field: keyof TFormData, value: any) => void;
  }) => ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  canSubmit?: (formData: TFormData) => boolean;
}

// Specific modal types
export interface BonusCreateModalProps<TFormData = any, TFormErrors = any>
  extends BaseModalProps {
  steps: StepConfig[];
  currentStep: number;
  onStepChange: (step: number) => void;
  formData: TFormData;
  errors: TFormErrors;
  onInputChange: (field: keyof TFormData, value: any) => void;
  isSubmitting: boolean;
  onSubmit: () => void;
  submitButtonText?: string;
  canGoNext?: (currentStep: number, formData: TFormData) => boolean;
  canGoPrevious?: (currentStep: number) => boolean;
  bonusType?: string;
  children: (stepProps: BaseStepProps<TFormData, TFormErrors>) => ReactNode;
}

export interface BonusTemplateCreateModalProps<
  TFormData = any,
  TFormErrors = any
> extends BaseModalProps {
  steps: StepConfig[];
  currentStep: number;
  onStepChange: (step: number) => void;
  formData: TFormData;
  errors: TFormErrors;
  onInputChange: (field: keyof TFormData, value: any) => void;
  isSubmitting: boolean;
  onSubmit: () => void;
  submitButtonText?: string;
  canGoNext?: (currentStep: number, formData: TFormData) => boolean;
  canGoPrevious?: (currentStep: number) => boolean;
  bonusType?: string;
  children: (stepProps: BaseStepProps<TFormData, TFormErrors>) => ReactNode;
}

export interface PromocodeCreateModalProps<TFormData = any, TFormErrors = any>
  extends BaseModalProps {
  formData: TFormData;
  errors: TFormErrors;
  onInputChange: (field: keyof TFormData, value: any) => void;
  isSubmitting: boolean;
  onSubmit: () => void;
  submitButtonText?: string;
  canSubmit?: (formData: TFormData) => boolean;
  children: (formProps: {
    formData: TFormData;
    errors: TFormErrors;
    onInputChange: (field: keyof TFormData, value: any) => void;
  }) => ReactNode;
}

export interface ClaimDetailsModalProps extends BaseModalProps {
  claim: any;
  children: ReactNode;
}

export interface BulkAssignmentCreateModalProps<
  TFormData = any,
  TFormErrors = any
> extends BaseModalProps {
  formData: TFormData;
  errors: TFormErrors;
  onInputChange: (field: keyof TFormData, value: any) => void;
  isSubmitting: boolean;
  onSubmit: () => void;
  submitButtonText?: string;
  canSubmit?: (formData: TFormData) => boolean;
  children: (formProps: {
    formData: TFormData;
    errors: TFormErrors;
    onInputChange: (field: keyof TFormData, value: any) => void;
  }) => ReactNode;
}

export interface BulkAssignmentDetailsModalProps extends BaseModalProps {
  job: any;
  children: ReactNode;
}

// Common form data types that can be extended
export interface BaseFormData {
  [key: string]: any;
}

export interface BaseFormErrors {
  [key: string]: string;
}

// Validation function type
export type ValidationFunction<T> = (data: T) => BaseFormErrors;

// Step validation function type
export type StepValidationFunction<T> = (
  step: number,
  data: T
) => BaseFormErrors;
