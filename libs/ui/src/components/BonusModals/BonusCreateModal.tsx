import React from 'react';
import { StepModal } from './StepModal';
import { BonusCreateModalProps } from './types';

export const BonusCreateModal = <TFormData, TFormErrors>({
  isOpen,
  onClose,
  steps,
  currentStep,
  onStepChange,
  formData,
  errors,
  onInputChange,
  isSubmitting,
  onSubmit,
  submitButtonText = 'Create Bonus',
  canGoNext,
  canGoPrevious,
  bonusType = 'bonus',
  children,
}: BonusCreateModalProps<TFormData, TFormErrors>) => {
  const stepModalProps = {
    isOpen,
    onClose,
    title: `Create ${bonusType.charAt(0).toUpperCase() + bonusType.slice(1)}`,
    steps,
    currentStep,
    onStepChange,
    formData,
    errors,
    onInputChange,
    isSubmitting,
    onSubmit,
    submitButtonText,
    canGoNext,
    canGoPrevious,
    size: 'xl' as const,
  };

  return <StepModal {...stepModalProps}>{children}</StepModal>;
};
