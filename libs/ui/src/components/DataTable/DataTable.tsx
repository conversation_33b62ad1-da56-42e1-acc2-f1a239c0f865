import React, { useMemo } from 'react';
import FilterSection from './FilterSection';
import TableHeader from './TableHeader';
import TableBody from './TableBody';
import Pagination from './Pagination';
import { cn } from './utils';
import type { DataTableProps, FilterGroup, SortState } from './types';

const DataTable = <T,>({
  data,
  columns,
  filterFields = [],
  filterGroups = [],
  total,
  currentPage,
  itemsPerPage,
  isLoading = false,
  sortState = { field: null, direction: 'asc' },
  filters = {},
  onPageChange,
  onItemsPerPageChange,
  onSort,
  onFilterChange,
  onRowClick,
  emptyState,
  title,
}: DataTableProps<T>) => {
  // Convert legacy filterFields to filterGroups format
  const finalFilterGroups: FilterGroup[] = useMemo(() => {
    if (filterGroups.length > 0) {
      return filterGroups;
    }

    if (filterFields.length > 0) {
      // Group fields by their group property or put them in a default group
      const groupedFields: Record<string, typeof filterFields> = {};

      filterFields.forEach((field) => {
        const groupName = field.group || 'default';
        if (!groupedFields[groupName]) {
          groupedFields[groupName] = [];
        }
        groupedFields[groupName].push(field);
      });

      return Object.entries(groupedFields).map(([groupName, fields]) => ({
        name: groupName,
        displayName: groupName === 'default' ? 'Filters' : groupName,
        fields,
      }));
    }

    return [];
  }, [filterFields, filterGroups]);

  const totalPages = Math.ceil(total / itemsPerPage);

  return (
    <div className="bg-dark-800 shadow-xl rounded-lg border border-dark-600 overflow-hidden flex flex-col">
      {/* Title */}
      {title && (
        <div className="px-6 py-4 border-b border-dark-600">
          <h2 className="text-lg font-semibold text-gray-300">{title}</h2>
        </div>
      )}

      {/* Filters */}
      {finalFilterGroups.length > 0 && onFilterChange && (
        <FilterSection
          filterGroups={finalFilterGroups}
          filters={filters}
          onFilterChange={onFilterChange}
        />
      )}

      {/* Table */}
      <div className="overflow-x-auto grow">
        <table className="min-w-full divide-y divide-dark-600">
          <TableHeader
            columns={columns}
            sortState={sortState}
            onSort={onSort}
          />
          <TableBody
            data={data}
            columns={columns}
            isLoading={isLoading}
            onRowClick={onRowClick}
            emptyState={emptyState}
          />
        </table>
      </div>

      {/* Pagination */}
      {total > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={total}
          itemsPerPage={itemsPerPage}
          onPageChange={onPageChange}
          onItemsPerPageChange={onItemsPerPageChange}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default DataTable;
