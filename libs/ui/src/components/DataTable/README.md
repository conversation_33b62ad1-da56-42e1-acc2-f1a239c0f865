# DataTable Component

A comprehensive, reusable table component with advanced features including filtering, sorting, pagination, and responsive design.

## Features

- **Configurable columns** with custom render functions
- **Advanced filtering** with grouping support and multiple field types
- **Column sorting** with visual indicators
- **Pagination** with customizable items per page
- **Loading and empty states**
- **Row click handling**
- **Responsive design** with horizontal scrolling
- **Dark theme** optimized styling
- **TypeScript** support with full type safety

## Basic Usage

```tsx
import { DataTable, TableColumn } from '@panels/ui';

interface User {
  id: number;
  name: string;
  email: string;
  status: 'active' | 'inactive';
}

const columns: TableColumn<User>[] = [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (user) => <span>#{user.id}</span>
  },
  {
    key: 'name',
    label: 'Name',
    width: '200px',
    sortable: true,
    render: (user) => <span className="font-medium">{user.name}</span>
  },
  {
    key: 'email',
    label: 'Email',
    width: '250px',
    sortable: true,
    render: (user) => <span className="text-gray-400">{user.email}</span>
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: false,
    render: (user) => (
      <span className={`px-2 py-1 rounded-full text-xs ${
        user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`}>
        {user.status}
      </span>
    )
  }
];

function UserTable() {
  const [users, setUsers] = useState<User[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState({ field: null, direction: 'asc' });
  const [filters, setFilters] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  return (
    <DataTable
      data={users}
      columns={columns}
      total={users.length}
      currentPage={currentPage}
      itemsPerPage={itemsPerPage}
      isLoading={isLoading}
      sortState={sortState}
      filters={filters}
      onPageChange={setCurrentPage}
      onItemsPerPageChange={setItemsPerPage}
      onSort={(field) => {
        setSortState(prev => ({
          field,
          direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
        }));
      }}
      onFilterChange={setFilters}
      onRowClick={(user) => console.log('Clicked user:', user)}
      title="Users"
    />
  );
}
```

## Advanced Usage with Filters

```tsx
import { DataTable, FilterGroup } from '@panels/ui';

const filterGroups: FilterGroup[] = [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'name',
        label: 'Name',
        type: 'text',
        placeholder: 'Search by name...'
      },
      {
        key: 'status',
        label: 'Status',
        type: 'select',
        options: [
          { value: 'active', label: 'Active' },
          { value: 'inactive', label: 'Inactive' }
        ]
      }
    ]
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'created_after',
        label: 'Created After',
        type: 'date'
      },
      {
        key: 'min_age',
        label: 'Minimum Age',
        type: 'number',
        min: 0,
        max: 120
      }
    ]
  }
];

<DataTable
  data={users}
  columns={columns}
  filterGroups={filterGroups}
  // ... other props
/>
```

## Component Architecture

The DataTable is split into several focused components:

- **`DataTable`** - Main component that orchestrates everything
- **`FilterSection`** - Handles all filtering UI and logic
- **`TableHeader`** - Renders table headers with sorting
- **`TableBody`** - Renders table rows with loading/empty states
- **`Pagination`** - Handles pagination controls
- **`Button`**, **`Input`**, **`Select`** - Basic UI components

## Props Reference

### DataTableProps

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `data` | `T[]` | ✅ | Array of data items to display |
| `columns` | `TableColumn<T>[]` | ✅ | Column configuration |
| `total` | `number` | ✅ | Total number of items (for pagination) |
| `currentPage` | `number` | ✅ | Current page number |
| `itemsPerPage` | `number` | ✅ | Items per page |
| `onPageChange` | `(page: number) => void` | ✅ | Page change handler |
| `onItemsPerPageChange` | `(items: number) => void` | ✅ | Items per page change handler |
| `filterFields` | `FilterField[]` | ❌ | Legacy filter fields (use filterGroups instead) |
| `filterGroups` | `FilterGroup[]` | ❌ | Organized filter groups |
| `isLoading` | `boolean` | ❌ | Loading state |
| `sortState` | `SortState` | ❌ | Current sort state |
| `filters` | `Record<string, unknown>` | ❌ | Current filter values |
| `onSort` | `(field: string) => void` | ❌ | Sort handler |
| `onFilterChange` | `(filters: Record<string, unknown>) => void` | ❌ | Filter change handler |
| `onRowClick` | `(item: T) => void` | ❌ | Row click handler |
| `emptyState` | `EmptyState` | ❌ | Custom empty state |
| `title` | `string` | ❌ | Table title |

### TableColumn

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `key` | `string` | ✅ | Unique column identifier |
| `label` | `string` | ✅ | Column header text |
| `width` | `string` | ✅ | Column width (CSS value) |
| `sortable` | `boolean` | ✅ | Whether column is sortable |
| `render` | `(item: T) => ReactNode` | ✅ | Cell render function |

### FilterField

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `key` | `string` | ✅ | Filter field key |
| `label` | `string` | ✅ | Field label |
| `type` | `'text' \| 'select' \| 'date' \| 'datetime-local' \| 'number'` | ✅ | Input type |
| `placeholder` | `string` | ❌ | Input placeholder |
| `options` | `{value: string, label: string}[]` | ❌ | Options for select type |
| `group` | `string` | ❌ | Group name (for legacy filterFields) |
| `min` | `number` | ❌ | Minimum value for number inputs |
| `max` | `number` | ❌ | Maximum value for number inputs |

## Styling

The component uses Tailwind CSS with a dark theme. Key color classes:

- `dark-800`, `dark-700`, `dark-600` - Background colors
- `gray-300`, `gray-400` - Text colors
- `primary-500` - Accent color
- `red-500`, `green-500` - Status colors

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- Focus management
- High contrast colors
