import React from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { cn } from './utils';
import type { TableHeaderProps, SortState } from './types';

const TableHeader = <T,>({
  columns,
  sortState,
  onSort,
}: TableHeaderProps<T>) => {
  const handleSort = (field: string) => {
    if (onSort) {
      onSort(field);
    }
  };

  const getSortIcon = (field: string) => {
    if (sortState.field !== field) {
      return null;
    }

    return sortState.direction === 'asc' ? (
      <ChevronUp className="h-4 w-4" />
    ) : (
      <ChevronDown className="h-4 w-4" />
    );
  };

  return (
    <thead className="bg-dark-700">
      <tr>
        {columns.map((column) => (
          <th
            key={column.key}
            className={cn(
              'px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider',
              column.sortable &&
                onSort &&
                'cursor-pointer hover:bg-dark-600 transition-colors',
              column.width && `w-${column.width}`,
              column.sticky === 'right' &&
                'sticky right-0 bg-dark-700 z-10 border-l border-dark-600',
              column.sticky === 'left' &&
                'sticky left-0 bg-dark-700 z-10 border-r border-dark-600'
            )}
            style={column.width ? { width: column.width } : undefined}
            onClick={() => column.sortable && handleSort(column.key)}
          >
            <div className="flex items-center space-x-1">
              <span>{column.label}</span>
              {column.sortable && onSort && (
                <div className="flex flex-col">
                  {getSortIcon(column.key) || (
                    <div className="text-gray-500">
                      <ChevronUp className="h-3 w-3 -mb-1" />
                      <ChevronDown className="h-3 w-3" />
                    </div>
                  )}
                </div>
              )}
            </div>
          </th>
        ))}
      </tr>
    </thead>
  );
};

export default TableHeader;
