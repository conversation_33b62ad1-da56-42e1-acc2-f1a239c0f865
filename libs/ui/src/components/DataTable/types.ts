import React from 'react';

// Column configuration interface
export interface TableColumn<T = any> {
  key: string;
  label: string;
  width: string;
  sortable: boolean;
  sticky?: 'left' | 'right';
  render: (item: T) => React.ReactNode;
}

// Filter field interface
export interface FilterField {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'datetime-local' | 'number';
  placeholder?: string;
  options?: { value: string; label: string }[];
  group?: string; // Optional grouping
  min?: number; // Minimum value for number inputs
  max?: number; // Maximum value for number inputs
}

// Filter group interface
export interface FilterGroup {
  name: string;
  displayName: string;
  fields: FilterField[];
}

// Sort state interface
export interface SortState {
  field: string | null;
  direction: 'asc' | 'desc';
}

// Main component props
export interface DataTableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  filterFields?: FilterField[];
  filterGroups?: FilterGroup[];
  total: number;
  currentPage: number;
  itemsPerPage: number;
  isLoading?: boolean;
  sortState?: SortState;
  filters?: Record<string, unknown>;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  onSort?: (field: string) => void;
  onFilterChange?: (filters: Record<string, unknown>) => void;
  onRowClick?: (item: T) => void;
  emptyState?: {
    icon?: React.ReactNode;
    title: string;
    description: string;
    action?: React.ReactNode;
  };
  title?: string;
}

// Filter section props
export interface FilterSectionProps {
  filterGroups: FilterGroup[];
  filters: Record<string, unknown>;
  onFilterChange: (filters: Record<string, unknown>) => void;
}

// Table header props
export interface TableHeaderProps<T> {
  columns: TableColumn<T>[];
  sortState: SortState;
  onSort?: (field: string) => void;
}

// Table body props
export interface TableBodyProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  isLoading: boolean;
  onRowClick?: (item: T) => void;
  emptyState?: {
    icon?: React.ReactNode;
    title: string;
    description: string;
    action?: React.ReactNode;
  };
}

// Pagination props (re-exported for convenience)
export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  isLoading?: boolean;
}
