import React from 'react';
import { Database } from 'lucide-react';
import { cn } from './utils';
import type { TableBodyProps } from './types';

const TableBody = <T,>({
  data,
  columns,
  isLoading,
  onRowClick,
  emptyState,
}: TableBodyProps<T>) => {
  // Loading state
  if (isLoading) {
    return (
      <tbody className="bg-dark-800 divide-y divide-dark-600">
        {Array.from({ length: 5 }).map((_, index) => (
          <tr key={index} className="animate-pulse">
            {columns.map((column) => (
              <td
                key={column.key}
                className={cn(
                  'px-6 py-4 whitespace-nowrap',
                  column.sticky === 'right' &&
                    'sticky right-0 bg-dark-800 z-10 border-l border-dark-600',
                  column.sticky === 'left' &&
                    'sticky left-0 bg-dark-800 z-10 border-r border-dark-600'
                )}
              >
                <div className="h-4 bg-dark-600 rounded w-3/4"></div>
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    );
  }

  // Empty state
  if (data.length === 0) {
    const defaultEmptyState = {
      icon: <Database className="h-12 w-12 text-gray-400" />,
      title: 'No data available',
      description: 'There are no items to display at the moment.',
    };

    const finalEmptyState = emptyState || defaultEmptyState;

    return (
      <tbody className="bg-dark-800">
        <tr>
          <td colSpan={columns.length} className="px-6 py-12">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                {finalEmptyState.icon}
              </div>
              <h3 className="text-lg font-medium text-gray-300 mb-2">
                {finalEmptyState.title}
              </h3>
              <p className="text-gray-400 mb-4">
                {finalEmptyState.description}
              </p>
              {finalEmptyState.action && (
                <div className="flex justify-center">
                  {finalEmptyState.action}
                </div>
              )}
            </div>
          </td>
        </tr>
      </tbody>
    );
  }

  // Data rows
  return (
    <tbody className="bg-dark-800 divide-y divide-dark-600">
      {data.map((item, index) => (
        <tr
          key={index}
          className={cn(
            'hover:bg-dark-700 transition-colors',
            onRowClick && 'cursor-pointer'
          )}
          onClick={() => onRowClick && onRowClick(item)}
        >
          {columns.map((column) => (
            <td
              key={column.key}
              className={cn(
                'px-6 py-4 whitespace-nowrap text-sm text-gray-300',
                column.sticky === 'right' &&
                  'sticky right-0 bg-dark-800 z-10 border-l border-dark-600',
                column.sticky === 'left' &&
                  'sticky left-0 bg-dark-800 z-10 border-r border-dark-600'
              )}
              style={column.width ? { width: column.width } : undefined}
            >
              {column.render(item)}
            </td>
          ))}
        </tr>
      ))}
    </tbody>
  );
};

export default TableBody;
