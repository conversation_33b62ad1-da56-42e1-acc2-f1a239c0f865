import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Filter } from 'lucide-react';
import Button from './Button';
import Input from './Input';
import Select from './Select';
import { cn } from './utils';
import type { FilterSectionProps, FilterField } from './types';

const FilterSection: React.FC<FilterSectionProps> = ({
  filterGroups,
  filters,
  onFilterChange,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  const handleFilterChange = (key: string, value: unknown) => {
    const newFilters = { ...filters };
    if (value === '' || value === null || value === undefined) {
      delete newFilters[key];
    } else {
      newFilters[key] = value;
    }
    onFilterChange(newFilters);
  };

  const clearAllFilters = () => {
    onFilterChange({});
  };

  const toggleGroup = (groupName: string) => {
    const newExpandedGroups = new Set(expandedGroups);
    if (newExpandedGroups.has(groupName)) {
      newExpandedGroups.delete(groupName);
    } else {
      newExpandedGroups.add(groupName);
    }
    setExpandedGroups(newExpandedGroups);
  };

  const renderFilterField = (field: FilterField) => {
    const value = filters[field.key] || '';

    switch (field.type) {
      case 'select':
        return (
          <Select
            key={field.key}
            label={field.label}
            value={value as string}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
            options={field.options || []}
            placeholder={field.placeholder}
          />
        );

      case 'number':
        return (
          <Input
            key={field.key}
            type="number"
            label={field.label}
            value={value as string}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
            placeholder={field.placeholder}
            min={field.min}
            max={field.max}
          />
        );

      case 'date':
      case 'datetime-local':
        return (
          <Input
            key={field.key}
            type={field.type}
            label={field.label}
            value={value as string}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
            placeholder={field.placeholder}
          />
        );

      default:
        return (
          <Input
            key={field.key}
            type="text"
            label={field.label}
            value={value as string}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
            placeholder={field.placeholder}
          />
        );
    }
  };

  const activeFiltersCount = Object.keys(filters).length;

  if (filterGroups.length === 0) {
    return null;
  }

  return (
    <div className="bg-dark-800 border-b border-dark-600">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <span className="text-sm font-medium text-gray-300">Filters</span>
            {activeFiltersCount > 0 && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-500 text-dark-800">
                {activeFiltersCount}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
              >
                Clear All
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center space-x-1"
            >
              <span>{isExpanded ? 'Hide' : 'Show'} Filters</span>
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {isExpanded && (
          <div className="mt-4 space-y-4">
            {filterGroups.map((group) => (
              <div key={group.name} className="border border-dark-600 rounded-lg">
                <button
                  onClick={() => toggleGroup(group.name)}
                  className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-dark-700 transition-colors rounded-t-lg"
                >
                  <span className="text-sm font-medium text-gray-300">
                    {group.displayName}
                  </span>
                  {expandedGroups.has(group.name) ? (
                    <ChevronUp className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  )}
                </button>
                
                {expandedGroups.has(group.name) && (
                  <div className="px-4 py-3 border-t border-dark-600 bg-dark-700 rounded-b-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                      {group.fields.map(renderFilterField)}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterSection;
