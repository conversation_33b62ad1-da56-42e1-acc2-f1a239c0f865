import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Set<PERSON><PERSON>, Menu, Search, User, ChevronDown } from 'lucide-react';
import { cn } from '../../utils';
import type { UserMenuAction } from './types';

export const Header: React.FC<{
  toggleSidebar: () => void;
  showSearch: boolean;
  searchPlaceholder: string;
  onSearch?: (query: string) => void;
  user?: {
    name?: string;
    email?: string;
    avatar?: React.ReactNode;
  };
  userMenuActions: UserMenuAction[];
}> = ({
  toggleSidebar,
  showSearch,
  searchPlaceholder,
  onSearch,
  user,
  userMenuActions,
}) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const userMenuRef = useRef<HTMLDivElement>(null);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch?.(query);
  };

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userMenuRef.current &&
        !userMenuRef.current.contains(event.target as Node)
      ) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="flex items-center justify-between px-6 py-4 bg-dark-800 border-b border-dark-700">
      <div className="flex items-center gap-4">
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-md hover:bg-dark-700 text-dark-300"
        >
          <Menu size={20} />
        </button>

        {showSearch && (
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search size={16} className="text-dark-400" />
            </div>
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={handleSearch}
              className="py-2 pl-10 pr-4 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500 text-sm w-64 text-white placeholder-dark-400"
            />
          </div>
        )}
      </div>

      <div className="flex items-center gap-4">
        <div className="flex items-center gap-1 px-3 py-1.5 bg-dark-700 rounded-md">
          <div className="w-2 h-2 rounded-full bg-green-500"></div>
          <span className="text-sm font-medium text-white">Online</span>
        </div>

        <button
          className="relative p-2 rounded-md text-dark-500 cursor-not-allowed opacity-50"
          disabled
        >
          <Bell size={20} />
          <span className="absolute top-1 right-1 w-2 h-2 bg-dark-500 rounded-full"></span>
        </button>

        <button
          className="p-2 rounded-md text-dark-500 cursor-not-allowed opacity-50"
          disabled
        >
          <Settings size={20} />
        </button>

        <div
          ref={userMenuRef}
          className="relative ml-4 pl-4 border-l border-dark-600"
        >
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center gap-3 hover:bg-dark-700 rounded-md p-2 transition-colors"
          >
            <div className="flex flex-col items-end">
              <span className="text-sm font-medium text-white">
                {user?.name || 'Admin User'}
              </span>
              <span className="text-xs text-dark-400">
                {user?.email || '<EMAIL>'}
              </span>
            </div>
            {user?.avatar || (
              <div className="w-9 h-9 bg-primary-500 rounded-full flex items-center justify-center text-white">
                <User size={18} />
              </div>
            )}
            <ChevronDown size={16} className="text-dark-400" />
          </button>

          {showUserMenu && (
            <div className="absolute right-0 top-full mt-2 w-48 bg-dark-700 border border-dark-600 rounded-md shadow-lg z-50">
              <div className="py-1">
                {userMenuActions.map((action, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      action.onClick();
                      setShowUserMenu(false);
                    }}
                    className={cn(
                      'flex items-center gap-2 w-full px-4 py-2 text-sm text-dark-300 hover:bg-dark-600 transition-colors',
                      action.variant === 'danger'
                        ? 'hover:text-red-400'
                        : 'hover:text-primary-400'
                    )}
                  >
                    {action.icon}
                    {action.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};
