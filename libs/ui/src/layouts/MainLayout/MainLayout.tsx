import React, { useState } from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { DefaultLogo } from './DefaultLogo';
import { cn } from '../../utils';
import type { MainLayoutProps } from './types';

// Main Layout Component
export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  appName = 'Admin Panel',
  appLogo,
  showSearch = true,
  searchPlaceholder = 'Search...',
  onSearch,
  user,
  userMenuActions = [],
  sidebarSections,
  theme = 'dark',
  className = '',
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const finalAppLogo = appLogo || <DefaultLogo />;

  return (
    <div
      className={cn(
        'flex h-screen overflow-hidden',
        theme === 'dark'
          ? 'bg-dark-900 text-white'
          : 'bg-dark-100 text-dark-900',
        className
      )}
    >
      <Sidebar
        open={sidebarOpen}
        appName={appName}
        appLogo={finalAppLogo}
        sections={sidebarSections}
      />

      <div className="flex flex-col flex-1 overflow-hidden">
        <Header
          toggleSidebar={toggleSidebar}
          showSearch={showSearch}
          searchPlaceholder={searchPlaceholder}
          onSearch={onSearch}
          user={user}
          userMenuActions={userMenuActions}
        />

        <main
          className={cn(
            'flex-1 overflow-y-auto p-6',
            theme === 'dark' ? 'bg-dark-800' : 'bg-dark-50'
          )}
        >
          {children}
        </main>
      </div>
    </div>
  );
};

// Re-export types for convenience
export type {
  MainLayoutProps,
  NavItem,
  NavSection,
  NavDropdown,
  UserMenuAction,
} from './types';
