import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { NavigationItem } from './NavigationItem';
import type { NavDropdown, NavItem } from './types';

// Utility function for class names
const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

export const DropdownNavigation: React.FC<{ item: NavDropdown }> = ({
  item,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const renderNavItem = (navItem: NavItem | NavDropdown, index: number) => {
    if ('type' in navItem && navItem.type === 'dropdown') {
      return <DropdownNavigation key={index} item={navItem} />;
    }
    return <NavigationItem key={index} {...(navItem as NavItem)} />;
  };

  return (
    <div>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-3 px-4 py-3 text-dark-300 hover:bg-dark-700 hover:text-primary-400 rounded-md transition-all duration-200 cursor-pointer w-full text-left"
      >
        {item.icon}
        <span className="flex-1">{item.label}</span>
        <ChevronDown
          size={16}
          className={cn(
            'transition-transform duration-200',
            isOpen && 'rotate-180'
          )}
        />
      </button>
      {isOpen && (
        <div className="pl-4 mt-1 space-y-1">
          {item.children.map((child, index) => renderNavItem(child, index))}
        </div>
      )}
    </div>
  );
};
