import React from 'react';
import { NavLink } from 'react-router-dom';
import type { NavItem } from './types';
import { cn } from '../../utils';

export const NavigationItem: React.FC<NavItem> = ({
  to,
  icon,
  label,
  badge,
  end = false,
}) => {
  return (
    <NavLink
      to={to}
      end={end}
      className={({ isActive }) =>
        cn(
          'flex items-center gap-3 px-4 py-3 text-dark-300 hover:bg-dark-700 hover:text-primary-400 rounded-md transition-all duration-200 cursor-pointer',
          isActive && 'bg-dark-700 text-primary-400'
        )
      }
    >
      {icon}
      <span className="flex-1">{label}</span>
      {badge && (
        <span className="px-2 py-0.5 text-xs bg-primary-500 text-white rounded-full">
          {badge}
        </span>
      )}
    </NavLink>
  );
};
