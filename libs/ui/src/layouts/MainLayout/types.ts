import React from 'react';

export interface NavItem {
  to: string;
  icon: React.ReactNode;
  label: string;
  badge?: string | number;
  end?: boolean;
}

export interface NavSection {
  title: string;
  items: (NavItem | NavDropdown)[];
}

export interface NavDropdown {
  type: 'dropdown';
  icon: React.ReactNode;
  label: string;
  children: (NavItem | NavDropdown)[];
}

export interface UserMenuAction {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  variant?: 'default' | 'danger';
}

export interface MainLayoutProps {
  children: React.ReactNode;

  // Header configuration
  appName?: string;
  appLogo?: React.ReactNode;
  showSearch?: boolean;
  searchPlaceholder?: string;
  onSearch?: (query: string) => void;

  // User configuration
  user?: {
    name?: string;
    email?: string;
    avatar?: React.ReactNode;
  };
  userMenuActions?: UserMenuAction[];

  // Sidebar configuration
  sidebarSections: NavSection[];

  // Theme and styling
  theme?: 'dark' | 'light';
  className?: string;
}
