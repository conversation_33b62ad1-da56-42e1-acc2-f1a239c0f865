import React from 'react';
import { NavigationItem } from './NavigationItem';
import { DropdownNavigation } from './DropdownNavigation';
import { cn } from '../../utils';
import type { NavSection, NavItem, NavDropdown } from './types';

export const Sidebar: React.FC<{
  open: boolean;
  appName: string;
  appLogo: React.ReactNode;
  sections: NavSection[];
}> = ({ open, appName, appLogo, sections }) => {
  const renderNavItem = (item: NavItem | NavDropdown, index: number) => {
    if ('type' in item && item.type === 'dropdown') {
      return <DropdownNavigation key={index} item={item} />;
    }
    return <NavigationItem key={index} {...(item as NavItem)} />;
  };

  return (
    <aside
      className={cn(
        'bg-dark-800 border-r border-dark-700 h-screen transition-all duration-300 overflow-y-auto',
        open ? 'w-64' : 'w-0'
      )}
    >
      <div className="p-4 border-b border-dark-700">
        <div className="flex items-center gap-3">
          {appLogo}
          <span className="font-bold text-xl text-white">{appName}</span>
          <span className="text-xs bg-dark-700 px-2 py-0.5 rounded text-primary-400">
            Admin
          </span>
        </div>
      </div>

      <div className="p-3">
        {sections.map((section, sectionIndex) => (
          <div key={sectionIndex}>
            {sectionIndex > 0 && (
              <div className="mt-6 mb-2 px-4 text-xs font-semibold text-dark-400 uppercase">
                {section.title}
              </div>
            )}
            <div className={sectionIndex === 0 ? '' : 'space-y-1'}>
              {section.items.map((item, itemIndex) =>
                renderNavItem(item, itemIndex)
              )}
            </div>
          </div>
        ))}
      </div>
    </aside>
  );
};
