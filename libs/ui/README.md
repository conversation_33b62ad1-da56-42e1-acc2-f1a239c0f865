# @panels/ui

Shared UI components library for the panels monorepo.

## Components

### MainLayout

A comprehensive admin layout component extracted from the ebetlab app and made reusable across all panel applications.

#### Features

- **Responsive Sidebar**: Collapsible sidebar with configurable navigation sections
- **Header with Search**: Top header with search functionality and user menu
- **Nested Navigation**: Support for dropdown menus and nested navigation items
- **User Management**: Configurable user menu with custom actions
- **Theme Support**: Dark and light theme variants
- **TypeScript**: Full TypeScript support with comprehensive type definitions

#### Usage

```tsx
import { MainLayout, type NavSection, type UserMenuAction } from '@panels/ui';
import { LayoutDashboard, Users, Settings } from 'lucide-react';

const sidebarSections: NavSection[] = [
  {
    title: 'Main',
    items: [
      {
        to: '/',
        icon: <LayoutDashboard size={20} />,
        label: 'Dashboard',
        end: true
      }
    ]
  },
  {
    title: 'Management',
    items: [
      {
        type: 'dropdown',
        icon: <Users size={20} />,
        label: 'Users',
        children: [
          {
            to: '/users/list',
            icon: <Users size={16} />,
            label: 'User List'
          }
        ]
      }
    ]
  }
];

const userMenuActions: UserMenuAction[] = [
  {
    icon: <Settings size={16} />,
    label: 'Settings',
    onClick: () => navigate('/settings')
  }
];

function App() {
  return (
    <MainLayout
      appName="My Admin Panel"
      sidebarSections={sidebarSections}
      userMenuActions={userMenuActions}
      user={{ name: 'John Doe', email: '<EMAIL>' }}
      onSearch={(query) => console.log('Search:', query)}
    >
      <YourPageContent />
    </MainLayout>
  );
}
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `ReactNode` | - | Main content area |
| `appName` | `string` | `'Admin Panel'` | Application name in sidebar |
| `appLogo` | `ReactNode` | Default flame logo | Custom logo component |
| `showSearch` | `boolean` | `true` | Show/hide search bar |
| `searchPlaceholder` | `string` | `'Search...'` | Search input placeholder |
| `onSearch` | `(query: string) => void` | - | Search callback function |
| `user` | `{ name?, email?, avatar? }` | - | User information |
| `userMenuActions` | `UserMenuAction[]` | Default actions | Custom user menu items |
| `sidebarSections` | `NavSection[]` | - | Sidebar navigation structure |
| `theme` | `'dark' \| 'light'` | `'dark'` | Color theme |
| `className` | `string` | `''` | Additional CSS classes |

#### Types

```tsx
interface NavItem {
  to: string;
  icon: ReactNode;
  label: string;
  badge?: string | number;
  end?: boolean;
}

interface NavDropdown {
  type: 'dropdown';
  icon: ReactNode;
  label: string;
  children: (NavItem | NavDropdown)[];
}

interface NavSection {
  title: string;
  items: (NavItem | NavDropdown)[];
}

interface UserMenuAction {
  icon: ReactNode;
  label: string;
  onClick: () => void;
  variant?: 'default' | 'danger';
}
```

## Development

This library was generated with [Nx](https://nx.dev).

### Running unit tests

Run `nx test @panels/ui` to execute the unit tests via [Vitest](https://vitest.dev/).

### Building

Run `nx build ui` to build the library.

## Dependencies

- React 18+
- react-router-dom
- lucide-react
- Tailwind CSS
