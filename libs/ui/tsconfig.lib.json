{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts", "vite/client"], "rootDir": "src", "jsx": "react-jsx", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo"}, "exclude": ["out-tsc", "dist", "**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "**/*.spec.js", "**/*.test.js", "**/*.spec.jsx", "**/*.test.jsx"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"]}