on:
  push:
    branches:
      - 'master'
    paths:
      - 'apps/pronet/**'
      - 'libs/**'
      - '.github/workflows/deploy-pronet.yaml'
      - package.json
      - nx.json
      - 'dockerfiles/pronet/**'

concurrency:
  group: ${{ github.ref_name }}-bo-panel-fe-pronet
  cancel-in-progress: false

jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy to production
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies
        run: yarn

      - name: Build project
        run: npx nx build pronet
        env:
          VITE_API_URL: ${{ vars.PROD_VITE_API_URL }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.PROD_CI_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PROD_CI_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.PROD_CI_AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        run: |
          aws ecr get-login-password --region ${{ vars.PROD_CI_AWS_REGION }} | \
          docker login --username AWS --password-stdin ${{ vars.PROD_CI_ECR_REGISTRY }}

      - name: Get commit hash
        id: set-tag
        run: echo "unique_image_tag=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Build and Push Docker Image
        env:
          ECR_REGISTRY: ${{ vars.PROD_CI_ECR_REGISTRY }}
          ECR_REPOSITORY: makroz/bo-panel-fe-pronet-makrozorg-prod
          IMAGE_TAG: ${{ steps.set-tag.outputs.unique_image_tag }}
        run: |
          docker build -f dockerfiles/pronet/Dockerfile -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Create lock
        uses: abatilo/github-action-locks@v1
        with:
          timeout: "30"
          table: "github-lock-makroz"
          key: "LockID"
          name: "${{ github.ref_name }}-deploy"

      - name: Change tag for argocd
        env:
          APP: prod/makroz/bo-panel-fe-pronet
          IMAGE_TAG: ${{ steps.set-tag.outputs.unique_image_tag }}
          AUTHOR: ${{ github.event.commits[0].author.name }}
          ACCESS_TOKEN: ${{ secrets.PROD_CI_PAT_TOKEN }}
        run: |
          cd ..
          git clone https://oauth2:$<EMAIL>/bonusby-technical/makroz-argocd.git argocd
          cd argocd
          sed -i -E "s/tag:(.*)/tag: \"${IMAGE_TAG}\"/" $APP/values.yaml
          sed -i -E "s/author:(.*)/author: ${AUTHOR}/" $APP/values.yaml
          git config --global user.name "GitHub CI"
          git config --global user.email "<EMAIL>"
          git add . && git commit -m "CI deploy new image to ${APP}" && git push

      - name: Login to argocd
        uses: clowdhaus/argo-cd-action/@main
        with:
          command: login
          options: ${{ vars.PROD_CI_ARGO_HOST }} --grpc-web  --username ${{ secrets.PROD_CI_ARGO_USER }} --password ${{ secrets.PROD_CI_ARGO_PASS }}

      - name: Refresh argocd app
        uses: clowdhaus/argo-cd-action/@main
        with:
          command: app get
          options: --refresh bo-panel-fe-pronet

      - name: Sync argocd app
        uses: clowdhaus/argo-cd-action/@main
        with:
          command: app sync
          options: bo-panel-fe-pronet

      - name: Wait for argocd app
        uses: clowdhaus/argo-cd-action/@main
        with:
          command: app wait
          options: bo-panel-fe-pronet
