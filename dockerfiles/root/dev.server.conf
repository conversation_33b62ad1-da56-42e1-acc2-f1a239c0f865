upstream pronet   { server pronet:5001; }
# upstream ebetlab  { server ebetlab:5002; }

map $http_cookie $backend {
    "~*environment=pronet"  pronet;
    # "~*environment=ebetlab" ebetlab;
    default         "";
}

map $backend $do_proxy {
    ""      0;
    default 1;
}

server {
  listen 5000;

  root /usr/share/nginx/html/home/<USER>
  index index.html index.htm;

  location / {
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    if ($do_proxy) {
        proxy_pass http://$backend;
        break;
    }

    try_files $uri $uri/ /index.html;
  }
}
