upstream pronet   { server bo-panel-fe-pronet:80; }
upstream ebetlab  { server bo-panel-fe-ebetlab:80; }

map $http_cookie $backend {
    "~*environment=pronet"  pronet;
    "~*environment=ebetlab" ebetlab;
    default         "";
}

map $backend $do_proxy {
    ""      0;
    default 1;
}

server {
  listen 80;

  root /usr/share/nginx/html;
  index index.html index.htm;

  location / {
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    if ($do_proxy) {
        proxy_pass http://$backend;
        break;
    }

    try_files $uri $uri/ /index.html;
  }
}
