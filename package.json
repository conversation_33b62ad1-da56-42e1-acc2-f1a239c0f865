{"name": "@panels/source", "version": "0.0.0", "license": "MIT", "scripts": {"start:root": "nx serve root", "start:home": "nx serve home", "start:pronet": "nx serve pronet"}, "engines": {"node": ">=20"}, "private": true, "dependencies": {"@headlessui/react": "^2.2.4", "chart.js": "^4.4.1", "clsx": "^2.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "lucide-react": "^0.344.0", "node-fetch": "^2.7.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-router-dom": "6.29.0", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/js": "^9.9.1", "@nx/eslint": "21.2.3", "@nx/eslint-plugin": "21.2.3", "@nx/js": "21.2.3", "@nx/react": "21.2.3", "@nx/vite": "21.2.3", "@nx/web": "21.2.3", "@nx/workspace": "21.2.3", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/node": "^20.0.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/ui": "^3.0.0", "ajv": "^8.0.0", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "nx": "^21.3.8", "postcss": "8.4.38", "prettier": "^2.6.2", "tailwindcss": "3.4.3", "tslib": "^2.3.0", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.0.0", "vite-plugin-dts": "~4.5.0", "vitest": "^3.0.0"}, "workspaces": ["apps/*", "lib/*", "libs/*"], "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}