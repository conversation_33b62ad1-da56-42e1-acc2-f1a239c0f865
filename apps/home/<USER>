const { createGlobPatternsForDependencies } = require('@nx/react/tailwind');
const { join } = require('path');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    join(
      __dirname,
      '{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html}'
    ),
    ...createGlobPatternsForDependencies(__dirname),
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#e6fbff',
          100: '#ccf7fe',
          200: '#99effd',
          300: '#66e7fc',
          400: '#33dffa',
          500: '#18cffb', // Main primary color
          600: '#0ca5c8',
          700: '#097c96',
          800: '#065264',
          900: '#032932',
        },
        secondary: {
          50: '#e6fbff',
          100: '#ccf7fe',
          200: '#99effd',
          300: '#66e7fc',
          400: '#33dffa',
          500: '#18cffb', // Use same as primary
          600: '#0ca5c8',
          700: '#097c96',
          800: '#065264',
          900: '#032932',
        },
        dark: {
          50: '#e6edf2',
          100: '#ccdbe4',
          200: '#99b7c9',
          300: '#6693ae',
          400: '#336f93',
          500: '#004b78',
          600: '#003c60',
          700: '#002d48',
          800: '#061d2b', // Main dark color
          900: '#000f15',
        },
        success: {
          500: '#10b981',
          600: '#059669',
        },
        warning: {
          500: '#f59e0b',
          600: '#d97706',
        },
        error: {
          500: '#ef4444',
          600: '#dc2626',
        },
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
    },
  },
  plugins: [],
};
