import { useEffect, useState } from 'react';
import { Button, Input, Select } from './ui';

interface Environment {
  displayName: string;
  type: string;
  disabled: boolean;
  redirect: string;
}

const environments: Environment[] = [
  {
    displayName: 'Makrobet (dagur)',
    type: 'pronet',
    disabled: false,
    redirect: import.meta.env.VITE_PRONET_APP_URL,
  },
  {
    displayName: 'Betroz (ebetlab)',
    type: 'ebetlab',
    disabled: true,
    redirect: import.meta.env.VITE_EBETLAB_APP_URL,
  },
];

interface FormData {
  environment: string;
  username: string;
  password: string;
  otpCode: string;
}

interface FormErrors {
  [key: string]: string;
}

export const AuthForm = () => {
  const [formData, setFormData] = useState<FormData>({
    environment: '',
    username: '',
    password: '',
    otpCode: '',
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [generalError, setGeneralError] = useState<string>('');

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
    // Clear general error
    if (generalError) {
      setGeneralError('');
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.environment) {
      newErrors.environment = 'Please select an environment';
    }

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    }

    if (!formData.otpCode.trim()) {
      newErrors.otpCode = 'OTP code is required';
    }

    setFormErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setGeneralError('');

    try {
      const selectedEnv = environments.find(
        (env) => env.type === formData.environment
      );

      if (!selectedEnv) {
        throw new Error('Invalid environment selected');
      }

      if (selectedEnv.disabled) {
        throw new Error(`${selectedEnv.displayName} is currently disabled`);
      }

      // For now, only implement login for pronet
      if (formData.environment === 'pronet') {
        const response = await makePronetLogin({
          username: formData.username.trim(),
          password: formData.password,
          otpCode: formData.otpCode.trim(),
        });

        if (response.success && response.data.session) {
          document.cookie = `environment=${formData.environment}; path=/; SameSite=Lax`;

          await new Promise((res) => setTimeout(res, 300));
          window.location.href =
            (selectedEnv.redirect || '') + `?session=${response.data.session}`;
        } else {
          throw new Error(response.error || 'Login failed');
        }
      } else {
        throw new Error(
          `Login for ${selectedEnv.displayName} is not implemented yet`
        );
      }
    } catch (error) {
      setGeneralError(
        error instanceof Error ? error.message : 'An unexpected error occurred'
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Simple API request function for pronet login
  const makePronetLogin = async (credentials: {
    username: string;
    password: string;
    otpCode: string;
  }) => {
    try {
      // Make request to pronet Dagur login endpoint
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/pronet/v1/auth/dagur/login`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(credentials),
        }
      );

      const data = await response.json();

      if (response.ok) {
        return data;
      } else {
        return { success: false, error: data.error || 'Login failed' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  };

  const selectedEnvironment = environments.find(
    (env) => env.type === formData.environment
  );

  useEffect(() => {
    const environmentName = document.cookie
      .split('; ')
      .find((row) => row.startsWith('environment='));
    if (!environmentName) {
      return;
    }

    const environment = environments.find(
      (env) => env.type === environmentName?.split('=')[1]
    );
    if (environment) {
      window.location.href = environment.redirect || '/';
    }
  }, []);

  return (
    <div className="bg-dark-800 shadow-xl rounded-lg border border-dark-600 p-8">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-gray-300 mb-2">
          Admin Panel Login
        </h1>
        <p className="text-gray-400">
          Select your environment and sign in to continue
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Environment Selection */}
        <Select
          label="Environment"
          placeholder="Select an environment..."
          value={formData.environment}
          onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
            handleInputChange('environment', e.target.value)
          }
          options={environments.map((env) => ({
            value: env.type,
            label: env.displayName,
            disabled: env.disabled,
          }))}
          error={formErrors.environment}
        />

        {/* Username */}
        <Input
          label="Username"
          type="text"
          placeholder="Enter your username"
          value={formData.username}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleInputChange('username', e.target.value)
          }
          error={formErrors.username}
          disabled={isLoading}
        />

        {/* Password */}
        <Input
          label="Password"
          type="password"
          placeholder="Enter your password"
          value={formData.password}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleInputChange('password', e.target.value)
          }
          error={formErrors.password}
          disabled={isLoading}
        />

        {/* OTP Code */}
        <Input
          label="OTP Code"
          type="text"
          placeholder="Enter your OTP code"
          value={formData.otpCode}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleInputChange('otpCode', e.target.value)
          }
          error={formErrors.otpCode}
          disabled={isLoading}
        />

        {/* General Error */}
        {generalError && (
          <div className="bg-red-900/20 border border-red-700 rounded-md p-3">
            <p className="text-sm text-red-400">{generalError}</p>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full"
          loading={isLoading}
          disabled={isLoading || (selectedEnvironment?.disabled ?? false)}
        >
          {isLoading ? 'Signing in...' : 'Sign In'}
        </Button>
      </form>
    </div>
  );
};
