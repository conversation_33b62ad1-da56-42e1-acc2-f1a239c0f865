# Betroz Admin Panel

A modern React-based admin panel for managing Betroz platform settings and configurations. Built with TypeScript, Tailwind CSS, and integrated with EbetLab API services.

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- Yarn package manager

### Installation & Setup

1. **Install dependencies:**
   ```bash
   yarn install
   ```

2. **Start the development server:**
   ```bash
   yarn dev
   ```

3. **Start the proxy server (required):**
   ```bash
   yarn server
   ```

4. **Access the application:**
   - Frontend: `http://localhost:5173`
   - Backend Proxy: `http://localhost:3001`

## 🏗️ Architecture

### Frontend (React + TypeScript)
- **Framework:** React 18 with TypeScript
- **Styling:** Tailwind CSS with custom dark theme
- **Routing:** React Router DOM
- **State Management:** React Context API
- **Build Tool:** Vite

### Backend Proxy Server
- **Runtime:** Node.js with Express
- **Purpose:** Proxy requests to EbetLab API with proper authentication
- **CORS:** Enabled for local development

## 🔐 Authentication

The application uses EbetLab's authentication system:
- **Login Modal:** Captures username, password, and OTP
- **Authentication Flow:** 
  1. Cloudflare clearance (`cfInit`)
  2. X-Fingerprint calculation
  3. Signed challenge generation
  4. Token-based authentication
- **Storage:** Authentication data stored in localStorage (`betroz_auth` and `betroz_login`)

## 📱 Features

### 🏠 Dashboard
- Overview of system metrics and statistics
- Quick access to main features

### 👤 Admin Profile
- User information display from authentication data
- Role and permission details
- Security settings overview

### 🎨 CSS Settings Management
- **List View:** Paginated table with all CSS settings
- **Search & Filter:** By theme, status, operator
- **Detailed View:** Individual CSS setting information
- **Edit Functionality:** 
  - Live CSS code editor with syntax highlighting
  - Theme selection (Light/Dark/Both)
  - Active/Inactive toggle
  - Real-time preview capabilities

## 🛠️ Technical Stack

### Dependencies
- **React:** UI framework
- **TypeScript:** Type safety
- **Tailwind CSS:** Utility-first styling
- **React Router:** Client-side routing
- **Lucide React:** Icon library
- **Chart.js:** Data visualization
- **Express:** Backend proxy server
- **CORS:** Cross-origin resource sharing

### Development Tools
- **Vite:** Fast build tool and dev server
- **ESLint:** Code linting
- **PostCSS:** CSS processing
- **Autoprefixer:** CSS vendor prefixing

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── css-settings/   # CSS management components
│   ├── layout/         # Layout components (Header, Sidebar)
│   └── ui/             # Base UI components (Button, Modal, etc.)
├── contexts/           # React Context providers
├── pages/              # Page components
├── utils/              # Utility functions and API calls
└── styles/             # Global styles and Tailwind config
```

## 🔌 API Integration

### EbetLab API Endpoints
- **Authentication:** `POST /api/operator/configuration/token`
- **CSS Settings List:** `POST /api/operator/css-settings/index2/{page}/{limit}`
- **CSS Setting Details:** `POST /api/operator/css-settings/show/{id}`

### Proxy Server Endpoints
- **Authentication:** `POST /api/auth`
- **CSS Settings:** `POST /api/css-settings`
- **Single CSS Setting:** `POST /api/css-setting`

## 🔧 Configuration

### Environment Variables
Create a `.env` file for any additional configuration (optional).

### Authentication Requirements
- Valid EbetLab credentials (username, password, OTP)
- Active operator account with appropriate permissions

## 🎯 Key Features

### CSS Settings Management
- **Pagination:** Configurable page size (10, 20, 50, 100)
- **Real-time Editing:** Live CSS code editor
- **Theme Support:** Light, Dark, and Both themes
- **Status Management:** Active/Inactive toggle
- **CDN Integration:** Direct CSS file access
- **Syntax Highlighting:** Code editor with CSS syntax support

### User Experience
- **Responsive Design:** Works on desktop and mobile
- **Dark Theme:** Consistent dark UI throughout
- **Loading States:** Proper feedback during API calls
- **Error Handling:** User-friendly error messages
- **Navigation:** Intuitive sidebar and breadcrumb navigation

## 🚦 Development Commands

```bash
# Install dependencies
yarn install

# Start development server (frontend)
yarn dev

# Start proxy server (backend)
yarn server

# Build for production
yarn build

# Preview production build
yarn preview
```

## 📝 Notes for Developers

1. **Always run both servers:** The frontend requires the proxy server for API calls
2. **Authentication required:** You need valid EbetLab credentials to access features
3. **CORS handling:** All EbetLab API calls go through the proxy server
4. **State management:** Authentication state is managed via React Context
5. **Styling:** Use Tailwind CSS classes, custom components available in `/components/ui/`

## 🔒 Security

- Authentication tokens are stored securely in localStorage
- All API calls require fresh authentication signatures
- CORS protection enabled
- Input validation on all forms

## 🐛 Troubleshooting

### Common Issues
1. **"Authentication data required" error:** Ensure you're logged in with valid credentials
2. **CORS errors:** Make sure the proxy server (`yarn server`) is running
3. **Build errors:** Run `yarn install` to ensure all dependencies are installed
4. **Port conflicts:** Default ports are 5173 (frontend) and 3000 (backend)

---

**Built with ❤️ for the Betroz platform**
