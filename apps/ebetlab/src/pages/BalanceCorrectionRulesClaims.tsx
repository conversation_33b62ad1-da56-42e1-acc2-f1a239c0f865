import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { DollarSign, ArrowLeft, RefreshCw, AlertCircle, User, Calendar, Hash, CheckCircle, XCircle, TrendingUp, TrendingDown } from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import { fetchBalanceCorrectionRuleClaims, type BalanceCorrectionRuleClaim } from '../utils/api';

interface BalanceCorrectionRuleClaimsData {
  total: number;
  data: BalanceCorrectionRuleClaim[];
}

const BalanceCorrectionRulesClaims: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [claimsData, setClaimsData] = useState<BalanceCorrectionRuleClaimsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sortState, setSortState] = useState<SortState>({
    field: 'id',
    direction: 'desc'
  });

  const loadClaims = useCallback(async (page: number = currentPage, limit: number = itemsPerPage, searchFilters: Record<string, unknown> = filters) => {
    if (!id) return;
    
    setIsLoading(true);
    setError('');

    try {
      const response = await fetchBalanceCorrectionRuleClaims(id, page, limit);

      if (response.success && response.data) {
        setClaimsData(response.data.data.claims);
      } else {
        setError(response.error || 'Failed to load claims');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [id, currentPage, itemsPerPage, filters]);

  // Load claims on component mount
  useEffect(() => {
    loadClaims();
  }, [loadClaims]);

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadClaims(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadClaims(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadClaims(1, itemsPerPage, newFilters);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (claim: BalanceCorrectionRuleClaim) => {
    // Future: navigate to claim details
    console.log('View claim:', claim.id);
  };

  // Format timestamp
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Get completion badge
  const getCompletionBadge = (completed: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          completed
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
        }`}
      >
        {completed ? <CheckCircle className="w-3 h-3 mr-1" /> : <XCircle className="w-3 h-3 mr-1" />}
        {completed ? 'Completed' : 'Pending'}
      </span>
    );
  };

  // Get activity badge
  const getActivityBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isActive
            ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
            : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
        }`}
      >
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  // Get way badge (up/down)
  const getWayBadge = (way: string) => {
    const isUp = way === 'up';
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isUp
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        {isUp ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
        {way.toUpperCase()}
      </span>
    );
  };

  // Table columns configuration
  const tableColumns: TableColumn<BalanceCorrectionRuleClaim>[] = [
    {
      key: 'id',
      label: 'Claim ID',
      width: '100px',
      sortable: true,
      render: (claim: BalanceCorrectionRuleClaim) => (
        <span className="font-mono text-sm">#{claim.id}</span>
      )
    },
    {
      key: 'customer',
      label: 'Customer',
      width: '150px',
      sortable: true,
      render: (claim: BalanceCorrectionRuleClaim) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <div>
            <div className="text-sm font-medium">{claim.customer.username}</div>
            <div className="text-xs text-gray-500">ID: {claim.customer.id}</div>
          </div>
        </div>
      )
    },
    {
      key: 'way',
      label: 'Direction',
      width: '100px',
      sortable: true,
      render: (claim: BalanceCorrectionRuleClaim) => getWayBadge(claim.way)
    },
    {
      key: 'amount',
      label: 'Amount',
      width: '120px',
      sortable: true,
      render: (claim: BalanceCorrectionRuleClaim) => (
        <div>
          <div className="text-sm font-medium">{claim.amount} {claim.currency}</div>
          <div className="text-xs text-gray-500">${claim.usd_amount} USD</div>
        </div>
      )
    },
    {
      key: 'balance_change',
      label: 'Balance Change',
      width: '150px',
      sortable: false,
      render: (claim: BalanceCorrectionRuleClaim) => (
        <div className="text-sm">
          <div className="text-gray-400">Before: {claim.before_balance}</div>
          <div className="text-gray-200">After: {claim.after_balance}</div>
        </div>
      )
    },
    {
      key: 'wager_progress',
      label: 'Wager Progress',
      width: '120px',
      sortable: false,
      render: (claim: BalanceCorrectionRuleClaim) => (
        <div className="text-sm">
          <div className="text-gray-200">{claim.wagered} / {claim.required_wager}</div>
          <div className="text-xs text-gray-500">
            {((parseFloat(claim.wagered) / parseFloat(claim.required_wager)) * 100).toFixed(1)}%
          </div>
        </div>
      )
    },
    {
      key: 'completion_status',
      label: 'Completion',
      width: '120px',
      sortable: true,
      render: (claim: BalanceCorrectionRuleClaim) => getCompletionBadge(claim.completed)
    },
    {
      key: 'activity_status',
      label: 'Activity',
      width: '100px',
      sortable: true,
      render: (claim: BalanceCorrectionRuleClaim) => getActivityBadge(claim.is_active)
    },
    {
      key: 'timestamp',
      label: 'Created',
      width: '150px',
      sortable: true,
      render: (claim: BalanceCorrectionRuleClaim) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{formatDate(claim.timestamp)}</span>
        </div>
      )
    }
  ];

  // Filter configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Information',
      fields: [
        {
          key: 'customer_id',
          label: 'Customer ID',
          type: 'text',
          placeholder: 'Search by customer ID...'
        },
        {
          key: 'username',
          label: 'Username',
          type: 'text',
          placeholder: 'Search by username...'
        }
      ]
    },
    {
      name: 'status',
      displayName: 'Status',
      fields: [
        {
          key: 'completed',
          label: 'Completion Status',
          type: 'select',
          options: [
            { value: 'true', label: 'Completed' },
            { value: 'false', label: 'Pending' }
          ]
        },
        {
          key: 'is_active',
          label: 'Activity Status',
          type: 'select',
          options: [
            { value: 'true', label: 'Active' },
            { value: 'false', label: 'Inactive' }
          ]
        },
        {
          key: 'way',
          label: 'Direction',
          type: 'select',
          options: [
            { value: 'up', label: 'Up' },
            { value: 'down', label: 'Down' }
          ]
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          onClick={() => navigate('/balance-correction-rules')}
          variant="ghost"
          size="sm"
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Rules
        </Button>
        
        <div className="flex items-center gap-3">
          <DollarSign className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Balance Correction Rule Claims</h1>
            <p className="text-gray-400">Rule ID: {id}</p>
          </div>
        </div>

        <div className="ml-auto">
          <Button
            onClick={() => loadClaims(currentPage, itemsPerPage, filters)}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Claims</h4>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={claimsData?.data || []}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={claimsData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Claims"
        emptyState={{
          icon: <DollarSign className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No claims found',
          description: 'This balance correction rule has no claims yet.',
          action: undefined
        }}
      />
    </div>
  );
};

export default BalanceCorrectionRulesClaims;
