import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Settings, RefreshCw, AlertCircle, Eye, Calendar, User, Gift, Hash, Activity, Tag, Plus, Edit, Trash2, FileText } from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import { fetchAutoBonusRules, deleteAutoBonusRule, type AutoBonusRuleData } from '../utils/api';

interface AutoBonusRulesData {
  total: number;
  data: AutoBonusRuleData[];
}

const AutoBonusRulesList: React.FC = () => {
  const navigate = useNavigate();
  const [rulesData, setRulesData] = useState<AutoBonusRulesData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sortState, setSortState] = useState<SortState>({
    field: 'id',
    direction: 'desc'
  });

  const loadAutoBonusRules = useCallback(async (page: number = currentPage, limit: number = itemsPerPage, searchFilters: Record<string, unknown> = filters) => {
    setIsLoading(true);
    setError('');

    try {
      // Include sorting parameters in the request
      const searchParamsWithSort = {
        ...searchFilters,
        sortBy: sortState.field,
        direction: sortState.direction
      };

      const response = await fetchAutoBonusRules(page, limit, searchParamsWithSort);

      if (response.success && response.data) {
        setRulesData(response.data.data);
      } else {
        setError(response.error || 'Failed to load auto bonus rules');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load auto bonus rules on component mount
  useEffect(() => {
    loadAutoBonusRules();
  }, [loadAutoBonusRules]);

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadAutoBonusRules(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadAutoBonusRules(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadAutoBonusRules(1, itemsPerPage, newFilters);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (rule: AutoBonusRuleData) => {
    navigate(`/auto-bonus-rules/details/${rule.id}`);
  };

  // Handle view details
  const handleView = (ruleId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/auto-bonus-rules/details/${ruleId}`);
  };

  // Handle edit
  const handleEdit = (ruleId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/auto-bonus-rules/edit/${ruleId}`);
  };

  // Handle delete
  const handleDelete = async (ruleId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm('Are you sure you want to delete this auto bonus rule?')) return;

    try {
      const response = await deleteAutoBonusRule(ruleId);

      if (response.success) {
        loadAutoBonusRules(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to delete auto bonus rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete auto bonus rule');
    }
  };

  // Handle show claims
  const handleShowClaims = (ruleId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/auto-bonus-rules/${ruleId}/claims`);
  };

  // Format timestamp
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format rules array
  const formatRules = (rules: Array<{ field: string; value: string; operator: string }>) => {
    return rules.map(rule => `${rule.field} ${rule.operator} ${rule.value}`).join(', ');
  };

  // Get status badge
  const getStatusBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isActive
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        <Activity className="w-3 h-3 mr-1" />
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  // Table columns configuration
  const tableColumns: TableColumn<AutoBonusRuleData>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (rule: AutoBonusRuleData) => (
        <span className="font-mono text-sm">#{rule.id}</span>
      )
    },
    {
      key: 'bonus_code',
      label: 'Bonus Code',
      width: '120px',
      sortable: true,
      render: (rule: AutoBonusRuleData) => (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium">{rule.bonus_code || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'operator',
      label: 'Operator',
      width: '150px',
      sortable: true,
      render: (rule: AutoBonusRuleData) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <div>
            <div className="text-sm font-medium">{rule.operator.name}</div>
            <div className="text-xs text-gray-500">{rule.operator.email}</div>
          </div>
        </div>
      )
    },
    {
      key: 'bonus',
      label: 'Bonus',
      width: '200px',
      sortable: true,
      render: (rule: AutoBonusRuleData) => (
        <div className="flex items-center gap-2">
          <Gift className="w-4 h-4 text-gray-400" />
          <div>
            <div className="text-sm font-medium">{rule.bonus.name}</div>
            <div className="text-xs text-gray-500">{rule.bonus.currency}</div>
          </div>
        </div>
      )
    },
    {
      key: 'rules',
      label: 'Rules',
      width: '200px',
      sortable: false,
      render: (rule: AutoBonusRuleData) => (
        <span className="text-sm text-gray-200 font-mono">
          {formatRules(rule.rules)}
        </span>
      )
    },
    {
      key: 'claims_count',
      label: 'Claims',
      width: '80px',
      sortable: true,
      render: (rule: AutoBonusRuleData) => (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium">{rule.claims_count}</span>
        </div>
      )
    },
    {
      key: 'total',
      label: 'Total',
      width: '80px',
      sortable: true,
      render: (rule: AutoBonusRuleData) => (
        <span className="text-sm font-medium">{rule.total}</span>
      )
    },
    {
      key: 'is_active',
      label: 'Status',
      width: '100px',
      sortable: true,
      render: (rule: AutoBonusRuleData) => (
        <div className="flex items-center gap-2">
          <Activity className="w-4 h-4 text-gray-400" />
          {getStatusBadge(rule.is_active)}
        </div>
      )
    },
    {
      key: 'timestamp',
      label: 'Created',
      width: '150px',
      sortable: true,
      render: (rule: AutoBonusRuleData) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{formatDate(rule.timestamp)}</span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '200px',
      sortable: false,
      render: (rule: AutoBonusRuleData) => (
        <div className="flex gap-1">
          <button
            onClick={(e) => handleView(rule.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-blue-400 transition-colors"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={(e) => handleEdit(rule.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500 transition-colors"
            title="Edit Rule"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => handleShowClaims(rule.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-green-400 transition-colors"
            title="Show Claims"
          >
            <FileText size={16} />
          </button>
          <button
            onClick={(e) => handleDelete(rule.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-red-400 transition-colors"
            title="Delete Rule"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  // Filter configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Information',
      fields: [
        {
          key: 'id',
          label: 'Rule ID',
          type: 'text',
          placeholder: 'Search by rule ID...'
        },
        {
          key: 'bonus_code',
          label: 'Bonus Code',
          type: 'text',
          placeholder: 'Search by bonus code...'
        },
        {
          key: 'operator_id',
          label: 'Operator ID',
          type: 'text',
          placeholder: 'Search by operator ID...'
        }
      ]
    },
    {
      name: 'status',
      displayName: 'Status & Activity',
      fields: [
        {
          key: 'is_active',
          label: 'Status',
          type: 'select',
          options: [
            { value: 'true', label: 'Active' },
            { value: 'false', label: 'Inactive' }
          ]
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Settings className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Auto Bonus Rules</h1>
            <p className="text-gray-400">Manage automatic bonus rule assignments</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => loadAutoBonusRules(currentPage, itemsPerPage, filters)}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>

          <Button
            onClick={() => navigate('/auto-bonus-rules/create')}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Rule
          </Button>
        </div>
      </div>

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Auto Bonus Rules</h4>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={rulesData?.data || []}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={rulesData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Auto Bonus Rules"
        emptyState={{
          icon: <Settings className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No auto bonus rules found',
          description: 'Get started by creating your first auto bonus rule.',
          action: (
            <Button
              onClick={() => navigate('/auto-bonus-rules/create')}
              variant="primary"
            >
              Create Rule
            </Button>
          )
        }}
      />
    </div>
  );
};

export default AutoBonusRulesList;
