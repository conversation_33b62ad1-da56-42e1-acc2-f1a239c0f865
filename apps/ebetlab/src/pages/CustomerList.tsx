import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Download, Eye, RefreshCw, Filter, X, ChevronUp, ChevronDown } from 'lucide-react';
import { fetchCustomers, type CustomerData, type CustomersResponse } from '../utils/api';
import DateRangePicker from '../components/ui/DateRangePicker';
import { VIP_LEVELS, getVipLevelDisplayName, getVipLevelColor } from '../constants/vipLevels';
import QuickUserInfoModal from '../components/customer-details/QuickUserInfoModal';


// Types for filters
interface FilterState {
  [key: string]: string | number | null;
}

interface DateRange {
  from: Date | null;
  to: Date | null;
}

// Table column configuration
interface TableColumn {
  key: string;
  label: string;
  width: string;
  sortable?: boolean; // Whether this column can be sorted
  render: (customer: CustomerData) => React.ReactNode;
}

// Sorting state
interface SortState {
  field: string | null;
  direction: 'asc' | 'desc' | null;
}

// Filter configuration
interface FilterConfig {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date_range' | 'range';
  options?: string[];
  minKey?: string;
  maxKey?: string;
}

// Helper functions
const formatCurrency = (amount: string | undefined): string => {
  if (!amount) return '-';
  return `$${parseFloat(amount).toFixed(2)}`;
};

const formatDeposit = (deposit: { amount?: string; currency?: string } | null): string => {
  if (!deposit?.amount || !deposit?.currency) return '-';
  return `${parseFloat(deposit.amount).toFixed(2)} ${deposit.currency}`;
};

const formatWithdraw = (withdraw: { amount?: string; currency?: string } | null): string => {
  if (!withdraw?.amount || !withdraw?.currency) return '-';
  return `${parseFloat(withdraw.amount).toFixed(2)} ${withdraw.currency}`;
};

const formatBonus = (bonus: any): string => {
  if (!bonus) return '-';
  if (bonus.amount && bonus.currency) {
    return `${parseFloat(bonus.amount).toFixed(2)} ${bonus.currency}`;
  }
  return 'Bonus claimed';
};

const formatTimestamp = (timestamp: number | null | undefined): string => {
  if (!timestamp) return '-';
  return new Date(timestamp * 1000).toLocaleString();
};

const formatDate = (timestamp: number | null | undefined): string => {
  if (!timestamp) return '-';
  return new Date(timestamp * 1000).toLocaleDateString();
};

const getRankBadge = (rank: string): React.ReactNode => {
  const colorClass = getVipLevelColor(rank || 'no-vip');
  const displayText = getVipLevelDisplayName(rank || 'no-vip');

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
      {displayText}
    </span>
  );
};

const getVerificationBadge = (level: number | undefined): React.ReactNode => {
  const isVerified = (level || 0) >= 2;
  const colorClass = isVerified ? 'bg-success-500/20 text-success-500' : 'bg-warning-500/20 text-warning-500';

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
      Level {level || 0}
    </span>
  );
};

const CustomerList: React.FC = () => {
  const navigate = useNavigate();

  // State management
  const [customers, setCustomers] = useState<CustomerData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Filter states
  const [activeFilters, setActiveFilters] = useState<FilterState>({});
  const [showFilters, setShowFilters] = useState(false);

  // Sorting state
  const [sortState, setSortState] = useState<SortState>({ field: null, direction: null });

  // Quick info modal state
  const [showQuickInfoModal, setShowQuickInfoModal] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | number | null>(null);

  // Handle view customer
  const handleViewCustomer = (customer: CustomerData) => {
    navigate(`/customers/details/${customer.id}`);
  };

  // Handle quick info modal
  const handleQuickInfo = (customer: CustomerData) => {
    setSelectedCustomerId(customer.id);
    setShowQuickInfoModal(true);
  };

  // Handle column header click for sorting
  const handleColumnSort = (columnKey: string, sortable: boolean = true) => {
    if (!sortable) return;

    setSortState(prevState => {
      if (prevState.field !== columnKey) {
        // New column, start with ascending
        return { field: columnKey, direction: 'asc' };
      } else if (prevState.direction === 'asc') {
        // Same column, switch to descending
        return { field: columnKey, direction: 'desc' };
      } else if (prevState.direction === 'desc') {
        // Same column, remove sorting
        return { field: null, direction: null };
      } else {
        // No current sorting, start with ascending
        return { field: columnKey, direction: 'asc' };
      }
    });

    // Reset to first page when sorting changes
    setCurrentPage(1);
  };

  // Render sort indicator for column headers
  const renderSortIndicator = (columnKey: string) => {
    if (sortState.field !== columnKey) {
      return null;
    }

    if (sortState.direction === 'asc') {
      return <ChevronUp size={16} className="inline ml-1" />;
    } else if (sortState.direction === 'desc') {
      return <ChevronDown size={16} className="inline ml-1" />;
    }

    return null;
  };

// Table column configuration
const tableColumns: TableColumn[] = [
  {
    key: 'id',
    label: 'ID',
    width: '100px',
    sortable: true,
    render: (customer) => <span className="font-mono text-sm">{customer.id}</span>
  },
  {
    key: 'username',
    label: 'Username',
    width: '120px',
    sortable: true,
    render: (customer) => <span className="font-medium">{customer.username || '-'}</span>
  },
  {
    key: 'email',
    label: 'Email',
    width: '200px',
    sortable: true,
    render: (customer) => <span className="text-sm">{customer.email || '-'}</span>
  },
  {
    key: 'registration_country',
    label: 'Country',
    width: '100px',
    sortable: true,
    render: (customer) => <span className="text-sm uppercase">{customer.registration_country || '-'}</span>
  },
  {
    key: 'rank',
    label: 'Rank',
    width: '120px',
    sortable: true,
    render: (customer) => getRankBadge(customer.rank || 'no-vip')
  },
  {
    key: 'rank_percentage',
    label: 'Rank %',
    width: '100px',
    sortable: true,
    render: (customer) => <span className="text-sm">{customer.rank_percentage || '0'}%</span>
  },
  {
    key: 'total_turnover',
    label: 'Total Turnover',
    width: '140px',
    sortable: true,
    render: (customer) => <span className="font-medium">{formatCurrency(customer.total_turnover)}</span>
  },
  {
    key: 'next_wager_limit',
    label: 'Next Wager Limit',
    width: '140px',
    sortable: true,
    render: (customer) => <span className="text-sm">{formatCurrency(customer.next_wager_limit)}</span>
  },
  {
    key: 'profile_name',
    label: 'First Name',
    width: '120px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{customer.profile?.name?.trim() || '-'}</span>
  },
  {
    key: 'profile_surname',
    label: 'Last Name',
    width: '120px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{customer.profile?.surname?.trim() || '-'}</span>
  },
  {
    key: 'verification_level',
    label: 'Verification',
    width: '120px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => getVerificationBadge(customer.profile?.verification_level)
  },
  {
    key: 'birthday',
    label: 'Birthday',
    width: '120px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{customer.profile?.birthday || '-'}</span>
  },
  {
    key: 'identity_no',
    label: 'Identity No',
    width: '140px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="font-mono text-sm">{customer.profile?.identity_no || '-'}</span>
  },
  {
    key: 'registration_ts',
    label: 'Registration Date',
    width: '160px',
    sortable: true,
    render: (customer) => <span className="text-sm">{formatDate(customer.registration_ts)}</span>
  },
  {
    key: 'last_online_at',
    label: 'Last Online',
    width: '160px',
    sortable: true,
    render: (customer) => <span className="text-sm">{formatTimestamp(customer.last_online_at)}</span>
  },
  {
    key: 'first_deposit_amount',
    label: 'First Deposit Amount',
    width: '150px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{formatDeposit(customer.first_deposit)}</span>
  },
  {
    key: 'first_deposit_date',
    label: 'First Deposit Date',
    width: '160px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{formatTimestamp(customer.first_deposit?.timestamp)}</span>
  },
  {
    key: 'last_deposit_amount',
    label: 'Last Deposit Amount',
    width: '150px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{formatDeposit(customer.last_deposit)}</span>
  },
  {
    key: 'last_deposit_date',
    label: 'Last Deposit Date',
    width: '160px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{formatTimestamp(customer.last_deposit?.timestamp)}</span>
  },
  {
    key: 'last_withdraw_amount',
    label: 'Last Withdraw Amount',
    width: '150px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm text-red-400">{formatWithdraw(customer.last_withdraw)}</span>
  },
  {
    key: 'last_withdraw_date',
    label: 'Last Withdraw Date',
    width: '160px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{formatTimestamp(customer.last_withdraw?.timestamp)}</span>
  },
  {
    key: 'last_bonus_date',
    label: 'Last Bonus Claim',
    width: '160px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm text-purple-400">{formatTimestamp(customer.last_bonus?.timestamp)}</span>
  },
  {
    key: 'summary_in',
    label: 'Total Deposits (USD)',
    width: '160px',
    sortable: true, // Enable sorting for financial data
    render: (customer) => (
      <span className="text-sm font-semibold text-green-400">
        {formatCurrency(customer.summary?.total_in_usd)}
      </span>
    )
  },
  {
    key: 'summary_out',
    label: 'Total Withdrawals (USD)',
    width: '160px',
    sortable: true, // Enable sorting for financial data
    render: (customer) => (
      <span className="text-sm font-semibold text-red-400">
        {formatCurrency(customer.summary?.total_out_usd)}
      </span>
    )
  },
  {
    key: 'net_balance',
    label: 'Net Balance (USD)',
    width: '150px',
    sortable: false, // Calculated field
    render: (customer) => {
      const totalIn = parseFloat(customer.summary?.total_in_usd || '0');
      const totalOut = parseFloat(customer.summary?.total_out_usd || '0');
      const netBalance = totalIn - totalOut;
      return (
        <span className={`text-sm font-semibold ${
          netBalance >= 0 ? 'text-green-400' : 'text-red-400'
        }`}>
          {netBalance >= 0 ? '+' : ''}{formatCurrency(netBalance.toString())}
        </span>
      );
    }
  },
  {
    key: 'summary_percentage',
    label: 'Summary %',
    width: '120px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{customer.summary?.percentage || '0'}%</span>
  },
  {
    key: 'total_rakeback',
    label: 'Total Rakeback',
    width: '140px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{formatCurrency(customer.summary?.total_rakeback_usd)}</span>
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
        customer.status?.name === 'STANDART' ? 'bg-success-500/20 text-success-500' : 'bg-error-500/20 text-error-500'
      }`}>
        {customer.status?.name || '-'}
      </span>
    )
  },
  {
    key: 'is_public',
    label: 'Public',
    width: '100px',
    sortable: true,
    render: (customer) => (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
        customer.is_public ? 'bg-success-500/20 text-success-500' : 'bg-gray-500/20 text-gray-400'
      }`}>
        {customer.is_public ? 'Yes' : 'No'}
      </span>
    )
  },
  {
    key: 'lang',
    label: 'Language',
    width: '100px',
    sortable: true,
    render: (customer) => <span className="text-sm uppercase">{customer.lang || '-'}</span>
  },
  {
    key: 'ref_code',
    label: 'Ref Code',
    width: '120px',
    sortable: true,
    render: (customer) => <span className="text-sm">{customer.ref_code || '-'}</span>
  },
  {
    key: 'affiliator',
    label: 'Affiliator',
    width: '140px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{customer.affiliator?.name || '-'}</span>
  },
  {
    key: 'phone',
    label: 'Phone',
    width: '140px',
    sortable: false, // Nested field, won't work with API
    render: (customer) => <span className="text-sm">{customer.phone?.full || '-'}</span>
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '100px',
    sortable: false, // Actions column should not be sortable
    render: (customer) => (
      <div className="flex gap-2">
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleViewCustomer(customer);
          }}
          className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500 transition-colors"
          title="View Full Details"
        >
          <Eye size={16} />
        </button>
      </div>
    )
  }
];

// Enhanced filter configuration with all available search parameters
const filterConfigs: FilterConfig[] = [
  // Basic Information
  { key: 'id', label: 'Customer ID', type: 'text' },
  { key: 'username', label: 'Username', type: 'text' },
  { key: 'email', label: 'Email', type: 'text' },
  { key: 'name', label: 'First Name', type: 'text' },
  { key: 'surname', label: 'Last Name', type: 'text' },
  { key: 'identity_no', label: 'Identity Number', type: 'text' },
  { key: 'birthday', label: 'Birthday (YYYY-MM-DD)', type: 'text' },
  { key: 'phone', label: 'Phone Number', type: 'text' },

  // Location & Registration
  { key: 'registration_country', label: 'Registration Country', type: 'text' },
  { key: 'ip', label: 'IP Address', type: 'text' },
  { key: 'language', label: 'Language', type: 'select', options: ['en', 'tr', 'de', 'fr', 'es', 'pt', 'ru'] },

  // Account Status & Verification
  { key: 'verification_level', label: 'Verification Level', type: 'select', options: ['1', '2', '3', '4', '5'] },
  { key: 'status_id', label: 'Account Status', type: 'select', options: ['1', '2', '3', '4', '5'] },
  { key: 'operator_id', label: 'Operator ID', type: 'text' },

  // VIP & Referrals
  { key: 'rank', label: 'VIP Rank', type: 'select', options: VIP_LEVELS },
  { key: 'ref_code', label: 'Referral Code', type: 'text' },
  { key: 'referrer', label: 'Referrer ID', type: 'text' },
  { key: 'affiliator', label: 'Affiliator ID', type: 'text' },

  // Financial Filters - Range Types
  { key: 'total_deposit', label: 'Total Deposits (USD)', type: 'range', minKey: 'total_deposit_greater', maxKey: 'total_deposit_lower' },
  { key: 'total_withdraw', label: 'Total Withdrawals (USD)', type: 'range', minKey: 'total_withdraw_greater', maxKey: 'total_withdraw_lower' },
  { key: 'total_turnover', label: 'Total Turnover (USD)', type: 'range', minKey: 'total_turnover_greater', maxKey: 'total_turnover_lower' },
  { key: 'net_percentage', label: 'Net Percentage', type: 'range', minKey: 'net_percentage_min', maxKey: 'net_percentage_max' },
  { key: 'total_reload', label: 'Total Reload (USD)', type: 'range', minKey: 'total_reload_min', maxKey: 'total_reload_max' },
  { key: 'total_rain', label: 'Total Rain (USD)', type: 'range', minKey: 'total_rain_min', maxKey: 'total_rain_max' },
  { key: 'total_bonus_drop', label: 'Total Bonus Drop (USD)', type: 'range', minKey: 'total_bonus_drop_min', maxKey: 'total_bonus_drop_max' },
  { key: 'rakeback', label: 'Rakeback (USD)', type: 'range', minKey: 'rakebackMin', maxKey: 'rakebackMax' },

  // Date Range Filters
  { key: 'register_range', label: 'Registration Date Range', type: 'date_range' },
  { key: 'first_deposit_range', label: 'First Deposit Date Range', type: 'date_range' },
  { key: 'last_deposit_range', label: 'Last Deposit Date Range', type: 'date_range' },
  { key: 'last_withdraw_range', label: 'Last Withdraw Date Range', type: 'date_range' },
  { key: 'last_bonus_range', label: 'Last Bonus Claim Date Range', type: 'date_range' },
  { key: 'last_login_range', label: 'Last Login Date Range', type: 'date_range' }
];



  // API functions
  const loadCustomers = async (page: number = 1) => {
    setLoading(true);
    setError(null);

    try {
      // Include sorting parameters in the request
      const searchParams = {
        ...activeFilters,
        sortBy: sortState.field,
        direction: sortState.direction
      };

      const result = await fetchCustomers(page, itemsPerPage, searchParams);

      if (result.success && result.data) {
        const customerData = result.data.data?.data || [];
        const total = result.data.data?.total || 0;

        setCustomers(customerData);
        setTotalCustomers(total);
      } else {
        setError(result.error || 'Failed to load customers');
        setCustomers([]);
      }
    } catch (err) {
      console.error('Error loading customers:', err);
      setError('An unexpected error occurred');
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    loadCustomers(currentPage);
  }, [currentPage, activeFilters, itemsPerPage, sortState]);

  // Filter management
  const handleFilterChange = (filterKey: string, value: string) => {
    const newFilters = { ...activeFilters };

    if (!value || value.trim() === '') {
      delete newFilters[filterKey];
    } else {
      newFilters[filterKey] = value.trim();
    }

    setActiveFilters(newFilters);
    setCurrentPage(1);
  };

  const handleDateRangeFilter = (filterKey: string, dateRange: DateRange) => {
    const newFilters = { ...activeFilters };
    const startKey = filterKey.replace('_range', '_start');
    const endKey = filterKey.replace('_range', '_end');

    if (dateRange.from && dateRange.to) {
      newFilters[startKey] = Math.floor(dateRange.from.getTime() / 1000);
      newFilters[endKey] = Math.floor(dateRange.to.getTime() / 1000);
    } else {
      delete newFilters[startKey];
      delete newFilters[endKey];
    }

    setActiveFilters(newFilters);
    setCurrentPage(1);
  };

  const removeFilter = (filterKey: string) => {
    const newFilters = { ...activeFilters };

    if (filterKey.includes('_start') || filterKey.includes('_end')) {
      const baseKey = filterKey.replace('_start', '').replace('_end', '');
      delete newFilters[baseKey + '_start'];
      delete newFilters[baseKey + '_end'];
    } else {
      // Check if this is part of a range filter
      const rangeConfig = filterConfigs.find(f => f.type === 'range' && (f.minKey === filterKey || f.maxKey === filterKey));
      if (rangeConfig) {
        delete newFilters[rangeConfig.minKey!];
        delete newFilters[rangeConfig.maxKey!];
      } else {
        delete newFilters[filterKey];
      }
    }

    setActiveFilters(newFilters);
    setCurrentPage(1);
  };

  const clearAllFilters = () => {
    setActiveFilters({});
    setCurrentPage(1);
  };

  const handleRefresh = () => {
    loadCustomers(currentPage);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setItemsPerPage(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };



  // Export functions
  const exportUsers = () => {
    const dataStr = JSON.stringify(customers, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `customers_raw_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const exportTable = () => {
    // Flatten the customer data structure
    const flattenedData = customers.map(customer => {
      const flattened: any = {};

      // Add direct properties (excluding nested objects and unused fields)
      flattened.id = customer.id;
      flattened.username = customer.username;
      flattened.email = customer.email;
      flattened.registration_country = customer.registration_country;
      flattened.rank = customer.rank;
      flattened.rank_percentage = customer.rank_percentage;
      flattened.total_turnover = customer.total_turnover;
      flattened.next_wager_limit = customer.next_wager_limit;
      flattened.registration_ts = customer.registration_ts;
      flattened.last_online_at = customer.last_online_at;
      flattened.ref_code = customer.ref_code;
      flattened.is_public = customer.is_public;
      flattened.lang = customer.lang;

      // Flatten profile data
      if (customer.profile) {
        flattened['profile.name'] = customer.profile.name;
        flattened['profile.surname'] = customer.profile.surname;
        flattened['profile.verification_level'] = customer.profile.verification_level;
        flattened['profile.birthday'] = customer.profile.birthday;
        flattened['profile.identity_no'] = customer.profile.identity_no;
      }

      // Flatten first deposit data
      if (customer.first_deposit) {
        flattened['first_deposit.amount'] = customer.first_deposit.amount;
        flattened['first_deposit.currency'] = customer.first_deposit.currency;
        flattened['first_deposit.timestamp'] = customer.first_deposit.timestamp;
      }

      // Flatten last deposit data
      if (customer.last_deposit) {
        flattened['last_deposit.amount'] = customer.last_deposit.amount;
        flattened['last_deposit.currency'] = customer.last_deposit.currency;
        flattened['last_deposit.timestamp'] = customer.last_deposit.timestamp;
      }

      // Flatten summary data
      if (customer.summary) {
        flattened['summary.total_in_usd'] = customer.summary.total_in_usd;
        flattened['summary.total_out_usd'] = customer.summary.total_out_usd;
        flattened['summary.percentage'] = customer.summary.percentage;
        flattened['summary.total_rakeback_usd'] = customer.summary.total_rakeback_usd;
      }

      // Flatten status data
      if (customer.status) {
        flattened['status.name'] = customer.status.name;
      }

      // Flatten affiliator data
      if (customer.affiliator) {
        flattened['affiliator.name'] = customer.affiliator.name;
      }

      // Flatten phone data
      if (customer.phone) {
        flattened['phone.full'] = customer.phone.full;
      }

      return flattened;
    });

    const dataStr = JSON.stringify(flattenedData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `customers_table_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Filter components with organized sections
  const FilterSection: React.FC = () => {
    const [tempFilters, setTempFilters] = useState<{ [key: string]: string }>({});
    const [tempDateRanges, setTempDateRanges] = useState<{ [key: string]: DateRange }>({});
    const [tempRangeFilters, setTempRangeFilters] = useState<{ [key: string]: { min: string; max: string } }>({});
    const [activeTab, setActiveTab] = useState<string>('basic');

    const handleTempFilterChange = (key: string, value: string) => {
      setTempFilters(prev => ({ ...prev, [key]: value }));
    };

    const handleTempDateRangeChange = (key: string, range: DateRange) => {
      setTempDateRanges(prev => ({ ...prev, [key]: range }));
    };

    const handleTempRangeChange = (key: string, type: 'min' | 'max', value: string) => {
      setTempRangeFilters(prev => ({
        ...prev,
        [key]: {
          ...prev[key],
          [type]: value
        }
      }));
    };

    // Organize filters into logical groups
    const filterGroups = {
      basic: {
        label: 'Basic Information',
        filters: filterConfigs.filter(f =>
          ['id', 'username', 'email', 'name', 'surname', 'identity_no', 'birthday', 'phone'].includes(f.key)
        )
      },
      location: {
        label: 'Location & Account',
        filters: filterConfigs.filter(f =>
          ['registration_country', 'ip', 'language', 'verification_level', 'status_id', 'operator_id'].includes(f.key)
        )
      },
      vip: {
        label: 'VIP & Referrals',
        filters: filterConfigs.filter(f =>
          ['rank', 'ref_code', 'referrer', 'affiliator'].includes(f.key)
        )
      },
      financial: {
        label: 'Financial Filters',
        filters: filterConfigs.filter(f =>
          f.type === 'range' || f.key.includes('deposit') || f.key.includes('withdraw') ||
          f.key.includes('turnover') || f.key.includes('percentage') || f.key.includes('reload') ||
          f.key.includes('rain') || f.key.includes('bonus') || f.key.includes('rakeback')
        )
      },
      dates: {
        label: 'Date Ranges',
        filters: filterConfigs.filter(f => f.type === 'date_range')
      }
    };

    const applyFilters = () => {
      const newFilters = { ...activeFilters };

      // Apply text and select filters
      Object.entries(tempFilters).forEach(([key, value]) => {
        if (value && value.trim()) {
          newFilters[key] = value.trim();
        } else {
          delete newFilters[key];
        }
      });

      // Apply date range filters
      Object.entries(tempDateRanges).forEach(([key, range]) => {
        const startKey = key.replace('_range', '_start');
        const endKey = key.replace('_range', '_end');

        if (range.from && range.to) {
          newFilters[startKey] = Math.floor(range.from.getTime() / 1000);
          newFilters[endKey] = Math.floor(range.to.getTime() / 1000);
        } else {
          delete newFilters[startKey];
          delete newFilters[endKey];
        }
      });

      // Apply range filters
      Object.entries(tempRangeFilters).forEach(([key, range]) => {
        const config = filterConfigs.find(f => f.key === key);
        if (config && config.minKey && config.maxKey) {
          if (range.min && range.min.trim()) {
            newFilters[config.minKey] = range.min.trim();
          } else {
            delete newFilters[config.minKey];
          }

          if (range.max && range.max.trim()) {
            newFilters[config.maxKey] = range.max.trim();
          } else {
            delete newFilters[config.maxKey];
          }
        }
      });

      setActiveFilters(newFilters);
      setCurrentPage(1);
      setShowFilters(false);
    };

    const resetFilters = () => {
      setTempFilters({});
      setTempDateRanges({});
      setTempRangeFilters({});
    };

    return (
      <div className="bg-dark-700 border border-dark-600 rounded-md p-4 mb-4">
        {/* Filter Tabs */}
        <div className="flex flex-wrap gap-2 mb-6 border-b border-dark-600 pb-4">
          {Object.entries(filterGroups).map(([key, group]) => (
            <button
              key={key}
              onClick={() => setActiveTab(key)}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === key
                  ? 'bg-primary-500 text-white'
                  : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
              }`}
            >
              {group.label}
            </button>
          ))}
        </div>

        {/* Active Filter Group */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filterGroups[activeTab as keyof typeof filterGroups]?.filters.map((config) => (
            <div key={config.key} className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                {config.label}
              </label>

              {config.type === 'text' && (
                <input
                  type="text"
                  value={tempFilters[config.key] || ''}
                  onChange={(e) => handleTempFilterChange(config.key, e.target.value)}
                  placeholder={`Enter ${config.label.toLowerCase()}...`}
                  className="w-full py-2 px-3 bg-dark-800 border border-dark-600 rounded-md text-sm focus:outline-none focus:border-primary-500"
                />
              )}

              {config.type === 'select' && (
                <select
                  value={tempFilters[config.key] || ''}
                  onChange={(e) => handleTempFilterChange(config.key, e.target.value)}
                  className="w-full py-2 px-3 bg-dark-800 border border-dark-600 rounded-md text-sm focus:outline-none focus:border-primary-500"
                >
                  <option value="">All {config.label}</option>
                  {config.options?.map(option => (
                    <option key={option} value={option}>
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </option>
                  ))}
                </select>
              )}

              {config.type === 'date_range' && (
                <DateRangePicker
                  value={tempDateRanges[config.key] || { from: null, to: null }}
                  onChange={(range) => handleTempDateRangeChange(config.key, range)}
                  className="w-full"
                />
              )}

              {config.type === 'range' && (
                <div className="flex gap-2">
                  <div className="flex-1">
                    <input
                      type="text"
                      value={tempRangeFilters[config.key]?.min || ''}
                      onChange={(e) => handleTempRangeChange(config.key, 'min', e.target.value)}
                      placeholder="From"
                      className="w-full py-2 px-3 bg-dark-800 border border-dark-600 rounded-md text-sm focus:outline-none focus:border-primary-500"
                    />
                  </div>
                  <div className="flex-1">
                    <input
                      type="text"
                      value={tempRangeFilters[config.key]?.max || ''}
                      onChange={(e) => handleTempRangeChange(config.key, 'max', e.target.value)}
                      placeholder="To"
                      className="w-full py-2 px-3 bg-dark-800 border border-dark-600 rounded-md text-sm focus:outline-none focus:border-primary-500"
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="flex gap-3 mt-4 pt-4 border-t border-dark-600">
          <button
            onClick={applyFilters}
            className="btn btn-primary"
          >
            Apply Filters
          </button>
          <button
            onClick={resetFilters}
            className="btn btn-outline"
          >
            Reset
          </button>
          <button
            onClick={() => setShowFilters(false)}
            className="btn btn-outline"
          >
            Cancel
          </button>
        </div>
      </div>
    );
  };



  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Customer List</h1>
          <p className="text-gray-400 text-sm mt-1">
            {loading ? 'Loading...' : `${totalCustomers} customers found`}
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={handleRefresh}
            className="btn btn-outline flex items-center gap-2"
            disabled={loading}
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
          <button
            onClick={exportUsers}
            className="btn btn-outline flex items-center gap-2"
            disabled={loading || customers.length === 0}
          >
            <Download size={16} />
            Export Users
          </button>
          <button
            onClick={exportTable}
            className="btn btn-outline flex items-center gap-2"
            disabled={loading || customers.length === 0}
          >
            <Download size={16} />
            Export Table
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-error-500/20 border border-error-500 rounded-md p-4">
          <p className="text-error-500">{error}</p>
        </div>
      )}



      <div className="admin-card">


        {/* Filter Toggle Button */}
        <div className="flex gap-3 mb-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn btn-outline flex items-center gap-2"
          >
            <Filter size={16} />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
          {Object.keys(activeFilters).length > 0 && (
            <button
              onClick={clearAllFilters}
              className="btn btn-outline"
            >
              Clear All Filters
            </button>
          )}
        </div>

        {/* Filter Section */}
        {showFilters && <FilterSection />}

        {/* Active Filters Display */}
        {Object.keys(activeFilters).length > 0 && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Filter size={16} className="text-gray-400" />
              <span className="text-sm font-medium text-gray-300">Active Filters:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {Object.entries(activeFilters).map(([key, value]) => {
                // Handle date range filters
                if (key.endsWith('_start') || key.endsWith('_end')) {
                  const baseKey = key.replace('_start', '').replace('_end', '');
                  const startKey = baseKey + '_start';
                  const endKey = baseKey + '_end';

                  if (key.endsWith('_start') && activeFilters[startKey] && activeFilters[endKey]) {
                    const startDate = new Date((activeFilters[startKey] as number) * 1000).toLocaleDateString();
                    const endDate = new Date((activeFilters[endKey] as number) * 1000).toLocaleDateString();
                    return (
                      <div key={baseKey} className="flex items-center gap-1 bg-primary-500/20 text-primary-400 px-2 py-1 rounded-md text-sm">
                        <span>{baseKey.replace('_', ' ')}: {startDate} - {endDate}</span>
                        <button
                          onClick={() => removeFilter(startKey)}
                          className="hover:text-primary-300"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    );
                  }
                  return null;
                }

                // Handle range filters (min/max pairs)
                const rangeConfig = filterConfigs.find(f => f.type === 'range' && (f.minKey === key || f.maxKey === key));
                if (rangeConfig) {
                  const minValue = activeFilters[rangeConfig.minKey!];
                  const maxValue = activeFilters[rangeConfig.maxKey!];

                  // Only show once for the min key, skip the max key
                  if (key === rangeConfig.minKey && (minValue || maxValue)) {
                    const displayText = minValue && maxValue
                      ? `${minValue} - ${maxValue}`
                      : minValue
                        ? `≥ ${minValue}`
                        : `≤ ${maxValue}`;

                    return (
                      <div key={rangeConfig.key} className="flex items-center gap-1 bg-primary-500/20 text-primary-400 px-2 py-1 rounded-md text-sm">
                        <span>{rangeConfig.label}: {displayText}</span>
                        <button
                          onClick={() => {
                            removeFilter(rangeConfig.minKey!);
                            removeFilter(rangeConfig.maxKey!);
                          }}
                          className="hover:text-primary-300"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    );
                  }
                  return null;
                }

                // Skip if this is part of a date range that's already displayed
                const startKey = key + '_start';
                const endKey = key + '_end';
                if (activeFilters[startKey] && activeFilters[endKey]) {
                  return null;
                }

                return (
                  <div key={key} className="flex items-center gap-1 bg-primary-500/20 text-primary-400 px-2 py-1 rounded-md text-sm">
                    <span>{key.replace('_', ' ')}: {String(value)}</span>
                    <button
                      onClick={() => removeFilter(key)}
                      className="hover:text-primary-300"
                    >
                      <X size={14} />
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Customer Table */}
        <div className="overflow-x-auto">
          <table className="data-table" style={{ minWidth: '3000px' }}>
            <thead>
              <tr>
                {tableColumns.map((column, index) => (
                  <th
                    key={column.key}
                    style={{ minWidth: column.width }}
                    className={`${index === 0 ? 'rounded-tl-lg' : index === tableColumns.length - 1 ? 'rounded-tr-lg' : ''} ${
                      column.sortable ? 'cursor-pointer hover:bg-dark-600 transition-colors' : ''
                    }`}
                    onClick={() => handleColumnSort(column.key, column.sortable)}
                  >
                    <div className="flex items-center justify-between">
                      <span>{column.label}</span>
                      {column.sortable && renderSortIndicator(column.key)}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={tableColumns.length} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <RefreshCw size={20} className="animate-spin mr-2" />
                      Loading customers...
                    </div>
                  </td>
                </tr>
              ) : customers.length > 0 ? (
                customers.map((customer) => (
                  <tr
                    key={customer.id}
                    onClick={() => handleQuickInfo(customer)}
                    className="cursor-pointer hover:bg-dark-700/50 transition-colors"
                  >
                    {tableColumns.map((column) => (
                      <td key={`${customer.id}-${column.key}`}>
                        {column.render(customer)}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={tableColumns.length} className="text-center py-8">
                    <p className="text-gray-400">No customers found.</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalCustomers > 0 && (
          <div className="flex items-center justify-between mt-6">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">Show:</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                  disabled={loading}
                  className="bg-dark-600 border border-dark-500 rounded px-2 py-1 text-sm text-gray-200 focus:outline-none focus:border-primary-500 disabled:opacity-50"
                >
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                <span className="text-sm text-gray-400">per page</span>
              </div>
              <div className="text-sm text-gray-400">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalCustomers)} of {totalCustomers} customers
              </div>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1 || loading}
                className="btn btn-outline disabled:opacity-50"
              >
                Previous
              </button>
              <span className="px-3 py-2 text-sm">
                Page {currentPage} of {Math.ceil(totalCustomers / itemsPerPage)}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(Math.ceil(totalCustomers / itemsPerPage), prev + 1))}
                disabled={currentPage >= Math.ceil(totalCustomers / itemsPerPage) || loading}
                className="btn btn-outline disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Quick User Info Modal */}
      {selectedCustomerId && (
        <QuickUserInfoModal
          isOpen={showQuickInfoModal}
          onClose={() => {
            setShowQuickInfoModal(false);
            setSelectedCustomerId(null);
          }}
          customerId={selectedCustomerId}
        />
      )}
    </div>
  );
};

export default CustomerList;
