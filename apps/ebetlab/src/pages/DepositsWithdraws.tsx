import React, { useState, useCallback } from 'react';
import { DollarSign, Download, Filter, RefreshCw, Eye, TrendingUp, TrendingDown, AlertCircle, Search, Users, Calendar, ArrowDownLeft, ArrowUpRight } from 'lucide-react';
import { fetchTransactions, fetchTransactionsSummary, type TransactionData, type TransactionSearchParams, type TransactionSummaryData } from '../utils/api';
import { useDataFetching } from '../hooks/useDataFetching';
import Pagination from '../components/ui/Pagination';
import Modal from '../components/ui/Modal';

interface TransactionDetailsModalProps {
  transaction: TransactionData;
  onClose: () => void;
}

const TransactionDetailsModal: React.FC<TransactionDetailsModalProps> = ({ transaction, onClose }) => {
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  const formatTimestamp = (timestamp: number | null) => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleString();
  };

  const getStatusBadge = (status: any) => {
    const statusColors: { [key: string]: string } = {
      'completed': 'bg-success-500/20 text-success-500',
      'pending': 'bg-warning-500/20 text-warning-500',
      'cancelled': 'bg-error-500/20 text-error-500',
      'failed': 'bg-error-500/20 text-error-500'
    };
    
    const colorClass = statusColors[status?.key] || 'bg-gray-500/20 text-gray-500';
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
        {status?.name || 'Unknown'}
      </span>
    );
  };

  return (
    <Modal isOpen={true} onClose={onClose} title="Transaction Details">
      <div className="space-y-6 max-h-96 overflow-y-auto">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
              Transaction Info
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Transaction ID</label>
              <p className="text-gray-100 font-mono">#{transaction.id}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Method</label>
              <p className="text-gray-100">{transaction.method}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
              {getStatusBadge(transaction.status)}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Amount</label>
              <div>
                <p className="text-gray-100 font-medium">
                  {formatCurrency(transaction.amount, transaction.currency)}
                </p>
                <p className="text-sm text-gray-400">${parseFloat(transaction.usd_amount).toFixed(2)} USD</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Unique ID</label>
              <p className="text-gray-100 font-mono text-sm break-all">{transaction.unique_id}</p>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
              Customer & Provider
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Customer</label>
              <div>
                <p className="text-gray-100">#{transaction.customer.id} - {transaction.customer.username}</p>
                {transaction.customer.profile && (
                  <p className="text-sm text-gray-400">
                    {transaction.customer.profile.name} {transaction.customer.profile.surname}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Payment Provider</label>
              <p className="text-gray-100">{transaction.payment_provider?.name || '-'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Completed At</label>
              <p className="text-gray-100">{formatTimestamp(transaction.completed_at)}</p>
            </div>

            {transaction.target_address && (
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">Target Address</label>
                <p className="text-gray-100 font-mono text-sm break-all">{transaction.target_address}</p>
              </div>
            )}
          </div>
        </div>

        {/* Additional Details */}
        {(transaction.approve_details || transaction.discount || transaction.last_action) && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
              Additional Details
            </h3>

            {transaction.approve_details && (
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">Approval Details</label>
                <pre className="text-gray-100 text-sm bg-dark-800 p-3 rounded border border-dark-600 overflow-x-auto">
                  {JSON.stringify(transaction.approve_details, null, 2)}
                </pre>
              </div>
            )}

            {transaction.discount && (
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">Discount</label>
                <div className="bg-dark-800 p-3 rounded border border-dark-600">
                  <p className="text-gray-100">Amount: {formatCurrency(transaction.discount.amount, transaction.currency)}</p>
                  <p className="text-gray-100">Percentage: {transaction.discount.percentage}%</p>
                </div>
              </div>
            )}

            {transaction.last_action && (
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">Last Action</label>
                <pre className="text-gray-100 text-sm bg-dark-800 p-3 rounded border border-dark-600 overflow-x-auto">
                  {JSON.stringify(transaction.last_action, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}

        {/* Raw Data */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
            Raw Transaction Data
          </h3>
          <pre className="text-gray-100 text-sm bg-dark-800 p-3 rounded border border-dark-600 overflow-x-auto max-h-64">
            {JSON.stringify(transaction, null, 2)}
          </pre>
        </div>
      </div>
    </Modal>
  );
};

const DepositsWithdraws: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<TransactionData | null>(null);
  const [searchParams, setSearchParams] = useState<Partial<TransactionSearchParams>>({});

  // Fetch transactions data
  const fetchTransactionsData = useCallback(async () => {
    const result = await fetchTransactions(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load transactions');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  // Fetch summary data
  const fetchSummaryData = useCallback(async () => {
    const result = await fetchTransactionsSummary(searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load transaction summary');
    }
  }, [searchParams]);

  const { data: transactionsData, loading, error, refetch } = useDataFetching({
    fetchFn: fetchTransactionsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  const { data: summaryData, loading: summaryLoading } = useDataFetching({
    fetchFn: fetchSummaryData,
    dependencies: [searchParams]
  });

  // Format currency with proper symbol
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number | null) => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format date only
  const formatDate = (timestamp: number | null) => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleDateString();
  };

  // Get status badge
  const getStatusBadge = (status: any) => {
    const statusColors: { [key: string]: string } = {
      'completed': 'bg-success-500/20 text-success-500',
      'pending': 'bg-warning-500/20 text-warning-500',
      'cancelled': 'bg-error-500/20 text-error-500',
      'failed': 'bg-error-500/20 text-error-500'
    };

    const colorClass = statusColors[status?.key] || 'bg-gray-500/20 text-gray-500';

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
        {status?.name || 'Unknown'}
      </span>
    );
  };

  // Get transaction type badge
  const getTypeBadge = (type: number) => {
    const typeConfig: { [key: number]: { label: string; color: string; icon: React.ReactNode } } = {
      1: { label: 'Deposit', color: 'bg-green-500/20 text-green-400', icon: <ArrowDownLeft size={14} /> },
      17: { label: 'Withdraw', color: 'bg-red-500/20 text-red-400', icon: <ArrowUpRight size={14} /> },
      2: { label: 'Withdraw', color: 'bg-red-500/20 text-red-400', icon: <ArrowUpRight size={14} /> }
    };

    const config = typeConfig[type] || { label: 'Unknown', color: 'bg-gray-500/20 text-gray-400', icon: null };

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full ${config.color}`}>
        {config.icon}
        {config.label}
      </span>
    );
  };

  // Truncate unique ID to 30 characters
  const truncateUniqueId = (uniqueId: string) => {
    if (uniqueId.length <= 30) return uniqueId;
    return `${uniqueId.slice(0, 30)}...`;
  };

  // Handle transaction click
  const handleTransactionClick = (transaction: TransactionData) => {
    setSelectedTransaction(transaction);
  };

  // Handle close modal
  const handleCloseModal = () => {
    setSelectedTransaction(null);
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: string | number | null) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value === '' ? null : value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Handle export
  const handleExport = () => {
    if (!transactionsData?.data) return;
    
    const csvContent = [
      // Headers
      ['Transaction ID', 'Type', 'Method', 'Customer', 'Amount', 'Status', 'Completed At', 'Provider'].join(','),
      // Data rows
      ...transactionsData.data.map(transaction => [
        transaction.id,
        transaction.type === 1 ? 'Deposit' : transaction.type === 17 || transaction.type === 2 ? 'Withdraw' : 'Unknown',
        transaction.method,
        `${transaction.customer.id} - ${transaction.customer.username}`,
        `${transaction.amount} ${transaction.currency}`,
        transaction.status?.name || 'Unknown',
        formatTimestamp(transaction.completed_at),
        transaction.payment_provider?.name || '-'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transactions-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-100">Deposits & Withdrawals</h1>
          <p className="text-gray-400 mt-1">Transaction management and analytics</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
              showFilters 
                ? 'bg-primary-600 text-white' 
                : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
            }`}
          >
            <Filter size={16} />
            Filters
          </button>
          <button
            onClick={refetch}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
          <button
            onClick={handleExport}
            disabled={loading || !transactionsData?.data?.length}
            className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50"
          >
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      {summaryData && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Deposits</p>
                <p className="text-2xl font-bold text-gray-100">{summaryData.deposit.total.toLocaleString()}</p>
                <p className="text-lg font-medium text-primary-500">${parseFloat(summaryData.deposit.deposit).toFixed(2)}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-primary-500" />
            </div>
          </div>
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Withdrawals</p>
                <p className="text-2xl font-bold text-gray-100">{summaryData.withdraw.total.toLocaleString()}</p>
                <p className="text-lg font-medium text-red-500">${parseFloat(summaryData.withdraw.withdraw).toFixed(2)}</p>
              </div>
              <TrendingDown className="w-8 h-8 text-red-500" />
            </div>
          </div>
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Net Amount</p>
                <p className="text-2xl font-bold text-gray-100">
                  {(summaryData.deposit.total + summaryData.withdraw.total).toLocaleString()}
                </p>
                <p className={`text-lg font-medium ${
                  (parseFloat(summaryData.deposit.deposit) - parseFloat(summaryData.withdraw.withdraw)) >= 0 
                    ? 'text-primary-500' 
                    : 'text-red-500'
                }`}>
                  ${(parseFloat(summaryData.deposit.deposit) - parseFloat(summaryData.withdraw.withdraw)).toFixed(2)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-secondary-500" />
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-semibold text-gray-100 mb-4">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
              <input
                type="text"
                placeholder="Search by username..."
                value={searchParams.username || ''}
                onChange={(e) => handleFilterChange('username', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Method</label>
              <input
                type="text"
                placeholder="Payment method..."
                value={searchParams.method || ''}
                onChange={(e) => handleFilterChange('method', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Currency</label>
              <select
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="TRY">TRY</option>
                <option value="GBP">GBP</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Status</label>
              <select
                value={searchParams.status_id || ''}
                onChange={(e) => handleFilterChange('status_id', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Statuses</option>
                <option value="1">Pending</option>
                <option value="3">Cancelled</option>
                <option value="5">Completed</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Min Amount (USD)</label>
              <input
                type="number"
                placeholder="0.00"
                value={searchParams.usd_min || ''}
                onChange={(e) => handleFilterChange('usd_min', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Max Amount (USD)</label>
              <input
                type="number"
                placeholder="0.00"
                value={searchParams.usd_max || ''}
                onChange={(e) => handleFilterChange('usd_max', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Transaction Type</label>
              <select
                value={searchParams.type || ''}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Types</option>
                <option value="1">Deposit</option>
                <option value="17">Withdrawal</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Provider</label>
              <input
                type="text"
                placeholder="Payment provider..."
                value={searchParams.provider || ''}
                onChange={(e) => handleFilterChange('provider', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-error-500/20 border border-error-500 rounded-md p-4">
          <div className="flex items-center gap-2">
            <AlertCircle size={20} className="text-error-500" />
            <p className="text-error-500 font-medium">Error loading transactions</p>
          </div>
          <p className="text-gray-400 mt-1">{error}</p>
        </div>
      )}

      {/* Table */}
      {!error && (
        <>
          <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-dark-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Transaction</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Customer</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Method</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Completed</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Provider</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {loading ? (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <RefreshCw size={20} className="animate-spin mr-2" />
                          Loading transactions...
                        </div>
                      </td>
                    </tr>
                  ) : transactionsData?.data?.length ? (
                    transactionsData.data.map((transaction) => (
                      <tr
                        key={transaction.id}
                        onClick={() => handleTransactionClick(transaction)}
                        className="cursor-pointer hover:bg-dark-600/50 transition-colors"
                      >
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">#{transaction.id}</div>
                            <div className="text-xs text-gray-400 font-mono">{truncateUniqueId(transaction.unique_id)}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          {getTypeBadge(transaction.type)}
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">#{transaction.customer.id}</div>
                            <div className="text-sm text-gray-400">{transaction.customer.username}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {transaction.method}
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">
                              {formatCurrency(transaction.amount, transaction.currency)}
                            </div>
                            <div className="text-xs text-gray-400">${parseFloat(transaction.usd_amount).toFixed(2)} USD</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          {getStatusBadge(transaction.status)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {formatDate(transaction.completed_at)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {transaction.payment_provider?.name || '-'}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <p className="text-gray-400">No transactions found.</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil((transactionsData?.total || 0) / itemsPerPage)}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            totalItems={transactionsData?.total || 0}
          />
        </>
      )}

      {/* Transaction Details Modal */}
      {selectedTransaction && (
        <TransactionDetailsModal
          transaction={selectedTransaction}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default DepositsWithdraws;
