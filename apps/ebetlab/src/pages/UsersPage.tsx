import React from 'react';
import { Search, Filter, MoreHorizontal, User, Mail, Shield, Flag } from 'lucide-react';

interface UserData {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'pending' | 'blocked';
  balance: string;
  joined: string;
  vipLevel: number;
}

const UsersPage: React.FC = () => {
  const users: UserData[] = [
    {
      id: '#28491',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'active',
      balance: '₿ 0.875',
      joined: '2023-05-12',
      vipLevel: 3,
    },
    {
      id: '#19384',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'active',
      balance: '₿ 1.245',
      joined: '2023-06-24',
      vipLevel: 4,
    },
    {
      id: '#45672',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'blocked',
      balance: '₿ 0.042',
      joined: '2023-03-18',
      vipLevel: 0,
    },
    {
      id: '#36729',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'pending',
      balance: '₿ 0.521',
      joined: '2023-07-30',
      vipLevel: 2,
    },
    {
      id: '#58294',
      name: '<PERSON> <PERSON>',
      email: '<EMAIL>',
      status: 'active',
      balance: '₿ 3.761',
      joined: '2023-01-05',
      vipLevel: 5,
    },
  ];

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: 'bg-green-500/20 text-green-500',
      pending: 'bg-yellow-500/20 text-yellow-500',
      blocked: 'bg-red-500/20 text-red-500',
    };
    
    return (
      <span className={`px-2 py-1 rounded-md text-xs font-medium ${statusClasses[status as keyof typeof statusClasses]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getVipBadge = (level: number) => {
    if (level === 0) return <span className="text-gray-400">-</span>;
    
    const stars = Array(level).fill('★').join('');
    return <span className="text-primary-500 font-medium">{stars}</span>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Users Management</h1>
        <button className="btn btn-primary">
          Add New User
        </button>
      </div>
      
      <div className="admin-card">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search users by name, email or ID..."
              className="py-2 pl-10 pr-4 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500 text-sm w-full"
            />
          </div>
          
          <div className="flex gap-3">
            <button className="btn btn-outline flex items-center gap-2">
              <Filter size={16} />
              <span>Filters</span>
            </button>
            
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Status</option>
              <option>Active</option>
              <option>Pending</option>
              <option>Blocked</option>
            </select>
            
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All VIP Levels</option>
              <option>VIP 1</option>
              <option>VIP 2</option>
              <option>VIP 3+</option>
            </select>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="data-table">
            <thead>
              <tr>
                <th className="rounded-tl-lg">User ID</th>
                <th>Name</th>
                <th>Email</th>
                <th>Status</th>
                <th>Balance</th>
                <th>Join Date</th>
                <th>VIP Level</th>
                <th className="rounded-tr-lg">Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id}>
                  <td>{user.id}</td>
                  <td className="font-medium">{user.name}</td>
                  <td>{user.email}</td>
                  <td>{getStatusBadge(user.status)}</td>
                  <td>{user.balance}</td>
                  <td>{user.joined}</td>
                  <td>{getVipBadge(user.vipLevel)}</td>
                  <td>
                    <div className="flex gap-2">
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500">
                        <User size={16} />
                      </button>
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500">
                        <Mail size={16} />
                      </button>
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-warning-500">
                        <Shield size={16} />
                      </button>
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-error-500">
                        <Flag size={16} />
                      </button>
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500">
                        <MoreHorizontal size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="flex justify-between items-center mt-6">
          <div className="text-sm text-gray-400">
            Showing 1 to 5 of 432 entries
          </div>
          
          <div className="flex gap-2">
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Previous
            </button>
            <button className="px-3 py-1.5 rounded-md border border-primary-500 bg-primary-500/10 text-primary-500">
              1
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              2
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              3
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UsersPage;