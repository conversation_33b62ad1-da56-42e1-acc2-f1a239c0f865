import React, { useState, useEffect } from 'react';
import {
  Database,
  Plus,
  Edit,
  Trash2,
  RefreshCw,
  ChevronDown,
  Save,
  X
} from 'lucide-react';
import UnifiedTable, { TableColumn } from '../components/ui/UnifiedTable';
import Modal from '../components/ui/Modal';
import {
  fetchExtendedUsers,
  createExtendedUser,
  updateExtendedUser,
  deleteExtendedUser,
  ExtendedUser,
  CreateExtendedUserRequest,
  UpdateExtendedUserRequest
} from '../utils/api/extended-users';
import {
  fetchMissionRules,
  createMissionRule,
  updateMissionRule,
  deleteMissionRule,
  MissionRule,
  CreateMissionRuleRequest,
  UpdateMissionRuleRequest,
  RULE_TYPE_OPTIONS,
  COMPARE_OPERATOR_OPTIONS,
  RuleType,
  CompareOperator
} from '../utils/api/mission-rules';
import {
  fetchMissions,
  createMission,
  updateMission,
  deleteMission,
  Mission,
  CreateMissionRequest,
  UpdateMissionRequest,
  MISSION_TYPE_OPTIONS,
  MissionType,
  getMissionStatusDisplay
} from '../utils/api/missions';
import {
  fetchMissionParticipations,
  createMissionParticipation,
  updateMissionParticipation,
  deleteMissionParticipation,
  MissionParticipation,
  CreateMissionParticipationRequest,
  UpdateMissionParticipationRequest,
  PARTICIPATION_STATUS_OPTIONS,
  getParticipationStatusDisplay
} from '../utils/api/mission-participations';
import {
  fetchMissionRuleAssignments,
  createMissionRuleAssignment,
  deleteMissionRuleAssignment,
  MissionRuleAssignment,
  CreateMissionRuleAssignmentRequest,
  getAssignmentDisplay,
  getAssignmentStatus
} from '../utils/api/mission-rule-assignments';
import {
  fetchMissionObjectives,
  createMissionObjective,
  updateMissionObjective,
  deleteMissionObjective,
  MissionObjective,
  CreateMissionObjectiveRequest,
  UpdateMissionObjectiveRequest,
  OBJECTIVE_TYPE_OPTIONS,
  OBJECTIVE_OPERATOR_OPTIONS,
  OBJECTIVE_SUBTYPE_OPTIONS,
  getSubtypeOptions,
  ObjectiveType,
  ObjectiveOperator,
  getObjectiveDisplay,
  getObjectiveStatusDisplay
} from '../utils/api/mission-objectives';
import {
  fetchMissionObjectiveAssignments,
  createMissionObjectiveAssignment,
  deleteMissionObjectiveAssignment,
  MissionObjectiveAssignment,
  CreateMissionObjectiveAssignmentRequest,
  getObjectiveAssignmentDisplay,
  getObjectiveAssignmentStatus
} from '../utils/api/mission-objective-assignments';
import {
  fetchFinalMissionClaims,
  createFinalMissionClaim,
  updateFinalMissionClaim,
  deleteFinalMissionClaim,
  fetchFinalMissionClaimStatistics,
  FinalMissionClaim,
  CreateFinalMissionClaimRequest,
  UpdateFinalMissionClaimRequest,
  FinalMissionClaimStatistics,
  CLAIM_TYPE_OPTIONS,
  ClaimType,
  getClaimTypeDisplay
} from '../utils/api/final-mission-claims';

const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'tr', name: 'Turkish' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' }
];

// Entity configuration
interface EntityConfig {
  name: string;
  displayName: string;
  apiPath: string;
}

const ENTITIES: EntityConfig[] = [
  {
    name: 'extended-users',
    displayName: 'Extended Users',
    apiPath: 'extended-users'
  },
  {
    name: 'mission-rules',
    displayName: 'Mission Rules',
    apiPath: 'mission-rules'
  },
  {
    name: 'missions',
    displayName: 'Missions',
    apiPath: 'missions'
  },
  {
    name: 'mission-participations',
    displayName: 'Mission Participations',
    apiPath: 'mission-participations'
  },
  {
    name: 'mission-rule-assignments',
    displayName: 'Mission Rule Assignments',
    apiPath: 'mission-rule-assignments'
  },
  {
    name: 'mission-objectives',
    displayName: 'Mission Objectives',
    apiPath: 'mission-objectives'
  },
  {
    name: 'mission-objective-assignments',
    displayName: 'Mission Objective Assignments',
    apiPath: 'mission-objective-assignments'
  },
  {
    name: 'final-mission-claims',
    displayName: 'Final Mission Claims',
    apiPath: 'final-mission-claims'
  }
  // Future entities can be added here
];

const CrudManager: React.FC = () => {
  const [selectedEntity, setSelectedEntity] = useState<EntityConfig>(ENTITIES[0]);
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrev, setHasPrev] = useState(false);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('ASC');

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  // Form states for Extended Users
  const [createExtendedUserForm, setCreateExtendedUserForm] = useState<CreateExtendedUserRequest>({
    externalId: 0,
    points: 0
  });
  const [editExtendedUserForm, setEditExtendedUserForm] = useState<UpdateExtendedUserRequest>({
    externalId: 0,
    points: 0
  });

  // Form states for Mission Rules
  const [createMissionRuleForm, setCreateMissionRuleForm] = useState<CreateMissionRuleRequest>({
    ruleType: 'totalDeposit',
    compare: 'ge',
    compareValue: '',
    minDate: null,
    maxDate: null
  });
  const [editMissionRuleForm, setEditMissionRuleForm] = useState<UpdateMissionRuleRequest>({
    ruleType: 'totalDeposit',
    compare: 'ge',
    compareValue: '',
    minDate: null,
    maxDate: null
  });

  // Form states for Missions
  const [createMissionForm, setCreateMissionForm] = useState<CreateMissionRequest>({
    name: '',
    missionType: 'daily',
    reward: 0,
    description: '',
    startDate: Math.floor(Date.now() / 1000),
    endDate: Math.floor(Date.now() / 1000) + 86400, // Default to 1 day from now
    name_i18n: { en: '' },
    description_i18n: { en: '' },
    isActive: true
  });
  const [editMissionForm, setEditMissionForm] = useState<UpdateMissionRequest>({
    name: '',
    missionType: 'daily',
    reward: 0,
    description: '',
    startDate: Math.floor(Date.now() / 1000),
    endDate: Math.floor(Date.now() / 1000) + 86400,
    name_i18n: { en: '' },
    description_i18n: { en: '' },
    isActive: true
  });

  // Form states for Mission Participations
  const [createMissionParticipationForm, setCreateMissionParticipationForm] = useState<CreateMissionParticipationRequest>({
    userId: 0,
    missionId: 0,
    isCompleted: false
  });
  const [editMissionParticipationForm, setEditMissionParticipationForm] = useState<UpdateMissionParticipationRequest>({
    isCompleted: false
  });

  // Form states for Mission Rule Assignments
  const [createMissionRuleAssignmentForm, setCreateMissionRuleAssignmentForm] = useState<CreateMissionRuleAssignmentRequest>({
    missionId: 0,
    missionRuleId: 0
  });

  // Form states for Mission Objectives
  const [createMissionObjectiveForm, setCreateMissionObjectiveForm] = useState<CreateMissionObjectiveRequest>({
    missionId: 0,
    objectiveType: 'slot',
    subtype: 'spins',
    operator: 'ge',
    targetValue: '',
    description: '',
    timeframeStart: null,
    timeframeEnd: null,
    metadata: null
  });
  const [editMissionObjectiveForm, setEditMissionObjectiveForm] = useState<UpdateMissionObjectiveRequest>({
    missionId: 0,
    objectiveType: 'slot',
    subtype: 'spins',
    operator: 'ge',
    targetValue: '',
    description: '',
    timeframeStart: null,
    timeframeEnd: null,
    metadata: null
  });

  // Form states for Mission Objective Assignments
  const [createMissionObjectiveAssignmentForm, setCreateMissionObjectiveAssignmentForm] = useState<CreateMissionObjectiveAssignmentRequest>({
    userId: 0,
    missionObjectiveId: 0,
    progress: 0,
    lastCheckedRecordTimestamp: null
  });

  // Form states for Final Mission Claims
  const [createFinalMissionClaimForm, setCreateFinalMissionClaimForm] = useState<CreateFinalMissionClaimRequest>({
    userId: 0,
    claimType: 'daily',
    grantedReward: 0
  });
  const [editFinalMissionClaimForm, setEditFinalMissionClaimForm] = useState<UpdateFinalMissionClaimRequest>({
    userId: 0,
    claimType: 'daily',
    grantedReward: 0
  });

  // Statistics state for Final Mission Claims
  const [statistics, setStatistics] = useState<any>(null);
  const [statisticsLoading, setStatisticsLoading] = useState(false);

  // Load statistics for Final Mission Claims
  const loadStatistics = async () => {
    if (selectedEntity.name !== 'final-mission-claims') {
      setStatistics(null);
      return;
    }

    setStatisticsLoading(true);
    try {
      const result = await fetchFinalMissionClaimStatistics();
      if (result.success) {
        setStatistics(result.data);
      } else {
        console.error('Failed to load statistics:', result.error);
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    } finally {
      setStatisticsLoading(false);
    }
  };

  // Helper functions for i18n fields
  const updateCreateMissionNameI18n = (lang: string, value: string) => {
    setCreateMissionForm(prev => ({
      ...prev,
      name_i18n: { ...prev.name_i18n, [lang]: value }
    }));
  };

  const updateCreateMissionDescriptionI18n = (lang: string, value: string) => {
    setCreateMissionForm(prev => ({
      ...prev,
      description_i18n: { ...prev.description_i18n, [lang]: value }
    }));
  };

  const updateEditMissionNameI18n = (lang: string, value: string) => {
    setEditMissionForm(prev => ({
      ...prev,
      name_i18n: { ...prev.name_i18n, [lang]: value }
    }));
  };

  const updateEditMissionDescriptionI18n = (lang: string, value: string) => {
    setEditMissionForm(prev => ({
      ...prev,
      description_i18n: { ...prev.description_i18n, [lang]: value }
    }));
  };

  // Load data on component mount and entity change
  useEffect(() => {
    setCurrentPage(1);
    setFilters({});
    setSortBy('');
    setSortOrder('ASC');
    loadData();
    loadStatistics();
  }, [selectedEntity]);

  // Load data when pagination or filters change
  useEffect(() => {
    loadData();
  }, [currentPage, itemsPerPage, filters, sortBy, sortOrder]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = {
        page: currentPage,
        limit: itemsPerPage,
        sortBy: sortBy || undefined,
        sortOrder: sortOrder,
        ...filters
      };

      let result;
      if (selectedEntity.name === 'extended-users') {
        result = await fetchExtendedUsers(queryParams);
      } else if (selectedEntity.name === 'mission-rules') {
        result = await fetchMissionRules(queryParams);
      } else if (selectedEntity.name === 'missions') {
        result = await fetchMissions(queryParams);
      } else if (selectedEntity.name === 'mission-participations') {
        result = await fetchMissionParticipations(queryParams);
      } else if (selectedEntity.name === 'mission-rule-assignments') {
        result = await fetchMissionRuleAssignments(queryParams);
      } else if (selectedEntity.name === 'mission-objectives') {
        result = await fetchMissionObjectives(queryParams);
      } else if (selectedEntity.name === 'mission-objective-assignments') {
        result = await fetchMissionObjectiveAssignments(queryParams);
      } else if (selectedEntity.name === 'final-mission-claims') {
        result = await fetchFinalMissionClaims(queryParams);
      }

      if (result?.success && result.data) {
        setData(result.data);
        if (result.meta) {
          setTotalItems(result.meta.total);
          setTotalPages(result.meta.totalPages);
          setHasNext(result.meta.hasNext);
          setHasPrev(result.meta.hasPrev);
        }
      } else {
        setError(result?.error || 'Failed to load data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      let result;
      if (selectedEntity.name === 'extended-users') {
        result = await createExtendedUser(createExtendedUserForm);
        if (result.success) {
          setCreateExtendedUserForm({ externalId: 0, points: 0 });
        }
      } else if (selectedEntity.name === 'mission-rules') {
        result = await createMissionRule(createMissionRuleForm); // Send as-is (string)
        if (result.success) {
          setCreateMissionRuleForm({
            ruleType: 'totalDeposit',
            compare: 'ge',
            compareValue: '',
            minDate: null,
            maxDate: null
          });
        }
      } else if (selectedEntity.name === 'missions') {
        result = await createMission(createMissionForm);
        if (result.success) {
          setCreateMissionForm({
            name: '',
            missionType: 'daily',
            reward: 0,
            description: '',
            startDate: Math.floor(Date.now() / 1000),
            endDate: Math.floor(Date.now() / 1000) + 86400,
            name_i18n: { en: '' },
            description_i18n: { en: '' },
            isActive: true
          });
        }
      } else if (selectedEntity.name === 'mission-participations') {
        result = await createMissionParticipation(createMissionParticipationForm);
        if (result.success) {
          setCreateMissionParticipationForm({
            userId: 0,
            missionId: 0,
            isCompleted: false
          });
        }
      } else if (selectedEntity.name === 'mission-rule-assignments') {
        result = await createMissionRuleAssignment(createMissionRuleAssignmentForm);
        if (result.success) {
          setCreateMissionRuleAssignmentForm({
            missionId: 0,
            missionRuleId: 0
          });
        }
      } else if (selectedEntity.name === 'mission-objectives') {
        result = await createMissionObjective(createMissionObjectiveForm);
        if (result.success) {
          setCreateMissionObjectiveForm({
            missionId: 0,
            objectiveType: 'slot',
            subtype: 'spins',
            operator: 'ge',
            targetValue: '',
            description: '',
            timeframeStart: null,
            timeframeEnd: null,
            metadata: null
          });
        }
      } else if (selectedEntity.name === 'mission-objective-assignments') {
        result = await createMissionObjectiveAssignment(createMissionObjectiveAssignmentForm);
        if (result.success) {
          setCreateMissionObjectiveAssignmentForm({
            userId: 0,
            missionObjectiveId: 0,
            progress: 0,
            lastCheckedRecordTimestamp: null
          });
        }
      } else if (selectedEntity.name === 'final-mission-claims') {
        result = await createFinalMissionClaim(createFinalMissionClaimForm);
        if (result.success) {
          setCreateFinalMissionClaimForm({
            userId: 0,
            claimType: 'daily',
            grantedReward: 0
          });
        }
      }

      if (result?.success) {
        setShowCreateModal(false);
        loadData(); // Refresh data
      } else {
        alert(result?.error || 'Failed to create item');
      }
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to create item');
    }
  };

  const handleEdit = async () => {
    if (!selectedItem) return;

    try {
      let result;
      if (selectedEntity.name === 'extended-users') {
        result = await updateExtendedUser(selectedItem.id, editExtendedUserForm);
        if (result.success) {
          setEditExtendedUserForm({ externalId: 0, points: 0 });
        }
      } else if (selectedEntity.name === 'mission-rules') {
        result = await updateMissionRule(selectedItem.id, editMissionRuleForm); // Send as-is (string)
        if (result.success) {
          setEditMissionRuleForm({
            ruleType: 'totalDeposit',
            compare: 'ge',
            compareValue: '',
            minDate: null,
            maxDate: null
          });
        }
      } else if (selectedEntity.name === 'missions') {
        result = await updateMission(selectedItem.id, editMissionForm);
        if (result.success) {
          setEditMissionForm({
            name: '',
            missionType: 'daily',
            reward: 0,
            description: '',
            startDate: Math.floor(Date.now() / 1000),
            endDate: Math.floor(Date.now() / 1000) + 86400,
            name_i18n: { en: '' },
            description_i18n: { en: '' },
            isActive: true
          });
        }
      } else if (selectedEntity.name === 'mission-participations') {
        result = await updateMissionParticipation(selectedItem.id, editMissionParticipationForm);
        if (result.success) {
          setEditMissionParticipationForm({
            isCompleted: false
          });
        }
      } else if (selectedEntity.name === 'mission-objectives') {
        result = await updateMissionObjective(selectedItem.id, editMissionObjectiveForm);
        if (result.success) {
          setEditMissionObjectiveForm({
            missionId: 0,
            objectiveType: 'slot',
            subtype: 'spins',
            operator: 'ge',
            targetValue: '',
            description: '',
            timeframeStart: null,
            timeframeEnd: null,
            metadata: null
          });
        }
      } else if (selectedEntity.name === 'final-mission-claims') {
        result = await updateFinalMissionClaim(selectedItem.id, editFinalMissionClaimForm);
        if (result.success) {
          setEditFinalMissionClaimForm({
            userId: 0,
            claimType: 'daily',
            grantedReward: 0
          });
        }
      }

      if (result?.success) {
        setShowEditModal(false);
        setSelectedItem(null);
        loadData(); // Refresh data
      } else {
        alert(result?.error || 'Failed to update item');
      }
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to update item');
    }
  };

  const handleDelete = async () => {
    if (!selectedItem) return;

    try {
      let result;
      if (selectedEntity.name === 'extended-users') {
        result = await deleteExtendedUser(selectedItem.id);
      } else if (selectedEntity.name === 'mission-rules') {
        result = await deleteMissionRule(selectedItem.id);
      } else if (selectedEntity.name === 'missions') {
        result = await deleteMission(selectedItem.id);
      } else if (selectedEntity.name === 'mission-participations') {
        result = await deleteMissionParticipation(selectedItem.id);
      } else if (selectedEntity.name === 'mission-rule-assignments') {
        result = await deleteMissionRuleAssignment(selectedItem.id);
      } else if (selectedEntity.name === 'mission-objectives') {
        result = await deleteMissionObjective(selectedItem.id);
      } else if (selectedEntity.name === 'mission-objective-assignments') {
        result = await deleteMissionObjectiveAssignment(selectedItem.id);
      } else if (selectedEntity.name === 'final-mission-claims') {
        result = await deleteFinalMissionClaim(selectedItem.id);
      }

      if (result?.success) {
        setShowDeleteModal(false);
        setSelectedItem(null);
        loadData(); // Refresh data
      } else {
        alert(result?.error || 'Failed to delete item');
      }
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to delete item');
    }
  };

  const openEditModal = (item: any) => {
    // Mission rule assignments and objective assignments don't support editing, only create and delete
    if (selectedEntity.name === 'mission-rule-assignments' || selectedEntity.name === 'mission-objective-assignments') {
      return;
    }

    setSelectedItem(item);
    if (selectedEntity.name === 'extended-users') {
      setEditExtendedUserForm({
        externalId: item.externalId,
        points: item.points
      });
    } else if (selectedEntity.name === 'mission-rules') {
      // Always use compareValue as string
      setEditMissionRuleForm({
        ruleType: item.ruleType,
        compare: item.compare,
        compareValue: String(item.compareValue || ''),
        minDate: item.minDate,
        maxDate: item.maxDate
      });
    } else if (selectedEntity.name === 'missions') {
      setEditMissionForm({
        name: item.name,
        missionType: item.missionType,
        reward: item.reward,
        description: item.description,
        startDate: item.startDate,
        endDate: item.endDate,
        name_i18n: item.name_i18n || { en: item.name },
        description_i18n: item.description_i18n || { en: item.description },
        isActive: item.isActive !== undefined ? item.isActive : true
      });
    } else if (selectedEntity.name === 'mission-participations') {
      setEditMissionParticipationForm({
        isCompleted: item.isCompleted
      });
    } else if (selectedEntity.name === 'mission-objectives') {
      setEditMissionObjectiveForm({
        missionId: item.missionId,
        objectiveType: item.objectiveType,
        subtype: item.subtype || '',
        operator: item.operator,
        targetValue: item.targetValue,
        description: item.description,
        timeframeStart: item.timeframeStart,
        timeframeEnd: item.timeframeEnd,
        metadata: item.metadata
      });
    } else if (selectedEntity.name === 'final-mission-claims') {
      setEditFinalMissionClaimForm({
        userId: item.userId,
        claimType: item.claimType,
        grantedReward: item.grantedReward
      });
    }
    setShowEditModal(true);
  };

  const openDeleteModal = (item: any) => {
    setSelectedItem(item);
    setShowDeleteModal(true);
  };

  // Dynamic table columns based on selected entity
  const getColumns = (): TableColumn<any>[] => {
    const baseColumns = [
      {
        key: 'id',
        label: 'ID',
        width: '80px',
        render: (item: any) => (
          <span className="font-mono text-sm font-medium text-gray-100">
            #{item.id}
          </span>
        )
      }
    ];

    if (selectedEntity.name === 'extended-users') {
      return [
        ...baseColumns,
        {
          key: 'externalId',
          label: 'External ID',
          width: '120px',
          render: (item: ExtendedUser) => (
            <span className="font-medium text-blue-400">
              {item?.externalId || 'N/A'}
            </span>
          )
        },
        {
          key: 'points',
          label: 'Points',
          width: '100px',
          render: (item: ExtendedUser) => (
            <span className="font-medium text-green-400">
              {item?.points?.toLocaleString() || '0'}
            </span>
          )
        },
        {
          key: 'createdAt',
          label: 'Created At',
          width: '180px',
          render: (item: ExtendedUser) => (
            <span className="text-gray-300">
              {item?.createdAt ? new Date(item.createdAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'updatedAt',
          label: 'Updated At',
          width: '180px',
          render: (item: ExtendedUser) => (
            <span className="text-gray-300">
              {item?.updatedAt ? new Date(item.updatedAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'actions',
          label: 'Actions',
          width: '120px',
          render: (item: ExtendedUser) => (
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openEditModal(item);
                }}
                className="p-1 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded"
                title="Edit"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteModal(item);
                }}
                className="p-1 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )
        }
      ];
    } else if (selectedEntity.name === 'mission-rules') {
      return [
        ...baseColumns,
        {
          key: 'ruleType',
          label: 'Rule Type',
          width: '140px',
          render: (item: MissionRule) => (
            <span className="font-medium text-blue-400">
              {RULE_TYPE_OPTIONS.find(opt => opt.value === item?.ruleType)?.label || item?.ruleType || 'Unknown'}
            </span>
          )
        },
        {
          key: 'compare',
          label: 'Operator',
          width: '100px',
          render: (item: MissionRule) => (
            <span className="font-medium text-purple-400">
              {COMPARE_OPERATOR_OPTIONS.find(opt => opt.value === item?.compare)?.label || item?.compare || 'Unknown'}
            </span>
          )
        },
        {
          key: 'compareValue',
          label: 'Value',
          width: '120px',
          render: (item: MissionRule) => (
            <span className="font-medium text-green-400">
              {item?.compareValue || 'N/A'}
            </span>
          )
        },
        {
          key: 'dateRange',
          label: 'Date Range',
          width: '200px',
          render: (item: MissionRule) => (
            <div className="text-gray-300 text-sm">
              {item?.minDate || item?.maxDate ? (
                <>
                  {item.minDate ? new Date(item.minDate * 1000).toLocaleDateString() : 'No start'}
                  {' - '}
                  {item.maxDate ? new Date(item.maxDate * 1000).toLocaleDateString() : 'No end'}
                </>
              ) : (
                <span className="text-gray-500">No date range</span>
              )}
            </div>
          )
        },
        {
          key: 'createdAt',
          label: 'Created At',
          width: '180px',
          render: (item: MissionRule) => (
            <span className="text-gray-300">
              {item?.createdAt ? new Date(item.createdAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'actions',
          label: 'Actions',
          width: '120px',
          render: (item: MissionRule) => (
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openEditModal(item);
                }}
                className="p-1 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded"
                title="Edit"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteModal(item);
                }}
                className="p-1 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )
        }
      ];
    } else if (selectedEntity.name === 'missions') {
      return [
        ...baseColumns,
        {
          key: 'name',
          label: 'Mission Name',
          width: '200px',
          render: (item: Mission) => (
            <span className="font-medium text-blue-400">
              {item?.name || 'Unnamed Mission'}
            </span>
          )
        },
        {
          key: 'missionType',
          label: 'Type',
          width: '120px',
          render: (item: Mission) => (
            <span className="font-medium text-purple-400">
              {MISSION_TYPE_OPTIONS.find(opt => opt.value === item?.missionType)?.label || item?.missionType || 'Unknown'}
            </span>
          )
        },
        {
          key: 'reward',
          label: 'Reward',
          width: '100px',
          render: (item: Mission) => (
            <span className="font-medium text-green-400">
              {item?.reward?.toLocaleString() || '0'}
            </span>
          )
        },
        {
          key: 'isActive',
          label: 'Active',
          width: '80px',
          render: (item: Mission) => (
            <div className="flex items-center justify-center">
              {item?.isActive ? (
                <span className="text-green-400 font-medium">✓</span>
              ) : (
                <span className="text-red-400 font-medium">✗</span>
              )}
            </div>
          )
        },
        {
          key: 'duration',
          label: 'Duration',
          width: '200px',
          render: (item: Mission) => {
            // For daily/weekly/monthly missions, show "-"
            if (item?.missionType === 'daily' || item?.missionType === 'weekly' || item?.missionType === 'monthly') {
              return (
                <div className="text-gray-300 text-sm">
                  <div>-</div>
                  <div className="text-gray-500">to -</div>
                </div>
              );
            }

            // For custom missions, show actual dates
            return (
              <div className="text-gray-300 text-sm">
                <div>{item?.startDate ? new Date(item.startDate * 1000).toLocaleDateString() : 'No start date'}</div>
                <div className="text-gray-500">to {item?.endDate ? new Date(item.endDate * 1000).toLocaleDateString() : 'No end date'}</div>
              </div>
            );
          }
        },
        {
          key: 'description',
          label: 'Description',
          width: '250px',
          render: (item: Mission) => (
            <span className="text-gray-300 text-sm">
              {item?.description ? (item.description.length > 50 ? `${item.description.substring(0, 50)}...` : item.description) : 'No description'}
            </span>
          )
        },
        {
          key: 'createdAt',
          label: 'Created At',
          width: '180px',
          render: (item: Mission) => (
            <span className="text-gray-300">
              {item?.createdAt ? new Date(item.createdAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'actions',
          label: 'Actions',
          width: '120px',
          render: (item: Mission) => (
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openEditModal(item);
                }}
                className="p-1 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded"
                title="Edit"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteModal(item);
                }}
                className="p-1 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )
        }
      ];
    } else if (selectedEntity.name === 'mission-participations') {
      return [
        ...baseColumns,
        {
          key: 'userId',
          label: 'User ID',
          width: '100px',
          render: (item: MissionParticipation) => (
            <span className="font-medium text-blue-400">
              {item?.userId || 'N/A'}
            </span>
          )
        },
        {
          key: 'missionId',
          label: 'Mission ID',
          width: '100px',
          render: (item: MissionParticipation) => (
            <span className="font-medium text-purple-400">
              {item?.missionId || 'N/A'}
            </span>
          )
        },
        {
          key: 'missionName',
          label: 'Mission Name',
          width: '200px',
          render: (item: MissionParticipation) => (
            <span className="font-medium text-green-400">
              {item?.mission?.name || 'Unknown Mission'}
            </span>
          )
        },
        {
          key: 'missionType',
          label: 'Mission Type',
          width: '120px',
          render: (item: MissionParticipation) => (
            <span className="text-gray-300">
              {item?.mission?.missionType || 'Unknown'}
            </span>
          )
        },
        {
          key: 'isCompleted',
          label: 'Status',
          width: '100px',
          render: (item: MissionParticipation) => {
            const statusDisplay = getParticipationStatusDisplay(item?.isCompleted || false);
            return (
              <span className={`font-medium ${statusDisplay.color}`}>
                {statusDisplay.label}
              </span>
            );
          }
        },
        {
          key: 'reward',
          label: 'Reward',
          width: '100px',
          render: (item: MissionParticipation) => (
            <span className="font-medium text-yellow-400">
              {item?.mission?.reward?.toLocaleString() || '0'}
            </span>
          )
        },
        {
          key: 'createdAt',
          label: 'Joined At',
          width: '180px',
          render: (item: MissionParticipation) => (
            <span className="text-gray-300">
              {item?.createdAt ? new Date(item.createdAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'updatedAt',
          label: 'Last Updated',
          width: '180px',
          render: (item: MissionParticipation) => (
            <span className="text-gray-300">
              {item?.updatedAt ? new Date(item.updatedAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'actions',
          label: 'Actions',
          width: '120px',
          render: (item: MissionParticipation) => (
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openEditModal(item);
                }}
                className="p-1 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded"
                title="Edit"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteModal(item);
                }}
                className="p-1 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )
        }
      ];
    } else if (selectedEntity.name === 'mission-rule-assignments') {
      return [
        ...baseColumns,
        {
          key: 'missionId',
          label: 'Mission ID',
          width: '100px',
          render: (item: MissionRuleAssignment) => (
            <span className="font-medium text-blue-400">
              {item?.missionId || 'N/A'}
            </span>
          )
        },
        {
          key: 'missionName',
          label: 'Mission Name',
          width: '200px',
          render: (item: MissionRuleAssignment) => (
            <span className="font-medium text-green-400">
              {item?.mission?.name || `Mission #${item?.missionId}` || 'Unknown Mission'}
            </span>
          )
        },
        {
          key: 'missionRuleId',
          label: 'Rule ID',
          width: '100px',
          render: (item: MissionRuleAssignment) => (
            <span className="font-medium text-purple-400">
              {item?.missionRuleId || 'N/A'}
            </span>
          )
        },
        {
          key: 'ruleType',
          label: 'Rule Type',
          width: '140px',
          render: (item: MissionRuleAssignment) => (
            <span className="font-medium text-orange-400">
              {item?.missionRule?.ruleType || 'Unknown Rule'}
            </span>
          )
        },
        {
          key: 'ruleCondition',
          label: 'Rule Condition',
          width: '200px',
          render: (item: MissionRuleAssignment) => (
            <span className="text-gray-300 text-sm">
              {item?.missionRule ? (
                `${item.missionRule.compare} ${item.missionRule.parsedCompareValue}`
              ) : (
                'Unknown Condition'
              )}
            </span>
          )
        },
        {
          key: 'status',
          label: 'Status',
          width: '100px',
          render: (item: MissionRuleAssignment) => {
            const statusDisplay = getAssignmentStatus(item);
            return (
              <span className={`font-medium ${statusDisplay.color}`}>
                {statusDisplay.label}
              </span>
            );
          }
        },
        {
          key: 'createdAt',
          label: 'Created At',
          width: '180px',
          render: (item: MissionRuleAssignment) => (
            <span className="text-gray-300">
              {item?.createdAt ? new Date(item.createdAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'actions',
          label: 'Actions',
          width: '80px',
          render: (item: MissionRuleAssignment) => (
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteModal(item);
                }}
                className="p-1 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded"
                title="Delete Assignment"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )
        }
      ];
    } else if (selectedEntity.name === 'mission-objectives') {
      return [
        ...baseColumns,
        {
          key: 'missionId',
          label: 'Mission ID',
          width: '100px',
          render: (item: MissionObjective) => (
            <span className="font-medium text-blue-400">
              #{item?.missionId || 'N/A'}
            </span>
          )
        },
        {
          key: 'objectiveType',
          label: 'Type',
          width: '120px',
          render: (item: MissionObjective) => (
            <span className="font-medium text-purple-400">
              {OBJECTIVE_TYPE_OPTIONS.find(opt => opt.value === item?.objectiveType)?.label || item?.objectiveType || 'Unknown'}
            </span>
          )
        },
        {
          key: 'operator',
          label: 'Operator',
          width: '100px',
          render: (item: MissionObjective) => (
            <span className="font-medium text-orange-400">
              {OBJECTIVE_OPERATOR_OPTIONS.find(opt => opt.value === item?.operator)?.label || item?.operator || 'Unknown'}
            </span>
          )
        },
        {
          key: 'targetValue',
          label: 'Target Value',
          width: '120px',
          render: (item: MissionObjective) => (
            <span className="font-medium text-green-400">
              {item?.targetValue || '0'}
            </span>
          )
        },
        {
          key: 'status',
          label: 'Status',
          width: '120px',
          render: (item: MissionObjective) => {
            const statusDisplay = getObjectiveStatusDisplay(item);
            return (
              <span className={`font-medium ${statusDisplay.color}`}>
                {statusDisplay.status}
              </span>
            );
          }
        },
        {
          key: 'description',
          label: 'Description',
          width: '200px',
          render: (item: MissionObjective) => (
            <span className="text-gray-300 text-sm">
              {item?.description ? (item.description.length > 40 ? `${item.description.substring(0, 40)}...` : item.description) : 'No description'}
            </span>
          )
        },
        {
          key: 'metadata',
          label: 'Metadata',
          width: '150px',
          render: (item: MissionObjective) => (
            <span className="text-gray-400 text-xs font-mono">
              {item?.metadata ? (JSON.stringify(item.metadata).length > 30 ? `${JSON.stringify(item.metadata).substring(0, 30)}...` : JSON.stringify(item.metadata)) : 'None'}
            </span>
          )
        },
        {
          key: 'createdAt',
          label: 'Created At',
          width: '180px',
          render: (item: MissionObjective) => (
            <span className="text-gray-300">
              {item?.createdAt ? new Date(item.createdAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'actions',
          label: 'Actions',
          width: '120px',
          render: (item: MissionObjective) => (
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openEditModal(item);
                }}
                className="p-1 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded"
                title="Edit"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteModal(item);
                }}
                className="p-1 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )
        }
      ];
    } else if (selectedEntity.name === 'mission-objective-assignments') {
      return [
        ...baseColumns,
        {
          key: 'userId',
          label: 'User ID',
          width: '100px',
          render: (item: MissionObjectiveAssignment) => (
            <span className="font-medium text-blue-400">
              #{item?.userId || 'N/A'}
            </span>
          )
        },
        {
          key: 'missionObjectiveId',
          label: 'Objective ID',
          width: '100px',
          render: (item: MissionObjectiveAssignment) => (
            <span className="font-medium text-purple-400">
              #{item?.missionObjectiveId || 'N/A'}
            </span>
          )
        },
        {
          key: 'objectiveDescription',
          label: 'Objective Description',
          width: '200px',
          render: (item: MissionObjectiveAssignment) => (
            <span className="font-medium text-purple-400">
              {item?.missionObjective?.description || `Objective #${item?.missionObjectiveId}` || 'Unknown Objective'}
            </span>
          )
        },
        {
          key: 'progress',
          label: 'Progress',
          width: '100px',
          render: (item: MissionObjectiveAssignment) => (
            <span className="font-medium text-green-400">
              {item?.progress || 0}
            </span>
          )
        },
        {
          key: 'lastChecked',
          label: 'Last Checked',
          width: '180px',
          render: (item: MissionObjectiveAssignment) => (
            <span className="text-gray-300 text-sm">
              {item?.lastCheckedRecordTimestamp ? new Date(item.lastCheckedRecordTimestamp).toLocaleString() : 'Never'}
            </span>
          )
        },
        {
          key: 'status',
          label: 'Status',
          width: '100px',
          render: (item: MissionObjectiveAssignment) => {
            const statusDisplay = getObjectiveAssignmentStatus(item);
            return (
              <span className={`font-medium ${statusDisplay.color}`}>
                {statusDisplay.status}
              </span>
            );
          }
        },
        {
          key: 'createdAt',
          label: 'Created At',
          width: '180px',
          render: (item: MissionObjectiveAssignment) => (
            <span className="text-gray-300">
              {item?.createdAt ? new Date(item.createdAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'actions',
          label: 'Actions',
          width: '80px',
          render: (item: MissionObjectiveAssignment) => (
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteModal(item);
                }}
                className="p-1 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded"
                title="Delete Assignment"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )
        }
      ];
    } else if (selectedEntity.name === 'final-mission-claims') {
      return [
        ...baseColumns,
        {
          key: 'userId',
          label: 'User ID',
          width: '100px',
          render: (item: FinalMissionClaim) => (
            <span className="font-medium text-blue-400">
              {item?.userId || 'N/A'}
            </span>
          )
        },
        {
          key: 'claimType',
          label: 'Claim Type',
          width: '120px',
          render: (item: FinalMissionClaim) => (
            <span className="font-medium text-purple-400">
              {getClaimTypeDisplay(item?.claimType)}
            </span>
          )
        },
        {
          key: 'grantedReward',
          label: 'Granted Reward',
          width: '140px',
          render: (item: FinalMissionClaim) => (
            <span className="font-medium text-green-400">
              {item?.grantedReward || 0}
            </span>
          )
        },
        {
          key: 'userInfo',
          label: 'User Info',
          width: '200px',
          render: (item: FinalMissionClaim) => (
            <div className="text-sm">
              {item?.user ? (
                <>
                  <div className="text-gray-300">
                    External ID: <span className="text-blue-400">{item.user.externalId}</span>
                  </div>
                  <div className="text-gray-300">
                    Points: <span className="text-green-400">{item.user.points}</span>
                  </div>
                </>
              ) : (
                <span className="text-gray-500">No user data</span>
              )}
            </div>
          )
        },
        {
          key: 'createdAt',
          label: 'Created At',
          width: '180px',
          render: (item: FinalMissionClaim) => (
            <span className="text-gray-300">
              {item?.createdAt ? new Date(item.createdAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'updatedAt',
          label: 'Updated At',
          width: '180px',
          render: (item: FinalMissionClaim) => (
            <span className="text-gray-300">
              {item?.updatedAt ? new Date(item.updatedAt).toLocaleString() : 'Unknown'}
            </span>
          )
        },
        {
          key: 'actions',
          label: 'Actions',
          width: '100px',
          render: (item: FinalMissionClaim) => (
            <div className="flex space-x-1">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openEditModal(item);
                }}
                className="p-1 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded"
                title="Edit"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteModal(item);
                }}
                className="p-1 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )
        }
      ];
    }

    return baseColumns;
  };

  const columns = getColumns();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Database className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">CRUD Manager</h1>
            <p className="text-gray-400">Manage Makroz entities</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {/* Entity Selector */}
          <div className="relative">
            <select
              value={selectedEntity.name}
              onChange={(e) => {
                const entity = ENTITIES.find(ent => ent.name === e.target.value);
                if (entity) setSelectedEntity(entity);
              }}
              className="appearance-none bg-dark-700 border border-dark-600 rounded-md px-4 py-2 pr-8 text-gray-100 focus:outline-none focus:border-primary-500"
            >
              {ENTITIES.map(entity => (
                <option key={entity.name} value={entity.name}>
                  {entity.displayName}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>
          
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn btn-primary flex items-center gap-2"
          >
            <Plus size={16} />
            Create New
          </button>
          
          <button
            onClick={loadData}
            className="btn btn-outline flex items-center gap-2"
            disabled={loading}
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Statistics Section for Final Mission Claims */}
      {selectedEntity.name === 'final-mission-claims' && (
        <div className="bg-dark-800 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-100 mb-4">Statistics</h3>
          {statisticsLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="w-6 h-6 text-primary-500 animate-spin" />
              <span className="ml-2 text-gray-300">Loading statistics...</span>
            </div>
          ) : statistics ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="bg-dark-700 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-400">{statistics.totalClaims}</div>
                <div className="text-sm text-gray-300">Total Claims</div>
              </div>
              <div className="bg-dark-700 rounded-lg p-4">
                <div className="text-2xl font-bold text-green-400">{statistics.dailyClaims}</div>
                <div className="text-sm text-gray-300">Daily Claims</div>
              </div>
              <div className="bg-dark-700 rounded-lg p-4">
                <div className="text-2xl font-bold text-purple-400">{statistics.weeklyClaims}</div>
                <div className="text-sm text-gray-300">Weekly Claims</div>
              </div>
              <div className="bg-dark-700 rounded-lg p-4">
                <div className="text-2xl font-bold text-orange-400">{statistics.monthlyClaims}</div>
                <div className="text-sm text-gray-300">Monthly Claims</div>
              </div>
              <div className="bg-dark-700 rounded-lg p-4">
                <div className="text-2xl font-bold text-yellow-400">${statistics.totalRewardsGranted}</div>
                <div className="text-sm text-gray-300">Total Rewards</div>
              </div>
            </div>
          ) : (
            <div className="text-gray-400 text-center py-4">
              Failed to load statistics
            </div>
          )}
        </div>
      )}

      {/* Filter Section */}
      <div className="bg-dark-800 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-semibold text-gray-100 mb-4">Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Search Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Search</label>
            <input
              type="text"
              value={filters.search || ''}
              onChange={(e) => {
                setFilters(prev => ({ ...prev, search: e.target.value || undefined }));
                setCurrentPage(1);
              }}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              placeholder="Search..."
            />
          </div>

          {/* Entity-specific filters */}
          {selectedEntity.name === 'extended-users' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">External ID (Exact)</label>
                <input
                  type="number"
                  value={filters.externalId !== undefined ? filters.externalId : ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      externalId: value === '' ? undefined : Number(value)
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter exact external ID"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">General Search</label>
                <input
                  type="text"
                  value={filters.search || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, search: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search external ID (partial match)"
                />
              </div>
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Exact Points</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.points !== undefined ? filters.points : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFilters(prev => ({
                        ...prev,
                        points: value === '' ? undefined : Number(value)
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    placeholder="e.g., 500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Min Points</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.minPoints !== undefined ? filters.minPoints : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFilters(prev => ({
                        ...prev,
                        minPoints: value === '' ? undefined : Number(value)
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    placeholder="Min"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Max Points</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.maxPoints !== undefined ? filters.maxPoints : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFilters(prev => ({
                        ...prev,
                        maxPoints: value === '' ? undefined : Number(value)
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    placeholder="Max"
                  />
                </div>
              </div>
            </>
          )}

          {selectedEntity.name === 'mission-rules' && (
            <>
              {/* Enum Filters */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Rule Type</label>
                <select
                  value={filters.ruleType || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, ruleType: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Rule Types</option>
                  {RULE_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Compare Operator</label>
                <select
                  value={filters.compare || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, compare: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Operators</option>
                  {COMPARE_OPERATOR_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              {/* Text Filters */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Compare Value</label>
                <input
                  type="text"
                  value={filters.compareValue || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, compareValue: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Filter by compare value..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">General Search</label>
                <input
                  type="text"
                  value={filters.search || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, search: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search across all fields..."
                />
              </div>

              {/* Date Range Filters */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Min Date From</label>
                <input
                  type="datetime-local"
                  value={filters.minDateFrom ? new Date(filters.minDateFrom * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined;
                    setFilters(prev => ({ ...prev, minDateFrom: timestamp }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Min Date To</label>
                <input
                  type="datetime-local"
                  value={filters.minDateTo ? new Date(filters.minDateTo * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined;
                    setFilters(prev => ({ ...prev, minDateTo: timestamp }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Max Date From</label>
                <input
                  type="datetime-local"
                  value={filters.maxDateFrom ? new Date(filters.maxDateFrom * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined;
                    setFilters(prev => ({ ...prev, maxDateFrom: timestamp }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Max Date To</label>
                <input
                  type="datetime-local"
                  value={filters.maxDateTo ? new Date(filters.maxDateTo * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined;
                    setFilters(prev => ({ ...prev, maxDateTo: timestamp }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
            </>
          )}

          {selectedEntity.name === 'missions' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Mission Name</label>
                <input
                  type="text"
                  value={filters.name || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, name: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search by mission name..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <input
                  type="text"
                  value={filters.description || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, description: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search by description..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">General Search</label>
                <input
                  type="text"
                  value={filters.search || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, search: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search across all fields..."
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Mission Type</label>
                  <select
                    value={filters.missionType || ''}
                    onChange={(e) => {
                      setFilters(prev => ({ ...prev, missionType: e.target.value || undefined }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  >
                    <option value="">All Mission Types</option>
                    {MISSION_TYPE_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Status</label>
                  <select
                    value={filters.status || ''}
                    onChange={(e) => {
                      setFilters(prev => ({ ...prev, status: e.target.value || undefined }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  >
                    <option value="">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="upcoming">Upcoming</option>
                    <option value="expired">Expired</option>
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Exact Reward</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.reward !== undefined ? filters.reward : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFilters(prev => ({
                        ...prev,
                        reward: value === '' ? undefined : Number(value)
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    placeholder="e.g., 500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Min Reward</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.rewardMin !== undefined ? filters.rewardMin : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFilters(prev => ({
                        ...prev,
                        rewardMin: value === '' ? undefined : Number(value)
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    placeholder="Min"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Max Reward</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.rewardMax !== undefined ? filters.rewardMax : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFilters(prev => ({
                        ...prev,
                        rewardMax: value === '' ? undefined : Number(value)
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    placeholder="Max"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Start Date From</label>
                  <input
                    type="datetime-local"
                    value={filters.startDateFrom ? new Date(filters.startDateFrom * 1000).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      setFilters(prev => ({
                        ...prev,
                        startDateFrom: e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Start Date To</label>
                  <input
                    type="datetime-local"
                    value={filters.startDateTo ? new Date(filters.startDateTo * 1000).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      setFilters(prev => ({
                        ...prev,
                        startDateTo: e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">End Date From</label>
                  <input
                    type="datetime-local"
                    value={filters.endDateFrom ? new Date(filters.endDateFrom * 1000).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      setFilters(prev => ({
                        ...prev,
                        endDateFrom: e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">End Date To</label>
                  <input
                    type="datetime-local"
                    value={filters.endDateTo ? new Date(filters.endDateTo * 1000).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      setFilters(prev => ({
                        ...prev,
                        endDateTo: e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>
            </>
          )}

          {selectedEntity.name === 'mission-participations' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Search</label>
                <input
                  type="text"
                  value={filters.search || ''}
                  onChange={(e) => {
                    setFilters(prev => ({
                      ...prev,
                      search: e.target.value || undefined
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search by user ID or mission ID"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">User ID</label>
                <input
                  type="number"
                  value={filters.userId !== undefined ? filters.userId : ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      userId: value === '' ? undefined : Number(value)
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Filter by user ID"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Mission ID</label>
                <input
                  type="number"
                  value={filters.missionId !== undefined ? filters.missionId : ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      missionId: value === '' ? undefined : Number(value)
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Filter by mission ID"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Completion Status</label>
                <select
                  value={filters.isCompleted !== undefined ? filters.isCompleted.toString() : ''}
                  onChange={(e) => {
                    setFilters(prev => ({
                      ...prev,
                      isCompleted: e.target.value === '' ? undefined : e.target.value === 'true'
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Statuses</option>
                  <option value="true">Completed</option>
                  <option value="false">Not Completed</option>
                </select>
              </div>

              {/* Date Range Filters */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Created From</label>
                <input
                  type="datetime-local"
                  value={filters.createdAtFrom ? new Date(filters.createdAtFrom * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      createdAtFrom: value ? Math.floor(new Date(value).getTime() / 1000) : undefined
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Created To</label>
                <input
                  type="datetime-local"
                  value={filters.createdAtTo ? new Date(filters.createdAtTo * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      createdAtTo: value ? Math.floor(new Date(value).getTime() / 1000) : undefined
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Updated From</label>
                <input
                  type="datetime-local"
                  value={filters.updatedAtFrom ? new Date(filters.updatedAtFrom * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      updatedAtFrom: value ? Math.floor(new Date(value).getTime() / 1000) : undefined
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Updated To</label>
                <input
                  type="datetime-local"
                  value={filters.updatedAtTo ? new Date(filters.updatedAtTo * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      updatedAtTo: value ? Math.floor(new Date(value).getTime() / 1000) : undefined
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
            </>
          )}

          {selectedEntity.name === 'mission-objectives' && (
            <>
              {/* Basic Filters */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Mission ID</label>
                <input
                  type="number"
                  value={filters.missionId || ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      missionId: value === '' ? undefined : parseInt(value)
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Filter by mission ID"
                  min="1"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Objective Type</label>
                <select
                  value={filters.objectiveType || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, objectiveType: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Types</option>
                  {OBJECTIVE_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Operator</label>
                <select
                  value={filters.operator || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, operator: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Operators</option>
                  {OBJECTIVE_OPERATOR_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Subtype</label>
                <input
                  type="text"
                  value={filters.subtype || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, subtype: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Filter by subtype..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Target Value</label>
                <input
                  type="text"
                  value={filters.targetValue || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, targetValue: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Filter by target value..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <input
                  type="text"
                  value={filters.description || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, description: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Filter by description..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">General Search</label>
                <input
                  type="text"
                  value={filters.search || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, search: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search description & target value..."
                />
              </div>

              {/* Timeframe Filters */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Timeframe Start From</label>
                <input
                  type="datetime-local"
                  value={filters.timeframeStartFrom ? new Date(filters.timeframeStartFrom * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined;
                    setFilters(prev => ({ ...prev, timeframeStartFrom: timestamp }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Timeframe Start To</label>
                <input
                  type="datetime-local"
                  value={filters.timeframeStartTo ? new Date(filters.timeframeStartTo * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined;
                    setFilters(prev => ({ ...prev, timeframeStartTo: timestamp }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Timeframe End From</label>
                <input
                  type="datetime-local"
                  value={filters.timeframeEndFrom ? new Date(filters.timeframeEndFrom * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined;
                    setFilters(prev => ({ ...prev, timeframeEndFrom: timestamp }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Timeframe End To</label>
                <input
                  type="datetime-local"
                  value={filters.timeframeEndTo ? new Date(filters.timeframeEndTo * 1000).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : undefined;
                    setFilters(prev => ({ ...prev, timeframeEndTo: timestamp }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>
            </>
          )}

          {selectedEntity.name === 'final-mission-claims' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">User ID (Exact)</label>
                <input
                  type="number"
                  value={filters.userId !== undefined ? filters.userId : ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      userId: value === '' ? undefined : Number(value)
                    }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter exact user ID"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Claim Type</label>
                <select
                  value={filters.claimType || ''}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, claimType: e.target.value || undefined }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Claim Types</option>
                  {CLAIM_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Min Reward</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.minReward !== undefined ? filters.minReward : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFilters(prev => ({
                        ...prev,
                        minReward: value === '' ? undefined : Number(value)
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    placeholder="Min"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Max Reward</label>
                  <input
                    type="number"
                    min="0"
                    value={filters.maxReward !== undefined ? filters.maxReward : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFilters(prev => ({
                        ...prev,
                        maxReward: value === '' ? undefined : Number(value)
                      }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    placeholder="Max"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Created From</label>
                  <input
                    type="date"
                    value={filters.createdAtFrom || ''}
                    onChange={(e) => {
                      setFilters(prev => ({ ...prev, createdAtFrom: e.target.value || undefined }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Created To</label>
                  <input
                    type="date"
                    value={filters.createdAtTo || ''}
                    onChange={(e) => {
                      setFilters(prev => ({ ...prev, createdAtTo: e.target.value || undefined }));
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>
            </>
          )}
        </div>

        {/* Clear Filters Button */}
        <div className="mt-4">
          <button
            onClick={() => {
              setFilters({});
              setCurrentPage(1);
            }}
            className="px-4 py-2 bg-dark-600 hover:bg-dark-500 text-gray-100 rounded-md text-sm"
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <label className="text-sm text-gray-300">Items per page:</label>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="px-3 py-1 bg-dark-700 border border-dark-600 rounded text-gray-100 text-sm"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
          <div className="text-sm text-gray-400">
            Showing {data.length > 0 ? ((currentPage - 1) * itemsPerPage) + 1 : 0} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} items
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={!hasPrev}
            className="px-3 py-1 bg-dark-700 border border-dark-600 rounded text-gray-100 text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-dark-600"
          >
            Previous
          </button>
          <span className="text-sm text-gray-300">
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={!hasNext}
            className="px-3 py-1 bg-dark-700 border border-dark-600 rounded text-gray-100 text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-dark-600"
          >
            Next
          </button>
        </div>
      </div>

      {/* Table */}
      <UnifiedTable
        data={data}
        columns={columns}
        title={`${selectedEntity.displayName} (${totalItems})`}
        subtitle={`Manage ${selectedEntity.displayName.toLowerCase()} in the system`}
        isLoading={loading}
        error={error}
        onRetry={loadData}
        emptyState={{
          icon: <Database className="w-12 h-12 text-gray-400 mb-4" />,
          title: `No ${selectedEntity.displayName.toLowerCase()} found`,
          description: `Create your first ${selectedEntity.displayName.toLowerCase()} to get started.`,
          action: (
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn btn-primary flex items-center gap-2"
            >
              <Plus size={16} />
              Create New
            </button>
          )
        }}
      />

      {/* Create Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={`Create New ${selectedEntity.displayName.slice(0, -1)}`}
      >
        <div className="space-y-4">
          {selectedEntity.name === 'extended-users' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  External ID *
                </label>
                <input
                  type="number"
                  value={createExtendedUserForm.externalId}
                  onChange={(e) => setCreateExtendedUserForm(prev => ({ ...prev, externalId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter external ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Points (optional)
                </label>
                <input
                  type="number"
                  value={createExtendedUserForm.points}
                  onChange={(e) => setCreateExtendedUserForm(prev => ({ ...prev, points: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter initial points (default: 0)"
                />
              </div>
            </>
          )}

          {selectedEntity.name === 'mission-rules' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Rule Type *
                </label>
                <select
                  value={createMissionRuleForm.ruleType}
                  onChange={(e) => setCreateMissionRuleForm(prev => ({ ...prev, ruleType: e.target.value as RuleType }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {RULE_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Compare Operator *
                </label>
                <select
                  value={createMissionRuleForm.compare}
                  onChange={(e) => setCreateMissionRuleForm(prev => ({ ...prev, compare: e.target.value as CompareOperator }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {COMPARE_OPERATOR_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Compare Value *
                </label>
                <input
                  type="text"
                  value={createMissionRuleForm.compareValue}
                  onChange={(e) => setCreateMissionRuleForm(prev => ({ ...prev, compareValue: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter comparison value (number for numeric types, string for text)"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Min Date (optional)
                  </label>
                  <input
                    type="date"
                    value={createMissionRuleForm.minDate ? new Date(createMissionRuleForm.minDate * 1000).toISOString().split('T')[0] : ''}
                    onChange={(e) => {
                      const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : null;
                      setCreateMissionRuleForm(prev => ({ ...prev, minDate: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Max Date (optional)
                  </label>
                  <input
                    type="date"
                    value={createMissionRuleForm.maxDate ? new Date(createMissionRuleForm.maxDate * 1000).toISOString().split('T')[0] : ''}
                    onChange={(e) => {
                      const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : null;
                      setCreateMissionRuleForm(prev => ({ ...prev, maxDate: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>
            </>
          )}

          {selectedEntity.name === 'missions' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission Name *
                </label>
                <input
                  type="text"
                  value={createMissionForm.name}
                  onChange={(e) => setCreateMissionForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission Type *
                </label>
                <select
                  value={createMissionForm.missionType}
                  onChange={(e) => setCreateMissionForm(prev => ({ ...prev, missionType: e.target.value as MissionType }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {MISSION_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Reward *
                </label>
                <input
                  type="number"
                  min="0"
                  value={createMissionForm.reward}
                  onChange={(e) => setCreateMissionForm(prev => ({ ...prev, reward: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter reward amount"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description *
                </label>
                <textarea
                  value={createMissionForm.description}
                  onChange={(e) => setCreateMissionForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission description"
                  rows={3}
                  required
                />
              </div>

              {/* Mission Names by Language */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Mission Names by Language
                </label>
                <div className="space-y-3">
                  {SUPPORTED_LANGUAGES.map(lang => (
                    <div key={lang.code}>
                      <label className="block text-xs text-gray-400 mb-1">
                        {lang.name} ({lang.code.toUpperCase()})
                        {lang.code === 'en' && ' *'}
                      </label>
                      <input
                        type="text"
                        value={createMissionForm.name_i18n[lang.code] || ''}
                        onChange={(e) => updateCreateMissionNameI18n(lang.code, e.target.value)}
                        placeholder={`Mission name in ${lang.name}`}
                        className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500"
                        required={lang.code === 'en'}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Mission Descriptions by Language */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Mission Descriptions by Language
                </label>
                <div className="space-y-3">
                  {SUPPORTED_LANGUAGES.map(lang => (
                    <div key={lang.code}>
                      <label className="block text-xs text-gray-400 mb-1">
                        {lang.name} ({lang.code.toUpperCase()})
                      </label>
                      <textarea
                        value={createMissionForm.description_i18n[lang.code] || ''}
                        onChange={(e) => updateCreateMissionDescriptionI18n(lang.code, e.target.value)}
                        placeholder={`Mission description in ${lang.name}`}
                        rows={3}
                        className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500 resize-vertical"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Active Status */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="createIsActive"
                  checked={createMissionForm.isActive}
                  onChange={(e) => setCreateMissionForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500 focus:ring-2"
                />
                <label htmlFor="createIsActive" className="text-sm font-medium text-gray-300">
                  Mission is Active
                </label>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Start Date *
                  </label>
                  <input
                    type="datetime-local"
                    value={new Date(createMissionForm.startDate * 1000).toISOString().slice(0, 16)}
                    onChange={(e) => {
                      const timestamp = Math.floor(new Date(e.target.value).getTime() / 1000);
                      setCreateMissionForm(prev => ({ ...prev, startDate: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    End Date *
                  </label>
                  <input
                    type="datetime-local"
                    value={new Date(createMissionForm.endDate * 1000).toISOString().slice(0, 16)}
                    onChange={(e) => {
                      const timestamp = Math.floor(new Date(e.target.value).getTime() / 1000);
                      setCreateMissionForm(prev => ({ ...prev, endDate: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    required
                  />
                </div>
              </div>
            </>
          )}

          {selectedEntity.name === 'mission-participations' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  User ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={createMissionParticipationForm.userId}
                  onChange={(e) => setCreateMissionParticipationForm(prev => ({ ...prev, userId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter user ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={createMissionParticipationForm.missionId}
                  onChange={(e) => setCreateMissionParticipationForm(prev => ({ ...prev, missionId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Completion Status
                </label>
                <select
                  value={createMissionParticipationForm.isCompleted ? 'true' : 'false'}
                  onChange={(e) => setCreateMissionParticipationForm(prev => ({ ...prev, isCompleted: e.target.value === 'true' }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  {PARTICIPATION_STATUS_OPTIONS.map(option => (
                    <option key={option.value.toString()} value={option.value.toString()}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </>
          )}

          {selectedEntity.name === 'mission-rule-assignments' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={createMissionRuleAssignmentForm.missionId}
                  onChange={(e) => setCreateMissionRuleAssignmentForm(prev => ({ ...prev, missionId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission Rule ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={createMissionRuleAssignmentForm.missionRuleId}
                  onChange={(e) => setCreateMissionRuleAssignmentForm(prev => ({ ...prev, missionRuleId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission rule ID"
                  required
                />
              </div>

              <div className="bg-dark-800 p-3 rounded-md">
                <p className="text-sm text-gray-400 mb-2">Assignment Info:</p>
                <p className="text-gray-300 text-sm">
                  This will create a new assignment linking the specified mission to the specified rule.
                  Make sure both the mission and rule exist before creating the assignment.
                </p>
              </div>
            </>
          )}

          {selectedEntity.name === 'mission-objectives' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={createMissionObjectiveForm.missionId}
                  onChange={(e) => setCreateMissionObjectiveForm(prev => ({ ...prev, missionId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Objective Type *
                </label>
                <select
                  value={createMissionObjectiveForm.objectiveType}
                  onChange={(e) => {
                    const newObjectiveType = e.target.value as ObjectiveType;
                    const subtypeOptions = getSubtypeOptions(newObjectiveType);
                    setCreateMissionObjectiveForm(prev => ({
                      ...prev,
                      objectiveType: newObjectiveType,
                      subtype: subtypeOptions.length > 0 ? subtypeOptions[0].value : ''
                    }));
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {OBJECTIVE_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Subtype *
                </label>
                <select
                  value={createMissionObjectiveForm.subtype}
                  onChange={(e) => setCreateMissionObjectiveForm(prev => ({ ...prev, subtype: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {getSubtypeOptions(createMissionObjectiveForm.objectiveType).map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Operator *
                </label>
                <select
                  value={createMissionObjectiveForm.operator}
                  onChange={(e) => setCreateMissionObjectiveForm(prev => ({ ...prev, operator: e.target.value as ObjectiveOperator }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {OBJECTIVE_OPERATOR_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Target Value *
                </label>
                <input
                  type="text"
                  value={createMissionObjectiveForm.targetValue}
                  onChange={(e) => setCreateMissionObjectiveForm(prev => ({ ...prev, targetValue: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter target value"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description *
                </label>
                <textarea
                  value={createMissionObjectiveForm.description}
                  onChange={(e) => setCreateMissionObjectiveForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter objective description"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Timeframe Start (optional)
                  </label>
                  <input
                    type="datetime-local"
                    value={createMissionObjectiveForm.timeframeStart ? new Date(createMissionObjectiveForm.timeframeStart).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      const timestamp = e.target.value ? new Date(e.target.value).getTime() : null;
                      setCreateMissionObjectiveForm(prev => ({ ...prev, timeframeStart: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Timeframe End (optional)
                  </label>
                  <input
                    type="datetime-local"
                    value={createMissionObjectiveForm.timeframeEnd ? new Date(createMissionObjectiveForm.timeframeEnd).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      const timestamp = e.target.value ? new Date(e.target.value).getTime() : null;
                      setCreateMissionObjectiveForm(prev => ({ ...prev, timeframeEnd: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Metadata (Optional JSON)
                </label>
                <textarea
                  value={createMissionObjectiveForm.metadata ? JSON.stringify(createMissionObjectiveForm.metadata, null, 2) : ''}
                  onChange={(e) => {
                    try {
                      const parsed = e.target.value.trim() ? JSON.parse(e.target.value) : null;
                      setCreateMissionObjectiveForm(prev => ({ ...prev, metadata: parsed }));
                    } catch {
                      // Invalid JSON, keep the text for user to fix
                    }
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500 font-mono text-sm"
                  placeholder='{"bonusRequired": true, "minBonusAmount": 50}'
                  rows={4}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Optional JSON metadata for additional objective configuration. Leave empty if not needed.
                </p>
              </div>
            </>
          )}

          {selectedEntity.name === 'mission-objective-assignments' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  User ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={createMissionObjectiveAssignmentForm.userId}
                  onChange={(e) => setCreateMissionObjectiveAssignmentForm(prev => ({ ...prev, userId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter user ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission Objective ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={createMissionObjectiveAssignmentForm.missionObjectiveId}
                  onChange={(e) => setCreateMissionObjectiveAssignmentForm(prev => ({ ...prev, missionObjectiveId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission objective ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Initial Progress
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={createMissionObjectiveAssignmentForm.progress}
                  onChange={(e) => setCreateMissionObjectiveAssignmentForm(prev => ({ ...prev, progress: parseFloat(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter initial progress (default: 0)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Last Checked Timestamp (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={createMissionObjectiveAssignmentForm.lastCheckedRecordTimestamp ? new Date(createMissionObjectiveAssignmentForm.lastCheckedRecordTimestamp).toISOString().slice(0, 16) : ''}
                  onChange={(e) => {
                    const timestamp = e.target.value ? new Date(e.target.value).getTime() : null;
                    setCreateMissionObjectiveAssignmentForm(prev => ({ ...prev, lastCheckedRecordTimestamp: timestamp }));
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                />
              </div>

              <div className="bg-dark-800 p-3 rounded-md">
                <p className="text-sm text-gray-400 mb-2">Assignment Info:</p>
                <p className="text-gray-300 text-sm">
                  This will create a new user progress assignment for the specified objective.
                  Make sure the user and objective exist before creating the assignment.
                </p>
              </div>
            </>
          )}

          {selectedEntity.name === 'final-mission-claims' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  User ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={createFinalMissionClaimForm.userId}
                  onChange={(e) => setCreateFinalMissionClaimForm(prev => ({ ...prev, userId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter user ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Claim Type *
                </label>
                <select
                  value={createFinalMissionClaimForm.claimType}
                  onChange={(e) => setCreateFinalMissionClaimForm(prev => ({ ...prev, claimType: e.target.value as ClaimType }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {CLAIM_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Granted Reward *
                </label>
                <input
                  type="number"
                  min="0"
                  step="1"
                  value={createFinalMissionClaimForm.grantedReward}
                  onChange={(e) => setCreateFinalMissionClaimForm(prev => ({ ...prev, grantedReward: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter reward amount (points)"
                  required
                />
              </div>

              <div className="bg-dark-800 p-3 rounded-md">
                <p className="text-sm text-gray-400 mb-2">Final Mission Claim Info:</p>
                <p className="text-gray-300 text-sm">
                  This will create a new final mission claim for the specified user.
                  The reward will be added to the user's points balance.
                </p>
              </div>
            </>
          )}

          <div className="flex justify-end gap-3 pt-4">
            <button
              onClick={() => setShowCreateModal(false)}
              className="btn btn-outline flex items-center gap-2"
            >
              <X size={16} />
              Cancel
            </button>
            <button
              onClick={handleCreate}
              className="btn btn-primary flex items-center gap-2"
            >
              <Save size={16} />
              Create
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title={`Edit ${selectedEntity.displayName.slice(0, -1)}`}
      >
        <div className="space-y-4">
          {selectedItem && selectedEntity.name === 'extended-users' && (
            <>
              <div className="bg-dark-800 p-3 rounded-md">
                <p className="text-sm text-gray-400">Editing user:</p>
                <p className="text-gray-100">ID: <span className="font-medium text-blue-400">{selectedItem.id}</span></p>
                <p className="text-gray-100">Current External ID: <span className="font-medium text-blue-400">{selectedItem.externalId}</span></p>
                <p className="text-gray-100">Current Points: <span className="font-medium text-green-400">{selectedItem.points}</span></p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  External ID *
                </label>
                <input
                  type="number"
                  value={editExtendedUserForm.externalId}
                  onChange={(e) => setEditExtendedUserForm(prev => ({ ...prev, externalId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter external ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Points *
                </label>
                <input
                  type="number"
                  value={editExtendedUserForm.points}
                  onChange={(e) => setEditExtendedUserForm(prev => ({ ...prev, points: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter points value"
                  required
                />
              </div>
            </>
          )}

          {selectedItem && selectedEntity.name === 'mission-rules' && (
            <>
              <div className="bg-dark-800 p-3 rounded-md">
                <p className="text-sm text-gray-400">Editing mission rule:</p>
                <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                <p className="text-gray-100">Current Rule: <span className="font-medium text-green-400">
                  {RULE_TYPE_OPTIONS.find(opt => opt.value === selectedItem.ruleType)?.label} {COMPARE_OPERATOR_OPTIONS.find(opt => opt.value === selectedItem.compare)?.label} {selectedItem.compareValue}
                </span></p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Rule Type *
                </label>
                <select
                  value={editMissionRuleForm.ruleType}
                  onChange={(e) => setEditMissionRuleForm(prev => ({ ...prev, ruleType: e.target.value as RuleType }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {RULE_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Compare Operator *
                </label>
                <select
                  value={editMissionRuleForm.compare}
                  onChange={(e) => setEditMissionRuleForm(prev => ({ ...prev, compare: e.target.value as CompareOperator }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {COMPARE_OPERATOR_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Compare Value *
                </label>
                <input
                  type="text"
                  value={editMissionRuleForm.compareValue}
                  onChange={(e) => setEditMissionRuleForm(prev => ({ ...prev, compareValue: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter comparison value"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Min Date (optional)
                  </label>
                  <input
                    type="date"
                    value={editMissionRuleForm.minDate ? new Date(editMissionRuleForm.minDate * 1000).toISOString().split('T')[0] : ''}
                    onChange={(e) => {
                      const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : null;
                      setEditMissionRuleForm(prev => ({ ...prev, minDate: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Max Date (optional)
                  </label>
                  <input
                    type="date"
                    value={editMissionRuleForm.maxDate ? new Date(editMissionRuleForm.maxDate * 1000).toISOString().split('T')[0] : ''}
                    onChange={(e) => {
                      const timestamp = e.target.value ? Math.floor(new Date(e.target.value).getTime() / 1000) : null;
                      setEditMissionRuleForm(prev => ({ ...prev, maxDate: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>
            </>
          )}

          {selectedItem && selectedEntity.name === 'missions' && (
            <>
              <div className="bg-dark-800 p-3 rounded-md">
                <p className="text-sm text-gray-400">Editing mission:</p>
                <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                <p className="text-gray-100">Current Name: <span className="font-medium text-green-400">{selectedItem.name}</span></p>
                <p className="text-gray-100">Type: <span className="font-medium text-purple-400">
                  {MISSION_TYPE_OPTIONS.find(opt => opt.value === selectedItem.missionType)?.label}
                </span></p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission Name *
                </label>
                <input
                  type="text"
                  value={editMissionForm.name}
                  onChange={(e) => setEditMissionForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission Type *
                </label>
                <select
                  value={editMissionForm.missionType}
                  onChange={(e) => setEditMissionForm(prev => ({ ...prev, missionType: e.target.value as MissionType }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {MISSION_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Reward *
                </label>
                <input
                  type="number"
                  min="0"
                  value={editMissionForm.reward}
                  onChange={(e) => setEditMissionForm(prev => ({ ...prev, reward: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter reward amount"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description *
                </label>
                <textarea
                  value={editMissionForm.description}
                  onChange={(e) => setEditMissionForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission description"
                  rows={3}
                  required
                />
              </div>

              {/* Mission Names by Language */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Mission Names by Language
                </label>
                <div className="space-y-3">
                  {SUPPORTED_LANGUAGES.map(lang => (
                    <div key={lang.code}>
                      <label className="block text-xs text-gray-400 mb-1">
                        {lang.name} ({lang.code.toUpperCase()})
                        {lang.code === 'en' && ' *'}
                      </label>
                      <input
                        type="text"
                        value={editMissionForm.name_i18n?.[lang.code] || ''}
                        onChange={(e) => updateEditMissionNameI18n(lang.code, e.target.value)}
                        placeholder={`Mission name in ${lang.name}`}
                        className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500"
                        required={lang.code === 'en'}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Mission Descriptions by Language */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Mission Descriptions by Language
                </label>
                <div className="space-y-3">
                  {SUPPORTED_LANGUAGES.map(lang => (
                    <div key={lang.code}>
                      <label className="block text-xs text-gray-400 mb-1">
                        {lang.name} ({lang.code.toUpperCase()})
                      </label>
                      <textarea
                        value={editMissionForm.description_i18n?.[lang.code] || ''}
                        onChange={(e) => updateEditMissionDescriptionI18n(lang.code, e.target.value)}
                        placeholder={`Mission description in ${lang.name}`}
                        rows={3}
                        className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500 resize-vertical"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Active Status */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="editIsActive"
                  checked={editMissionForm.isActive || false}
                  onChange={(e) => setEditMissionForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500 focus:ring-2"
                />
                <label htmlFor="editIsActive" className="text-sm font-medium text-gray-300">
                  Mission is Active
                </label>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Start Date *
                  </label>
                  <input
                    type="datetime-local"
                    value={editMissionForm.startDate ? new Date(editMissionForm.startDate * 1000).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      const timestamp = Math.floor(new Date(e.target.value).getTime() / 1000);
                      setEditMissionForm(prev => ({ ...prev, startDate: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    End Date *
                  </label>
                  <input
                    type="datetime-local"
                    value={editMissionForm.endDate ? new Date(editMissionForm.endDate * 1000).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      const timestamp = Math.floor(new Date(e.target.value).getTime() / 1000);
                      setEditMissionForm(prev => ({ ...prev, endDate: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                    required
                  />
                </div>
              </div>
            </>
          )}

          {selectedItem && selectedEntity.name === 'mission-participations' && (
            <>
              <div className="bg-dark-800 p-3 rounded-md">
                <p className="text-sm text-gray-400">Editing mission participation:</p>
                <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                <p className="text-gray-100">User ID: <span className="font-medium text-green-400">{selectedItem.userId}</span></p>
                <p className="text-gray-100">Mission: <span className="font-medium text-purple-400">
                  {selectedItem.mission?.name || `Mission #${selectedItem.missionId}`}
                </span></p>
                <p className="text-gray-100">Current Status: <span className={`font-medium ${getParticipationStatusDisplay(selectedItem.isCompleted).color}`}>
                  {getParticipationStatusDisplay(selectedItem.isCompleted).label}
                </span></p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Completion Status *
                </label>
                <select
                  value={editMissionParticipationForm.isCompleted ? 'true' : 'false'}
                  onChange={(e) => setEditMissionParticipationForm(prev => ({ ...prev, isCompleted: e.target.value === 'true' }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {PARTICIPATION_STATUS_OPTIONS.map(option => (
                    <option key={option.value.toString()} value={option.value.toString()}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </>
          )}

          {selectedItem && selectedEntity.name === 'mission-objectives' && (
            <>
              <div className="bg-dark-800 p-3 rounded-md">
                <p className="text-sm text-gray-400">Editing mission objective:</p>
                <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                <p className="text-gray-100">Mission ID: <span className="font-medium text-green-400">#{selectedItem.missionId}</span></p>
                <p className="text-gray-100">Type: <span className="font-medium text-purple-400">
                  {OBJECTIVE_TYPE_OPTIONS.find(opt => opt.value === selectedItem.objectiveType)?.label}
                </span></p>
                <p className="text-gray-100">Current Target: <span className="font-medium text-orange-400">
                  {OBJECTIVE_OPERATOR_OPTIONS.find(opt => opt.value === selectedItem.operator)?.label} {selectedItem.targetValue}
                </span></p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Mission ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={editMissionObjectiveForm.missionId}
                  onChange={(e) => setEditMissionObjectiveForm(prev => ({ ...prev, missionId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter mission ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Objective Type *
                </label>
                <select
                  value={editMissionObjectiveForm.objectiveType}
                  onChange={(e) => {
                    const newObjectiveType = e.target.value as ObjectiveType;
                    const subtypeOptions = getSubtypeOptions(newObjectiveType);
                    setEditMissionObjectiveForm(prev => ({
                      ...prev,
                      objectiveType: newObjectiveType,
                      subtype: subtypeOptions.length > 0 ? subtypeOptions[0].value : ''
                    }));
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {OBJECTIVE_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Subtype *
                </label>
                <select
                  value={editMissionObjectiveForm.subtype}
                  onChange={(e) => setEditMissionObjectiveForm(prev => ({ ...prev, subtype: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {getSubtypeOptions(editMissionObjectiveForm.objectiveType).map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Operator *
                </label>
                <select
                  value={editMissionObjectiveForm.operator}
                  onChange={(e) => setEditMissionObjectiveForm(prev => ({ ...prev, operator: e.target.value as ObjectiveOperator }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {OBJECTIVE_OPERATOR_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Target Value *
                </label>
                <input
                  type="text"
                  value={editMissionObjectiveForm.targetValue}
                  onChange={(e) => setEditMissionObjectiveForm(prev => ({ ...prev, targetValue: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter target value"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description *
                </label>
                <textarea
                  value={editMissionObjectiveForm.description}
                  onChange={(e) => setEditMissionObjectiveForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter objective description"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Timeframe Start (optional)
                  </label>
                  <input
                    type="datetime-local"
                    value={editMissionObjectiveForm.timeframeStart ? new Date(editMissionObjectiveForm.timeframeStart).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      const timestamp = e.target.value ? new Date(e.target.value).getTime() : null;
                      setEditMissionObjectiveForm(prev => ({ ...prev, timeframeStart: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Timeframe End (optional)
                  </label>
                  <input
                    type="datetime-local"
                    value={editMissionObjectiveForm.timeframeEnd ? new Date(editMissionObjectiveForm.timeframeEnd).toISOString().slice(0, 16) : ''}
                    onChange={(e) => {
                      const timestamp = e.target.value ? new Date(e.target.value).getTime() : null;
                      setEditMissionObjectiveForm(prev => ({ ...prev, timeframeEnd: timestamp }));
                    }}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Metadata (Optional JSON)
                </label>
                <textarea
                  value={editMissionObjectiveForm.metadata ? JSON.stringify(editMissionObjectiveForm.metadata, null, 2) : ''}
                  onChange={(e) => {
                    try {
                      const parsed = e.target.value.trim() ? JSON.parse(e.target.value) : null;
                      setEditMissionObjectiveForm(prev => ({ ...prev, metadata: parsed }));
                    } catch {
                      // Invalid JSON, keep the text for user to fix
                    }
                  }}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500 font-mono text-sm"
                  placeholder='{"bonusRequired": true, "minBonusAmount": 50}'
                  rows={4}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Optional JSON metadata for additional objective configuration. Leave empty if not needed.
                </p>
              </div>
            </>
          )}

          {selectedItem && selectedEntity.name === 'final-mission-claims' && (
            <>
              <div className="bg-dark-800 p-3 rounded-md">
                <p className="text-sm text-gray-400">Editing final mission claim:</p>
                <p className="text-gray-100">ID: <span className="font-medium text-blue-400">{selectedItem.id}</span></p>
                <p className="text-gray-100">Current User ID: <span className="font-medium text-blue-400">{selectedItem.userId}</span></p>
                <p className="text-gray-100">Current Claim Type: <span className="font-medium text-purple-400">{getClaimTypeDisplay(selectedItem.claimType)}</span></p>
                <p className="text-gray-100">Current Reward: <span className="font-medium text-green-400">{selectedItem.grantedReward}</span></p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  User ID *
                </label>
                <input
                  type="number"
                  min="1"
                  value={editFinalMissionClaimForm.userId}
                  onChange={(e) => setEditFinalMissionClaimForm(prev => ({ ...prev, userId: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter user ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Claim Type *
                </label>
                <select
                  value={editFinalMissionClaimForm.claimType}
                  onChange={(e) => setEditFinalMissionClaimForm(prev => ({ ...prev, claimType: e.target.value as ClaimType }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  required
                >
                  {CLAIM_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Granted Reward *
                </label>
                <input
                  type="number"
                  min="0"
                  step="1"
                  value={editFinalMissionClaimForm.grantedReward}
                  onChange={(e) => setEditFinalMissionClaimForm(prev => ({ ...prev, grantedReward: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Enter reward amount (points)"
                  required
                />
              </div>
            </>
          )}

          <div className="flex justify-end gap-3 pt-4">
            <button
              onClick={() => setShowEditModal(false)}
              className="btn btn-outline flex items-center gap-2"
            >
              <X size={16} />
              Cancel
            </button>
            <button
              onClick={handleEdit}
              className="btn btn-primary flex items-center gap-2"
            >
              <Save size={16} />
              Update
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Confirm Deletion"
      >
        <div className="space-y-4">
          {selectedItem && (
            <div>
              <p className="text-gray-300 mb-4">
                Are you sure you want to delete this {selectedEntity.displayName.slice(0, -1).toLowerCase()}?
              </p>
              <div className="bg-dark-800 p-3 rounded-md">
                {selectedEntity.name === 'extended-users' && (
                  <>
                    <p className="text-gray-100">External ID: <span className="font-medium text-blue-400">{selectedItem.externalId}</span></p>
                    <p className="text-gray-100">Points: <span className="font-medium text-green-400">{selectedItem.points}</span></p>
                  </>
                )}
                {selectedEntity.name === 'mission-rules' && (
                  <>
                    <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                    <p className="text-gray-100">Rule Type: <span className="font-medium text-purple-400">
                      {RULE_TYPE_OPTIONS.find(opt => opt.value === selectedItem.ruleType)?.label}
                    </span></p>
                    <p className="text-gray-100">Condition: <span className="font-medium text-green-400">
                      {COMPARE_OPERATOR_OPTIONS.find(opt => opt.value === selectedItem.compare)?.label} {selectedItem.compareValue}
                    </span></p>
                  </>
                )}
                {selectedEntity.name === 'missions' && (
                  <>
                    <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                    <p className="text-gray-100">Name: <span className="font-medium text-green-400">{selectedItem.name}</span></p>
                    <p className="text-gray-100">Type: <span className="font-medium text-purple-400">
                      {MISSION_TYPE_OPTIONS.find(opt => opt.value === selectedItem.missionType)?.label}
                    </span></p>
                    <p className="text-gray-100">Reward: <span className="font-medium text-yellow-400">{selectedItem.reward}</span></p>
                  </>
                )}
                {selectedEntity.name === 'mission-participations' && (
                  <>
                    <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                    <p className="text-gray-100">User ID: <span className="font-medium text-green-400">{selectedItem.userId}</span></p>
                    <p className="text-gray-100">Mission: <span className="font-medium text-purple-400">
                      {selectedItem.mission?.name || `Mission #${selectedItem.missionId}`}
                    </span></p>
                    <p className="text-gray-100">Status: <span className={`font-medium ${getParticipationStatusDisplay(selectedItem.isCompleted).color}`}>
                      {getParticipationStatusDisplay(selectedItem.isCompleted).label}
                    </span></p>
                  </>
                )}
                {selectedEntity.name === 'mission-rule-assignments' && (
                  <>
                    <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                    <p className="text-gray-100">Mission: <span className="font-medium text-green-400">
                      {selectedItem.mission?.name || `Mission #${selectedItem.missionId}`}
                    </span></p>
                    <p className="text-gray-100">Rule: <span className="font-medium text-purple-400">
                      {selectedItem.missionRule?.ruleType || `Rule #${selectedItem.missionRuleId}`}
                    </span></p>
                    <p className="text-gray-100">Assignment: <span className="font-medium text-orange-400">
                      {getAssignmentDisplay(selectedItem).displayText}
                    </span></p>
                  </>
                )}
                {selectedEntity.name === 'mission-objectives' && (
                  <>
                    <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                    <p className="text-gray-100">Mission ID: <span className="font-medium text-green-400">#{selectedItem.missionId}</span></p>
                    <p className="text-gray-100">Type: <span className="font-medium text-purple-400">
                      {OBJECTIVE_TYPE_OPTIONS.find(opt => opt.value === selectedItem.objectiveType)?.label}
                    </span></p>
                    <p className="text-gray-100">Target: <span className="font-medium text-orange-400">
                      {OBJECTIVE_OPERATOR_OPTIONS.find(opt => opt.value === selectedItem.operator)?.label} {selectedItem.targetValue}
                    </span></p>
                    <p className="text-gray-100">Description: <span className="font-medium text-gray-300">
                      {selectedItem.description ? (selectedItem.description.length > 50 ? `${selectedItem.description.substring(0, 50)}...` : selectedItem.description) : 'No description'}
                    </span></p>
                  </>
                )}
                {selectedEntity.name === 'mission-objective-assignments' && (
                  <>
                    <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                    <p className="text-gray-100">User ID: <span className="font-medium text-green-400">#{selectedItem.userId}</span></p>
                    <p className="text-gray-100">Objective: <span className="font-medium text-purple-400">
                      {selectedItem.missionObjective?.description || `Objective #${selectedItem.missionObjectiveId}`}
                    </span></p>
                    <p className="text-gray-100">Progress: <span className="font-medium text-orange-400">
                      {selectedItem.progress || 0}
                    </span></p>
                    <p className="text-gray-100">Assignment: <span className="font-medium text-gray-300">
                      {getObjectiveAssignmentDisplay(selectedItem).displayText}
                    </span></p>
                  </>
                )}
                {selectedEntity.name === 'final-mission-claims' && (
                  <>
                    <p className="text-gray-100">ID: <span className="font-medium text-blue-400">#{selectedItem.id}</span></p>
                    <p className="text-gray-100">User ID: <span className="font-medium text-blue-400">{selectedItem.userId}</span></p>
                    <p className="text-gray-100">Claim Type: <span className="font-medium text-purple-400">
                      {getClaimTypeDisplay(selectedItem.claimType)}
                    </span></p>
                    <p className="text-gray-100">Granted Reward: <span className="font-medium text-green-400">
                      {selectedItem.grantedReward}
                    </span></p>
                    {selectedItem.user && (
                      <p className="text-gray-100">User Info: <span className="font-medium text-gray-300">
                        External ID: {selectedItem.user.externalId}, Points: {selectedItem.user.points}
                      </span></p>
                    )}
                  </>
                )}
              </div>
              <p className="text-red-400 text-sm mt-2">This action cannot be undone.</p>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4">
            <button
              onClick={() => setShowDeleteModal(false)}
              className="btn btn-outline flex items-center gap-2"
            >
              <X size={16} />
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className="btn btn-danger flex items-center gap-2"
            >
              <Trash2 size={16} />
              Delete
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CrudManager;
