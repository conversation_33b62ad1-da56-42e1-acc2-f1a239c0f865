import React from 'react';
import { Save } from 'lucide-react';

const SettingsPage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Settings</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-1">
          <div className="admin-card h-full">
            <h3 className="text-lg font-semibold mb-4">Settings Categories</h3>
            <div className="space-y-1">
              <button className="w-full text-left py-2 px-3 rounded-md bg-primary-500/10 text-primary-500 font-medium">
                General Settings
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md hover:bg-dark-700">
                Security
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md hover:bg-dark-700">
                Payments
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md hover:bg-dark-700">
                Game Providers
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md hover:bg-dark-700">
                KYC Settings
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md hover:bg-dark-700">
                Email Templates
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md hover:bg-dark-700">
                API Integration
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md hover:bg-dark-700">
                Admin Users
              </button>
            </div>
          </div>
        </div>
        
        <div className="md:col-span-3">
          <div className="admin-card">
            <h3 className="text-lg font-semibold mb-6">General Settings</h3>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Site Name
                  </label>
                  <input
                    type="text"
                    value="Betroz"
                    className="w-full py-2 px-3 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Admin Email
                  </label>
                  <input
                    type="email"
                    value="<EMAIL>"
                    className="w-full py-2 px-3 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Site Description
                </label>
                <textarea
                  rows={3}
                  className="w-full py-2 px-3 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500"
                  defaultValue="Betroz is a premier crypto casino and betting platform offering a wide range of games and sports betting options."
                ></textarea>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Default Currency
                  </label>
                  <select className="w-full py-2 px-3 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500">
                    <option>Bitcoin (BTC)</option>
                    <option>Ethereum (ETH)</option>
                    <option>Litecoin (LTC)</option>
                    <option>Dogecoin (DOGE)</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Timezone
                  </label>
                  <select className="w-full py-2 px-3 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500">
                    <option>UTC</option>
                    <option>UTC+1 (CET)</option>
                    <option>UTC+2 (EET)</option>
                    <option>UTC-5 (EST)</option>
                    <option>UTC-8 (PST)</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Maintenance Mode
                  </label>
                  <div className="flex items-center mt-3">
                    <label className="inline-flex items-center">
                      <input type="radio" name="maintenance" className="form-radio text-primary-500" value="off" defaultChecked />
                      <span className="ml-2">Off</span>
                    </label>
                    <label className="inline-flex items-center ml-6">
                      <input type="radio" name="maintenance" className="form-radio text-primary-500" value="on" />
                      <span className="ml-2">On</span>
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Registration Enabled
                  </label>
                  <div className="flex items-center mt-3">
                    <label className="inline-flex items-center">
                      <input type="radio" name="registration" className="form-radio text-primary-500" value="enabled" defaultChecked />
                      <span className="ml-2">Enabled</span>
                    </label>
                    <label className="inline-flex items-center ml-6">
                      <input type="radio" name="registration" className="form-radio text-primary-500" value="disabled" />
                      <span className="ml-2">Disabled</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Require Email Verification
                  </label>
                  <div className="flex items-center mt-3">
                    <label className="inline-flex items-center">
                      <input type="radio" name="email_verification" className="form-radio text-primary-500" value="required" defaultChecked />
                      <span className="ml-2">Required</span>
                    </label>
                    <label className="inline-flex items-center ml-6">
                      <input type="radio" name="email_verification" className="form-radio text-primary-500" value="optional" />
                      <span className="ml-2">Optional</span>
                    </label>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Minimum Deposit Amount (BTC)
                </label>
                <input
                  type="text"
                  value="0.001"
                  className="w-full py-2 px-3 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Maximum Withdrawal Amount (BTC)
                </label>
                <input
                  type="text"
                  value="1.000"
                  className="w-full py-2 px-3 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500"
                />
              </div>
              
              <div className="flex justify-end">
                <button className="btn btn-primary flex items-center gap-2">
                  <Save size={16} />
                  <span>Save Changes</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;