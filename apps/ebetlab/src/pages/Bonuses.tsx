import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, RefreshCw, AlertCircle, Dices, Edit, Trash2, Eye, Calendar, DollarSign, Tag, Activity, Ban } from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import { fetchBonuses, deleteBonus, deactivateBonus, type BonusData } from '../utils/api';

interface BonusesData {
  total: number;
  data: BonusData[];
}

const Bonuses: React.FC = () => {
  const navigate = useNavigate();
  const [bonusesData, setBonusesData] = useState<BonusesData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sortState, setSortState] = useState<SortState>({
    field: 'id',
    direction: 'desc'
  });

  const loadBonuses = useCallback(async (page: number = currentPage, limit: number = itemsPerPage, searchFilters: Record<string, unknown> = filters) => {
    setIsLoading(true);
    setError('');

    try {
      // Include sorting parameters in the request
      const searchParamsWithSort = {
        ...searchFilters,
        sortBy: sortState.field,
        direction: sortState.direction
      };

      const response = await fetchBonuses(page, limit, searchParamsWithSort);

      if (response.success && response.data) {
        setBonusesData(response.data.data);
      } else {
        setError(response.error || 'Failed to load bonuses');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load bonuses on component mount
  useEffect(() => {
    loadBonuses();
  }, [loadBonuses]);

  // Utility functions
  const formatDate = (timestamp: string) => {
    if (!timestamp) return 'N/A';
    const date = new Date(parseInt(timestamp) * 1000);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isActive
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        <Activity className="w-3 h-3 mr-1" />
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  const getProductBadge = (product: string) => {
    const colors = {
      casino: 'bg-purple-500/20 text-purple-400 border-purple-500/30',
      sportsbook: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      poker: 'bg-orange-500/20 text-orange-400 border-orange-500/30'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${colors[product as keyof typeof colors] || 'bg-gray-500/20 text-gray-400 border-gray-500/30'}`}>
        {product}
      </span>
    );
  };

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadBonuses(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadBonuses(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadBonuses(1, itemsPerPage, newFilters);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (bonus: BonusData) => {
    navigate(`/bonuses/view/${bonus.id}`);
  };

  const handleEdit = (bonusId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/bonuses/edit/${bonusId}`);
  };

  const handleView = (bonusId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/bonuses/view/${bonusId}`);
  };

  const handleDelete = async (bonusId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm('Are you sure you want to delete this bonus?')) return;

    try {
      const response = await deleteBonus(bonusId);

      if (response.success) {
        loadBonuses(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to delete bonus');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete bonus');
    }
  };

  const handleDeactivate = async (bonusId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm('Are you sure you want to deactivate this bonus?')) return;

    try {
      const response = await deactivateBonus(bonusId);

      if (response.success) {
        loadBonuses(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to deactivate bonus');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deactivate bonus');
    }
  };

  // Table configuration
  const tableColumns: TableColumn<BonusData>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (bonus) => <span className="font-mono text-sm">#{bonus.id}</span>
    },
    {
      key: 'name',
      label: 'Name',
      width: '200px',
      sortable: true,
      render: (bonus) => (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-400" />
          <span className="font-medium text-sm">{bonus.name}</span>
        </div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      width: '250px',
      sortable: false,
      render: (bonus) => (
        <span className="text-sm text-gray-300 max-w-xs truncate block">
          {bonus.description || 'No description'}
        </span>
      )
    },
    {
      key: 'model',
      label: 'Model',
      width: '120px',
      sortable: true,
      render: (bonus) => <span className="text-sm">{bonus.model}</span>
    },
    {
      key: 'product',
      label: 'Product',
      width: '120px',
      sortable: true,
      render: (bonus) => getProductBadge(bonus.product)
    },
    {
      key: 'currency',
      label: 'Currency',
      width: '100px',
      sortable: true,
      render: (bonus) => {
        // Handle both list view (currency object) and single view (currency string)
        const currencyName = typeof bonus.currency === 'string'
          ? bonus.currency
          : bonus.currency?.name || 'N/A';

        return (
          <div className="flex items-center gap-2">
            <DollarSign className="w-4 h-4 text-gray-400" />
            <span className="text-sm">{currencyName}</span>
          </div>
        );
      }
    },
    {
      key: 'operator',
      label: 'Operator',
      width: '150px',
      sortable: true,
      render: (bonus) => {
        // Show operator name if available (list view), otherwise show operator ID
        if (bonus.operator?.name) {
          return <span className="text-sm">{bonus.operator.name}</span>;
        }
        return <span className="text-sm font-mono">#{bonus.operator_id}</span>;
      }
    },
    {
      key: 'is_active',
      label: 'Status',
      width: '100px',
      sortable: true,
      render: (bonus) => getStatusBadge(bonus.is_active)
    },
    {
      key: 'from',
      label: 'Start Date',
      width: '160px',
      sortable: true,
      render: (bonus) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm whitespace-nowrap">{formatDate(bonus.from)}</span>
        </div>
      )
    },
    {
      key: 'to',
      label: 'End Date',
      width: '160px',
      sortable: true,
      render: (bonus) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm whitespace-nowrap">{formatDate(bonus.to)}</span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '160px',
      sortable: false,
      render: (bonus) => (
        <div className="flex gap-1">
          <button
            onClick={(e) => handleView(bonus.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-blue-400 transition-colors"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={(e) => handleEdit(bonus.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500 transition-colors"
            title="Edit Bonus"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => handleDeactivate(bonus.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-orange-400 transition-colors"
            title="Deactivate Bonus"
          >
            <Ban size={16} />
          </button>
          <button
            onClick={(e) => handleDelete(bonus.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-red-400 transition-colors"
            title="Delete Bonus"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  // Filter configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Information',
      fields: [
        {
          key: 'name',
          label: 'Name',
          type: 'text',
          placeholder: 'Search by bonus name...'
        },
        {
          key: 'description',
          label: 'Description',
          type: 'text',
          placeholder: 'Search by description...'
        },
        {
          key: 'model',
          label: 'Model',
          type: 'text',
          placeholder: 'Search by model...'
        }
      ]
    },
    {
      name: 'classification',
      displayName: 'Classification',
      fields: [
        {
          key: 'product',
          label: 'Product',
          type: 'select',
          options: [
            { value: 'casino', label: 'Casino' },
            { value: 'sportsbook', label: 'Sportsbook' },
            { value: 'poker', label: 'Poker' }
          ]
        },
        {
          key: 'is_active',
          label: 'Status',
          type: 'select',
          options: [
            { value: 'true', label: 'Active' },
            { value: 'false', label: 'Inactive' }
          ]
        },
        {
          key: 'currency',
          label: 'Currency',
          type: 'text',
          placeholder: 'e.g., USD, EUR...'
        }
      ]
    },
    {
      name: 'dates',
      displayName: 'Date Range',
      fields: [
        {
          key: 'from_start',
          label: 'Start Date From',
          type: 'date'
        },
        {
          key: 'from_end',
          label: 'Start Date To',
          type: 'date'
        },
        {
          key: 'to_start',
          label: 'End Date From',
          type: 'date'
        },
        {
          key: 'to_end',
          label: 'End Date To',
          type: 'date'
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Dices className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Bonus Management</h1>
            <p className="text-gray-400">Manage casino bonuses and promotions</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => loadBonuses(currentPage, itemsPerPage, filters)}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>

          <Button
            onClick={() => navigate('/bonuses/create')}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Bonus
          </Button>
        </div>
      </div>

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Bonuses</h4>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={bonusesData?.data || []}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={bonusesData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Bonuses"
        emptyState={{
          icon: <Tag className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No bonuses found',
          description: 'Get started by creating your first bonus.',
          action: (
            <Button
              onClick={() => navigate('/bonuses/create')}
              variant="primary"
            >
              Create Bonus
            </Button>
          )
        }}
      />
    </div>
  );
};

export default Bonuses;
