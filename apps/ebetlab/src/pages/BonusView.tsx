import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, AlertCircle, Dices, Calendar, DollarSign, Tag, Activity, User, Settings } from 'lucide-react';
import Button from '../components/ui/Button';
import { fetchBonusDetails, deleteBonus, type BonusData } from '../utils/api';

const BonusView: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [bonus, setBonus] = useState<BonusData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const loadBonus = async () => {
      if (!id) {
        setError('Bonus ID is required');
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetchBonusDetails(id);

        if (response.success && response.data) {
          setBonus(response.data.data);
        } else {
          setError(response.error || 'Failed to load bonus details');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load bonus details');
      } finally {
        setIsLoading(false);
      }
    };

    loadBonus();
  }, [id]);

  const formatDate = (timestamp: string) => {
    if (!timestamp) return 'N/A';
    return new Date(parseInt(timestamp) * 1000).toLocaleString();
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          isActive
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        <Activity className="w-4 h-4 mr-2" />
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  const handleDelete = async () => {
    if (!bonus || !window.confirm('Are you sure you want to delete this bonus?')) return;

    try {
      const response = await deleteBonus(bonus.id);
      
      if (response.success) {
        navigate('/bonuses');
      } else {
        setError(response.error || 'Failed to delete bonus');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete bonus');
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate('/bonuses')}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Bonuses
          </Button>
        </div>
        
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
          <p className="text-gray-400 mt-2">Loading bonus details...</p>
        </div>
      </div>
    );
  }

  if (error || !bonus) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate('/bonuses')}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Bonuses
          </Button>
        </div>
        
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error</h4>
              <p className="text-red-400 text-sm">{error || 'Bonus not found'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate('/bonuses')}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Bonuses
          </Button>
          
          <div className="flex items-center gap-3">
            <Dices className="w-8 h-8 text-primary-500" />
            <div>
              <h1 className="text-2xl font-bold text-gray-100">{bonus.name}</h1>
              <p className="text-gray-400">Bonus Details</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={() => navigate(`/bonuses/edit/${bonus.id}`)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Edit className="w-4 h-4" />
            Edit
          </Button>
          
          <Button
            onClick={handleDelete}
            variant="outline"
            className="flex items-center gap-2 text-red-400 border-red-500/30 hover:bg-red-500/10"
          >
            <Trash2 className="w-4 h-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Bonus Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4 flex items-center gap-2">
            <Tag className="w-5 h-5" />
            Basic Information
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">ID</label>
              <span className="text-gray-200 font-mono">#{bonus.id}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Name</label>
              <span className="text-gray-200">{bonus.name}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Description</label>
              <span className="text-gray-200">{bonus.description || 'No description'}</span>
            </div>

            {bonus.note && (
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">Note</label>
                <span className="text-gray-200">{bonus.note}</span>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Model</label>
              <span className="text-gray-200">{bonus.model}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Product</label>
              <span className="text-gray-200 capitalize">{bonus.product}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Typeable Type</label>
              <span className="text-gray-200">{bonus.typeable_type}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
              {getStatusBadge(bonus.is_active)}
            </div>
          </div>
        </div>

        {/* Financial & System Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4 flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Financial & System
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Currency</label>
              <span className="text-gray-200">{bonus.currency}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Currency ID</label>
              <span className="text-gray-200 font-mono">{bonus.currency_id}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Merchant ID</label>
              <span className="text-gray-200 font-mono">{bonus.merchant_id}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Website ID</label>
              <span className="text-gray-200 font-mono">{bonus.website_id}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Operator ID</label>
              <span className="text-gray-200 font-mono">{bonus.operator_id}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Block Balance</label>
              <span className="text-gray-200">{bonus.block_balance}</span>
            </div>
          </div>
        </div>

        {/* Date & Expiration Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4 flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Date & Expiration
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Start Date</label>
              <span className="text-gray-200">{formatDate(bonus.from)}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">End Date</label>
              <span className="text-gray-200">{formatDate(bonus.to)}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Created</label>
              <span className="text-gray-200">{formatDate(bonus.timestamp)}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Expire Type</label>
              <span className="text-gray-200 capitalize">{bonus.expire_type}</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Expire Unit</label>
              <span className="text-gray-200">{bonus.expire_unit}</span>
            </div>
          </div>
        </div>

        {/* Settings */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4 flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Settings
          </h3>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Phone Verification</span>
              <span className={`text-sm ${bonus.phone_verification ? 'text-green-400' : 'text-gray-500'}`}>
                {bonus.phone_verification ? 'Required' : 'Not Required'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-400">Email Verification</span>
              <span className={`text-sm ${bonus.email_verification ? 'text-green-400' : 'text-gray-500'}`}>
                {bonus.email_verification ? 'Required' : 'Not Required'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-400">Requestable</span>
              <span className={`text-sm ${bonus.request_able ? 'text-green-400' : 'text-gray-500'}`}>
                {bonus.request_able ? 'Yes' : 'No'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-400">Disable Withdraw</span>
              <span className={`text-sm ${bonus.disable_withdraw ? 'text-red-400' : 'text-green-400'}`}>
                {bonus.disable_withdraw ? 'Yes' : 'No'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-400">Cancelable</span>
              <span className={`text-sm ${bonus.cancel_able ? 'text-green-400' : 'text-gray-500'}`}>
                {bonus.cancel_able ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Typeable Information */}
      {bonus.typeable && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4 flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Bonus Details ({bonus.model})
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              {bonus.typeable.quantity && (
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Quantity</label>
                  <span className="text-gray-200">{bonus.typeable.quantity}</span>
                </div>
              )}

              {bonus.typeable.bet_level && (
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Bet Level</label>
                  <span className="text-gray-200">{bonus.typeable.bet_level}</span>
                </div>
              )}

              {bonus.typeable.max_win && (
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Max Win</label>
                  <span className="text-gray-200">{bonus.typeable.max_win}</span>
                </div>
              )}

              {bonus.typeable.game_merchant_name && (
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Game Provider</label>
                  <span className="text-gray-200">{bonus.typeable.game_merchant_name}</span>
                </div>
              )}
            </div>

            <div className="space-y-4">
              {bonus.typeable.wager_multiplier && (
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Wager Multiplier</label>
                  <span className="text-gray-200">{bonus.typeable.wager_multiplier}</span>
                </div>
              )}

              {bonus.typeable.allowed_min_usd && (
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Min USD</label>
                  <span className="text-gray-200">{bonus.typeable.allowed_min_usd}</span>
                </div>
              )}

              {bonus.typeable.allowed_max_usd && (
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Max USD</label>
                  <span className="text-gray-200">{bonus.typeable.allowed_max_usd}</span>
                </div>
              )}

              {bonus.typeable.game_merchant && (
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Provider Name</label>
                  <span className="text-gray-200">{bonus.typeable.game_merchant.name}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Games Information */}
      {bonus.games && bonus.games.length > 0 && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Associated Games</h3>

          <div className="space-y-3">
            {bonus.games.map((game, index) => (
              <div key={game.id} className="flex items-center justify-between p-3 bg-dark-800 rounded-md">
                <div>
                  <span className="text-gray-200 font-medium">Game ID: {game.id}</span>
                </div>
                <div>
                  <span className="text-primary-400 font-medium">{game.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BonusView;
