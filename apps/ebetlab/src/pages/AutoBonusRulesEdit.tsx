import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Settings, ArrowLeft, Plus, Trash2, AlertCircle, CheckCircle } from 'lucide-react';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { useAppContext } from '../contexts/AppContext';
import { fetchBonuses, fetchAutoBonusRuleDetails, type BonusData } from '../utils/api';
import { getAuthHeaders } from '../utils/api/common';

interface FilterRule {
  field: string;
  operator: string;
  value: string;
}

interface FormData {
  bonus_id: number | null;
  currency: string;
  reference_tag: string;
  bonus_code: string;
  total: string;
  rules: FilterRule[];
}

const AVAILABLE_FIELDS = [
  { label: "Select Field", value: "" },
  { label: "Ref Code", value: "refCode" },
  { label: "Vip Rank", value: "rank" },
  { label: "KYC Verification Level", value: "kyc" },
  { label: "Registration", value: "registration" },
  { label: "Deposit Amount", value: "depositAmount" },
  { label: "Deposit Currency", value: "depositCurrency" },
  { label: "Deposit Date", value: "depositDate" },
  { label: "First Time Deposit Amount", value: "ftdAmount" },
  { label: "First Time Deposit Currency", value: "ftdCurrency" },
  { label: "First Time Deposit Date", value: "ftdDate" },
  { label: "Total Deposit Amount($)", value: "totalDeposit" },
  { label: "Total Withdraw Amount($)", value: "totalWithdraw" },
  { label: "Last Day USD Turnover", value: "lastDayTurnoverUSD" },
  { label: "Last Week USD Turnover", value: "lastWeekTurnoverUSD" },
  { label: "Last Month USD Turnover", value: "lastMonthTurnoverUSD" },
  { label: "Last Day USD Deposit", value: "lastDayDeposit" },
  { label: "Last Week USD Deposit", value: "lastWeekDeposit" },
  { label: "Last Month USD Deposit", value: "lastMonthDeposit" },
  { label: "Last Day USD Withdraw", value: "lastDayWithdraw" },
  { label: "Last Week USD Withdraw", value: "lastWeekWithdraw" },
  { label: "Last Month USD Withdraw", value: "lastMonthWithdraw" },
  { label: "Last Day USD Net", value: "lastDayNet" },
  { label: "Last Week USD Net", value: "lastWeekNet" },
  { label: "Last Month USD Net", value: "lastMonthNet" },
  { label: "Birthday Bonus", value: "birthday" }
];

const AVAILABLE_OPERATORS = [
  { label: "Select an Operator", value: "" },
  { label: ">", value: ">" },
  { label: "<", value: "<" },
  { label: ">=", value: ">=" },
  { label: "<=", value: "<=" },
  { label: "=", value: "=" }
];

const AutoBonusRulesEdit: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { configurations } = useAppContext();
  const [bonuses, setBonuses] = useState<BonusData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState<FormData>({
    bonus_id: null,
    currency: '',
    reference_tag: '',
    bonus_code: '',
    total: '',
    rules: [{ field: '', operator: '', value: '' }]
  });

  // Get currencies from configurations
  const currencies = configurations?.currencies || [];

  // Load bonuses and rule details on component mount
  useEffect(() => {
    const loadData = async () => {
      setIsLoadingData(true);
      try {
        // Load bonuses first
        const bonusesResponse = await fetchBonuses(1, 100);
        let loadedBonuses: BonusData[] = [];
        if (bonusesResponse.success && bonusesResponse.data) {
          loadedBonuses = bonusesResponse.data.data.data;
          setBonuses(loadedBonuses);
        }

        // Load rule details if ID is provided
        if (id) {
          const ruleResponse = await fetchAutoBonusRuleDetails(id);
          if (ruleResponse.success && ruleResponse.data) {
            const ruleData = ruleResponse.data.data;

            // Find the bonus in the loaded bonuses list to ensure it exists
            const selectedBonus = loadedBonuses.find(bonus => bonus.id === ruleData.bonus_id);

            setFormData({
              bonus_id: selectedBonus ? ruleData.bonus_id : null,
              currency: ruleData.currency || '',
              reference_tag: ruleData.reference_tag || '',
              bonus_code: ruleData.bonus_code,
              total: ruleData.total.toString(),
              rules: ruleData.rules.length > 0 ? ruleData.rules : [{ field: '', operator: '', value: '' }]
            });

            if (!selectedBonus) {
              setError(`Warning: The selected bonus (ID: ${ruleData.bonus_id}) is not available in the current bonus list.`);
            }
          } else {
            setError('Failed to load auto bonus rule details');
          }
        }
      } catch (err) {
        setError('Failed to load data');
        console.error('Error loading data:', err);
      } finally {
        setIsLoadingData(false);
      }
    };

    loadData();
  }, [id]);

  // Handle form field changes
  const handleInputChange = (field: keyof FormData, value: string | number | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle rule changes
  const handleRuleChange = (index: number, field: keyof FilterRule, value: string) => {
    setFormData(prev => ({
      ...prev,
      rules: prev.rules.map((rule, i) => 
        i === index ? { ...rule, [field]: value } : rule
      )
    }));
  };

  // Add new rule
  const addRule = () => {
    setFormData(prev => ({
      ...prev,
      rules: [...prev.rules, { field: '', operator: '', value: '' }]
    }));
  };

  // Remove rule
  const removeRule = (index: number) => {
    if (formData.rules.length > 1) {
      setFormData(prev => ({
        ...prev,
        rules: prev.rules.filter((_, i) => i !== index)
      }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    if (!formData.bonus_id) {
      setError('Please select a bonus');
      return false;
    }
    if (!formData.currency) {
      setError('Please select a currency');
      return false;
    }
    if (!formData.bonus_code.trim()) {
      setError('Please enter a bonus code');
      return false;
    }
    if (!formData.total.trim()) {
      setError('Please enter total amount');
      return false;
    }
    
    // Validate rules
    for (let i = 0; i < formData.rules.length; i++) {
      const rule = formData.rules[i];
      if (!rule.field || !rule.operator || !rule.value.trim()) {
        setError(`Please complete rule ${i + 1}`);
        return false;
      }
    }

    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const requestBody = {
        bonus_code: formData.bonus_code,
        rules: formData.rules,
        bonus_id: formData.bonus_id,
        currency: formData.currency,
        reference_tag: formData.reference_tag,
        total: formData.total
      };

      const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/auto-bonus-rules/${id}`, {
        method: 'PATCH',
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setSuccess('Auto bonus rule updated successfully!');
        setTimeout(() => {
          navigate('/auto-bonus-rules/list');
        }, 2000);
      } else {
        setError(result.message || 'Failed to update auto bonus rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update auto bonus rule');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading auto bonus rule...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          onClick={() => navigate('/auto-bonus-rules/list')}
          variant="ghost"
          size="sm"
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        
        <div className="flex items-center gap-3">
          <Settings className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Edit Auto Bonus Rule</h1>
            <p className="text-gray-400">Update automatic bonus rule assignment</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
          <span className="text-red-200">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 flex items-center gap-3">
          <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
          <span className="text-green-200">{success}</span>
        </div>
      )}

      {/* Form */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bonus <span className="text-red-400">*</span>
              </label>
              <select
                value={formData.bonus_id?.toString() || ''}
                onChange={(e) => handleInputChange('bonus_id', e.target.value ? parseInt(e.target.value) : null)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                required
              >
                <option value="">Select a bonus</option>
                {bonuses.map(bonus => (
                  <option key={bonus.id} value={bonus.id.toString()}>
                    {bonus.name} ({typeof bonus.currency === 'string' ? bonus.currency : bonus.currency?.name || 'N/A'})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Currency <span className="text-red-400">*</span>
              </label>
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange('currency', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                required
              >
                <option value="">Select currency</option>
                {currencies.map(currency => (
                  <option key={currency} value={currency}>
                    {currency}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bonus Code <span className="text-red-400">*</span>
              </label>
              <Input
                type="text"
                value={formData.bonus_code}
                onChange={(e) => handleInputChange('bonus_code', e.target.value)}
                placeholder="Enter bonus code"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Total Amount <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                value={formData.total}
                onChange={(e) => handleInputChange('total', e.target.value)}
                placeholder="Enter total amount"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Reference Tag
              </label>
              <Input
                type="text"
                value={formData.reference_tag}
                onChange={(e) => handleInputChange('reference_tag', e.target.value)}
                placeholder="Enter reference tag (optional)"
              />
            </div>
          </div>

          {/* Rules Section */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-200">Filter Rules</h3>
              <Button
                type="button"
                onClick={addRule}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Rule
              </Button>
            </div>

            <div className="space-y-4">
              {formData.rules.map((rule, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-dark-800 rounded-lg border border-dark-600">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Field <span className="text-red-400">*</span>
                    </label>
                    <select
                      value={rule.field}
                      onChange={(e) => handleRuleChange(index, 'field', e.target.value)}
                      className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      required
                    >
                      {AVAILABLE_FIELDS.map(field => (
                        <option key={field.value} value={field.value}>
                          {field.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Operator <span className="text-red-400">*</span>
                    </label>
                    <select
                      value={rule.operator}
                      onChange={(e) => handleRuleChange(index, 'operator', e.target.value)}
                      className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      required
                    >
                      {AVAILABLE_OPERATORS.map(operator => (
                        <option key={operator.value} value={operator.value}>
                          {operator.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Value <span className="text-red-400">*</span>
                    </label>
                    <Input
                      type="text"
                      value={rule.value}
                      onChange={(e) => handleRuleChange(index, 'value', e.target.value)}
                      placeholder="Enter value"
                    />
                  </div>

                  <div className="flex items-end">
                    <Button
                      type="button"
                      onClick={() => removeRule(index)}
                      variant="ghost"
                      size="sm"
                      className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                      disabled={formData.rules.length === 1}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              onClick={() => navigate('/auto-bonus-rules/list')}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
            >
              {isLoading ? 'Updating...' : 'Update Rule'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AutoBonusRulesEdit;
