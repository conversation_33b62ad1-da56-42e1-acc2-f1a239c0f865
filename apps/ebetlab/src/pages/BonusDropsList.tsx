import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Gift, RefreshCw, AlertCircle, Calendar, User, Tag, Hash, Activity, Edit, Trash2, Eye, Ban, DollarSign, Plus } from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import { 
  fetchBonusDrops, 
  deactivateBonusDrop, 
  deleteBonusDrop, 
  type BonusDropListItem 
} from '../utils/api';

interface BonusDropsData {
  total: number;
  data: BonusDropListItem[];
}

const BonusDropsList: React.FC = () => {
  const navigate = useNavigate();
  const [bonusDropsData, setBonusDropsData] = useState<BonusDropsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sortState, setSortState] = useState<SortState>({
    field: 'id',
    direction: 'desc'
  });

  const loadBonusDrops = useCallback(async (page: number = currentPage, limit: number = itemsPerPage, searchFilters: Record<string, unknown> = filters) => {
    setIsLoading(true);
    setError('');

    try {
      // Include sorting parameters in the request
      const searchParamsWithSort = {
        ...searchFilters,
        sortBy: sortState.field,
        direction: sortState.direction
      };

      const response = await fetchBonusDrops(page, limit, searchParamsWithSort);

      if (response.success && response.data) {
        setBonusDropsData(response.data.data);
      } else {
        setError(response.error || 'Failed to load bonus drops');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load bonus drops on component mount
  useEffect(() => {
    loadBonusDrops();
  }, [loadBonusDrops]);

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadBonusDrops(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadBonusDrops(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadBonusDrops(1, itemsPerPage, newFilters);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (bonusDrop: BonusDropListItem) => {
    navigate(`/bonus-drops/details/${bonusDrop.id}`);
  };

  // Handle view details
  const handleView = (bonusDropId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/bonus-drops/details/${bonusDropId}`);
  };

  // Handle edit
  const handleEdit = (bonusDropId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/bonus-drops/edit/${bonusDropId}`);
  };

  // Handle deactivate
  const handleDeactivate = async (bonusDropId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm('Are you sure you want to deactivate this bonus drop?')) return;

    try {
      const response = await deactivateBonusDrop(bonusDropId);

      if (response.success) {
        loadBonusDrops(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to deactivate bonus drop');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deactivate bonus drop');
    }
  };

  // Handle delete
  const handleDelete = async (bonusDropId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm('Are you sure you want to delete this bonus drop?')) return;

    try {
      const response = await deleteBonusDrop(bonusDropId);

      if (response.success) {
        loadBonusDrops(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to delete bonus drop');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete bonus drop');
    }
  };

  // Format timestamp
  const formatDate = (timestamp: number | string) => {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    return new Date(ts * 1000).toLocaleString();
  };

  // Format rules array
  const formatRules = (rules: Array<{ field: string; value: string | number; operator: string }>) => {
    return rules.map(rule => `${rule.field} ${rule.operator} ${rule.value}`).join(', ');
  };

  // Get status badge
  const getStatusBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isActive
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        <Activity className="w-3 h-3 mr-1" />
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  // Table columns configuration
  const tableColumns: TableColumn<BonusDropListItem>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (bonusDrop: BonusDropListItem) => (
        <span className="font-mono text-sm">#{bonusDrop.id}</span>
      )
    },
    {
      key: 'code',
      label: 'Code',
      width: '150px',
      sortable: true,
      render: (bonusDrop: BonusDropListItem) => (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-mono text-gray-200">{bonusDrop.code}</span>
        </div>
      )
    },
    {
      key: 'operator',
      label: 'Operator',
      width: '120px',
      sortable: true,
      render: (bonusDrop: BonusDropListItem) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{bonusDrop.operator.name}</span>
        </div>
      )
    },
    {
      key: 'amount',
      label: 'Amount',
      width: '120px',
      sortable: true,
      render: (bonusDrop: BonusDropListItem) => (
        <div className="flex items-center gap-2">
          <DollarSign className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium">{bonusDrop.amount} {bonusDrop.currency_code}</span>
        </div>
      )
    },
    {
      key: 'count',
      label: 'Count',
      width: '100px',
      sortable: true,
      render: (bonusDrop: BonusDropListItem) => (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium">{bonusDrop.count.toLocaleString()}</span>
        </div>
      )
    },
    {
      key: 'redeems_count',
      label: 'Redeems',
      width: '100px',
      sortable: true,
      render: (bonusDrop: BonusDropListItem) => (
        <span className="text-sm font-medium">{bonusDrop.redeems_count}</span>
      )
    },
    {
      key: 'required_wager',
      label: 'Required Wager',
      width: '120px',
      sortable: true,
      render: (bonusDrop: BonusDropListItem) => (
        <span className="text-sm font-medium">{bonusDrop.required_wager}</span>
      )
    },
    {
      key: 'rules',
      label: 'Rules',
      width: '200px',
      sortable: false,
      render: (bonusDrop: BonusDropListItem) => (
        <div className="text-sm text-gray-300">
          <div className="truncate" title={formatRules(bonusDrop.rules)}>
            {formatRules(bonusDrop.rules)}
          </div>
        </div>
      )
    },
    {
      key: 'is_active',
      label: 'Status',
      width: '100px',
      sortable: true,
      render: (bonusDrop: BonusDropListItem) => getStatusBadge(bonusDrop.is_active)
    },
    {
      key: 'timestamp',
      label: 'Created',
      width: '150px',
      sortable: true,
      render: (bonusDrop: BonusDropListItem) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{formatDate(bonusDrop.timestamp)}</span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '200px',
      sortable: false,
      render: (bonusDrop: BonusDropListItem) => (
        <div className="flex gap-1">
          <button
            onClick={(e) => handleView(bonusDrop.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-blue-400 transition-colors"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={(e) => handleEdit(bonusDrop.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500 transition-colors"
            title="Edit Bonus Drop"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => handleDeactivate(bonusDrop.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-orange-400 transition-colors"
            title="Deactivate Bonus Drop"
          >
            <Ban size={16} />
          </button>
          <button
            onClick={(e) => handleDelete(bonusDrop.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-red-400 transition-colors"
            title="Delete Bonus Drop"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  // Filter configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Information',
      fields: [
        {
          key: 'id',
          label: 'Bonus Drop ID',
          type: 'text',
          placeholder: 'Search by bonus drop ID...'
        },
        {
          key: 'code',
          label: 'Code',
          type: 'text',
          placeholder: 'Search by code...'
        },
        {
          key: 'currency_code',
          label: 'Currency',
          type: 'text',
          placeholder: 'Search by currency...'
        },
        {
          key: 'operator_id',
          label: 'Operator ID',
          type: 'text',
          placeholder: 'Search by operator ID...'
        }
      ]
    },
    {
      name: 'status',
      displayName: 'Status & Activity',
      fields: [
        {
          key: 'is_active',
          label: 'Status',
          type: 'select',
          options: [
            { value: 'true', label: 'Active' },
            { value: 'false', label: 'Inactive' }
          ]
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Gift className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Bonus Drops</h1>
            <p className="text-gray-400">Manage bonus drop assignments</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => loadBonusDrops(currentPage, itemsPerPage, filters)}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>

          <Button
            onClick={() => navigate('/bonus-drops/create')}
            variant="primary"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Bonus Drop
          </Button>
        </div>
      </div>

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Bonus Drops</h4>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={bonusDropsData?.data || []}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={bonusDropsData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Bonus Drops"
        emptyState={{
          icon: <Gift className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No bonus drops found',
          description: 'No bonus drops match your current criteria.',
          action: undefined
        }}
      />
    </div>
  );
};

export default BonusDropsList;
