import React, { useState, useEffect } from 'react';
import { Settings, AlertCircle, RefreshCw } from 'lucide-react';
import Button from '../components/ui/Button';
import CssSettingsTable from '../components/css-settings/CssSettingsTable';
import { fetchCssSettings, type PaginationParams } from '../utils/api';

interface CssSettingsData {
  total: number;
  data: Array<{
    id: number;
    operator_id: number;
    last_update: number;
    theme: string;
    is_active: boolean;
    name: string;
    operator: {
      id: number;
      name: string;
    };
  }>;
}

const CssSettings: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [cssSettingsData, setCssSettingsData] = useState<CssSettingsData | null>(null);
  const [error, setError] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const loadCssSettings = async (page: number = currentPage, limit: number = itemsPerPage) => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetchCssSettings({ page, limit });

      if (response.success && response.data) {
        setCssSettingsData(response.data);
      } else {
        setError(response.error || 'Failed to load CSS settings');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadCssSettings(page, itemsPerPage);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
    loadCssSettings(1, newItemsPerPage);
  };

  // Auto-load CSS settings on page visit
  useEffect(() => {
    loadCssSettings();
  }, []);

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center">
            <Settings className="w-6 h-6 text-dark-800" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-100">CSS Settings</h1>
            <p className="text-gray-400">Manage and configure CSS settings for your website</p>
          </div>
        </div>
      </div>

      {/* Refresh Section */}
      {!isLoading && cssSettingsData && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">Last updated: {new Date().toLocaleString()}</span>
            </div>
            <Button
              onClick={() => loadCssSettings(currentPage, itemsPerPage)}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </Button>
          </div>
        </div>
      )}

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading CSS Settings</h4>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* CSS Settings Table */}
      {cssSettingsData && (
        <CssSettingsTable
          settings={cssSettingsData.data}
          total={cssSettingsData.total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
          isLoading={isLoading}
        />
      )}

      {/* Loading State */}
      {isLoading && !cssSettingsData && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-12">
          <div className="flex flex-col items-center justify-center text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mb-4"></div>
            <h3 className="text-lg font-medium text-gray-200 mb-2">Loading CSS Settings</h3>
            <p className="text-gray-400">Please wait while we fetch your CSS settings...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default CssSettings;
