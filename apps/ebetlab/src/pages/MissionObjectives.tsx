import React, { useState, useEffect, useCallback } from 'react';
import { Target, Plus, Edit, Trash2, RefreshCw } from 'lucide-react';
import {
  fetchMissionObjectives,
  createMissionObjective,
  updateMissionObjective,
  deleteMissionObjective,
  MissionObjective,
  CreateMissionObjectiveRequest,
  UpdateMissionObjectiveRequest,
  OBJECTIVE_TYPE_OPTIONS,
  OBJECTIVE_OPERATOR_OPTIONS,
  OBJECTIVE_SUBTYPE_OPTIONS,
  getSubtypeOptions,
  MISSION_OBJECTIVES_SORT_FIELDS,
  MissionObjectivesQueryParams
} from '../utils/api/mission-objectives';
import DataTable from '../components/ui/DataTable';
import Modal from '../components/ui/Modal';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import MissionObjectivesFilter from '../components/mission-objectives/MissionObjectivesFilter';

interface MissionObjectivesData {
  total: number;
  data: MissionObjective[];
}

interface MissionObjectiveFormData {
  missionId: number;
  objectiveType: string;
  subtype: string;
  operator: string;
  targetValue: string;
  description: string;
  timeframeStartDuration: string;
  timeframeStartUnit: 'minutes' | 'hours' | 'days';
  timeframeEndDuration: string;
  timeframeEndUnit: 'minutes' | 'hours' | 'days';
}

const MissionObjectives: React.FC = () => {
  // Data states
  const [missionObjectivesData, setMissionObjectivesData] = useState<MissionObjectivesData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<any>({});
  const [sortState, setSortState] = useState<{ field: string; direction: 'asc' | 'desc' }>({
    field: 'createdAt',
    direction: 'desc'
  });

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedObjective, setSelectedObjective] = useState<MissionObjective | null>(null);

  // Form data
  const [formData, setFormData] = useState<MissionObjectiveFormData>({
    missionId: 0,
    objectiveType: 'slot',
    subtype: 'total_wins',
    operator: 'ge',
    targetValue: '',
    description: '',
    timeframeStartDuration: '',
    timeframeStartUnit: 'hours',
    timeframeEndDuration: '',
    timeframeEndUnit: 'hours'
  });

  // Load mission objectives
  const loadMissionObjectives = useCallback(async (
    page: number = currentPage,
    limit: number = itemsPerPage,
    currentFilters: any = filters
  ) => {
    setIsLoading(true);
    setError('');

    try {
      // Build query parameters
      const queryParams: MissionObjectivesQueryParams = {
        page,
        limit,
        sortBy: sortState.field,
        sortOrder: sortState.direction === 'asc' ? 'ASC' : 'DESC',
        ...currentFilters
      };

      const response = await fetchMissionObjectives(queryParams);

      if (response.success && response.data) {
        setMissionObjectivesData({
          total: response.meta.total,
          data: response.data
        });
      } else {
        setError(response.error || 'Failed to load mission objectives');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadMissionObjectives();
  }, [loadMissionObjectives]);

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadMissionObjectives(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadMissionObjectives(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadMissionObjectives(1, itemsPerPage, newFilters);
  };

  const handleClearFilters = () => {
    setFilters({});
    setCurrentPage(1);
    loadMissionObjectives(1, itemsPerPage, {});
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (objective: MissionObjective) => {
    setSelectedObjective(objective);

    // Convert existing timeframe to offset format
    let timeframeStartDuration = '';
    let timeframeStartUnit: 'minutes' | 'hours' | 'days' = 'hours';
    let timeframeEndDuration = '';
    let timeframeEndUnit: 'minutes' | 'hours' | 'days' = 'hours';

    const now = Date.now();

    if (objective.timeframeStart) {
      const startOffsetMs = objective.timeframeStart - now;
      const startOffsetSeconds = Math.max(0, Math.floor(startOffsetMs / 1000));

      // Convert to the most appropriate unit
      if (startOffsetSeconds % (24 * 60 * 60) === 0) {
        timeframeStartDuration = String(startOffsetSeconds / (24 * 60 * 60));
        timeframeStartUnit = 'days';
      } else if (startOffsetSeconds % (60 * 60) === 0) {
        timeframeStartDuration = String(startOffsetSeconds / (60 * 60));
        timeframeStartUnit = 'hours';
      } else {
        timeframeStartDuration = String(Math.floor(startOffsetSeconds / 60));
        timeframeStartUnit = 'minutes';
      }
    }

    if (objective.timeframeEnd) {
      const endOffsetMs = objective.timeframeEnd - now;
      const endOffsetSeconds = Math.max(0, Math.floor(endOffsetMs / 1000));

      // Convert to the most appropriate unit
      if (endOffsetSeconds % (24 * 60 * 60) === 0) {
        timeframeEndDuration = String(endOffsetSeconds / (24 * 60 * 60));
        timeframeEndUnit = 'days';
      } else if (endOffsetSeconds % (60 * 60) === 0) {
        timeframeEndDuration = String(endOffsetSeconds / (60 * 60));
        timeframeEndUnit = 'hours';
      } else {
        timeframeEndDuration = String(Math.floor(endOffsetSeconds / 60));
        timeframeEndUnit = 'minutes';
      }
    }

    setFormData({
      missionId: objective.missionId,
      objectiveType: objective.objectiveType,
      subtype: objective.subtype || '',
      operator: objective.operator,
      targetValue: objective.targetValue,
      description: objective.description,
      timeframeStartDuration,
      timeframeStartUnit,
      timeframeEndDuration,
      timeframeEndUnit
    });
    setShowEditModal(true);
  };

  // Form handlers
  const resetForm = () => {
    setFormData({
      missionId: 0,
      objectiveType: 'slot',
      subtype: 'total_wins',
      operator: 'ge',
      targetValue: '',
      description: '',
      timeframeStartDuration: '',
      timeframeStartUnit: 'hours',
      timeframeEndDuration: '',
      timeframeEndUnit: 'hours'
    });
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // When objective type changes, reset subtype to first available option
      if (field === 'objectiveType') {
        const subtypeOptions = getSubtypeOptions(value as any);
        newData.subtype = subtypeOptions.length > 0 ? subtypeOptions[0].value : '';
      }

      return newData;
    });
  };

  const convertTimeframeToSeconds = (duration: string, unit: 'minutes' | 'hours' | 'days'): number | null => {
    if (!duration) return null;

    const durationNum = Number(duration);
    if (isNaN(durationNum) || durationNum <= 0) return null;

    const multipliers = {
      minutes: 60,
      hours: 60 * 60,
      days: 60 * 60 * 24
    };

    return durationNum * multipliers[unit];
  };

  const handleCreate = async () => {
    try {
      const now = Date.now();

      // Calculate timeframe start (offset from now)
      const startOffsetSeconds = convertTimeframeToSeconds(formData.timeframeStartDuration, formData.timeframeStartUnit);
      const timeframeStart = startOffsetSeconds ? now + (startOffsetSeconds * 1000) : null;

      // Calculate timeframe end (offset from now)
      const endOffsetSeconds = convertTimeframeToSeconds(formData.timeframeEndDuration, formData.timeframeEndUnit);
      const timeframeEnd = endOffsetSeconds ? now + (endOffsetSeconds * 1000) : null;

      const objectiveData: CreateMissionObjectiveRequest = {
        missionId: formData.missionId,
        objectiveType: formData.objectiveType as any,
        subtype: formData.subtype,
        operator: formData.operator as any,
        targetValue: formData.targetValue,
        description: formData.description,
        timeframeStart,
        timeframeEnd
      };

      const response = await createMissionObjective(objectiveData);

      if (response.success) {
        setShowCreateModal(false);
        resetForm();
        loadMissionObjectives();
      } else {
        setError(response.error || 'Failed to create mission objective');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create mission objective');
    }
  };

  const handleUpdate = async () => {
    if (!selectedObjective) return;

    try {
      const now = Date.now();

      // Calculate timeframe start (offset from now)
      const startOffsetSeconds = convertTimeframeToSeconds(formData.timeframeStartDuration, formData.timeframeStartUnit);
      const timeframeStart = startOffsetSeconds ? now + (startOffsetSeconds * 1000) : null;

      // Calculate timeframe end (offset from now)
      const endOffsetSeconds = convertTimeframeToSeconds(formData.timeframeEndDuration, formData.timeframeEndUnit);
      const timeframeEnd = endOffsetSeconds ? now + (endOffsetSeconds * 1000) : null;

      const objectiveData: UpdateMissionObjectiveRequest = {
        missionId: formData.missionId,
        objectiveType: formData.objectiveType as any,
        subtype: formData.subtype,
        operator: formData.operator as any,
        targetValue: formData.targetValue,
        description: formData.description,
        timeframeStart,
        timeframeEnd
      };

      const response = await updateMissionObjective(selectedObjective.id, objectiveData);

      if (response.success) {
        setShowEditModal(false);
        setSelectedObjective(null);
        resetForm();
        loadMissionObjectives();
      } else {
        setError(response.error || 'Failed to update mission objective');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update mission objective');
    }
  };

  const handleDelete = async (objective: MissionObjective) => {
    if (!confirm(`Are you sure you want to delete the objective "${objective.description}"?`)) {
      return;
    }

    try {
      const response = await deleteMissionObjective(objective.id);

      if (response.success) {
        loadMissionObjectives();
      } else {
        setError(response.error || 'Failed to delete mission objective');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete mission objective');
    }
  };

  // Table configuration
  const tableColumns = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true
    },
    {
      key: 'missionId',
      label: 'Mission ID',
      width: '100px',
      sortable: true,
      render: (objective: MissionObjective) => (
        <span className="font-medium text-blue-400">#{objective.missionId}</span>
      )
    },
    {
      key: 'objectiveType',
      label: 'Type',
      width: '120px',
      sortable: true,
      render: (objective: MissionObjective) => (
        <span className="font-medium text-purple-400">
          {OBJECTIVE_TYPE_OPTIONS.find(opt => opt.value === objective.objectiveType)?.label || objective.objectiveType}
        </span>
      )
    },
    {
      key: 'subtype',
      label: 'Subtype',
      width: '140px',
      sortable: true,
      render: (objective: MissionObjective) => {
        const subtypeOptions = getSubtypeOptions(objective.objectiveType as any);
        const subtypeLabel = subtypeOptions.find(opt => opt.value === objective.subtype)?.label || objective.subtype;
        return (
          <span className="font-medium text-cyan-400">
            {subtypeLabel}
          </span>
        );
      }
    },
    {
      key: 'operator',
      label: 'Operator',
      width: '100px',
      sortable: true,
      render: (objective: MissionObjective) => (
        <span className="font-medium text-orange-400">
          {OBJECTIVE_OPERATOR_OPTIONS.find(opt => opt.value === objective.operator)?.label || objective.operator}
        </span>
      )
    },
    {
      key: 'targetValue',
      label: 'Target Value',
      width: '120px',
      sortable: true,
      render: (objective: MissionObjective) => (
        <span className="font-mono text-green-400">{objective.targetValue}</span>
      )
    },
    {
      key: 'description',
      label: 'Description',
      width: '200px',
      render: (objective: MissionObjective) => (
        <span className="text-gray-300 truncate" title={objective.description}>
          {objective.description}
        </span>
      )
    },
    {
      key: 'timeframe',
      label: 'Timeframe',
      width: '160px',
      render: (objective: MissionObjective) => (
        <div className="text-xs text-gray-400">
          {objective.timeframeStart && objective.timeframeEnd ? (
            <>
              <div>Start: {new Date(objective.timeframeStart * 1000).toLocaleDateString()}</div>
              <div>End: {new Date(objective.timeframeEnd * 1000).toLocaleDateString()}</div>
            </>
          ) : (
            <span>No timeframe</span>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      render: (objective: MissionObjective) => (
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleRowClick(objective);
            }}
            className="p-1 text-blue-400 hover:text-blue-300"
            title="Edit"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(objective);
            }}
            className="p-1 text-red-400 hover:text-red-300"
            title="Delete"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Target className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Mission Objectives</h1>
            <p className="text-gray-400">Manage objectives that define mission completion criteria</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => loadMissionObjectives()}
            variant="outline"
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            Refresh
          </Button>
          <Button
            onClick={() => {
              resetForm();
              setShowCreateModal(true);
            }}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Plus size={16} />
            Create Objective
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* Filters */}
      <MissionObjectivesFilter
        filters={filters}
        onFiltersChange={handleFilterChange}
        onClearFilters={handleClearFilters}
      />

      {/* Data Table */}
      <DataTable
        data={missionObjectivesData?.data || []}
        columns={tableColumns}
        total={missionObjectivesData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onRowClick={handleRowClick}
        title="Mission Objectives"
        emptyState={{
          icon: <Target className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No mission objectives found',
          description: 'Get started by creating your first mission objective.',
          action: (
            <Button
              onClick={() => {
                resetForm();
                setShowCreateModal(true);
              }}
              variant="primary"
            >
              Create Objective
            </Button>
          )
        }}
      />

      {/* Create Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          resetForm();
          setError('');
        }}
        title="Create Mission Objective"
        size="md"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Mission ID"
              type="number"
              value={formData.missionId}
              onChange={(e) => handleInputChange('missionId', parseInt(e.target.value) || 0)}
              placeholder="Enter mission ID..."
              required
              min="1"
            />

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Objective Type</label>
              <select
                value={formData.objectiveType}
                onChange={(e) => handleInputChange('objectiveType', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {OBJECTIVE_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Subtype</label>
            <select
              value={formData.subtype}
              onChange={(e) => handleInputChange('subtype', e.target.value)}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {getSubtypeOptions(formData.objectiveType as any).map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Operator</label>
              <select
                value={formData.operator}
                onChange={(e) => handleInputChange('operator', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {OBJECTIVE_OPERATOR_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <Input
              label="Target Value"
              value={formData.targetValue}
              onChange={(e) => handleInputChange('targetValue', e.target.value)}
              placeholder="Enter target value..."
              required
            />
          </div>

          <Input
            label="Description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter objective description..."
            required
          />

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Timeframe Start Offset (Optional)</label>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  type="number"
                  value={formData.timeframeStartDuration}
                  onChange={(e) => handleInputChange('timeframeStartDuration', e.target.value)}
                  placeholder="Enter start offset..."
                  min="0"
                  step="1"
                />
                <select
                  value={formData.timeframeStartUnit}
                  onChange={(e) => handleInputChange('timeframeStartUnit', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="minutes">Minutes</option>
                  <option value="hours">Hours</option>
                  <option value="days">Days</option>
                </select>
              </div>
              <p className="text-xs text-gray-500">
                Time offset from now when the objective becomes active (0 = immediately).
              </p>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Timeframe End Offset (Optional)</label>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  type="number"
                  value={formData.timeframeEndDuration}
                  onChange={(e) => handleInputChange('timeframeEndDuration', e.target.value)}
                  placeholder="Enter end offset..."
                  min="1"
                  step="1"
                />
                <select
                  value={formData.timeframeEndUnit}
                  onChange={(e) => handleInputChange('timeframeEndUnit', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="minutes">Minutes</option>
                  <option value="hours">Hours</option>
                  <option value="days">Days</option>
                </select>
              </div>
              <p className="text-xs text-gray-500">
                Time offset from now when the objective expires.
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowCreateModal(false);
                resetForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreate}
              variant="primary"
              disabled={!formData.missionId || !formData.targetValue || !formData.description || !formData.subtype}
            >
              Create Objective
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedObjective(null);
          resetForm();
          setError('');
        }}
        title={`Edit Mission Objective: ${selectedObjective?.description || ''}`}
        size="md"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Mission ID"
              type="number"
              value={formData.missionId}
              onChange={(e) => handleInputChange('missionId', parseInt(e.target.value) || 0)}
              placeholder="Enter mission ID..."
              required
              min="1"
            />

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Objective Type</label>
              <select
                value={formData.objectiveType}
                onChange={(e) => handleInputChange('objectiveType', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {OBJECTIVE_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Subtype</label>
            <select
              value={formData.subtype}
              onChange={(e) => handleInputChange('subtype', e.target.value)}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {getSubtypeOptions(formData.objectiveType as any).map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Operator</label>
              <select
                value={formData.operator}
                onChange={(e) => handleInputChange('operator', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {OBJECTIVE_OPERATOR_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <Input
              label="Target Value"
              value={formData.targetValue}
              onChange={(e) => handleInputChange('targetValue', e.target.value)}
              placeholder="Enter target value..."
              required
            />
          </div>

          <Input
            label="Description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter objective description..."
            required
          />

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Timeframe Start Offset (Optional)</label>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  type="number"
                  value={formData.timeframeStartDuration}
                  onChange={(e) => handleInputChange('timeframeStartDuration', e.target.value)}
                  placeholder="Enter start offset..."
                  min="0"
                  step="1"
                />
                <select
                  value={formData.timeframeStartUnit}
                  onChange={(e) => handleInputChange('timeframeStartUnit', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="minutes">Minutes</option>
                  <option value="hours">Hours</option>
                  <option value="days">Days</option>
                </select>
              </div>
              <p className="text-xs text-gray-500">
                Time offset from now when the objective becomes active (0 = immediately).
              </p>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Timeframe End Offset (Optional)</label>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  type="number"
                  value={formData.timeframeEndDuration}
                  onChange={(e) => handleInputChange('timeframeEndDuration', e.target.value)}
                  placeholder="Enter end offset..."
                  min="1"
                  step="1"
                />
                <select
                  value={formData.timeframeEndUnit}
                  onChange={(e) => handleInputChange('timeframeEndUnit', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="minutes">Minutes</option>
                  <option value="hours">Hours</option>
                  <option value="days">Days</option>
                </select>
              </div>
              <p className="text-xs text-gray-500">
                Time offset from now when the objective expires.
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowEditModal(false);
                setSelectedObjective(null);
                resetForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdate}
              variant="primary"
              disabled={!formData.missionId || !formData.targetValue || !formData.description || !formData.subtype}
            >
              Update Objective
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default MissionObjectives;
