import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Settings, ArrowLeft, Calendar, User, Gift, Hash, Activity, Tag, DollarSign, FileText, Edit } from 'lucide-react';
import Button from '../components/ui/Button';
import { fetchAutoBonusRuleDetails } from '../utils/api';

interface AutoBonusRuleDetailsData {
  id: number;
  website_id: number;
  bonus_code: string;
  bonus_id: number;
  currency: string;
  is_active: boolean;
  is_deleted: boolean;
  timestamp: number;
  total: number;
  rules: Array<{
    field: string;
    value: string;
    operator: string;
  }>;
  reference_tag: string;
  claims_count: number;
  claims_sum_income: number;
  claims_sum_usd_income: number;
  claims_sum_wallet_income: number;
  bonus: {
    id: number;
    currency: string;
    name: string;
    description: string;
    typeable_id: number;
    typeable_type: string;
    typeable?: {
      id: number;
      quantity: number;
      bet_level: number;
      game_merchant_id: number;
      aggregator_id: number;
      game_merchant_name: string;
      max_win: string;
      wager_multiplier: string | null;
      disable_bet_amount: string | null;
      allowed_min_usd: string | null;
      allowed_max_usd: string | null;
    };
  };
}

const AutoBonusRulesDetails: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [ruleData, setRuleData] = useState<AutoBonusRuleDetailsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Load rule details on component mount
  useEffect(() => {
    const loadRuleDetails = async () => {
      if (!id) return;
      
      setIsLoading(true);
      setError('');

      try {
        const response = await fetchAutoBonusRuleDetails(id);

        if (response.success && response.data) {
          setRuleData(response.data.data);
        } else {
          setError(response.error || 'Failed to load auto bonus rule details');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    loadRuleDetails();
  }, [id]);

  // Format timestamp
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format rules array
  const formatRules = (rules: Array<{ field: string; value: string; operator: string }>) => {
    return rules.map(rule => `${rule.field} ${rule.operator} ${rule.value}`).join(', ');
  };

  // Get status badge
  const getStatusBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          isActive
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        <Activity className="w-4 h-4 mr-2" />
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading auto bonus rule details...</p>
        </div>
      </div>
    );
  }

  if (error || !ruleData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate('/auto-bonus-rules/list')}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Rules
          </Button>
        </div>
        
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Rule Details</h4>
              <p className="text-red-400 text-sm">{error || 'Rule not found'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate('/auto-bonus-rules/list')}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Rules
          </Button>
          
          <div className="flex items-center gap-3">
            <Settings className="w-8 h-8 text-primary-500" />
            <div>
              <h1 className="text-2xl font-bold text-gray-100">Auto Bonus Rule Details</h1>
              <p className="text-gray-400">Rule ID: {ruleData.id}</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => navigate(`/auto-bonus-rules/${ruleData.id}/claims`)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <FileText className="w-4 h-4" />
            View Claims
          </Button>
          
          <Button
            onClick={() => navigate(`/auto-bonus-rules/edit/${ruleData.id}`)}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Edit className="w-4 h-4" />
            Edit Rule
          </Button>
        </div>
      </div>

      {/* Rule Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Basic Information</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Status</span>
              {getStatusBadge(ruleData.is_active)}
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Bonus Code</span>
              <div className="flex items-center gap-2">
                <Tag className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200 font-medium">{ruleData.bonus_code}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Currency</span>
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200 font-medium">{ruleData.currency}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Total Amount</span>
              <span className="text-gray-200 font-medium">{ruleData.total}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Reference Tag</span>
              <span className="text-gray-200 font-medium">{ruleData.reference_tag || 'N/A'}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Created</span>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{formatDate(ruleData.timestamp)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bonus Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Associated Bonus</h3>
          
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Gift className="w-5 h-5 text-primary-500" />
              <div>
                <div className="text-gray-200 font-medium">{ruleData.bonus.name}</div>
                <div className="text-gray-400 text-sm">ID: {ruleData.bonus.id}</div>
              </div>
            </div>
            
            <div className="text-gray-300 text-sm">
              {ruleData.bonus.description}
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Bonus Currency</span>
              <span className="text-gray-200 font-medium">{ruleData.bonus.currency}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Type</span>
              <span className="text-gray-200 font-medium">{ruleData.bonus.typeable_type.split('\\').pop()}</span>
            </div>

            {ruleData.bonus.typeable && (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Quantity</span>
                  <span className="text-gray-200 font-medium">{ruleData.bonus.typeable.quantity}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Max Win</span>
                  <span className="text-gray-200 font-medium">{ruleData.bonus.typeable.max_win}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Game Provider</span>
                  <span className="text-gray-200 font-medium">{ruleData.bonus.typeable.game_merchant_name}</span>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Filter Rules */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Filter Rules</h3>
          
          <div className="space-y-3">
            {ruleData.rules.map((rule, index) => (
              <div key={index} className="bg-dark-800 rounded-lg p-3 border border-dark-600">
                <div className="text-gray-200 font-mono text-sm">
                  {rule.field} {rule.operator} {rule.value}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Claims Statistics */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Claims Statistics</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Total Claims</span>
              <div className="flex items-center gap-2">
                <Hash className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200 font-medium">{ruleData.claims_count}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Total Income</span>
              <span className="text-gray-200 font-medium">{ruleData.claims_sum_income.toFixed(2)} {ruleData.currency}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Total USD Income</span>
              <span className="text-gray-200 font-medium">${ruleData.claims_sum_usd_income.toFixed(2)}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Wallet Income</span>
              <span className="text-gray-200 font-medium">{ruleData.claims_sum_wallet_income.toFixed(2)} {ruleData.currency}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoBonusRulesDetails;
