import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, AlertCircle, ChevronRight, ChevronLeft, Check, Gamepad2, Settings, Target, Save } from 'lucide-react';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { createBonus, fetchGamesByProviders, type CreateBonusRequest, type Game } from '../utils/api';
import { useProviders, useCurrencies } from '../hooks/useAppData';

// Step 2: Bonus Information
interface BonusInformation {
  name: string;
  activeFrom: string;
  activeTo: string;
  currency: string;
  expireType: string;
  expirationDays: string;
  numberOfFreeSpins: string;
  betLevel: string;
  maxWin: string;
  wagerMultiplier: string;
  minimumWinAmountToDisablePlayerBet: string;
  allowedMinimumWin: string;
  allowedMaximumWin: string;
  note: string;
  description: string;
}

// Step 3: Game Selection with Percentages
interface GameWithPercentage {
  id: number;
  name: string;
  provider_name: string;
  percentage: number;
}

const BonusCreate: React.FC = () => {
  const navigate = useNavigate();
  const providers = useProviders();
  const currencies = useCurrencies();
  
  // Form state
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [availableGames, setAvailableGames] = useState<Game[]>([]);
  const [loadingGames, setLoadingGames] = useState(false);

  // Step 1: Provider Selection
  const [selectedProviders, setSelectedProviders] = useState<number[]>([]);

  // Step 2: Bonus Information
  const [bonusInfo, setBonusInfo] = useState<BonusInformation>({
    name: '',
    activeFrom: '',
    activeTo: '',
    currency: currencies[0] || 'USD',
    expireType: 'days',
    expirationDays: '7',
    numberOfFreeSpins: '',
    betLevel: '',
    maxWin: '',
    wagerMultiplier: '',
    minimumWinAmountToDisablePlayerBet: '',
    allowedMinimumWin: '',
    allowedMaximumWin: '',
    note: '',
    description: ''
  });

  // Step 3: Game Selection
  const [selectedGames, setSelectedGames] = useState<GameWithPercentage[]>([]);

  // Load games when providers are selected
  useEffect(() => {
    if (selectedProviders.length > 0 && currentStep === 3) {
      loadGames();
    }
  }, [selectedProviders, currentStep]);

  const loadGames = async () => {
    setLoadingGames(true);
    setError('');

    try {
      const response = await fetchGamesByProviders(selectedProviders, 'freespin');
      if (response.success && response.data) {
        setAvailableGames(response.data.data);
        // Start with no games selected by default
        setSelectedGames([]);
      } else {
        setError(response.error || 'Failed to load games');
      }
    } catch (err) {
      setError('Failed to load games');
    } finally {
      setLoadingGames(false);
    }
  };

  // Step navigation
  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Provider selection handlers
  const toggleProvider = (providerId: number) => {
    setSelectedProviders(prev => 
      prev.includes(providerId) 
        ? prev.filter(id => id !== providerId)
        : [...prev, providerId]
    );
  };

  // Bonus info handlers
  const updateBonusInfo = (field: keyof BonusInformation, value: string) => {
    setBonusInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Game selection handlers
  const addGameToSelected = (game: Game) => {
    const isAlreadySelected = selectedGames.some(g => g.id === game.id);
    if (!isAlreadySelected) {
      setSelectedGames(prev => [...prev, {
        id: game.id,
        name: game.name,
        provider_name: game.provider_name,
        percentage: 100
      }]);
    }
  };

  const removeGameFromSelected = (gameId: number) => {
    setSelectedGames(prev => prev.filter(g => g.id !== gameId));
  };

  const updateGamePercentage = (gameId: number, percentage: number) => {
    setSelectedGames(prev =>
      prev.map(game =>
        game.id === gameId
          ? { ...game, percentage: Math.max(0, Math.min(100, percentage)) }
          : game
      )
    );
  };

  // Get available games (not yet selected)
  const getAvailableGames = () => {
    return availableGames.filter(game =>
      !selectedGames.some(selected => selected.id === game.id)
    );
  };

  // Form submission
  const handleSubmit = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Validate required fields
      if (!bonusInfo.name.trim()) {
        throw new Error('Bonus name is required');
      }
      if (selectedProviders.length === 0) {
        throw new Error('At least one provider must be selected');
      }
      if (selectedGames.length === 0) {
        throw new Error('At least one game must be selected');
      }

      // Convert datetime-local to Unix timestamp
      const convertToTimestamp = (dateTimeLocal: string) => {
        if (!dateTimeLocal) return 0;
        return Math.floor(new Date(dateTimeLocal).getTime() / 1000);
      };

      // Get primary provider (first selected)
      const primaryProvider = providers.find(p => p.id === selectedProviders[0]);

      // Prepare data for API
      const bonusData: CreateBonusRequest = {
        provider_id: selectedProviders[0],
        provider: {
          label: primaryProvider?.name || '',
          value: selectedProviders[0]
        },
        type: 'freespin',
        name: bonusInfo.name.trim(),
        from: convertToTimestamp(bonusInfo.activeFrom),
        to: convertToTimestamp(bonusInfo.activeTo),
        currency: bonusInfo.currency,
        product: 'casino',
        maximum_payout: '',
        maximum_payout_percentage: '',
        expire_type: bonusInfo.expireType,
        expire_unit: bonusInfo.expirationDays,
        verified_players: false,
        request_able: false,
        cancel_able: false,
        disable_withdraw: false,
        note: bonusInfo.note || undefined,
        description: bonusInfo.description || undefined,
        multiplier: '',
        minimum_bet: '',
        maximum_bet: '',
        minimum_amount: '',
        maximum_amount: '',
        bet_level: bonusInfo.betLevel || undefined,
        max_win: bonusInfo.maxWin || undefined,
        quantity: bonusInfo.numberOfFreeSpins || undefined,
        allowed_min_usd: bonusInfo.allowedMinimumWin || undefined,
        allowed_max_usd: bonusInfo.allowedMaximumWin || undefined,
        disable_bet_amount: bonusInfo.minimumWinAmountToDisablePlayerBet || undefined,
        wager_multiplier: bonusInfo.wagerMultiplier || undefined,
        games: selectedGames.map(game => ({
          id: game.id,
          percentage: game.percentage
        }))
      };

      const response = await createBonus(bonusData);

      if (response.success) {
        navigate('/bonuses');
      } else {
        setError(response.error || 'Failed to create bonus');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create bonus');
    } finally {
      setIsLoading(false);
    }
  };

  // Step validation
  const canProceedToStep2 = selectedProviders.length > 0;
  const canProceedToStep3 = bonusInfo.name.trim() && bonusInfo.activeFrom && bonusInfo.activeTo;
  const canSubmit = selectedGames.length > 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => navigate('/bonuses')}
          className="flex items-center gap-2 text-gray-400 hover:text-gray-200"
        >
          <ArrowLeft size={20} />
          Back to Bonuses
        </Button>
        
        <div className="flex items-center gap-3">
          <Settings className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Create New Bonus</h1>
            <p className="text-gray-400">3-step bonus creation process</p>
          </div>
        </div>
      </div>

      {/* Step Progress */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep === step 
                    ? 'bg-primary-500 text-white' 
                    : currentStep > step 
                      ? 'bg-green-500 text-white' 
                      : 'bg-dark-600 text-gray-400'
                }`}>
                  {currentStep > step ? <Check className="w-4 h-4" /> : step}
                </div>
                {step < 3 && (
                  <div className={`w-16 h-0.5 mx-2 ${
                    currentStep > step ? 'bg-green-500' : 'bg-dark-600'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="text-sm text-gray-400">
            Step {currentStep} of 3
          </div>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className={currentStep === 1 ? 'text-primary-400 font-medium' : 'text-gray-400'}>
            <Gamepad2 className="w-4 h-4 inline mr-1" />
            Select Providers
          </span>
          <span className={currentStep === 2 ? 'text-primary-400 font-medium' : 'text-gray-400'}>
            <Settings className="w-4 h-4 inline mr-1" />
            Bonus Information
          </span>
          <span className={currentStep === 3 ? 'text-primary-400 font-medium' : 'text-gray-400'}>
            <Target className="w-4 h-4 inline mr-1" />
            Configure Games
          </span>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
          <span className="text-red-200">{error}</span>
        </div>
      )}

      {/* Step Content */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
        {/* Step 1: Provider Selection */}
        {currentStep === 1 && (
          <div>
            <h3 className="text-lg font-medium text-gray-200 mb-6 flex items-center gap-2">
              <Gamepad2 className="w-5 h-5" />
              Select Game Providers
            </h3>
            <p className="text-gray-400 mb-6">Choose one or more game providers for this bonus.</p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {providers.map((provider) => (
                <div
                  key={provider.id}
                  onClick={() => toggleProvider(provider.id)}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    selectedProviders.includes(provider.id)
                      ? 'border-primary-500 bg-primary-500/10'
                      : 'border-dark-600 hover:border-dark-500'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-200">{provider.name}</h4>
                      <p className="text-sm text-gray-400">{provider.total} games</p>
                    </div>
                    <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                      selectedProviders.includes(provider.id)
                        ? 'border-primary-500 bg-primary-500'
                        : 'border-gray-400'
                    }`}>
                      {selectedProviders.includes(provider.id) && (
                        <Check className="w-3 h-3 text-white" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {selectedProviders.length > 0 && (
              <div className="mt-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                <p className="text-green-400 text-sm">
                  {selectedProviders.length} provider{selectedProviders.length > 1 ? 's' : ''} selected
                </p>
              </div>
            )}
          </div>
        )}

        {/* Step 2: Bonus Information */}
        {currentStep === 2 && (
          <div>
            <h3 className="text-lg font-medium text-gray-200 mb-6 flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Bonus Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Bonus Name *"
                placeholder="Enter bonus name"
                value={bonusInfo.name}
                onChange={(e) => updateBonusInfo('name', e.target.value)}
                required
              />

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">Currency *</label>
                <select
                  value={bonusInfo.currency}
                  onChange={(e) => updateBonusInfo('currency', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  required
                >
                  {currencies.map((currency) => (
                    <option key={currency} value={currency}>
                      {currency}
                    </option>
                  ))}
                </select>
              </div>

              <Input
                label="Active From *"
                type="datetime-local"
                value={bonusInfo.activeFrom}
                onChange={(e) => updateBonusInfo('activeFrom', e.target.value)}
                required
              />

              <Input
                label="Active To *"
                type="datetime-local"
                value={bonusInfo.activeTo}
                onChange={(e) => updateBonusInfo('activeTo', e.target.value)}
                required
              />

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">Expire Type</label>
                <select
                  value={bonusInfo.expireType}
                  onChange={(e) => updateBonusInfo('expireType', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="days">Days</option>
                </select>
              </div>

              <Input
                label="Expiration Days"
                type="number"
                placeholder="Enter expiration days"
                value={bonusInfo.expirationDays}
                onChange={(e) => updateBonusInfo('expirationDays', e.target.value)}
              />

              <Input
                label="Number of Free Spins"
                type="number"
                placeholder="Enter number of free spins"
                value={bonusInfo.numberOfFreeSpins}
                onChange={(e) => updateBonusInfo('numberOfFreeSpins', e.target.value)}
              />

              <Input
                label="Bet Level"
                type="number"
                placeholder="Enter bet level"
                value={bonusInfo.betLevel}
                onChange={(e) => updateBonusInfo('betLevel', e.target.value)}
              />

              <Input
                label="Max Win ($)"
                type="number"
                placeholder="Enter max win amount"
                value={bonusInfo.maxWin}
                onChange={(e) => updateBonusInfo('maxWin', e.target.value)}
              />

              <Input
                label="Wager Multiplier (x)"
                type="number"
                placeholder="Enter wager multiplier"
                value={bonusInfo.wagerMultiplier}
                onChange={(e) => updateBonusInfo('wagerMultiplier', e.target.value)}
              />

              <Input
                label="Minimum Win Amount to Disable Player Bet ($)"
                type="number"
                placeholder="Enter minimum win amount"
                value={bonusInfo.minimumWinAmountToDisablePlayerBet}
                onChange={(e) => updateBonusInfo('minimumWinAmountToDisablePlayerBet', e.target.value)}
              />

              <Input
                label="Allowed Minimum Win ($)"
                type="number"
                placeholder="Enter allowed minimum win"
                value={bonusInfo.allowedMinimumWin}
                onChange={(e) => updateBonusInfo('allowedMinimumWin', e.target.value)}
              />

              <Input
                label="Allowed Maximum Win ($)"
                type="number"
                placeholder="Enter allowed maximum win"
                value={bonusInfo.allowedMaximumWin}
                onChange={(e) => updateBonusInfo('allowedMaximumWin', e.target.value)}
              />
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-300 mb-2">Note</label>
              <textarea
                placeholder="Enter bonus note"
                value={bonusInfo.note}
                onChange={(e) => updateBonusInfo('note', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
              />
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
              <textarea
                placeholder="Enter bonus description"
                value={bonusInfo.description}
                onChange={(e) => updateBonusInfo('description', e.target.value)}
                rows={4}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
              />
            </div>
          </div>
        )}

        {/* Step 3: Game Selection */}
        {currentStep === 3 && (
          <div>
            <h3 className="text-lg font-medium text-gray-200 mb-6 flex items-center gap-2">
              <Target className="w-5 h-5" />
              Configure Games
            </h3>
            <p className="text-gray-400 mb-6">Select games from available list and configure their percentages (0-100%).</p>

            {loadingGames ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
                <div className="text-gray-400">Loading games...</div>
              </div>
            ) : availableGames.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Available Games Column */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-md font-medium text-gray-200">Available Games</h4>
                    <span className="text-sm text-gray-400">
                      {getAvailableGames().length} available
                    </span>
                  </div>

                  <div className="border border-dark-600 rounded-lg p-4 bg-dark-800/50 max-h-96 overflow-y-auto">
                    {getAvailableGames().length > 0 ? (
                      <div className="space-y-2">
                        {getAvailableGames().map((game) => (
                          <div
                            key={game.id}
                            className="p-3 rounded-lg border border-dark-600 hover:border-dark-500 transition-all cursor-pointer"
                            onClick={() => addGameToSelected(game)}
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <h5 className="font-medium text-gray-200 text-sm">{game.name}</h5>
                                <p className="text-xs text-gray-400">{game.provider_name}</p>
                              </div>
                              <div className="text-primary-400 hover:text-primary-300">
                                <ChevronRight className="w-4 h-4" />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-400 text-sm">
                        All games have been selected
                      </div>
                    )}
                  </div>
                </div>

                {/* Selected Games Column */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-md font-medium text-gray-200">Selected Games</h4>
                    <span className="text-sm text-gray-400">
                      {selectedGames.length} selected
                    </span>
                  </div>

                  <div className="border border-dark-600 rounded-lg p-4 bg-dark-800/50 max-h-96 overflow-y-auto">
                    {selectedGames.length > 0 ? (
                      <div className="space-y-3">
                        {selectedGames.map((game) => (
                          <div
                            key={game.id}
                            className="p-3 rounded-lg border border-primary-500/30 bg-primary-500/5"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex-1">
                                <h5 className="font-medium text-gray-200 text-sm">{game.name}</h5>
                                <p className="text-xs text-gray-400">{game.provider_name}</p>
                              </div>
                              <button
                                onClick={() => removeGameFromSelected(game.id)}
                                className="text-red-400 hover:text-red-300 p-1"
                                title="Remove game"
                              >
                                <ChevronLeft className="w-4 h-4" />
                              </button>
                            </div>
                            <div className="flex items-center gap-2">
                              <label className="text-xs text-gray-400">Percentage:</label>
                              <input
                                type="number"
                                min="0"
                                max="100"
                                value={game.percentage}
                                onChange={(e) => updateGamePercentage(game.id, parseInt(e.target.value) || 0)}
                                className="w-16 px-2 py-1 bg-dark-800 border border-dark-600 rounded text-gray-100 text-xs focus:outline-none focus:ring-1 focus:ring-primary-500"
                              />
                              <span className="text-xs text-gray-400">%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-400 text-sm">
                        No games selected yet.<br />
                        Click on games from the left to add them.
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400">No games available for selected providers</div>
              </div>
            )}

            {selectedGames.length > 0 && (
              <div className="mt-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                <p className="text-green-400 text-sm">
                  {selectedGames.length} game{selectedGames.length > 1 ? 's' : ''} selected with configured percentages
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="flex items-center justify-between">
        <div>
          {currentStep > 1 && (
            <Button
              variant="ghost"
              onClick={prevStep}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </Button>
          )}
        </div>

        <div className="flex items-center gap-3">
          {currentStep < 3 ? (
            <Button
              onClick={nextStep}
              disabled={
                (currentStep === 1 && !canProceedToStep2) ||
                (currentStep === 2 && !canProceedToStep3)
              }
              className="flex items-center gap-2"
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={!canSubmit || isLoading}
              loading={isLoading}
              className="flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              Create Bonus
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default BonusCreate;
