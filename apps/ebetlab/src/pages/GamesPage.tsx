import React from 'react';
import { Search, Filter, Edit, Trash, Eye, Gamepad2, ToggleLeft } from 'lucide-react';

interface GameData {
  id: string;
  name: string;
  provider: string;
  category: string;
  plays: number;
  rtp: string;
  status: 'active' | 'disabled';
  featured: boolean;
}

const GamesPage: React.FC = () => {
  const games: GameData[] = [
    {
      id: 'GAME-6721',
      name: 'Crypto Fortune',
      provider: 'PlayNGo',
      category: 'Slots',
      plays: 8742,
      rtp: '96.5%',
      status: 'active',
      featured: true,
    },
    {
      id: 'GAME-5923',
      name: 'Roulette Pro',
      provider: 'Evolution',
      category: 'Table Games',
      plays: 5621,
      rtp: '97.3%',
      status: 'active',
      featured: true,
    },
    {
      id: 'GAME-4872',
      name: 'Bitcoin Blast',
      provider: 'Pragmatic',
      category: 'Slots',
      plays: 4389,
      rtp: '95.8%',
      status: 'active',
      featured: false,
    },
    {
      id: 'GAME-3984',
      name: 'Blackjack VIP',
      provider: 'Evolution',
      category: 'Table Games',
      plays: 3245,
      rtp: '99.5%',
      status: 'active',
      featured: false,
    },
    {
      id: 'GAME-2756',
      name: 'Ethereum Spins',
      provider: 'NetEnt',
      category: 'Slots',
      plays: 2978,
      rtp: '96.2%',
      status: 'disabled',
      featured: false,
    },
  ];

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: 'bg-green-500/20 text-green-500',
      disabled: 'bg-gray-500/20 text-gray-400',
    };
    
    return (
      <span className={`px-2 py-1 rounded-md text-xs font-medium ${statusClasses[status as keyof typeof statusClasses]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getFeaturedBadge = (featured: boolean) => {
    if (featured) {
      return <span className="px-2 py-1 rounded-md text-xs font-medium bg-primary-500/20 text-primary-500">Featured</span>;
    }
    return <span className="text-gray-400">-</span>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Games Management</h1>
        <button className="btn btn-primary">
          Add New Game
        </button>
      </div>
      
      <div className="admin-card">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search games by name, provider or ID..."
              className="py-2 pl-10 pr-4 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500 text-sm w-full"
            />
          </div>
          
          <div className="flex gap-3">
            <button className="btn btn-outline flex items-center gap-2">
              <Filter size={16} />
              <span>Filters</span>
            </button>
            
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Categories</option>
              <option>Slots</option>
              <option>Table Games</option>
              <option>Live Casino</option>
              <option>Crypto Games</option>
            </select>
            
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Providers</option>
              <option>Evolution</option>
              <option>PlayNGo</option>
              <option>Pragmatic</option>
              <option>NetEnt</option>
            </select>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="data-table">
            <thead>
              <tr>
                <th className="rounded-tl-lg">Game ID</th>
                <th>Name</th>
                <th>Provider</th>
                <th>Category</th>
                <th>Total Plays</th>
                <th>RTP</th>
                <th>Status</th>
                <th>Featured</th>
                <th className="rounded-tr-lg">Actions</th>
              </tr>
            </thead>
            <tbody>
              {games.map((game) => (
                <tr key={game.id}>
                  <td>{game.id}</td>
                  <td className="font-medium flex items-center gap-2">
                    <Gamepad2 size={18} className="text-primary-500" />
                    {game.name}
                  </td>
                  <td>{game.provider}</td>
                  <td>{game.category}</td>
                  <td>{game.plays.toLocaleString()}</td>
                  <td>{game.rtp}</td>
                  <td>{getStatusBadge(game.status)}</td>
                  <td>{getFeaturedBadge(game.featured)}</td>
                  <td>
                    <div className="flex gap-2">
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500">
                        <Eye size={16} />
                      </button>
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500">
                        <Edit size={16} />
                      </button>
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-warning-500">
                        <ToggleLeft size={16} />
                      </button>
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-error-500">
                        <Trash size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="flex justify-between items-center mt-6">
          <div className="text-sm text-gray-400">
            Showing 1 to 5 of 362 entries
          </div>
          
          <div className="flex gap-2">
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Previous
            </button>
            <button className="px-3 py-1.5 rounded-md border border-primary-500 bg-primary-500/10 text-primary-500">
              1
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              2
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              3
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GamesPage;