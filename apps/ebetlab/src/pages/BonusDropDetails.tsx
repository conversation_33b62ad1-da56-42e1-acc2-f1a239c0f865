import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Gift, ArrowLeft, Calendar, User, Tag, Hash, Activity, DollarSign, Clock, Edit } from 'lucide-react';
import Button from '../components/ui/Button';
import { fetchBonusDropDetails, type BonusDropListItem } from '../utils/api';

const BonusDropDetails: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [bonusDropData, setBonusDropData] = useState<BonusDropListItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Load bonus drop details on component mount
  useEffect(() => {
    const loadBonusDropDetails = async () => {
      if (!id) return;
      
      setIsLoading(true);
      setError('');

      try {
        const response = await fetchBonusDropDetails(id);

        if (response.success && response.data) {
          setBonusDropData(response.data.data);
        } else {
          setError(response.error || 'Failed to load bonus drop details');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    loadBonusDropDetails();
  }, [id]);

  // Format timestamp
  const formatDate = (timestamp: number | string) => {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    return new Date(ts * 1000).toLocaleString();
  };

  // Format rules array
  const formatRules = (rules: Array<{ field: string; value: string | number; operator: string; base?: number }>) => {
    return rules.map(rule => {
      if (rule.base) {
        return `${rule.field} ${rule.operator} ${rule.value} (base: ${formatDate(rule.base)})`;
      }
      return `${rule.field} ${rule.operator} ${rule.value}`;
    });
  };

  // Get status badge
  const getStatusBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          isActive
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        <Activity className="w-4 h-4 mr-2" />
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading bonus drop details...</p>
        </div>
      </div>
    );
  }

  if (error || !bonusDropData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate('/bonus-drops/list')}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Bonus Drops
          </Button>
        </div>
        
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Bonus Drop Details</h4>
              <p className="text-red-400 text-sm">{error || 'Bonus drop not found'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate('/bonus-drops/list')}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Bonus Drops
          </Button>
          
          <div className="flex items-center gap-3">
            <Gift className="w-8 h-8 text-primary-500" />
            <div>
              <h1 className="text-2xl font-bold text-gray-100">Bonus Drop Details</h1>
              <p className="text-gray-400">Bonus Drop ID: {bonusDropData.id}</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => navigate(`/bonus-drops/edit/${bonusDropData.id}`)}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Edit className="w-4 h-4" />
            Edit Bonus Drop
          </Button>
        </div>
      </div>

      {/* Bonus Drop Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Basic Information</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Status</span>
              {getStatusBadge(bonusDropData.is_active)}
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Code</span>
              <div className="flex items-center gap-2">
                <Tag className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200 font-mono">{bonusDropData.code}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Operator</span>
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{bonusDropData.operator?.name || `ID: ${bonusDropData.operator_id}`}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Currency</span>
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200 font-medium">{bonusDropData.currency_code}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Created</span>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{formatDate(bonusDropData.timestamp)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Amount & Count Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Amount & Count Information</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Amount</span>
              <div className="text-right">
                <div className="text-gray-200 font-medium">{bonusDropData.amount} {bonusDropData.currency_code}</div>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Count</span>
              <div className="flex items-center gap-2">
                <Hash className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200 font-medium">{bonusDropData.count.toLocaleString()}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Required Wager</span>
              <span className="text-gray-200 font-medium">{bonusDropData.required_wager}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Redeems Count</span>
              <span className="text-gray-200 font-medium">{bonusDropData.redeems_count}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Corrections Count</span>
              <span className="text-gray-200 font-medium">{bonusDropData.corrections_count}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Reference Code</span>
              <span className="text-gray-200 font-medium">{bonusDropData.ref_code || 'N/A'}</span>
            </div>
          </div>
        </div>

        {/* Validity Period */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Validity Period</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Start Date</span>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{formatDate(bonusDropData.start)}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">End Date</span>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{formatDate(bonusDropData.end)}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Duration</span>
              <span className="text-gray-200">
                {Math.ceil((parseInt(bonusDropData.end) - parseInt(bonusDropData.start)) / (24 * 60 * 60))} days
              </span>
            </div>

            {bonusDropData.deactivated_at && (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Deactivated At</span>
                  <span className="text-gray-200">{formatDate(bonusDropData.deactivated_at)}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Deactivated By</span>
                  <span className="text-gray-200">ID: {bonusDropData.deactivated_by}</span>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Rules */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Rules</h3>
          
          <div className="space-y-3">
            {formatRules(bonusDropData.rules).map((rule, index) => (
              <div key={index} className="bg-dark-800 rounded-lg p-3 border border-dark-600">
                <div className="text-gray-200 font-mono text-sm">
                  {rule}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BonusDropDetails;
