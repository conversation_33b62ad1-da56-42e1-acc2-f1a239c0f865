import React from 'react';
import { Plus, Search, Tag, Calendar, Gift } from 'lucide-react';

interface PromotionData {
  id: string;
  name: string;
  type: string;
  startDate: string;
  endDate: string;
  status: 'active' | 'scheduled' | 'ended';
  users: number;
}

const MarketingPage: React.FC = () => {
  const promotions: PromotionData[] = [
    {
      id: 'PROMO-1234',
      name: 'Welcome Bonus 100%',
      type: 'Deposit Bonus',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      status: 'active',
      users: 3542,
    },
    {
      id: 'PROMO-2345',
      name: 'Weekend Cashback 15%',
      type: 'Cashback',
      startDate: '2023-07-01',
      endDate: '2023-09-30',
      status: 'active',
      users: 1876,
    },
    {
      id: 'PROMO-3456',
      name: 'Crypto Monday',
      type: 'Free Spins',
      startDate: '2023-06-05',
      endDate: '2023-08-28',
      status: 'active',
      users: 2104,
    },
    {
      id: 'PROMO-4567',
      name: 'VIP Exclusive Bonus',
      type: 'Deposit Bonus',
      startDate: '2023-08-01',
      endDate: '2023-08-31',
      status: 'scheduled',
      users: 0,
    },
    {
      id: 'PROMO-5678',
      name: 'Summer Promotion',
      type: 'Tournament',
      startDate: '2023-06-01',
      endDate: '2023-06-30',
      status: 'ended',
      users: 4821,
    },
  ];

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: 'bg-green-500/20 text-green-500',
      scheduled: 'bg-yellow-500/20 text-yellow-500',
      ended: 'bg-gray-500/20 text-gray-400',
    };
    
    return (
      <span className={`px-2 py-1 rounded-md text-xs font-medium ${statusClasses[status as keyof typeof statusClasses]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getTypeIcon = (type: string) => {
    if (type === 'Deposit Bonus') {
      return <Tag size={18} className="text-primary-500" />;
    } else if (type === 'Cashback') {
      return <Gift size={18} className="text-secondary-500" />;
    } else if (type === 'Free Spins') {
      return <Calendar size={18} className="text-primary-500" />;
    } else {
      return <Gift size={18} className="text-secondary-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Marketing & Promotions</h1>
        <button className="btn btn-primary flex items-center gap-2">
          <Plus size={16} />
          <span>New Promotion</span>
        </button>
      </div>
      
      <div className="admin-card">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search promotions..."
              className="py-2 pl-10 pr-4 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500 text-sm w-full"
            />
          </div>
          
          <div className="flex gap-3">
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Types</option>
              <option>Deposit Bonus</option>
              <option>Cashback</option>
              <option>Free Spins</option>
              <option>Tournament</option>
            </select>
            
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Status</option>
              <option>Active</option>
              <option>Scheduled</option>
              <option>Ended</option>
            </select>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="data-table">
            <thead>
              <tr>
                <th className="rounded-tl-lg">ID</th>
                <th>Name</th>
                <th>Type</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th>Status</th>
                <th>Users</th>
                <th className="rounded-tr-lg">Actions</th>
              </tr>
            </thead>
            <tbody>
              {promotions.map((promo) => (
                <tr key={promo.id}>
                  <td>{promo.id}</td>
                  <td className="font-medium">{promo.name}</td>
                  <td className="flex items-center gap-2">
                    {getTypeIcon(promo.type)}
                    {promo.type}
                  </td>
                  <td>{promo.startDate}</td>
                  <td>{promo.endDate}</td>
                  <td>{getStatusBadge(promo.status)}</td>
                  <td>{promo.users.toLocaleString()}</td>
                  <td>
                    <div className="flex gap-2">
                      <button className="btn btn-outline text-xs py-1 px-2">
                        Edit
                      </button>
                      <button className="btn btn-outline text-red-500 hover:bg-red-500/10 text-xs py-1 px-2">
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="flex justify-between items-center mt-6">
          <div className="text-sm text-gray-400">
            Showing 1 to 5 of 24 entries
          </div>
          
          <div className="flex gap-2">
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Previous
            </button>
            <button className="px-3 py-1.5 rounded-md border border-primary-500 bg-primary-500/10 text-primary-500">
              1
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              2
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              3
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketingPage;