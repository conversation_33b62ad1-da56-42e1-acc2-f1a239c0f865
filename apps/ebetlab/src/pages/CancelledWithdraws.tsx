import React, { useState, useCallback } from 'react';
import { DollarSign, Download, Filter, RefreshCw, X, Search, Users, Calendar, AlertCircle } from 'lucide-react';
import { fetchCancelledWithdraws, type CancelledWithdrawData, type CancelledWithdrawSearchParams } from '../utils/api/cancelled-withdraws';
import { useDataFetching } from '../hooks/useDataFetching';
import Pagination from '../components/ui/Pagination';
import DateRangePicker from '../components/ui/DateRangePicker';
import QuickUserInfoModal from '../components/customer-details/QuickUserInfoModal';

const CancelledWithdraws: React.FC = () => {
  // State management
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [searchParams, setSearchParams] = useState<Partial<CancelledWithdrawSearchParams>>({});
  const [showFilters, setShowFilters] = useState(false);
  const [dateRange, setDateRange] = useState<{ from: Date | null; to: Date | null }>({
    from: null,
    to: null
  });

  // Quick info modal state
  const [showQuickInfoModal, setShowQuickInfoModal] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | number | null>(null);

  // Data fetching
  const fetchCancelledWithdrawsData = useCallback(async () => {
    const result = await fetchCancelledWithdraws(currentPage, itemsPerPage, searchParams);

    if (result.success && result.data) {
      return result.data.data; // Return the data object with total and data array
    } else {
      throw new Error(result.error || 'Failed to load cancelled withdraws');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: withdrawsData, loading, error, refetch } = useDataFetching({
    fetchFn: fetchCancelledWithdrawsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  // Handle filter changes - directly update searchParams like in Corrections
  const handleFilterChange = (key: keyof CancelledWithdrawSearchParams, value: string) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value || null
    }));
    setCurrentPage(1);
  };

  // Handle date range changes
  const handleDateRangeChange = (range: { from: Date | null; to: Date | null }) => {
    setDateRange(range);
    const newSearchParams = { ...searchParams };

    if (range.from) {
      newSearchParams.from = range.from.toISOString().split('T')[0];
    } else {
      delete newSearchParams.from;
    }

    if (range.to) {
      newSearchParams.to = range.to.toISOString().split('T')[0];
    } else {
      delete newSearchParams.to;
    }

    setSearchParams(newSearchParams);
    setCurrentPage(1);
  };

  // Clear filters
  const handleClearFilters = () => {
    setSearchParams({});
    setDateRange({ from: null, to: null });
    setCurrentPage(1);
  };

  // Handle row click to show quick info modal
  const handleRowClick = (withdraw: CancelledWithdrawData) => {
    setSelectedCustomerId(withdraw.customer_id);
    setShowQuickInfoModal(true);
  };

  // Format currency with proper symbol
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Export to CSV
  const handleExport = () => {
    if (!withdraws || withdraws.length === 0) {
      alert('No data to export');
      return;
    }

    const csvHeaders = [
      'ID', 'Customer ID', 'Username', 'Method', 'Provider', 'Currency',
      'Amount', 'USD Amount', 'Target Address', 'Status', 'Operator', 'Note', 'Timestamp'
    ];

    const csvData = withdraws.map(withdraw => [
      withdraw.id,
      withdraw.customer_id,
      withdraw.customer.username,
      withdraw.method,
      withdraw.provider,
      withdraw.currency,
      withdraw.amount,
      withdraw.usd_amount,
      withdraw.target_address,
      withdraw.status.name,
      withdraw.operator.name,
      withdraw.last_action.note || '',
      formatTimestamp(withdraw.timestamp)
    ]);

    const csvContent = [csvHeaders, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `cancelled_withdraws_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const withdraws = withdrawsData?.data || [];
  const totalItems = withdrawsData?.total || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <X className="w-8 h-8 text-red-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Cancelled Withdrawals</h1>
            <p className="text-gray-400">Manage cancelled withdrawal transactions</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
              showFilters
                ? 'bg-primary-600 text-white'
                : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
            }`}
          >
            <Filter size={16} />
            Filters
          </button>
          <button
            onClick={refetch}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
          <button
            onClick={handleExport}
            disabled={!withdraws || withdraws.length === 0}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Cancelled</p>
              <p className="text-2xl font-bold text-gray-100">{totalItems.toLocaleString()}</p>
            </div>
            <X className="w-8 h-8 text-red-500" />
          </div>
        </div>
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Amount (USD)</p>
              <p className="text-2xl font-bold text-gray-100">
                ${withdraws.length > 0 ? withdraws.reduce((sum, withdraw) => sum + parseFloat(withdraw.usd_amount), 0).toFixed(2) : '0.00'}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-primary-500" />
          </div>
        </div>
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Unique Customers</p>
              <p className="text-2xl font-bold text-gray-100">
                {withdraws.length > 0 ? new Set(withdraws.map(w => w.customer_id)).size : 0}
              </p>
            </div>
            <Users className="w-8 h-8 text-secondary-500" />
          </div>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-semibold text-gray-100 mb-4">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Currency</label>
              <input
                type="text"
                placeholder="Currency code..."
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Manual/Auto</label>
              <select
                value={searchParams.is_manuel || 'all'}
                onChange={(e) => handleFilterChange('is_manuel', e.target.value === 'all' ? '' : e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">All</option>
                <option value="1">Manual</option>
                <option value="0">Automatic</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
              <DateRangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
              />
            </div>
          </div>
          <div className="flex justify-end mt-4">
            <button
              onClick={handleClearFilters}
              className="px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Withdraws Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600">
        <div className="p-6 border-b border-dark-600">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-100">Cancelled Withdrawals</h2>
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-400">
                Showing {withdraws.length} of {totalItems} withdrawals
              </span>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-dark-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">ID</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Customer</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Method</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Provider</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Amount</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">USD Amount</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Target Address</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Operator</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Note</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Date</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {loading ? (
                <tr>
                  <td colSpan={11} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <RefreshCw size={20} className="animate-spin mr-2" />
                      Loading withdrawals...
                    </div>
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan={11} className="text-center py-8">
                    <div className="text-red-400">
                      <p className="mb-2">Failed to load withdrawals</p>
                      <p className="text-sm text-gray-400">{error}</p>
                    </div>
                  </td>
                </tr>
              ) : withdraws.length > 0 ? (
                withdraws.map((withdraw) => (
                  <tr
                    key={withdraw.id}
                    onClick={() => handleRowClick(withdraw)}
                    className="cursor-pointer hover:bg-dark-600/50 transition-colors"
                  >
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-100">#{withdraw.id}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-100">#{withdraw.customer_id}</div>
                        <div className="text-sm text-gray-400">{withdraw.customer.username}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-300">{withdraw.method}</td>
                    <td className="px-6 py-4 text-sm text-gray-300">{withdraw.provider}</td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-100">
                        {formatCurrency(withdraw.amount, withdraw.currency)}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-100">
                        ${parseFloat(withdraw.usd_amount).toFixed(2)}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-300 font-mono max-w-32 truncate" title={withdraw.target_address}>
                        {withdraw.target_address}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-500/20 text-red-400">
                        {withdraw.status.name}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-300">{withdraw.operator.name}</td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-300 max-w-32 truncate" title={withdraw.last_action.note}>
                        {withdraw.last_action.note || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-300">{formatTimestamp(withdraw.timestamp)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={11} className="text-center py-8">
                    <p className="text-gray-400">No cancelled withdrawals found.</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalItems > 0 && (
          <div className="p-6 border-t border-dark-600">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalItems / itemsPerPage)}
              onPageChange={setCurrentPage}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={setItemsPerPage}
              totalItems={totalItems}
            />
          </div>
        )}
      </div>

      {/* Quick User Info Modal */}
      {selectedCustomerId && (
        <QuickUserInfoModal
          isOpen={showQuickInfoModal}
          onClose={() => {
            setShowQuickInfoModal(false);
            setSelectedCustomerId(null);
          }}
          customerId={selectedCustomerId}
        />
      )}
    </div>
  );
};

export default CancelledWithdraws;
