import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Gift, RefreshCw, AlertCircle, Calendar, User, Tag, Hash, DollarSign, TrendingUp, ArrowUpDown } from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import { fetchBonusDropRedeems, type BonusDropRedeem } from '../utils/api';

interface BonusDropRedeemsData {
  total: number;
  data: BonusDropRedeem[];
}

const BonusDropsUsage: React.FC = () => {
  const navigate = useNavigate();
  const [redeemsData, setRedeemsData] = useState<BonusDropRedeemsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sortState, setSortState] = useState<SortState>({
    field: 'id',
    direction: 'desc'
  });

  const loadBonusDropRedeems = useCallback(async (page: number = currentPage, limit: number = itemsPerPage, searchFilters: Record<string, unknown> = filters) => {
    setIsLoading(true);
    setError('');

    try {
      // Include sorting parameters in the request
      const searchParamsWithSort = {
        ...searchFilters,
        sortBy: sortState.field,
        direction: sortState.direction
      };

      const response = await fetchBonusDropRedeems(page, limit, searchParamsWithSort);

      if (response.success && response.data) {
        setRedeemsData(response.data.data);
      } else {
        setError(response.error || 'Failed to load bonus drop redeems');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load bonus drop redeems on component mount
  useEffect(() => {
    loadBonusDropRedeems();
  }, [loadBonusDropRedeems]);

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadBonusDropRedeems(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadBonusDropRedeems(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadBonusDropRedeems(1, itemsPerPage, newFilters);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (redeem: BonusDropRedeem) => {
    // Navigate to the bonus drop details for this redeem
    navigate(`/bonus-drops/details/${redeem.bonus_drop.id}`);
  };

  // Format timestamp
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format rules array
  const formatRules = (rules: Array<{ field: string; value: string | number; operator: string }>) => {
    return rules.map(rule => `${rule.field} ${rule.operator} ${rule.value}`).join(', ');
  };

  // Table columns configuration
  const tableColumns: TableColumn<BonusDropRedeem>[] = [
    {
      key: 'id',
      label: 'Redeem ID',
      width: '100px',
      sortable: true,
      render: (redeem: BonusDropRedeem) => (
        <span className="font-mono text-sm">#{redeem.id}</span>
      )
    },
    {
      key: 'customer',
      label: 'Customer',
      width: '150px',
      sortable: true,
      render: (redeem: BonusDropRedeem) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <div>
            <div className="text-sm font-medium">{redeem.customer.username}</div>
            <div className="text-xs text-gray-500">ID: {redeem.customer.id}</div>
          </div>
        </div>
      )
    },
    {
      key: 'bonus_drop',
      label: 'Bonus Drop',
      width: '150px',
      sortable: true,
      render: (redeem: BonusDropRedeem) => (
        <div className="flex items-center gap-2">
          <Gift className="w-4 h-4 text-gray-400" />
          <div>
            <div className="text-sm font-medium">{redeem.bonus_drop.code}</div>
            <div className="text-xs text-gray-500">ID: {redeem.bonus_drop.id}</div>
          </div>
        </div>
      )
    },
    {
      key: 'operator',
      label: 'Operator',
      width: '120px',
      sortable: true,
      render: (redeem: BonusDropRedeem) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{redeem.bonus_drop.operator.name}</span>
        </div>
      )
    },
    {
      key: 'amount',
      label: 'Amount',
      width: '120px',
      sortable: true,
      render: (redeem: BonusDropRedeem) => (
        <div>
          <div className="text-sm font-medium">{redeem.amount} {redeem.currency}</div>
          <div className="text-xs text-gray-500">${redeem.usd_amount} USD</div>
        </div>
      )
    },
    {
      key: 'balance_change',
      label: 'Balance Change',
      width: '150px',
      sortable: false,
      render: (redeem: BonusDropRedeem) => (
        <div className="text-sm">
          <div className="flex items-center gap-1 text-gray-400">
            <ArrowUpDown className="w-3 h-3" />
            <span>Before: {redeem.before_balance}</span>
          </div>
          <div className="flex items-center gap-1 text-gray-200">
            <TrendingUp className="w-3 h-3 text-green-400" />
            <span>After: {redeem.after_balance}</span>
          </div>
        </div>
      )
    },
    {
      key: 'bonus_drop_rules',
      label: 'Rules',
      width: '200px',
      sortable: false,
      render: (redeem: BonusDropRedeem) => (
        <div className="text-sm text-gray-300">
          <div className="truncate" title={formatRules(redeem.bonus_drop.rules)}>
            {formatRules(redeem.bonus_drop.rules)}
          </div>
        </div>
      )
    },
    {
      key: 'customer_rank',
      label: 'Customer Rank',
      width: '120px',
      sortable: true,
      render: (redeem: BonusDropRedeem) => (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-400" />
          <span className="text-sm capitalize">{redeem.customer.rank}</span>
        </div>
      )
    },
    {
      key: 'timestamp',
      label: 'Redeemed',
      width: '150px',
      sortable: true,
      render: (redeem: BonusDropRedeem) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{formatDate(redeem.timestamp)}</span>
        </div>
      )
    }
  ];

  // Filter configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Information',
      fields: [
        {
          key: 'id',
          label: 'Redeem ID',
          type: 'text',
          placeholder: 'Search by redeem ID...'
        },
        {
          key: 'customer_id',
          label: 'Customer ID',
          type: 'text',
          placeholder: 'Search by customer ID...'
        },
        {
          key: 'bonus_drop_id',
          label: 'Bonus Drop ID',
          type: 'text',
          placeholder: 'Search by bonus drop ID...'
        },
        {
          key: 'currency',
          label: 'Currency',
          type: 'text',
          placeholder: 'Search by currency...'
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Gift className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Bonus Drops Usage</h1>
            <p className="text-gray-400">View bonus drop redemptions and usage statistics</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => loadBonusDropRedeems(currentPage, itemsPerPage, filters)}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Bonus Drop Redeems</h4>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={redeemsData?.data || []}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={redeemsData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Bonus Drop Redeems"
        emptyState={{
          icon: <Gift className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No bonus drop redeems found',
          description: 'No redeems match your current criteria.',
          action: undefined
        }}
      />
    </div>
  );
};

export default BonusDropsUsage;
