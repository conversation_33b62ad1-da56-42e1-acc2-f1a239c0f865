import React, { useState, useCallback } from 'react';
import { DollarSign, Download, Filter, RefreshCw, Eye, TrendingUp, AlertCircle, Search, Users, Calendar } from 'lucide-react';
import { fetchFTDReports, type FTDData, type FTDSearchParams } from '../utils/api';
import { useDataFetching } from '../hooks/useDataFetching';
import Pagination from '../components/ui/Pagination';
import DateRangePicker from '../components/ui/DateRangePicker';
import QuickUserInfoModal from '../components/customer-details/QuickUserInfoModal';

const FTDReports: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [showQuickInfoModal, setShowQuickInfoModal] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | number | null>(null);
  const [searchParams, setSearchParams] = useState<Partial<FTDSearchParams>>({});
  const [dateRange, setDateRange] = useState<{ from: Date | null; to: Date | null }>({
    from: null,
    to: null
  });

  // Fetch FTD reports data
  const fetchFTDData = useCallback(async () => {
    const result = await fetchFTDReports(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load FTD reports');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: ftdData, loading, error, refetch } = useDataFetching({
    fetchFn: fetchFTDData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  // Format currency with proper symbol
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format date only
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString();
  };

  // Handle customer modal
  const handleCustomerClick = (ftd: FTDData) => {
    setSelectedCustomerId(ftd.customer_id);
    setShowQuickInfoModal(true);
  };

  const handleCloseModal = () => {
    setShowQuickInfoModal(false);
    setSelectedCustomerId(null);
  };

  // Helper functions
  function handleItemsPerPageChange(newItemsPerPage: number) {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  }

  function handleFilterChange(key: keyof FTDSearchParams, value: string) {
    setSearchParams(prev => ({
      ...prev,
      [key]: value || null
    }));
    setCurrentPage(1);
  }

  // Handle date range changes
  const handleDateRangeChange = (range: { from: Date | null; to: Date | null }) => {
    setDateRange(range);
    const newSearchParams = { ...searchParams };

    if (range.from) {
      newSearchParams.from = Math.floor(range.from.getTime() / 1000).toString();
    } else {
      delete newSearchParams.from;
    }

    if (range.to) {
      newSearchParams.to = Math.floor(range.to.getTime() / 1000).toString();
    } else {
      delete newSearchParams.to;
    }

    setSearchParams(newSearchParams);
    setCurrentPage(1);
  };

  function clearFilters() {
    setSearchParams({});
    setDateRange({ from: null, to: null });
    setCurrentPage(1);
  }

  // Export functionality
  const handleExport = () => {
    if (!ftdData?.data || ftdData.data.length === 0) {
      return;
    }

    const csvHeaders = [
      'Customer ID', 'Username', 'Registration Date', 'Currency', 'First Deposit Date',
      'First Deposit Amount', 'First Deposit USD', 'Total Deposit Amount', 'Total USD Amount', 'Phone'
    ];

    const csvData = ftdData.data.map(ftd => [
      ftd.customer_id,
      ftd.username,
      formatDate(ftd.registration_ts),
      ftd.currency,
      formatDate(ftd.first_deposit_date),
      ftd.first_deposit_amount,
      ftd.first_deposit_amount_usd,
      ftd.total_deposit_amount,
      ftd.total_usd_amount,
      ftd.phone_full || ''
    ]);

    const csvContent = [csvHeaders, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `ftd_reports_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <TrendingUp className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">FTD Reports</h1>
            <p className="text-gray-400">First Time Deposit reports and analytics</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
              showFilters
                ? 'bg-primary-600 text-white'
                : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
            }`}
          >
            <Filter size={16} />
            Filters
          </button>
          <button
            onClick={refetch}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
          <button
            onClick={handleExport}
            disabled={!ftdData?.data || ftdData.data.length === 0}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      {ftdData && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total FTDs</p>
                <p className="text-2xl font-bold text-gray-100">{ftdData.total_count.toLocaleString()}</p>
              </div>
              <Users className="w-8 h-8 text-primary-500" />
            </div>
          </div>
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Amount (USD)</p>
                <p className="text-2xl font-bold text-gray-100">${parseFloat(ftdData.total_sum || '0').toFixed(2)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-primary-500" />
            </div>
          </div>
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Average Amount</p>
                <p className="text-2xl font-bold text-gray-100">
                  ${ftdData.total_count > 0 ? (parseFloat(ftdData.total_sum || '0') / ftdData.total_count).toFixed(2) : '0.00'}
                </p>
              </div>
              <Users className="w-8 h-8 text-secondary-500" />
            </div>
          </div>
        </div>
      )}



      {/* Filters */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-semibold text-gray-100 mb-4">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
              <input
                type="text"
                placeholder="Search by username..."
                value={searchParams.username || ''}
                onChange={(e) => handleFilterChange('username', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Currency</label>
              <select
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="TRY">TRY</option>
                <option value="GBP">GBP</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Min Amount (USD)</label>
              <input
                type="number"
                placeholder="0.00"
                value={searchParams.usd_min || ''}
                onChange={(e) => handleFilterChange('usd_min', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Max Amount (USD)</label>
              <input
                type="number"
                placeholder="0.00"
                value={searchParams.usd_max || ''}
                onChange={(e) => handleFilterChange('usd_max', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Payment Method</label>
              <input
                type="text"
                placeholder="Payment method..."
                value={searchParams.method || ''}
                onChange={(e) => handleFilterChange('method', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Provider</label>
              <input
                type="text"
                placeholder="Payment provider..."
                value={searchParams.provider || ''}
                onChange={(e) => handleFilterChange('provider', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
              <DateRangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
              />
            </div>
          </div>
          <div className="flex justify-end mt-4">
            <button
              onClick={clearFilters}
              className="px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-500/20 border border-red-500 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-400" />
          <span className="text-red-400">{error}</span>
        </div>
      )}

      {/* Table */}
      {!error && (
        <>
          <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-dark-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Customer</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Registration</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Currency</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">First Deposit</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Total Deposits</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Phone</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <RefreshCw size={20} className="animate-spin mr-2" />
                          Loading FTD reports...
                        </div>
                      </td>
                    </tr>
                  ) : ftdData?.data?.length ? (
                    ftdData.data.map((ftd) => (
                      <tr
                        key={ftd.customer_id}
                        onClick={() => handleCustomerClick(ftd)}
                        className="cursor-pointer hover:bg-dark-600/50 transition-colors"
                      >
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">#{ftd.customer_id}</div>
                            <div className="text-sm text-gray-400">{ftd.username}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {formatDate(ftd.registration_ts)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {ftd.currency}
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">
                              {formatCurrency(ftd.first_deposit_amount, ftd.first_deposit_currency)}
                            </div>
                            <div className="text-xs text-gray-400">${parseFloat(ftd.first_deposit_amount_usd).toFixed(2)} USD</div>
                            <div className="text-xs text-gray-400">{formatDate(ftd.first_deposit_date)}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">
                              {formatCurrency(ftd.total_deposit_amount, ftd.currency)}
                            </div>
                            <div className="text-xs text-gray-400">${parseFloat(ftd.total_usd_amount).toFixed(2)} USD</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {ftd.phone_full || '-'}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="text-center py-8">
                        <p className="text-gray-400">No FTD reports found.</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil((ftdData?.total_count || 0) / itemsPerPage)}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            totalItems={ftdData?.total_count || 0}
          />
        </>
      )}

      {/* Quick User Info Modal */}
      {showQuickInfoModal && selectedCustomerId && (
        <QuickUserInfoModal
          isOpen={showQuickInfoModal}
          onClose={handleCloseModal}
          customerId={selectedCustomerId}
        />
      )}
    </div>
  );
};

export default FTDReports;
