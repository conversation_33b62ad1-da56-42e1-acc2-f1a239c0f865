import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Star, RefreshCw, AlertCircle, Calendar, User, Tag, Hash, Activity, Edit, Trash2, Eye, Ban, DollarSign, Plus } from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import { 
  fetchWelcomeBonuses, 
  deactivateWelcomeBonus, 
  deleteWelcomeBonus, 
  type WelcomeBonusData 
} from '../utils/api';

interface WelcomeBonusesData {
  total: number;
  data: WelcomeBonusData[];
}

const WelcomeBonuses: React.FC = () => {
  const navigate = useNavigate();
  const [bonusesData, setBonusesData] = useState<WelcomeBonusesData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sortState, setSortState] = useState<SortState>({
    field: 'id',
    direction: 'desc'
  });

  const loadWelcomeBonuses = useCallback(async (page: number = currentPage, limit: number = itemsPerPage, searchFilters: Record<string, unknown> = filters) => {
    setIsLoading(true);
    setError('');

    try {
      // Include sorting parameters in the request
      const searchParamsWithSort = {
        ...searchFilters,
        sortBy: sortState.field,
        direction: sortState.direction
      };

      const response = await fetchWelcomeBonuses(page, limit, searchParamsWithSort);

      if (response.success && response.data) {
        setBonusesData(response.data.data);
      } else {
        setError(response.error || 'Failed to load welcome bonuses');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load welcome bonuses on component mount
  useEffect(() => {
    loadWelcomeBonuses();
  }, [loadWelcomeBonuses]);

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadWelcomeBonuses(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadWelcomeBonuses(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadWelcomeBonuses(1, itemsPerPage, newFilters);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (bonus: WelcomeBonusData) => {
    navigate(`/welcome-bonus/details/${bonus.id}`);
  };

  // Handle view details
  const handleView = (bonusId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/welcome-bonus/details/${bonusId}`);
  };

  // Handle edit
  const handleEdit = (bonusId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/welcome-bonus/edit/${bonusId}`);
  };

  // Handle deactivate
  const handleDeactivate = async (bonusId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm('Are you sure you want to deactivate this welcome bonus?')) return;

    try {
      const response = await deactivateWelcomeBonus(bonusId);

      if (response.success) {
        loadWelcomeBonuses(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to deactivate welcome bonus');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deactivate welcome bonus');
    }
  };

  // Handle delete
  const handleDelete = async (bonusId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm('Are you sure you want to delete this welcome bonus?')) return;

    try {
      const response = await deleteWelcomeBonus(bonusId);

      if (response.success) {
        loadWelcomeBonuses(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to delete welcome bonus');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete welcome bonus');
    }
  };

  // Format timestamp
  const formatDate = (timestamp: string | number) => {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    return new Date(ts * 1000).toLocaleString();
  };

  // Get status badge
  const getStatusBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isActive
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        <Activity className="w-3 h-3 mr-1" />
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  // Table columns configuration
  const tableColumns: TableColumn<WelcomeBonusData>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => (
        <span className="font-mono text-sm">#{bonus.id}</span>
      )
    },
    {
      key: 'code',
      label: 'Code',
      width: '150px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-mono text-gray-200">{bonus.code}</span>
        </div>
      )
    },
    {
      key: 'ref_code',
      label: 'Ref Code',
      width: '120px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => (
        <span className="text-sm font-mono text-gray-200">{bonus.ref_code}</span>
      )
    },
    {
      key: 'operator',
      label: 'Operator',
      width: '120px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{bonus.operator.name}</span>
        </div>
      )
    },
    {
      key: 'amount',
      label: 'Amount',
      width: '120px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => (
        <div>
          <div className="text-sm font-medium">{bonus.amount} {bonus.currency_code}</div>
          <div className="text-xs text-gray-500">${bonus.usd_amount} USD</div>
        </div>
      )
    },
    {
      key: 'required_wager',
      label: 'Required Wager',
      width: '130px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => (
        <div className="text-sm">
          <div className="font-medium">{bonus.required_wager}</div>
          <div className="text-xs text-gray-500">{bonus.required_wager_currency}</div>
        </div>
      )
    },
    {
      key: 'total',
      label: 'Total',
      width: '80px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium">{bonus.total}</span>
        </div>
      )
    },
    {
      key: 'redeems_count',
      label: 'Redeems',
      width: '80px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => (
        <span className="text-sm font-medium">{bonus.redeems_count}</span>
      )
    },
    {
      key: 'is_active',
      label: 'Status',
      width: '100px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => getStatusBadge(bonus.is_active)
    },
    {
      key: 'timestamp',
      label: 'Created',
      width: '150px',
      sortable: true,
      render: (bonus: WelcomeBonusData) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{formatDate(bonus.timestamp)}</span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '200px',
      sortable: false,
      render: (bonus: WelcomeBonusData) => (
        <div className="flex gap-1">
          <button
            onClick={(e) => handleView(bonus.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-blue-400 transition-colors"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={(e) => handleEdit(bonus.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500 transition-colors"
            title="Edit Bonus"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => handleDeactivate(bonus.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-orange-400 transition-colors"
            title="Deactivate Bonus"
          >
            <Ban size={16} />
          </button>
          <button
            onClick={(e) => handleDelete(bonus.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-red-400 transition-colors"
            title="Delete Bonus"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  // Filter configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Information',
      fields: [
        {
          key: 'id',
          label: 'Bonus ID',
          type: 'text',
          placeholder: 'Search by bonus ID...'
        },
        {
          key: 'code',
          label: 'Code',
          type: 'text',
          placeholder: 'Search by code...'
        },
        {
          key: 'ref_code',
          label: 'Ref Code',
          type: 'text',
          placeholder: 'Search by ref code...'
        },
        {
          key: 'currency_code',
          label: 'Currency',
          type: 'text',
          placeholder: 'Search by currency...'
        },
        {
          key: 'operator_id',
          label: 'Operator ID',
          type: 'text',
          placeholder: 'Search by operator ID...'
        }
      ]
    },
    {
      name: 'status',
      displayName: 'Status & Activity',
      fields: [
        {
          key: 'is_active',
          label: 'Status',
          type: 'select',
          options: [
            { value: 'true', label: 'Active' },
            { value: 'false', label: 'Inactive' }
          ]
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Star className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Welcome Bonuses</h1>
            <p className="text-gray-400">Manage welcome bonus assignments</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => loadWelcomeBonuses(currentPage, itemsPerPage, filters)}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>

          <Button
            onClick={() => navigate('/welcome-bonus/create')}
            variant="primary"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Welcome Bonus
          </Button>
        </div>
      </div>

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Welcome Bonuses</h4>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={bonusesData?.data || []}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={bonusesData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Welcome Bonuses"
        emptyState={{
          icon: <Star className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No welcome bonuses found',
          description: 'No bonuses match your current criteria.',
          action: undefined
        }}
      />
    </div>
  );
};

export default WelcomeBonuses;
