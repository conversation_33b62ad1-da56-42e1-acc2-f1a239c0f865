import React from 'react';
import { Search, Clock, CheckCircle, AlertCircle, MessageSquare, User, Calendar } from 'lucide-react';

interface TicketData {
  id: string;
  userId: string;
  subject: string;
  category: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created: string;
  lastReply: string;
}

const SupportPage: React.FC = () => {
  const tickets: TicketData[] = [
    {
      id: 'TICKET-8721',
      userId: '#28491',
      subject: 'Withdrawal not received after 24 hours',
      category: 'Payments',
      status: 'open',
      priority: 'high',
      created: '2023-07-21',
      lastReply: '2023-07-21',
    },
    {
      id: 'TICKET-8715',
      userId: '#19384',
      subject: 'Unable to access my account',
      category: 'Account',
      status: 'in-progress',
      priority: 'medium',
      created: '2023-07-20',
      lastReply: '2023-07-21',
    },
    {
      id: 'TICKET-8702',
      userId: '#45672',
      subject: 'Game crashed during play',
      category: 'Technical',
      status: 'open',
      priority: 'medium',
      created: '2023-07-19',
      lastReply: '2023-07-20',
    },
    {
      id: 'TICKET-8693',
      userId: '#36729',
      subject: 'Bonus not applied after deposit',
      category: 'Bonus',
      status: 'resolved',
      priority: 'low',
      created: '2023-07-18',
      lastReply: '2023-07-19',
    },
    {
      id: 'TICKET-8687',
      userId: '#58294',
      subject: 'Question about VIP program',
      category: 'VIP',
      status: 'closed',
      priority: 'low',
      created: '2023-07-17',
      lastReply: '2023-07-18',
    },
  ];

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      'open': 'bg-yellow-500/20 text-yellow-500',
      'in-progress': 'bg-blue-500/20 text-blue-500',
      'resolved': 'bg-green-500/20 text-green-500',
      'closed': 'bg-gray-500/20 text-gray-400',
    };
    
    return (
      <span className={`px-2 py-1 rounded-md text-xs font-medium ${statusClasses[status as keyof typeof statusClasses]}`}>
        {status === 'in-progress' ? 'In Progress' : status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityClasses = {
      'low': 'bg-gray-500/20 text-gray-400',
      'medium': 'bg-blue-500/20 text-blue-500',
      'high': 'bg-orange-500/20 text-orange-500',
      'urgent': 'bg-red-500/20 text-red-500',
    };
    
    return (
      <span className={`px-2 py-1 rounded-md text-xs font-medium ${priorityClasses[priority as keyof typeof priorityClasses]}`}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Support Tickets</h1>
        <div className="flex gap-3">
          <button className="btn btn-outline">
            View Live Chat
          </button>
          <button className="btn btn-primary">
            Create Ticket
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="stats-card">
          <div className="mb-4 text-gray-400 text-sm">Open Tickets</div>
          <div className="text-3xl font-semibold flex items-center gap-2">
            <Clock size={24} className="text-yellow-500" />
            24
          </div>
        </div>
        
        <div className="stats-card">
          <div className="mb-4 text-gray-400 text-sm">In Progress</div>
          <div className="text-3xl font-semibold flex items-center gap-2">
            <MessageSquare size={24} className="text-blue-500" />
            18
          </div>
        </div>
        
        <div className="stats-card">
          <div className="mb-4 text-gray-400 text-sm">Resolved Today</div>
          <div className="text-3xl font-semibold flex items-center gap-2">
            <CheckCircle size={24} className="text-green-500" />
            12
          </div>
        </div>
        
        <div className="stats-card">
          <div className="mb-4 text-gray-400 text-sm">Urgent Issues</div>
          <div className="text-3xl font-semibold flex items-center gap-2">
            <AlertCircle size={24} className="text-red-500" />
            5
          </div>
        </div>
      </div>
      
      <div className="admin-card">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search tickets by ID, user or subject..."
              className="py-2 pl-10 pr-4 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500 text-sm w-full"
            />
          </div>
          
          <div className="flex gap-3">
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Categories</option>
              <option>Payments</option>
              <option>Account</option>
              <option>Technical</option>
              <option>Bonus</option>
              <option>VIP</option>
            </select>
            
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Status</option>
              <option>Open</option>
              <option>In Progress</option>
              <option>Resolved</option>
              <option>Closed</option>
            </select>
            
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Priorities</option>
              <option>Urgent</option>
              <option>High</option>
              <option>Medium</option>
              <option>Low</option>
            </select>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="data-table">
            <thead>
              <tr>
                <th className="rounded-tl-lg">Ticket ID</th>
                <th>User ID</th>
                <th>Subject</th>
                <th>Category</th>
                <th>Status</th>
                <th>Priority</th>
                <th>Created</th>
                <th>Last Reply</th>
                <th className="rounded-tr-lg">Actions</th>
              </tr>
            </thead>
            <tbody>
              {tickets.map((ticket) => (
                <tr key={ticket.id}>
                  <td>{ticket.id}</td>
                  <td>{ticket.userId}</td>
                  <td className="font-medium max-w-[250px] truncate">{ticket.subject}</td>
                  <td>{ticket.category}</td>
                  <td>{getStatusBadge(ticket.status)}</td>
                  <td>{getPriorityBadge(ticket.priority)}</td>
                  <td>{ticket.created}</td>
                  <td>{ticket.lastReply}</td>
                  <td>
                    <div className="flex gap-2">
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500">
                        <MessageSquare size={16} />
                      </button>
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500">
                        <User size={16} />
                      </button>
                      <button className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500">
                        <Calendar size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="flex justify-between items-center mt-6">
          <div className="text-sm text-gray-400">
            Showing 1 to 5 of 64 entries
          </div>
          
          <div className="flex gap-2">
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Previous
            </button>
            <button className="px-3 py-1.5 rounded-md border border-primary-500 bg-primary-500/10 text-primary-500">
              1
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              2
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              3
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupportPage;