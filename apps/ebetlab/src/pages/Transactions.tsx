import React, { useState, useEffect, useCallback } from 'react';
import {
  Receipt, RefreshCw, AlertCircle, Trash2, Eye, Search,
  Filter, X, Download, DollarSign, Users, TrendingUp, TrendingDown,
  Clock, CheckCircle, XCircle, AlertTriangle, Coins, Activity
} from 'lucide-react';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { Pie } from 'react-chartjs-2';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import Modal from '../components/ui/Modal';
import {
  fetchPointsTransactions,
  fetchTransactionStatistics,
  deletePointsTransaction,
  formatTransactionAmount,
  getTransactionTypeDisplay,
  getTransactionCategoryDisplay,
  getTransactionTypeColor,
  getTransactionCategoryColor,
  TRANSACTION_TYPE_OPTIONS,
  TRANSACTION_CATEGORY_OPTIONS,
  POINTS_TRANSACTIONS_SORT_FIELDS,
  type PointsTransaction,
  type PointsTransactionsQueryParams,
  type TransactionStatistics
} from '../utils/api/points-transactions';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

const Transactions: React.FC = () => {
  // State management
  const [data, setData] = useState<{ total: number; data: PointsTransaction[] }>({ total: 0, data: [] });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [sortState, setSortState] = useState<SortState>({ field: 'createdAt', direction: 'desc' });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  // Modal states
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<PointsTransaction | null>(null);
  const [showTutorial, setShowTutorial] = useState(true);

  // Statistics
  const [statistics, setStatistics] = useState<TransactionStatistics>({
    totalCount: 0,
    totalVolume: 0,
    depositCount: 0,
    depositVolume: 0,
    chargeCount: 0,
    chargeVolume: 0,
    refundCount: 0,
    refundVolume: 0,
    withdrawCount: 0,
    withdrawVolume: 0
  });

  // Build query parameters for API calls
  const buildQueryParams = useCallback((): PointsTransactionsQueryParams => {
    const params: PointsTransactionsQueryParams = {
      page: currentPage,
      limit: itemsPerPage,
      sortBy: sortState.field || 'createdAt',
      sortOrder: sortState.direction === 'asc' ? 'ASC' : 'DESC'
    };

    // Add filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        switch (key) {
          case 'id':
            params.id = parseInt(value as string);
            break;
          case 'fromUserId':
            params.fromUserId = parseInt(value as string);
            break;
          case 'toUserId':
            params.toUserId = parseInt(value as string);
            break;
          case 'type':
            params.type = value as any;
            break;
          case 'category':
            params.category = value as any;
            break;
          case 'minAmount':
            params.minAmount = parseFloat(value as string);
            break;
          case 'maxAmount':
            params.maxAmount = parseFloat(value as string);
            break;
          case 'createdAfter':
            params.createdAfter = value as string;
            break;
          case 'createdBefore':
            params.createdBefore = value as string;
            break;
          case 'search':
            params.search = value as string;
            break;
        }
      }
    });

    return params;
  }, [currentPage, itemsPerPage, sortState, filters]);

  // Load statistics data
  const loadStatistics = useCallback(async () => {
    try {
      const response = await fetchTransactionStatistics();
      if (response.success && response.data) {
        setStatistics(response.data);
      }
    } catch (err) {
      console.error('Failed to load statistics:', err);
    }
  }, []);

  // Load transactions data
  const loadTransactions = useCallback(async () => {
    setIsLoading(true);
    setError('');

    try {
      const queryParams = buildQueryParams();
      const response = await fetchPointsTransactions(queryParams);

      if (response.success && response.data) {
        setData({
          total: response.meta.total,
          data: response.data
        });
      } else {
        throw new Error(response.error || 'Failed to load transactions');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load transactions');
      setData({ total: 0, data: [] });
    } finally {
      setIsLoading(false);
    }
  }, [buildQueryParams]);

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadTransactions();
    loadStatistics();
  }, [loadTransactions, loadStatistics]);

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (transaction: PointsTransaction) => {
    setSelectedTransaction(transaction);
    setShowDetailsModal(true);
  };

  const handleDelete = async (transaction: PointsTransaction, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm(`Are you sure you want to erase transaction #${transaction.id} from history? This action cannot be undone.`)) return;

    try {
      const response = await deletePointsTransaction(transaction.id);

      if (response.success) {
        loadTransactions();
      } else {
        setError(response.error || 'Failed to delete transaction');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete transaction');
    }
  };

  const handleView = (transaction: PointsTransaction, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedTransaction(transaction);
    setShowDetailsModal(true);
  };

  const handleRefresh = () => {
    loadTransactions();
    loadStatistics();
  };

  // Table columns configuration
  const tableColumns: TableColumn<PointsTransaction>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (transaction) => (
        <span className="font-mono text-sm text-gray-300">#{transaction.id}</span>
      )
    },
    {
      key: 'type',
      label: 'Type',
      width: '120px',
      sortable: true,
      render: (transaction) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTransactionTypeColor(transaction.type)}`}>
          {getTransactionTypeDisplay(transaction.type)}
        </span>
      )
    },
    {
      key: 'category',
      label: 'Category',
      width: '140px',
      sortable: true,
      render: (transaction) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTransactionCategoryColor(transaction.category)}`}>
          {getTransactionCategoryDisplay(transaction.category)}
        </span>
      )
    },
    {
      key: 'amount',
      label: 'Amount',
      width: '120px',
      sortable: true,
      render: (transaction) => (
        <span className="font-mono text-sm font-medium text-gray-100">
          {formatTransactionAmount(transaction.amount)}
        </span>
      )
    },
    {
      key: 'fromUserId',
      label: 'From User',
      width: '100px',
      sortable: true,
      render: (transaction) => (
        <span className="text-sm text-gray-300">
          {transaction.fromUserId ? `#${transaction.fromUserId}` : '-'}
        </span>
      )
    },
    {
      key: 'toUserId',
      label: 'To User',
      width: '100px',
      sortable: true,
      render: (transaction) => (
        <span className="text-sm text-gray-300">
          {transaction.toUserId ? `#${transaction.toUserId}` : '-'}
        </span>
      )
    },
    {
      key: 'createdAt',
      label: 'Created At',
      width: '160px',
      sortable: true,
      render: (transaction) => (
        <span className="text-sm text-gray-400">
          {new Date(transaction.createdAt).toLocaleString()}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '120px',
      sortable: false,
      render: (transaction) => (
        <div className="flex gap-1">
          <button
            onClick={(e) => handleView(transaction, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-blue-400 transition-colors"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={(e) => handleDelete(transaction, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-red-400 transition-colors"
            title="Erase from history"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  // Filter groups configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Filters',
      fields: [
        {
          key: 'search',
          label: 'Search',
          type: 'text',
          placeholder: 'Search transactions...'
        },
        {
          key: 'id',
          label: 'Transaction ID',
          type: 'number',
          placeholder: 'Enter transaction ID'
        },
        {
          key: 'type',
          label: 'Type',
          type: 'select',
          options: [
            { value: '', label: 'All Types' },
            ...TRANSACTION_TYPE_OPTIONS
          ]
        },
        {
          key: 'category',
          label: 'Category',
          type: 'select',
          options: [
            { value: '', label: 'All Categories' },
            ...TRANSACTION_CATEGORY_OPTIONS
          ]
        }
      ]
    },
    {
      name: 'users',
      displayName: 'User Filters',
      fields: [
        {
          key: 'fromUserId',
          label: 'From User ID',
          type: 'number',
          placeholder: 'Enter user ID'
        },
        {
          key: 'toUserId',
          label: 'To User ID',
          type: 'number',
          placeholder: 'Enter user ID'
        }
      ]
    },
    {
      name: 'amount',
      displayName: 'Amount Filters',
      fields: [
        {
          key: 'minAmount',
          label: 'Min Amount',
          type: 'number',
          placeholder: 'Minimum points'
        },
        {
          key: 'maxAmount',
          label: 'Max Amount',
          type: 'number',
          placeholder: 'Maximum points'
        }
      ]
    },
    {
      name: 'dates',
      displayName: 'Date Filters',
      fields: [
        {
          key: 'createdAfter',
          label: 'Created After',
          type: 'datetime-local'
        },
        {
          key: 'createdBefore',
          label: 'Created Before',
          type: 'datetime-local'
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Receipt className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Points Transactions</h1>
            <p className="text-gray-400">Monitor and manage all points transactions in the system</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            variant="secondary"
            size="sm"
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Tutorial Box */}
      {showTutorial && (
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Receipt className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-blue-400 mb-1">Points Transactions Overview</h3>
              <p className="text-sm text-gray-300 mb-3">
                This page shows all points transactions in the system, including mission rewards, market purchases, and admin adjustments.
                Use filters to find specific transactions and the "Erase from history" button to remove invalid or test transactions.
              </p>
              <div className="flex flex-wrap gap-4 text-xs text-gray-400">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Deposit: Points added to accounts</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                  <span>Charge: Points removed from accounts</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span>Refund: Points returned to accounts</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                  <span>Withdrawal: Points withdrawn from system</span>
                </div>
              </div>
            </div>
            <button
              onClick={() => setShowTutorial(false)}
              className="text-gray-400 hover:text-gray-200 transition-colors"
            >
              <X size={16} />
            </button>
          </div>
        </div>
      )}

      {/* Statistics Dashboard */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left side - Statistics Cards */}
        <div className="lg:col-span-2 space-y-4">
          {/* Total Statistics - Wide Box */}
          <div className="bg-dark-700 rounded-lg p-6 border border-dark-600">
            <div className="flex items-center gap-4">
              <Activity className="w-8 h-8 text-blue-400" />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-100 mb-2">Total Transactions</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-400">Count</p>
                    <p className="text-2xl font-bold text-blue-400">{statistics.totalCount.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Volume</p>
                    <p className="text-2xl font-bold text-blue-400">{statistics.totalVolume.toLocaleString()} points</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 2x2 Grid of Transaction Types */}
          <div className="grid grid-cols-2 gap-4">
            {/* Deposits */}
            <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <TrendingUp className="w-5 h-5 text-green-400" />
                  <h4 className="text-sm font-medium text-gray-100">Deposits</h4>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-green-400">
                    {statistics.totalCount > 0 ? ((statistics.depositCount / statistics.totalCount) * 100).toFixed(1) : '0'}%
                  </p>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-lg font-semibold text-green-400">{statistics.depositCount.toLocaleString()}</p>
                <p className="text-xs text-gray-400">{statistics.depositVolume.toLocaleString()} points</p>
              </div>
            </div>

            {/* Charges */}
            <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <TrendingDown className="w-5 h-5 text-red-400" />
                  <h4 className="text-sm font-medium text-gray-100">Charges</h4>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-red-400">
                    {statistics.totalCount > 0 ? ((statistics.chargeCount / statistics.totalCount) * 100).toFixed(1) : '0'}%
                  </p>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-lg font-semibold text-red-400">{statistics.chargeCount.toLocaleString()}</p>
                <p className="text-xs text-gray-400">{statistics.chargeVolume.toLocaleString()} points</p>
              </div>
            </div>

            {/* Refunds */}
            <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <RefreshCw className="w-5 h-5 text-blue-400" />
                  <h4 className="text-sm font-medium text-gray-100">Refunds</h4>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-blue-400">
                    {statistics.totalCount > 0 ? ((statistics.refundCount / statistics.totalCount) * 100).toFixed(1) : '0'}%
                  </p>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-lg font-semibold text-blue-400">{statistics.refundCount.toLocaleString()}</p>
                <p className="text-xs text-gray-400">{statistics.refundVolume.toLocaleString()} points</p>
              </div>
            </div>

            {/* Withdrawals */}
            <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="w-5 h-5 text-orange-400" />
                  <h4 className="text-sm font-medium text-gray-100">Withdrawals</h4>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-orange-400">
                    {statistics.totalCount > 0 ? ((statistics.withdrawCount / statistics.totalCount) * 100).toFixed(1) : '0'}%
                  </p>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-lg font-semibold text-orange-400">{statistics.withdrawCount.toLocaleString()}</p>
                <p className="text-xs text-gray-400">{statistics.withdrawVolume.toLocaleString()} points</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Pie Chart */}
        <div className="bg-dark-700 rounded-lg p-6 border border-dark-600">
          <h3 className="text-lg font-semibold text-gray-100 mb-4">Transaction Distribution</h3>
          <div className="h-64">
            <Pie
              data={{
                labels: ['Deposits', 'Charges', 'Refunds', 'Withdrawals'],
                datasets: [
                  {
                    data: [
                      statistics.depositCount,
                      statistics.chargeCount,
                      statistics.refundCount,
                      statistics.withdrawCount
                    ],
                    backgroundColor: [
                      'rgba(34, 197, 94, 0.8)',  // Green for deposits
                      'rgba(239, 68, 68, 0.8)',   // Red for charges
                      'rgba(59, 130, 246, 0.8)',  // Blue for refunds
                      'rgba(249, 115, 22, 0.8)'   // Orange for withdrawals
                    ],
                    borderColor: [
                      'rgba(34, 197, 94, 1)',
                      'rgba(239, 68, 68, 1)',
                      'rgba(59, 130, 246, 1)',
                      'rgba(249, 115, 22, 1)'
                    ],
                    borderWidth: 2
                  }
                ]
              }}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: {
                      color: '#D1D5DB',
                      padding: 15,
                      usePointStyle: true,
                      font: {
                        size: 12
                      }
                    }
                  },
                  tooltip: {
                    backgroundColor: 'rgba(31, 41, 55, 0.9)',
                    titleColor: '#F9FAFB',
                    bodyColor: '#D1D5DB',
                    borderColor: '#4B5563',
                    borderWidth: 1,
                    callbacks: {
                      label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                        const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0';
                        return `${label}: ${value.toLocaleString()} (${percentage}%)`;
                      }
                    }
                  }
                }
              }}
            />
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="flex items-center gap-2 p-4 bg-error-500/10 border border-error-500/20 rounded-lg">
          <AlertCircle className="h-5 w-5 text-error-500 flex-shrink-0" />
          <p className="text-sm text-error-500">{error}</p>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={data.data}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={data.total}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Points Transactions"
        emptyState={{
          icon: <Receipt className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No transactions found',
          description: 'No transactions match your current filters.',
          action: (
            <Button onClick={handleRefresh} variant="primary">
              Refresh Data
            </Button>
          )
        }}
      />

      {/* Transaction Details Modal */}
      {showDetailsModal && selectedTransaction && (
        <Modal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          title="Transaction Details"
          size="lg"
        >
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Transaction ID</label>
                <p className="text-gray-100 font-mono">#{selectedTransaction.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Type</label>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTransactionTypeColor(selectedTransaction.type)}`}>
                  {getTransactionTypeDisplay(selectedTransaction.type)}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Category</label>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTransactionCategoryColor(selectedTransaction.category)}`}>
                  {getTransactionCategoryDisplay(selectedTransaction.category)}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Amount</label>
                <p className="text-gray-100 font-mono font-medium">
                  {formatTransactionAmount(selectedTransaction.amount)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">From User ID</label>
                <p className="text-gray-100">{selectedTransaction.fromUserId || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">To User ID</label>
                <p className="text-gray-100">{selectedTransaction.toUserId || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Created At</label>
                <p className="text-gray-100">{new Date(selectedTransaction.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Updated At</label>
                <p className="text-gray-100">{new Date(selectedTransaction.updatedAt).toLocaleString()}</p>
              </div>
            </div>

            {/* Metadata */}
            {selectedTransaction.metadata && Object.keys(selectedTransaction.metadata).length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Metadata</label>
                <div className="bg-dark-800 rounded-lg p-4 border border-dark-600">
                  <pre className="text-sm text-gray-300 whitespace-pre-wrap">
                    {JSON.stringify(selectedTransaction.metadata, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* User Information */}
            {(selectedTransaction.fromUser || selectedTransaction.toUser) && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-100">User Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {selectedTransaction.fromUser && (
                    <div className="bg-dark-800 rounded-lg p-4 border border-dark-600">
                      <h4 className="text-sm font-medium text-gray-300 mb-2">From User</h4>
                      <div className="space-y-1 text-sm">
                        <p><span className="text-gray-400">ID:</span> <span className="text-gray-100">{selectedTransaction.fromUser.id}</span></p>
                        <p><span className="text-gray-400">External ID:</span> <span className="text-gray-100">{selectedTransaction.fromUser.externalId}</span></p>
                        <p><span className="text-gray-400">Points:</span> <span className="text-gray-100">{selectedTransaction.fromUser.points}</span></p>
                      </div>
                    </div>
                  )}
                  {selectedTransaction.toUser && (
                    <div className="bg-dark-800 rounded-lg p-4 border border-dark-600">
                      <h4 className="text-sm font-medium text-gray-300 mb-2">To User</h4>
                      <div className="space-y-1 text-sm">
                        <p><span className="text-gray-400">ID:</span> <span className="text-gray-100">{selectedTransaction.toUser.id}</span></p>
                        <p><span className="text-gray-400">External ID:</span> <span className="text-gray-100">{selectedTransaction.toUser.externalId}</span></p>
                        <p><span className="text-gray-400">Points:</span> <span className="text-gray-100">{selectedTransaction.toUser.points}</span></p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
              <Button
                onClick={() => setShowDetailsModal(false)}
                variant="secondary"
              >
                Close
              </Button>
              <Button
                onClick={(e) => {
                  setShowDetailsModal(false);
                  handleDelete(selectedTransaction, e as any);
                }}
                variant="danger"
                className="flex items-center gap-2"
              >
                <Trash2 size={16} />
                Erase from history
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default Transactions;
