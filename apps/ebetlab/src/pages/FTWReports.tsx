import React, { useState, useCallback } from 'react';
import { DollarSign, Download, Filter, RefreshCw, Eye, TrendingDown, AlertCircle, Search, Users, Calendar } from 'lucide-react';
import { fetchFTWReports, type FTWData, type FTWSearchParams } from '../utils/api';
import { useDataFetching } from '../hooks/useDataFetching';
import Pagination from '../components/ui/Pagination';
import QuickUserInfoModal from '../components/customer-details/QuickUserInfoModal';

const FTWReports: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [showQuickInfoModal, setShowQuickInfoModal] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | number | null>(null);
  const [searchParams, setSearchParams] = useState<Partial<FTWSearchParams>>({});

  // Fetch FTW reports data
  const fetchFTWData = useCallback(async () => {
    const result = await fetchFTWReports(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load FTW reports');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: ftwData, loading, error, refetch } = useDataFetching({
    fetchFn: fetchFTWData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  // Format currency with proper symbol
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format date only
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString();
  };

  // Handle customer modal
  const handleCustomerClick = (ftw: FTWData) => {
    setSelectedCustomerId(ftw.customer_id);
    setShowQuickInfoModal(true);
  };

  // Handle close modal
  const handleCloseModal = () => {
    setShowQuickInfoModal(false);
    setSelectedCustomerId(null);
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: string | number | null) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value === '' ? null : value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Handle export
  const handleExport = () => {
    if (!ftwData?.data) return;
    
    const csvContent = [
      // Headers
      ['Customer ID', 'Username', 'Registration Date', 'Currency', 'First Withdrawal Date', 'First Withdrawal Amount', 'Total Withdrawal Amount', 'Phone'].join(','),
      // Data rows
      ...ftwData.data.map(ftw => [
        ftw.customer_id,
        ftw.username,
        formatDate(ftw.registration_ts),
        ftw.currency,
        formatDate(ftw.first_withdrawal_date),
        `${ftw.first_withdrawal_amount} ${ftw.first_withdrawal_currency}`,
        `${ftw.total_withdrawal_amount} ${ftw.currency}`,
        ftw.phone_full || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ftw-reports-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-100">FTW Reports</h1>
          <p className="text-gray-400 mt-1">First Time Withdrawal reports and analytics</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
              showFilters 
                ? 'bg-primary-600 text-white' 
                : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
            }`}
          >
            <Filter size={16} />
            Filters
          </button>
          <button
            onClick={refetch}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
          <button
            onClick={handleExport}
            disabled={loading || !ftwData?.data?.length}
            className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50"
          >
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      {ftwData && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Withdrawals</p>
                <p className="text-2xl font-bold text-gray-100">{ftwData.total_count.toLocaleString()}</p>
              </div>
              <TrendingDown className="w-8 h-8 text-red-500" />
            </div>
          </div>
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Amount (USD)</p>
                <p className="text-2xl font-bold text-gray-100">${parseFloat(ftwData.total_sum || '0').toFixed(2)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-primary-500" />
            </div>
          </div>
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Average Amount</p>
                <p className="text-2xl font-bold text-gray-100">
                  ${ftwData.total_count > 0 ? (parseFloat(ftwData.total_sum || '0') / ftwData.total_count).toFixed(2) : '0.00'}
                </p>
              </div>
              <Users className="w-8 h-8 text-secondary-500" />
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-semibold text-gray-100 mb-4">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
              <input
                type="text"
                placeholder="Search by username..."
                value={searchParams.username || ''}
                onChange={(e) => handleFilterChange('username', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Currency</label>
              <select
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="TRY">TRY</option>
                <option value="GBP">GBP</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Min Amount (USD)</label>
              <input
                type="number"
                placeholder="0.00"
                value={searchParams.usd_min || ''}
                onChange={(e) => handleFilterChange('usd_min', e.target.value ? parseFloat(e.target.value) : null)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Max Amount (USD)</label>
              <input
                type="number"
                placeholder="0.00"
                value={searchParams.usd_max || ''}
                onChange={(e) => handleFilterChange('usd_max', e.target.value ? parseFloat(e.target.value) : null)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-error-500/20 border border-error-500 rounded-md p-4">
          <div className="flex items-center gap-2">
            <AlertCircle size={20} className="text-error-500" />
            <p className="text-error-500 font-medium">Error loading FTW reports</p>
          </div>
          <p className="text-gray-400 mt-1">{error}</p>
        </div>
      )}

      {/* Table */}
      {!error && (
        <>
          <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-dark-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Customer</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Registration</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">First Withdrawal</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Total Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Phone</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <RefreshCw size={20} className="animate-spin mr-2" />
                          Loading FTW reports...
                        </div>
                      </td>
                    </tr>
                  ) : ftwData?.data?.length ? (
                    ftwData.data.map((ftw) => (
                      <tr
                        key={ftw.customer_id}
                        onClick={() => handleCustomerClick(ftw)}
                        className="cursor-pointer hover:bg-dark-600/50 transition-colors"
                      >
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">#{ftw.customer_id}</div>
                            <div className="text-sm text-gray-400">{ftw.username}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {formatDate(ftw.registration_ts)}
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm text-gray-300">{formatDate(ftw.first_withdrawal_date)}</div>
                            <div className="text-xs text-gray-400">ID: {ftw.first_withdrawal_id}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">
                              {formatCurrency(ftw.first_withdrawal_amount, ftw.first_withdrawal_currency)}
                            </div>
                            <div className="text-xs text-gray-400">${parseFloat(ftw.first_withdrawal_amount_usd).toFixed(2)} USD</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">
                              {formatCurrency(ftw.total_withdrawal_amount, ftw.currency)}
                            </div>
                            <div className="text-xs text-gray-400">${parseFloat(ftw.total_usd_amount).toFixed(2)} USD</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {ftw.phone_full || '-'}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="text-center py-8">
                        <p className="text-gray-400">No FTW reports found.</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil((ftwData?.total_count || 0) / itemsPerPage)}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            totalItems={ftwData?.total_count || 0}
          />
        </>
      )}

      {/* Quick User Info Modal */}
      {showQuickInfoModal && selectedCustomerId && (
        <QuickUserInfoModal
          isOpen={showQuickInfoModal}
          onClose={handleCloseModal}
          customerId={selectedCustomerId}
        />
      )}
    </div>
  );
};

export default FTWReports;
