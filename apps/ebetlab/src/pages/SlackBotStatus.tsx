import React, { useState, useEffect, useCallback } from 'react';
import { RefreshCw, MessageSquare, Activity, CheckCircle, XCircle, Clock, Hash, Route, Calendar, X, Info } from 'lucide-react';
import { fetchSlackBots, type SlackBot, type SlackBotsResponse } from '../utils/api';
import UnifiedTable, { TableColumn } from '../components/ui/UnifiedTable';

const SlackBotStatus: React.FC = () => {
  // State management
  const [bots, setBots] = useState<SlackBot[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBot, setSelectedBot] = useState<SlackBot | null>(null);
  const [showBotModal, setShowBotModal] = useState(false);

  // Format timestamp to readable date
  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    if (status === 'active') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircle size={12} className="mr-1" />
          Active
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <XCircle size={12} className="mr-1" />
          Inactive
        </span>
      );
    }
  };

  // Format type text
  const formatType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ');
  };

  // Table column configuration for UnifiedTable
  const tableColumns: TableColumn<SlackBot>[] = [
    {
      key: 'id',
      label: 'Bot ID',
      width: '200px',
      render: (bot) => <span className="font-mono text-sm">{bot.id}</span>
    },
    {
      key: 'type',
      label: 'Type',
      width: '120px',
      render: (bot) => <span className="text-sm">{formatType(bot.type)}</span>
    },
    {
      key: 'route',
      label: 'Route',
      width: '250px',
      render: (bot) => (
        <span className="font-mono text-sm bg-gray-100 text-gray-800 px-2 py-1 rounded">
          {bot.route}
        </span>
      )
    },
    {
      key: 'channel',
      label: 'Channel',
      width: '150px',
      render: (bot) => (
        <span className="inline-flex items-center text-sm">
          <Hash size={14} className="mr-1 text-gray-400" />
          {bot.channel}
        </span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      width: '100px',
      render: (bot) => getStatusBadge(bot.status)
    },
    {
      key: 'registeredAt',
      label: 'Registered At',
      width: '180px',
      render: (bot) => (
        <span className="text-sm flex items-center">
          <Calendar size={14} className="mr-1 text-gray-400" />
          {formatDate(bot.registeredAt)}
        </span>
      )
    },
    {
      key: 'description',
      label: 'Description',
      width: '300px',
      render: (bot) => (
        <span className="text-sm text-gray-600" title={bot.description}>
          {bot.description.length > 50 
            ? `${bot.description.substring(0, 50)}...` 
            : bot.description
          }
        </span>
      )
    }
  ];

  // Event handlers
  const handleRefresh = () => {
    loadBots();
  };

  const handleBotClick = (bot: SlackBot) => {
    setSelectedBot(bot);
    setShowBotModal(true);
  };

  const handleCloseModal = () => {
    setShowBotModal(false);
    setSelectedBot(null);
  };

  // API functions
  const loadBots = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await fetchSlackBots();

      if (result.success && result.data) {
        setBots(result.data.bots || []);
        setStats(result.data.stats || null);
      } else {
        setError(result.error || 'Failed to load slack bots');
        setBots([]);
        setStats(null);
      }
    } catch (err) {
      console.error('Error loading slack bots:', err);
      setError('An unexpected error occurred');
      setBots([]);
      setStats(null);
    } finally {
      setLoading(false);
    }
  }, []);

  // Effects
  useEffect(() => {
    loadBots();
  }, [loadBots]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-500/20 rounded-lg">
            <MessageSquare className="w-6 h-6 text-purple-500" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">Slack Bot Status</h1>
            <p className="text-gray-400">Monitor registered Slack bots and their status</p>
          </div>
        </div>
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="btn btn-outline flex items-center gap-2"
        >
          <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
          Refresh
        </button>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Bots</p>
                <p className="text-2xl font-bold text-white">{stats.totalBots}</p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Active Channels</p>
                <p className="text-2xl font-bold text-white">{stats.activeChannels}</p>
              </div>
              <Hash className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Slack Status</p>
                <p className="text-lg font-bold text-white">
                  {stats.isSlackConfigured ? (
                    <span className="text-green-400">Configured</span>
                  ) : (
                    <span className="text-red-400">Not Configured</span>
                  )}
                </p>
              </div>
              {stats.isSlackConfigured ? (
                <CheckCircle className="w-8 h-8 text-green-500" />
              ) : (
                <XCircle className="w-8 h-8 text-red-500" />
              )}
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Registration Range</p>
                <p className="text-sm text-white">
                  {stats.registrationTimeRange.earliest === stats.registrationTimeRange.latest 
                    ? 'Same time' 
                    : 'Multiple times'
                  }
                </p>
              </div>
              <Clock className="w-8 h-8 text-purple-500" />
            </div>
          </div>
        </div>
      )}

      {/* Slack Bots Table */}
      <UnifiedTable
        data={bots}
        columns={tableColumns}
        title="Registered Slack Bots"
        subtitle={`${bots.length} bots registered`}
        isLoading={loading}
        error={error}
        onRowClick={handleBotClick}
        onRetry={handleRefresh}
        minWidth="1400px"
        emptyState={{
          icon: <MessageSquare className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No Slack bots found',
          description: 'There are no registered Slack bots to display.'
        }}
      />

      {/* Bot Details Modal */}
      {showBotModal && selectedBot && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div className="flex items-center gap-3">
                <MessageSquare className="w-6 h-6 text-purple-500" />
                <h2 className="text-xl font-bold text-white">Slack Bot Details</h2>
              </div>
              <button
                onClick={handleCloseModal}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 space-y-6">
              {/* Bot ID */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Bot ID</label>
                <div className="bg-gray-700 rounded-lg p-3">
                  <span className="font-mono text-sm text-white">{selectedBot.id}</span>
                </div>
              </div>

              {/* Type and Status */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Type</label>
                  <div className="bg-gray-700 rounded-lg p-3">
                    <span className="text-white">{formatType(selectedBot.type)}</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Status</label>
                  <div className="bg-gray-700 rounded-lg p-3">
                    {getStatusBadge(selectedBot.status)}
                  </div>
                </div>
              </div>

              {/* Route */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Route</label>
                <div className="bg-gray-700 rounded-lg p-3">
                  <span className="font-mono text-sm text-white">{selectedBot.route}</span>
                </div>
              </div>

              {/* Channel */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Slack Channel</label>
                <div className="bg-gray-700 rounded-lg p-3 flex items-center">
                  <Hash size={16} className="text-gray-400 mr-2" />
                  <span className="text-white">{selectedBot.channel}</span>
                </div>
              </div>

              {/* Registration Date */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Registered At</label>
                <div className="bg-gray-700 rounded-lg p-3 flex items-center">
                  <Calendar size={16} className="text-gray-400 mr-2" />
                  <span className="text-white">{formatDate(selectedBot.registeredAt)}</span>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description</label>
                <div className="bg-gray-700 rounded-lg p-3">
                  <p className="text-white text-sm leading-relaxed">{selectedBot.description}</p>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end p-6 border-t border-gray-700">
              <button
                onClick={handleCloseModal}
                className="btn btn-outline"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SlackBotStatus;
