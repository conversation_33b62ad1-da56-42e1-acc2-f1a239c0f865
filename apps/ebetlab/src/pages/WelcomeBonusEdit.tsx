import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Star, ArrowLeft, AlertCircle, CheckCircle } from 'lucide-react';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { useAppContext } from '../contexts/AppContext';
import { fetchWelcomeBonusDetails, updateWelcomeBonus, type WelcomeBonusCreateData, type WelcomeBonusData } from '../utils/api';

interface FormData {
  code: string;
  currency_code: string;
  amount: string;
  usd_amount: string;
  required_wager: string;
  required_wager_currency: string;
  total: string;
  ref_code: string;
  date_start: string;
  date_end: string;
}

const WelcomeBonusEdit: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { configurations } = useAppContext();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState<FormData>({
    code: '',
    currency_code: '',
    amount: '',
    usd_amount: '',
    required_wager: '',
    required_wager_currency: '',
    total: '',
    ref_code: '',
    date_start: '',
    date_end: ''
  });

  // Get currencies from configurations
  const currencies = configurations?.currencies || [];

  // Load bonus details on component mount
  useEffect(() => {
    const loadBonusDetails = async () => {
      if (!id) return;
      
      setIsLoadingData(true);
      setError('');

      try {
        const response = await fetchWelcomeBonusDetails(id);

        if (response.success && response.data) {
          const bonusData: WelcomeBonusData = response.data.data;
          
          // Convert timestamps to datetime-local format
          const formatDateForInput = (timestamp: number) => {
            const date = new Date(timestamp * 1000);
            return date.toISOString().slice(0, 16);
          };

          setFormData({
            code: bonusData.code,
            currency_code: bonusData.currency_code,
            amount: bonusData.amount,
            usd_amount: bonusData.usd_amount,
            required_wager: bonusData.required_wager,
            required_wager_currency: bonusData.required_wager_currency,
            total: bonusData.total.toString(),
            ref_code: bonusData.ref_code,
            date_start: formatDateForInput(bonusData.from),
            date_end: formatDateForInput(bonusData.to)
          });
        } else {
          setError(response.error || 'Failed to load welcome bonus details');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setIsLoadingData(false);
      }
    };

    loadBonusDetails();
  }, [id]);

  // Handle form field changes
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    if (!formData.code.trim()) {
      setError('Please enter a bonus code');
      return false;
    }
    if (!formData.currency_code) {
      setError('Please select a currency');
      return false;
    }
    if (!formData.amount.trim()) {
      setError('Please enter bonus amount');
      return false;
    }
    if (!formData.usd_amount.trim()) {
      setError('Please enter bonus amount in USD');
      return false;
    }
    if (!formData.required_wager.trim()) {
      setError('Please enter required wager');
      return false;
    }
    if (!formData.required_wager_currency.trim()) {
      setError('Please enter required wager currency');
      return false;
    }
    if (!formData.total.trim()) {
      setError('Please enter bonus count');
      return false;
    }
    if (!formData.ref_code.trim()) {
      setError('Please enter affiliate code');
      return false;
    }
    if (!formData.date_start) {
      setError('Please select start date');
      return false;
    }
    if (!formData.date_end) {
      setError('Please select end date');
      return false;
    }

    // Validate date range
    const startDate = new Date(formData.date_start);
    const endDate = new Date(formData.date_end);
    if (endDate <= startDate) {
      setError('End date must be after start date');
      return false;
    }

    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!validateForm()) {
      return;
    }

    if (!id) {
      setError('Bonus ID is missing');
      return;
    }

    setIsLoading(true);

    try {
      // Convert dates to timestamps
      const fromTimestamp = Math.floor(new Date(formData.date_start).getTime() / 1000);
      const toTimestamp = Math.floor(new Date(formData.date_end).getTime() / 1000);

      const requestBody: WelcomeBonusCreateData = {
        code: formData.code,
        currency_code: formData.currency_code,
        amount: formData.amount,
        usd_amount: formData.usd_amount,
        required_wager: formData.required_wager,
        required_wager_currency: formData.required_wager_currency,
        total: formData.total,
        ref_code: formData.ref_code,
        from: fromTimestamp,
        to: toTimestamp
      };

      const response = await updateWelcomeBonus(id, requestBody);
      
      if (response.success) {
        setSuccess('Welcome bonus updated successfully!');
        setTimeout(() => {
          navigate('/welcome-bonus');
        }, 2000);
      } else {
        setError(response.error || 'Failed to update welcome bonus');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update welcome bonus');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading welcome bonus...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          onClick={() => navigate('/welcome-bonus')}
          variant="ghost"
          size="sm"
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        
        <div className="flex items-center gap-3">
          <Star className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Edit Welcome Bonus</h1>
            <p className="text-gray-400">Update welcome bonus assignment</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
          <span className="text-red-200">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 flex items-center gap-3">
          <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
          <span className="text-green-200">{success}</span>
        </div>
      )}

      {/* Form */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Currency <span className="text-red-400">*</span>
              </label>
              <select
                value={formData.currency_code}
                onChange={(e) => handleInputChange('currency_code', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                required
              >
                <option value="">Select currency</option>
                {currencies.map(currency => (
                  <option key={currency} value={currency}>
                    {currency}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bonus Code <span className="text-red-400">*</span>
              </label>
              <Input
                type="text"
                value={formData.code}
                onChange={(e) => handleInputChange('code', e.target.value)}
                placeholder="Enter bonus code"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bonus Amount <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                step="0.00000001"
                value={formData.amount}
                onChange={(e) => handleInputChange('amount', e.target.value)}
                placeholder="Enter bonus amount"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bonus Amount USD <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.usd_amount}
                onChange={(e) => handleInputChange('usd_amount', e.target.value)}
                placeholder="Enter bonus amount in USD"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Required Wager <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.required_wager}
                onChange={(e) => handleInputChange('required_wager', e.target.value)}
                placeholder="Enter required wager"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Required Wager USD <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.required_wager_currency}
                onChange={(e) => handleInputChange('required_wager_currency', e.target.value)}
                placeholder="Enter required wager in USD"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bonus Count <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                value={formData.total}
                onChange={(e) => handleInputChange('total', e.target.value)}
                placeholder="Enter bonus count"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Affiliate Code <span className="text-red-400">*</span>
              </label>
              <Input
                type="text"
                value={formData.ref_code}
                onChange={(e) => handleInputChange('ref_code', e.target.value)}
                placeholder="Enter affiliate code"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Date Start <span className="text-red-400">*</span>
              </label>
              <Input
                type="datetime-local"
                value={formData.date_start}
                onChange={(e) => handleInputChange('date_start', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Date End <span className="text-red-400">*</span>
              </label>
              <Input
                type="datetime-local"
                value={formData.date_end}
                onChange={(e) => handleInputChange('date_end', e.target.value)}
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              onClick={() => navigate('/welcome-bonus')}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
            >
              {isLoading ? 'Updating...' : 'Update Welcome Bonus'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WelcomeBonusEdit;
