import React, { useState, useEffect, useCallback } from 'react';
import {
  Target, Plus, RefreshCw, AlertCircle, Edit, Trash2, Eye, Calendar,
  DollarSign, Activity, Users, BarChart3, Settings, Clock, CheckCircle,
  XCircle, Filter, Search, X, Coins, Power, Globe, Languages
} from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import Modal from '../components/ui/Modal';
import Input from '../components/ui/Input';
import {
  fetchMissions,
  createMission,
  updateMission,
  deleteMission,
  getMissionStatus,
  getMissionStatusDisplay,
  MISSION_TYPE_OPTIONS,
  type Mission,
  type CreateMissionRequest,
  type UpdateMissionRequest,
  type MissionType
} from '../utils/api/missions';
import {
  fetchMissionParticipations,
  fetchParticipationsByMissionId,
  fetchMissionStatistics,
  fetchEnhancedMissionStatistics,
  type MissionParticipation,
  type EnhancedMissionStats
} from '../utils/api/mission-participations';
import {
  fetchMissionRules,
  createMissionRule,
  updateMissionRule,
  deleteMissionRule,
  RULE_TYPE_OPTIONS,
  COMPARE_OPERATOR_OPTIONS,
  type MissionRule,
  type CreateMissionRuleRequest,
  type UpdateMissionRuleRequest,
  type RuleType,
  type CompareOperator
} from '../utils/api/mission-rules';
import {
  fetchAssignmentsByMissionId,
  assignMultipleRulesToMission,
  replaceAllRulesForMission
} from '../utils/api/mission-rule-assignments';


import { ManageMissionObjectivesModal } from '../components/mission-objectives';

const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'tr', name: 'Turkish' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' }
];

interface MissionsData {
  total: number;
  data: Mission[];
}

interface MissionFormData {
  name: string;
  missionType: MissionType;
  reward: number;
  description: string;
  startDate: string;
  endDate: string;
  name_i18n: Record<string, string>;
  description_i18n: Record<string, string>;
  isActive: boolean;
}

interface MissionRuleFormData {
  ruleType: RuleType;
  compare: CompareOperator;
  compareValue: string;
  minDate: string;
  maxDate: string;
}



const Missions: React.FC = () => {
  // State management
  const [missionsData, setMissionsData] = useState<MissionsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [rulesError, setRulesError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sortState, setSortState] = useState<SortState>({
    field: 'id',
    direction: 'desc'
  });

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showRulesModal, setShowRulesModal] = useState(false);
  const [showObjectivesModal, setShowObjectivesModal] = useState(false);
  const [showParticipationsModal, setShowParticipationsModal] = useState(false);
  const [showCreateRuleModal, setShowCreateRuleModal] = useState(false);
  const [showEditRuleModal, setShowEditRuleModal] = useState(false);
  const [showDisableModal, setShowDisableModal] = useState(false);
  const [showTutorial, setShowTutorial] = useState(true);
  const [removeCompletely, setRemoveCompletely] = useState(false);

  // Selected mission and form data
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [formData, setFormData] = useState<MissionFormData>({
    name: '',
    missionType: 'daily',
    reward: 0,
    description: '',
    startDate: '',
    endDate: '',
    name_i18n: { en: '' },
    description_i18n: { en: '' },
    isActive: true
  });

  // Additional data
  const [missionRules, setMissionRules] = useState<MissionRule[]>([]); // For main page rules list
  const [modalMissionRules, setModalMissionRules] = useState<MissionRule[]>([]); // For modal rules selection
  const [missionParticipations, setMissionParticipations] = useState<MissionParticipation[]>([]);
  const [missionStats, setMissionStats] = useState<EnhancedMissionStats | null>(null);
  const [assignedRules, setAssignedRules] = useState<number[]>([]);
  const [rulesFilter, setRulesFilter] = useState<any>({});
  const [selectedRule, setSelectedRule] = useState<MissionRule | null>(null);
  const [ruleFormData, setRuleFormData] = useState<MissionRuleFormData>({
    ruleType: 'joinedAt',
    compare: 'eq',
    compareValue: '',
    minDate: '',
    maxDate: ''
  });
  const [rulesLoading, setRulesLoading] = useState(false);

  // Helper function to convert datetime-local to Unix timestamp
  const convertDateTimeToTimestamp = (dateTimeString: string): number | undefined => {
    if (!dateTimeString) return undefined;
    return Math.floor(new Date(dateTimeString).getTime() / 1000);
  };

  // Load missions data
  const loadMissions = useCallback(async (
    page: number = currentPage,
    limit: number = itemsPerPage,
    searchFilters: Record<string, unknown> = filters
  ) => {
    setIsLoading(true);
    setError('');

    try {
      // Process date filters to convert datetime-local to timestamps
      const processedFilters = { ...searchFilters };

      // Convert datetime-local fields to Unix timestamps
      if (processedFilters.startDateFrom && typeof processedFilters.startDateFrom === 'string') {
        processedFilters.startDateFrom = convertDateTimeToTimestamp(processedFilters.startDateFrom);
      }
      if (processedFilters.startDateTo && typeof processedFilters.startDateTo === 'string') {
        processedFilters.startDateTo = convertDateTimeToTimestamp(processedFilters.startDateTo);
      }
      if (processedFilters.endDateFrom && typeof processedFilters.endDateFrom === 'string') {
        processedFilters.endDateFrom = convertDateTimeToTimestamp(processedFilters.endDateFrom);
      }
      if (processedFilters.endDateTo && typeof processedFilters.endDateTo === 'string') {
        processedFilters.endDateTo = convertDateTimeToTimestamp(processedFilters.endDateTo);
      }

      // Convert numeric fields to proper types
      if (processedFilters.reward !== undefined && typeof processedFilters.reward === 'string') {
        const rewardValue = parseFloat(processedFilters.reward);
        processedFilters.reward = !isNaN(rewardValue) ? rewardValue : undefined;
      }
      if (processedFilters.rewardMin !== undefined && typeof processedFilters.rewardMin === 'string') {
        const rewardMinValue = parseFloat(processedFilters.rewardMin);
        processedFilters.rewardMin = !isNaN(rewardMinValue) ? rewardMinValue : undefined;
      }
      if (processedFilters.rewardMax !== undefined && typeof processedFilters.rewardMax === 'string') {
        const rewardMaxValue = parseFloat(processedFilters.rewardMax);
        processedFilters.rewardMax = !isNaN(rewardMaxValue) ? rewardMaxValue : undefined;
      }

      // Build query parameters for native API filtering
      const queryParams = {
        page,
        limit,
        sortBy: sortState.field || undefined,
        sortOrder: sortState.direction === 'asc' ? 'ASC' as const : 'DESC' as const,
        ...processedFilters
      };

      // Use the new paginated API
      const response = await fetchMissions(queryParams);

      if (response.success && response.data) {
        setMissionsData({
          total: response.meta.total,
          data: response.data
        });
      } else {
        setError(response.error || 'Failed to load missions');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load mission rules with comprehensive filtering support
  const loadMissionRules = useCallback(async (queryParams?: any) => {
    setRulesLoading(true);
    try {
      // Default to getting all rules for selection, but allow filtering
      const params = queryParams || { limit: 1000 };
      const response = await fetchMissionRules(params);
      if (response.success) {
        setMissionRules(response.data);
      } else {
        setError(response.error || 'Failed to load mission rules');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load mission rules');
    } finally {
      setRulesLoading(false);
    }
  }, []);

  // Apply rules filter when it changes
  useEffect(() => {
    if (showRulesModal && Object.keys(rulesFilter).length > 0) {
      const filterParams = {
        limit: 1000,
        ...rulesFilter
      };
      loadMissionRules(filterParams);
    }
  }, [rulesFilter, showRulesModal, loadMissionRules]);



  // Load missions and rules on component mount
  useEffect(() => {
    loadMissions();
    loadMissionRules();
  }, [loadMissions, loadMissionRules]);

  // Utility functions
  const formatDate = (timestamp: number, missionType?: MissionType) => {
    if (!timestamp) return 'N/A';

    // For daily/weekly/monthly missions, always show "-"
    if (missionType && (missionType === 'daily' || missionType === 'weekly' || missionType === 'monthly')) {
      return '-';
    }

    const date = new Date(timestamp * 1000);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatDateForInput = (timestamp: number) => {
    if (!timestamp) return '';
    const date = new Date(timestamp * 1000);
    return date.toISOString().slice(0, 16); // Format for datetime-local input
  };

  const parseInputDate = (dateString: string): number => {
    return Math.floor(new Date(dateString).getTime() / 1000);
  };

  const getStatusBadge = (mission: Mission) => {
    const status = getMissionStatusDisplay(mission);
    const statusColors = {
      'Upcoming': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      'Active': 'bg-green-500/20 text-green-400 border-green-500/30',
      'Expired': 'bg-red-500/20 text-red-400 border-red-500/30'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${statusColors[status.label as keyof typeof statusColors]}`}>
        <Activity className="w-3 h-3 mr-1" />
        {status.label}
      </span>
    );
  };

  const getMissionTypeBadge = (missionType: MissionType) => {
    const typeColors = {
      daily: 'bg-green-500/20 text-green-400 border-green-500/30',
      weekly: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      monthly: 'bg-purple-500/20 text-purple-400 border-purple-500/30',
      custom: 'bg-orange-500/20 text-orange-400 border-orange-500/30'
    };

    const typeLabels = {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      custom: 'Custom'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border whitespace-nowrap ${typeColors[missionType]}`}>
        {typeLabels[missionType]}
      </span>
    );
  };

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadMissions(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadMissions(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadMissions(1, itemsPerPage, newFilters);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (mission: Mission) => {
    setSelectedMission(mission);
    setShowDetailsModal(true);
  };

  // Form handlers
  const resetForm = () => {
    setFormData({
      name: '',
      missionType: 'daily',
      reward: 0,
      description: '',
      startDate: '',
      endDate: '',
      name_i18n: { en: '' },
      description_i18n: { en: '' },
      isActive: true
    });
  };

  const handleInputChange = (field: keyof MissionFormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle i18n field changes
  const updateNameI18n = (lang: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      name_i18n: { ...prev.name_i18n, [lang]: value }
    }));
  };

  const updateDescriptionI18n = (lang: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      description_i18n: { ...prev.description_i18n, [lang]: value }
    }));
  };

  const validateForm = (): string | null => {
    if (!formData.name.trim()) return 'Mission name is required';
    if (!formData.description.trim()) return 'Mission description is required';
    if (formData.reward <= 0) return 'Reward must be greater than 0';

    // Only validate dates for custom missions
    if (formData.missionType === 'custom') {
      if (!formData.startDate) return 'Start date is required for custom missions';
      if (!formData.endDate) return 'End date is required for custom missions';

      const startTime = parseInputDate(formData.startDate);
      const endTime = parseInputDate(formData.endDate);

      if (endTime <= startTime) return 'End date must be after start date';
    }

    return null;
  };

  // CRUD operations
  const handleCreate = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      // For non-custom missions, use default dates (current time and 1 day later)
      const now = Math.floor(Date.now() / 1000);
      const defaultEndDate = now + 86400; // 24 hours later

      const missionData: CreateMissionRequest = {
        name: formData.name,
        missionType: formData.missionType,
        reward: formData.reward,
        description: formData.description,
        startDate: formData.missionType === 'custom' ? parseInputDate(formData.startDate) : now,
        endDate: formData.missionType === 'custom' ? parseInputDate(formData.endDate) : defaultEndDate,
        name_i18n: formData.name_i18n,
        description_i18n: formData.description_i18n,
        isActive: formData.isActive
      };

      const response = await createMission(missionData);

      if (response.success) {
        setShowCreateModal(false);
        resetForm();
        loadMissions(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to create mission');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create mission');
    }
  };

  const handleEdit = (mission: Mission, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedMission(mission);
    setFormData({
      name: mission.name,
      missionType: mission.missionType,
      reward: mission.reward,
      description: mission.description,
      startDate: formatDateForInput(mission.startDate),
      endDate: formatDateForInput(mission.endDate),
      name_i18n: mission.name_i18n || { en: mission.name },
      description_i18n: mission.description_i18n || { en: mission.description },
      isActive: mission.isActive !== undefined ? mission.isActive : true
    });
    setShowEditModal(true);
  };

  const handleUpdate = async () => {
    if (!selectedMission) return;

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      // For non-custom missions, use default dates (current time and 1 day later)
      const now = Math.floor(Date.now() / 1000);
      const defaultEndDate = now + 86400; // 24 hours later

      const missionData: UpdateMissionRequest = {
        name: formData.name,
        missionType: formData.missionType,
        reward: formData.reward,
        description: formData.description,
        startDate: formData.missionType === 'custom' ? parseInputDate(formData.startDate) : now,
        endDate: formData.missionType === 'custom' ? parseInputDate(formData.endDate) : defaultEndDate,
        name_i18n: formData.name_i18n,
        description_i18n: formData.description_i18n,
        isActive: formData.isActive
      };

      const response = await updateMission(selectedMission.id, missionData);

      if (response.success) {
        setShowEditModal(false);
        setSelectedMission(null);
        resetForm();
        loadMissions(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to update mission');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update mission');
    }
  };

  const handleDisable = (mission: Mission, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedMission(mission);
    setRemoveCompletely(false);

    // If mission is already inactive, enable it directly
    if (!mission.isActive) {
      handleToggleActive(mission, true);
    } else {
      // If mission is active, show disable modal
      setShowDisableModal(true);
    }
  };

  const handleToggleActive = async (mission: Mission, isActive: boolean) => {
    try {
      const response = await updateMission(mission.id, { isActive });
      if (response.success) {
        loadMissions(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || `Failed to ${isActive ? 'enable' : 'disable'} mission`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to ${isActive ? 'enable' : 'disable'} mission`);
    }
  };

  const handleConfirmDisable = async () => {
    if (!selectedMission) return;

    try {
      if (removeCompletely) {
        // Delete the mission completely
        const response = await deleteMission(selectedMission.id);
        if (response.success) {
          setShowDisableModal(false);
          setSelectedMission(null);
          setRemoveCompletely(false);
          loadMissions(currentPage, itemsPerPage, filters);
        } else {
          setError(response.error || 'Failed to delete mission');
        }
      } else {
        // Just disable the mission
        await handleToggleActive(selectedMission, false);
        setShowDisableModal(false);
        setSelectedMission(null);
        setRemoveCompletely(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update mission');
    }
  };

  const handleView = (mission: Mission, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedMission(mission);
    setShowDetailsModal(true);
  };

  const handleManageRules = async (mission: Mission, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedMission(mission);
    setRulesError(''); // Clear any previous errors

    try {
      // Load mission rules and current assignments
      const [rulesResponse, assignmentsResponse] = await Promise.all([
        fetchMissionRules({ limit: 1000 }), // Get all rules for selection
        fetchAssignmentsByMissionId(mission.id)
      ]);

      if (rulesResponse.success) {
        setModalMissionRules(rulesResponse.data); // Use modal-specific state
      }

      if (assignmentsResponse.success && assignmentsResponse.data) {
        // Handle new API response structure: data.assignments contains the array
        let assignmentsData: any[] = [];

        if ((assignmentsResponse.data as any).assignments && Array.isArray((assignmentsResponse.data as any).assignments)) {
          assignmentsData = (assignmentsResponse.data as any).assignments;
        } else if (Array.isArray(assignmentsResponse.data)) {
          // Fallback for direct array response
          assignmentsData = assignmentsResponse.data;
        }

        setAssignedRules(assignmentsData.map((assignment: any) => assignment.missionRuleId));
      }

      setShowRulesModal(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load mission rules');
    }
  };

  const handleViewParticipations = async (mission: Mission, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedMission(mission);

    try {
      // Load mission participations and statistics
      const [participationsResponse, statsResponse] = await Promise.all([
        fetchParticipationsByMissionId(mission.id),
        fetchEnhancedMissionStatistics(mission.id)
      ]);

      console.log('Participations Response:', participationsResponse);
      console.log('Stats Response:', statsResponse);

      if (participationsResponse.success) {
        console.log('Setting participations:', participationsResponse.data);
        // Ensure we have valid array data
        const participationsData = Array.isArray(participationsResponse.data) ? participationsResponse.data : [];
        setMissionParticipations(participationsData);
      } else {
        console.error('Participations fetch failed:', participationsResponse.error);
        setMissionParticipations([]);
      }

      if (statsResponse.success) {
        console.log('Setting stats:', statsResponse.data);
        setMissionStats(statsResponse.data);
      } else {
        console.error('Stats fetch failed:', statsResponse.error);
        setMissionStats(null);
      }

      setShowParticipationsModal(true);
    } catch (err) {
      console.error('Error in handleViewParticipations:', err);
      setError(err instanceof Error ? err.message : 'Failed to load mission participations');
    }
  };

  const handleRuleToggle = (ruleId: number) => {
    setAssignedRules(prev =>
      prev.includes(ruleId)
        ? prev.filter(id => id !== ruleId)
        : [...prev, ruleId]
    );
  };

  const handleSaveRules = async () => {
    if (!selectedMission) return;

    setRulesError(''); // Clear any previous errors

    try {
      const response = await replaceAllRulesForMission(selectedMission.id, {
        missionRuleIds: assignedRules
      });

      if (response.success) {
        setShowRulesModal(false);
        setSelectedMission(null);
        setAssignedRules([]);
        setRulesError('');
      } else {
        setRulesError(response.error || response.message || 'Failed to save mission rules');
      }
    } catch (err) {
      setRulesError(err instanceof Error ? err.message : 'Failed to save mission rules');
    }
  };

  // Mission Rule form handlers
  const resetRuleForm = () => {
    setRuleFormData({
      ruleType: 'joinedAt',
      compare: 'eq',
      compareValue: '',
      minDate: '',
      maxDate: ''
    });
  };

  const handleRuleInputChange = (field: keyof MissionRuleFormData, value: string) => {
    setRuleFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateRuleForm = (): string | null => {
    if (!ruleFormData.compareValue.trim()) return 'Compare value is required';
    return null;
  };

  const parseInputDateToTimestamp = (dateString: string): number | null => {
    if (!dateString) return null;
    return Math.floor(new Date(dateString).getTime() / 1000);
  };

  // Mission Rule CRUD operations
  const handleCreateRule = async () => {
    const validationError = validateRuleForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      const ruleData: CreateMissionRuleRequest = {
        ruleType: ruleFormData.ruleType,
        compare: ruleFormData.compare,
        compareValue: ruleFormData.compareValue, // Always send as string
        minDate: parseInputDateToTimestamp(ruleFormData.minDate),
        maxDate: parseInputDateToTimestamp(ruleFormData.maxDate)
      };

      const response = await createMissionRule(ruleData);

      if (response.success) {
        setShowCreateRuleModal(false);
        resetRuleForm();
        loadMissionRules();
      } else {
        setError(response.error || 'Failed to create mission rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create mission rule');
    }
  };

  const handleEditRule = (rule: MissionRule) => {
    setSelectedRule(rule);

    // Always use compareValue as string
    setRuleFormData({
      ruleType: rule.ruleType,
      compare: rule.compare,
      compareValue: String(rule.compareValue || ''),
      minDate: rule.minDate ? formatDateForInput(rule.minDate) : '',
      maxDate: rule.maxDate ? formatDateForInput(rule.maxDate) : ''
    });
    setShowEditRuleModal(true);
  };

  const handleUpdateRule = async () => {
    if (!selectedRule) return;

    const validationError = validateRuleForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      const ruleData: UpdateMissionRuleRequest = {
        ruleType: ruleFormData.ruleType,
        compare: ruleFormData.compare,
        compareValue: ruleFormData.compareValue, // Always send as string
        minDate: parseInputDateToTimestamp(ruleFormData.minDate),
        maxDate: parseInputDateToTimestamp(ruleFormData.maxDate)
      };

      const response = await updateMissionRule(selectedRule.id, ruleData);

      if (response.success) {
        setShowEditRuleModal(false);
        setSelectedRule(null);
        resetRuleForm();
        loadMissionRules();
      } else {
        setError(response.error || 'Failed to update mission rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update mission rule');
    }
  };

  const handleDeleteRule = async (rule: MissionRule) => {
    if (!window.confirm(`Are you sure you want to delete this mission rule?`)) return;

    try {
      const response = await deleteMissionRule(rule.id);

      if (response.success) {
        loadMissionRules();
      } else {
        setError(response.error || 'Failed to delete mission rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete mission rule');
    }
  };

  // Mission Objectives handlers
  const handleManageObjectives = (mission: Mission, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedMission(mission);
    setShowObjectivesModal(true);
  };









  // Table configuration
  const tableColumns: TableColumn<Mission>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (mission) => <span className="font-mono text-sm">#{mission.id}</span>
    },
    {
      key: 'name',
      label: 'Mission Name',
      width: '200px',
      sortable: true,
      render: (mission) => (
        <div className="flex items-center gap-2">
          <Target className="w-4 h-4 text-gray-400" />
          <span className="font-medium text-sm">{mission.name}</span>
        </div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      width: '250px',
      sortable: false,
      render: (mission) => (
        <span className="text-sm text-gray-300 max-w-xs truncate block">
          {mission.description || 'No description'}
        </span>
      )
    },
    {
      key: 'missionType',
      label: 'Type',
      width: '100px',
      sortable: true,
      render: (mission) => getMissionTypeBadge(mission.missionType)
    },
    {
      key: 'reward',
      label: 'Reward',
      width: '100px',
      sortable: true,
      render: (mission) => (
        <div className="flex items-center gap-2">
          <Coins className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium">{mission.reward}</span>
        </div>
      )
    },
    {
      key: 'startDate',
      label: 'Start Date',
      width: '160px',
      sortable: true,
      render: (mission) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm whitespace-nowrap">{formatDate(mission.startDate, mission.missionType)}</span>
        </div>
      )
    },
    {
      key: 'endDate',
      label: 'End Date',
      width: '160px',
      sortable: true,
      render: (mission) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm whitespace-nowrap">{formatDate(mission.endDate, mission.missionType)}</span>
        </div>
      )
    },
    {
      key: 'isActive',
      label: 'Active',
      width: '80px',
      sortable: true,
      render: (mission) => (
        <div className="flex items-center justify-center">
          {mission.isActive ? (
            <CheckCircle className="w-4 h-4 text-green-400" title="Active" />
          ) : (
            <XCircle className="w-4 h-4 text-red-400" title="Inactive" />
          )}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '200px',
      sortable: false,
      render: (mission) => (
        <div className="flex gap-1">
          <button
            onClick={(e) => handleView(mission, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-blue-400 transition-colors"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={(e) => handleEdit(mission, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500 transition-colors"
            title="Edit Mission"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => handleManageRules(mission, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-purple-400 transition-colors"
            title="Manage Rules"
          >
            <Settings size={16} />
          </button>
          <button
            onClick={(e) => handleManageObjectives(mission, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-orange-400 transition-colors"
            title="Manage Objectives"
          >
            <Target size={16} />
          </button>
          <button
            onClick={(e) => handleViewParticipations(mission, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-green-400 transition-colors"
            title="View Participations"
          >
            <Users size={16} />
          </button>
          <button
            onClick={(e) => handleDisable(mission, e)}
            className={`p-1.5 rounded-md text-gray-300 hover:bg-dark-700 transition-colors ${
              mission.isActive ? 'hover:text-orange-400' : 'hover:text-green-400'
            }`}
            title={mission.isActive ? 'Disable Mission' : 'Enable Mission'}
          >
            <Power size={16} />
          </button>
        </div>
      )
    }
  ];

  // Filter configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Information',
      fields: [
        {
          key: 'name',
          label: 'Mission Name',
          type: 'text',
          placeholder: 'Search by mission name...'
        },
        {
          key: 'description',
          label: 'Description',
          type: 'text',
          placeholder: 'Search by description...'
        },
        {
          key: 'search',
          label: 'General Search',
          type: 'text',
          placeholder: 'Search across all fields...'
        },
        {
          key: 'missionType',
          label: 'Mission Type',
          type: 'select',
          options: MISSION_TYPE_OPTIONS.map(option => ({
            value: option.value,
            label: option.label
          }))
        }
      ]
    },
    {
      name: 'status',
      displayName: 'Status & Timing',
      fields: [
        {
          key: 'status',
          label: 'Status',
          type: 'select',
          options: [
            { value: 'active', label: 'Active' },
            { value: 'upcoming', label: 'Upcoming' },
            { value: 'expired', label: 'Expired' }
          ]
        }
      ]
    },
    {
      name: 'rewards',
      displayName: 'Reward Filtering',
      fields: [
        {
          key: 'reward',
          label: 'Exact Reward',
          type: 'number',
          placeholder: 'e.g., 500',
          min: 0
        },
        {
          key: 'rewardMin',
          label: 'Minimum Reward',
          type: 'number',
          placeholder: 'e.g., 100',
          min: 0
        },
        {
          key: 'rewardMax',
          label: 'Maximum Reward',
          type: 'number',
          placeholder: 'e.g., 1000',
          min: 0
        }
      ]
    },
    {
      name: 'dates',
      displayName: 'Date Range Filtering',
      fields: [
        {
          key: 'startDateFrom',
          label: 'Start Date From',
          type: 'datetime-local',
          placeholder: 'Select start date from...'
        },
        {
          key: 'startDateTo',
          label: 'Start Date To',
          type: 'datetime-local',
          placeholder: 'Select start date to...'
        },
        {
          key: 'endDateFrom',
          label: 'End Date From',
          type: 'datetime-local',
          placeholder: 'Select end date from...'
        },
        {
          key: 'endDateTo',
          label: 'End Date To',
          type: 'datetime-local',
          placeholder: 'Select end date to...'
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Target className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Missions Management</h1>
            <p className="text-gray-400">Manage missions, rules, and user participations</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => loadMissions(currentPage, itemsPerPage, filters)}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>

          <Button
            onClick={() => {
              resetForm();
              setShowCreateModal(true);
            }}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Mission
          </Button>
        </div>
      </div>

      {/* Tutorial Section */}
      {showTutorial && (
        <div className="bg-gradient-to-r from-primary-500/10 to-blue-500/10 rounded-lg border border-primary-500/20 p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Target className="w-5 h-5 text-primary-500" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-100 mb-2">How Missions Work</h3>
                <div className="space-y-3 text-sm text-gray-300">
                  <p>
                    <strong className="text-primary-400">Missions</strong> are tasks that users can complete to earn points.
                    Each mission has a type (slots, live casino, deposit, etc.), reward amount, and time period.
                  </p>
                  <p>
                    <strong className="text-primary-400">Mission Rules</strong> determine who can participate in missions.
                    Rules can filter by user attributes like VIP level, total deposits, country, etc.
                  </p>
                  <p>
                    <strong className="text-primary-400">Workflow:</strong>
                    1) Create mission rules → 2) Create missions → 3) Assign rules to missions → 4) Users participate and earn points
                  </p>
                  <div className="flex items-center gap-4 mt-4 pt-3 border-t border-primary-500/20">
                    <div className="flex items-center gap-2 text-xs">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span>Active missions are currently running</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span>Upcoming missions will start soon</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span>Expired missions have ended</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Button
              onClick={() => setShowTutorial(false)}
              variant="ghost"
              size="sm"
              className="text-gray-400 hover:text-gray-200"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error</h4>
              <p className="text-red-400 text-sm">{error}</p>
              <Button
                onClick={() => setError('')}
                variant="ghost"
                size="sm"
                className="mt-2 text-red-400 hover:text-red-300"
              >
                Dismiss
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={missionsData?.data || []}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={missionsData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Missions"
        emptyState={{
          icon: <Target className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No missions found',
          description: 'Get started by creating your first mission.',
          action: (
            <Button
              onClick={() => {
                resetForm();
                setShowCreateModal(true);
              }}
              variant="primary"
            >
              Create Mission
            </Button>
          )
        }}
      />

      {/* Mission Rules Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Settings className="w-6 h-6 text-primary-500" />
            <div>
              <h2 className="text-xl font-bold text-gray-100">Mission Rules</h2>
              <p className="text-gray-400 text-sm">Define rules that determine who can participate in missions</p>
            </div>
          </div>

          <Button
            onClick={() => {
              resetRuleForm();
              setShowCreateRuleModal(true);
            }}
            variant="primary"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Rule
          </Button>
        </div>

        {/* Mission Rules Table */}
        <div className="bg-dark-700 rounded-lg border border-dark-600">
          <div className="p-4 border-b border-dark-600">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-200">Available Rules</h3>
              <div className="flex items-center gap-2">
                <Button
                  onClick={loadMissionRules}
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Refresh
                </Button>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            {rulesLoading ? (
              <div className="text-center py-8">
                <div className="flex items-center justify-center">
                  <RefreshCw size={20} className="animate-spin mr-2" />
                  Loading rules...
                </div>
              </div>
            ) : missionRules.length > 0 ? (
              <table className="w-full">
                <thead>
                  <tr className="border-b border-dark-600">
                    <th className="text-left p-4 text-sm font-medium text-gray-300">ID</th>
                    <th className="text-left p-4 text-sm font-medium text-gray-300">Rule Type</th>
                    <th className="text-left p-4 text-sm font-medium text-gray-300">Condition</th>
                    <th className="text-left p-4 text-sm font-medium text-gray-300">Value</th>
                    <th className="text-left p-4 text-sm font-medium text-gray-300">Date Range</th>
                    <th className="text-left p-4 text-sm font-medium text-gray-300">Created</th>
                    <th className="text-left p-4 text-sm font-medium text-gray-300">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {missionRules.map((rule) => (
                    <tr key={rule.id} className="border-b border-dark-600 hover:bg-dark-600/50 transition-colors">
                      <td className="p-4">
                        <span className="font-mono text-sm">#{rule.id}</span>
                      </td>
                      <td className="p-4">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30">
                          {rule.ruleType}
                        </span>
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-300">
                          {COMPARE_OPERATOR_OPTIONS.find(op => op.value === rule.compare)?.label || rule.compare}
                        </span>
                      </td>
                      <td className="p-4">
                        <span className="text-sm font-medium text-gray-200">{rule.compareValue}</span>
                      </td>
                      <td className="p-4">
                        <div className="text-xs text-gray-400">
                          {rule.minDate || rule.maxDate ? (
                            <>
                              {rule.minDate ? new Date(rule.minDate * 1000).toLocaleDateString() : 'Any'} - {rule.maxDate ? new Date(rule.maxDate * 1000).toLocaleDateString() : 'Any'}
                            </>
                          ) : (
                            'No date restriction'
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-xs text-gray-400">{new Date(rule.createdAt).toLocaleDateString()}</span>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-1">
                          <button
                            onClick={() => handleEditRule(rule)}
                            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500 transition-colors"
                            title="Edit Rule"
                          >
                            <Edit size={14} />
                          </button>
                          <button
                            onClick={() => handleDeleteRule(rule)}
                            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-red-400 transition-colors"
                            title="Delete Rule"
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="text-center py-12">
                <Settings className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-300 mb-2">No Mission Rules</h3>
                <p className="text-gray-400 mb-4">Create your first mission rule to get started.</p>
                <Button
                  onClick={() => {
                    resetRuleForm();
                    setShowCreateRuleModal(true);
                  }}
                  variant="primary"
                >
                  Create Rule
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>



      {/* Create Mission Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          resetForm();
          setError('');
        }}
        title="Create New Mission"
        size="lg"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Mission Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter mission name..."
              required
            />

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Mission Type</label>
              <select
                value={formData.missionType}
                onChange={(e) => handleInputChange('missionType', e.target.value as MissionType)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {MISSION_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <Input
            label="Reward Amount"
            type="number"
            value={formData.reward}
            onChange={(e) => handleInputChange('reward', parseFloat(e.target.value) || 0)}
            placeholder="Enter reward amount..."
            min="0"
            step="0.01"
            required
          />

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter mission description..."
              rows={3}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
              required
            />
          </div>

          {/* Mission Names by Language */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Mission Names by Language
            </label>
            <div className="space-y-3">
              {SUPPORTED_LANGUAGES.map(lang => (
                <div key={lang.code}>
                  <label className="block text-xs text-gray-400 mb-1">
                    {lang.name} ({lang.code.toUpperCase()})
                    {lang.code === 'en' && ' *'}
                  </label>
                  <Input
                    value={formData.name_i18n[lang.code] || ''}
                    onChange={(e) => updateNameI18n(lang.code, e.target.value)}
                    placeholder={`Mission name in ${lang.name}`}
                    required={lang.code === 'en'}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Mission Descriptions by Language */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Mission Descriptions by Language
            </label>
            <div className="space-y-3">
              {SUPPORTED_LANGUAGES.map(lang => (
                <div key={lang.code}>
                  <label className="block text-xs text-gray-400 mb-1">
                    {lang.name} ({lang.code.toUpperCase()})
                  </label>
                  <textarea
                    value={formData.description_i18n[lang.code] || ''}
                    onChange={(e) => updateDescriptionI18n(lang.code, e.target.value)}
                    placeholder={`Mission description in ${lang.name}`}
                    rows={3}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500 resize-vertical"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Active Status */}
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => handleInputChange('isActive', e.target.checked)}
              className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500 focus:ring-2"
            />
            <label htmlFor="isActive" className="text-sm font-medium text-gray-300">
              Mission is Active
            </label>
          </div>

          {/* Date pickers only show for custom missions */}
          {formData.missionType === 'custom' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Start Date & Time"
                type="datetime-local"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                required
              />

              <Input
                label="End Date & Time"
                type="datetime-local"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                required
              />
            </div>
          )}

          {/* Information about automatic scheduling for non-custom missions */}
          {formData.missionType !== 'custom' && (
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-md p-4">
              <p className="text-sm text-blue-400">
                <strong>Automatic Scheduling:</strong> {formData.missionType.charAt(0).toUpperCase() + formData.missionType.slice(1)} missions are automatically scheduled and don't require manual date selection.
              </p>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowCreateModal(false);
                resetForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreate}
              variant="primary"
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Create Mission
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Mission Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedMission(null);
          resetForm();
          setError('');
        }}
        title={`Edit Mission: ${selectedMission?.name || ''}`}
        size="lg"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Mission Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter mission name..."
              required
            />

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Mission Type</label>
              <select
                value={formData.missionType}
                onChange={(e) => handleInputChange('missionType', e.target.value as MissionType)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {MISSION_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <Input
            label="Reward Amount"
            type="number"
            value={formData.reward}
            onChange={(e) => handleInputChange('reward', parseFloat(e.target.value) || 0)}
            placeholder="Enter reward amount..."
            min="0"
            step="0.01"
            required
          />

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter mission description..."
              rows={3}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
              required
            />
          </div>

          {/* Mission Names by Language */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Mission Names by Language
            </label>
            <div className="space-y-3">
              {SUPPORTED_LANGUAGES.map(lang => (
                <div key={lang.code}>
                  <label className="block text-xs text-gray-400 mb-1">
                    {lang.name} ({lang.code.toUpperCase()})
                    {lang.code === 'en' && ' *'}
                  </label>
                  <Input
                    value={formData.name_i18n[lang.code] || ''}
                    onChange={(e) => updateNameI18n(lang.code, e.target.value)}
                    placeholder={`Mission name in ${lang.name}`}
                    required={lang.code === 'en'}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Mission Descriptions by Language */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Mission Descriptions by Language
            </label>
            <div className="space-y-3">
              {SUPPORTED_LANGUAGES.map(lang => (
                <div key={lang.code}>
                  <label className="block text-xs text-gray-400 mb-1">
                    {lang.name} ({lang.code.toUpperCase()})
                  </label>
                  <textarea
                    value={formData.description_i18n[lang.code] || ''}
                    onChange={(e) => updateDescriptionI18n(lang.code, e.target.value)}
                    placeholder={`Mission description in ${lang.name}`}
                    rows={3}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500 resize-vertical"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Active Status */}
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="editIsActive"
              checked={formData.isActive}
              onChange={(e) => handleInputChange('isActive', e.target.checked)}
              className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500 focus:ring-2"
            />
            <label htmlFor="editIsActive" className="text-sm font-medium text-gray-300">
              Mission is Active
            </label>
          </div>

          {/* Date pickers only show for custom missions */}
          {formData.missionType === 'custom' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Start Date & Time"
                type="datetime-local"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                required
              />

              <Input
                label="End Date & Time"
                type="datetime-local"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                required
              />
            </div>
          )}

          {/* Information about automatic scheduling for non-custom missions */}
          {formData.missionType !== 'custom' && (
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-md p-4">
              <p className="text-sm text-blue-400">
                <strong>Automatic Scheduling:</strong> {formData.missionType.charAt(0).toUpperCase() + formData.missionType.slice(1)} missions are automatically scheduled and don't require manual date selection.
              </p>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowEditModal(false);
                setSelectedMission(null);
                resetForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdate}
              variant="primary"
              className="flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              Update Mission
            </Button>
          </div>
        </div>
      </Modal>

      {/* Mission Details Modal */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedMission(null);
        }}
        title={`Mission Details: ${selectedMission?.name || ''}`}
        size="lg"
      >
        {selectedMission && (
          <div className="space-y-6">
            {/* Mission Header */}
            <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-500/20">
              <div className="flex items-center gap-3 mb-2">
                <Target className="w-6 h-6 text-blue-400" />
                <h3 className="text-lg font-semibold text-gray-100">{selectedMission.name}</h3>
                <span className="font-mono text-xs bg-dark-700 px-2 py-1 rounded text-gray-400">
                  #{selectedMission.id}
                </span>
              </div>
              <div className="flex items-center gap-4">
                {getMissionTypeBadge(selectedMission.missionType)}
                <div className="flex items-center gap-2">
                  {selectedMission.isActive ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400 font-medium text-sm">Active</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="w-4 h-4 text-red-400" />
                      <span className="text-red-400 font-medium text-sm">Inactive</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="bg-dark-800/50 rounded-lg p-4 border border-dark-600">
                  <label className="block text-sm font-medium text-gray-400 mb-2">Reward</label>
                  <div className="flex items-center gap-2">
                    <Coins className="w-5 h-5 text-yellow-400" />
                    <span className="text-gray-100 font-semibold text-lg">{selectedMission.reward.toFixed(2)}</span>
                    <span className="text-gray-400 text-sm">points</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="bg-dark-800/50 rounded-lg p-4 border border-dark-600">
                  <label className="block text-sm font-medium text-gray-400 mb-2">Mission Duration</label>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-green-400" />
                      <span className="text-xs text-gray-400 uppercase tracking-wide">Start:</span>
                      <span className="text-gray-100 text-sm font-medium">{formatDate(selectedMission.startDate, selectedMission.missionType)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-red-400" />
                      <span className="text-xs text-gray-400 uppercase tracking-wide">End:</span>
                      <span className="text-gray-100 text-sm font-medium">{formatDate(selectedMission.endDate, selectedMission.missionType)}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-dark-800/50 rounded-lg p-4 border border-dark-600">
                  <label className="block text-sm font-medium text-gray-400 mb-2">Created</label>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-blue-400" />
                    <span className="text-gray-100 text-sm">{new Date(selectedMission.createdAt).toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div className="flex items-center gap-2 mb-3">
                <Edit className="w-4 h-4 text-gray-400" />
                <label className="text-sm font-medium text-gray-300">Description</label>
              </div>
              <div className="bg-gradient-to-br from-dark-800 to-dark-900 rounded-lg p-4 border border-dark-600 shadow-lg">
                <p className="text-gray-100 text-sm leading-relaxed whitespace-pre-wrap">
                  {selectedMission.description || (
                    <span className="text-gray-400 italic">No description provided</span>
                  )}
                </p>
              </div>
            </div>

            {/* Localized Names */}
            {selectedMission.name_i18n && Object.keys(selectedMission.name_i18n).length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <Globe className="w-4 h-4 text-blue-400" />
                  <label className="text-sm font-medium text-gray-300">Localized Names</label>
                </div>
                <div className="bg-gradient-to-br from-dark-800 to-dark-900 rounded-lg p-4 border border-dark-600 shadow-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {Object.entries(selectedMission.name_i18n).map(([lang, name]) => {
                      if (!name) return null;
                      const language = SUPPORTED_LANGUAGES.find(l => l.code === lang);
                      return (
                        <div key={lang} className="bg-dark-700/50 rounded-md p-3 border border-dark-600/50 hover:border-blue-500/30 transition-colors">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-xs font-mono bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full uppercase font-semibold min-w-[40px] text-center">
                              {lang}
                            </span>
                            <span className="text-xs text-gray-400 font-medium">
                              {language?.name || lang.toUpperCase()}
                            </span>
                          </div>
                          <p className="text-gray-100 text-sm font-medium leading-relaxed">
                            {name}
                          </p>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            {/* Localized Descriptions */}
            {selectedMission.description_i18n && Object.keys(selectedMission.description_i18n).length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <Languages className="w-4 h-4 text-purple-400" />
                  <label className="text-sm font-medium text-gray-300">Localized Descriptions</label>
                </div>
                <div className="bg-gradient-to-br from-dark-800 to-dark-900 rounded-lg p-4 border border-dark-600 shadow-lg">
                  <div className="space-y-4">
                    {Object.entries(selectedMission.description_i18n).map(([lang, description]) => {
                      if (!description) return null;
                      const language = SUPPORTED_LANGUAGES.find(l => l.code === lang);
                      return (
                        <div key={lang} className="bg-dark-700/50 rounded-md p-4 border border-dark-600/50 hover:border-purple-500/30 transition-colors">
                          <div className="flex items-center gap-2 mb-3">
                            <span className="text-xs font-mono bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full uppercase font-semibold min-w-[40px] text-center">
                              {lang}
                            </span>
                            <span className="text-xs text-gray-400 font-medium">
                              {language?.name || lang.toUpperCase()}
                            </span>
                          </div>
                          <div className="pl-2 border-l-2 border-purple-500/30">
                            <p className="text-gray-100 text-sm leading-relaxed whitespace-pre-wrap">
                              {description}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
              <Button
                onClick={() => {
                  setShowDetailsModal(false);
                  handleEdit(selectedMission, { stopPropagation: () => {} } as React.MouseEvent);
                }}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                Edit Mission
              </Button>

              <Button
                onClick={() => {
                  setShowDetailsModal(false);
                  handleManageRules(selectedMission, { stopPropagation: () => {} } as React.MouseEvent);
                }}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Settings className="w-4 h-4" />
                Manage Rules
              </Button>

              <Button
                onClick={() => {
                  setShowDetailsModal(false);
                  handleManageObjectives(selectedMission, { stopPropagation: () => {} } as React.MouseEvent);
                }}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Target className="w-4 h-4" />
                Manage Objectives
              </Button>

              <Button
                onClick={() => {
                  setShowDetailsModal(false);
                  handleViewParticipations(selectedMission, { stopPropagation: () => {} } as React.MouseEvent);
                }}
                variant="primary"
                className="flex items-center gap-2"
              >
                <Users className="w-4 h-4" />
                View Participations
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* Mission Rules Management Modal */}
      <Modal
        isOpen={showRulesModal}
        onClose={() => {
          setShowRulesModal(false);
          setSelectedMission(null);
          setModalMissionRules([]); // Clear only modal-specific rules
          setAssignedRules([]);
          setRulesError('');
        }}
        title={`Manage Rules: ${selectedMission?.name || ''}`}
        size="lg"
      >
        <div className="space-y-6">
          {/* Error Section */}
          {rulesError && (
            <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-red-500 font-medium mb-1">Error</h4>
                <p className="text-red-400 text-sm">{rulesError}</p>
                <Button
                  onClick={() => setRulesError('')}
                  variant="ghost"
                  size="sm"
                  className="mt-2 text-red-400 hover:text-red-300"
                >
                  Dismiss
                </Button>
              </div>
            </div>
          )}

          {/* Rules Filter Section */}
          <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
            <h4 className="text-sm font-medium text-gray-300 mb-3">Filter Rules</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1">Rule Type</label>
                <select
                  value={rulesFilter.ruleType || ''}
                  onChange={(e) => setRulesFilter(prev => ({ ...prev, ruleType: e.target.value || undefined }))}
                  className="w-full px-2 py-1 text-sm bg-dark-700 border border-dark-600 rounded text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Types</option>
                  {RULE_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1">Compare Operator</label>
                <select
                  value={rulesFilter.compare || ''}
                  onChange={(e) => setRulesFilter(prev => ({ ...prev, compare: e.target.value || undefined }))}
                  className="w-full px-2 py-1 text-sm bg-dark-700 border border-dark-600 rounded text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Operators</option>
                  {COMPARE_OPERATOR_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1">Search</label>
                <input
                  type="text"
                  value={rulesFilter.search || ''}
                  onChange={(e) => setRulesFilter(prev => ({ ...prev, search: e.target.value || undefined }))}
                  className="w-full px-2 py-1 text-sm bg-dark-700 border border-dark-600 rounded text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search rules..."
                />
              </div>
            </div>
            <div className="flex justify-end mt-3">
              <button
                onClick={() => {
                  setRulesFilter({});
                  loadMissionRules({ limit: 1000 });
                }}
                className="px-3 py-1 text-xs bg-dark-600 hover:bg-dark-500 text-gray-300 rounded"
              >
                Clear Filters
              </button>
            </div>
          </div>

          <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Available Mission Rules</h4>
            <p className="text-xs text-gray-400 mb-4">
              Select the rules that should apply to this mission. Users must meet all selected rules to participate.
            </p>

            {missionRules.length > 0 ? (
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {missionRules.map((rule) => (
                  <label
                    key={rule.id}
                    className="flex items-start gap-3 p-3 bg-dark-700 rounded-md border border-dark-600 hover:border-dark-500 transition-colors cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={assignedRules.includes(rule.id)}
                      onChange={() => handleRuleToggle(rule.id)}
                      className="mt-1 rounded border-dark-500 bg-dark-800 text-primary-500 focus:ring-primary-500 focus:ring-offset-dark-800"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium text-gray-200">
                          {rule.ruleType}
                        </span>
                        <span className="text-xs text-gray-400">
                          {rule.compare} {rule.compareValue}
                        </span>
                      </div>
                      {(rule.minDate || rule.maxDate) && (
                        <div className="text-xs text-gray-500">
                          Date range: {rule.minDate ? new Date(rule.minDate * 1000).toLocaleDateString() : 'Any'} - {rule.maxDate ? new Date(rule.maxDate * 1000).toLocaleDateString() : 'Any'}
                        </div>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Settings className="w-8 h-8 text-gray-500 mx-auto mb-2" />
                <p className="text-gray-400 text-sm">No mission rules available</p>
                <p className="text-gray-500 text-xs">Create mission rules first to assign them to missions</p>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowRulesModal(false);
                setSelectedMission(null);
                setModalMissionRules([]); // Clear only modal-specific rules
                setAssignedRules([]);
                setRulesError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveRules}
              variant="primary"
              className="flex items-center gap-2"
            >
              <CheckCircle className="w-4 h-4" />
              Save Rules ({assignedRules.length})
            </Button>
          </div>
        </div>
      </Modal>

      {/* Mission Objectives Management Modal */}
      <ManageMissionObjectivesModal
        isOpen={showObjectivesModal}
        onClose={() => {
          setShowObjectivesModal(false);
          setSelectedMission(null);
        }}
        mission={selectedMission}
      />

      {/* Mission Participations Modal */}
      <Modal
        isOpen={showParticipationsModal}
        onClose={() => {
          setShowParticipationsModal(false);
          setSelectedMission(null);
          setMissionParticipations([]);
          setMissionStats(null);
        }}
        title={`Participations: ${selectedMission?.name || ''}`}
        size="xl"
      >
        <div className="space-y-6">
          {/* Enhanced Statistics Dashboard */}
          {missionStats && (
            <div className="space-y-6">
              {/* Primary Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
                  <div className="flex items-center gap-2 mb-2">
                    <Users className="w-5 h-5 text-blue-400" />
                    <span className="text-sm font-medium text-gray-300">Unique Participants</span>
                  </div>
                  <span className="text-2xl font-bold text-blue-400">{missionStats.statistics.uniqueParticipants}</span>
                  <div className="text-xs text-gray-400 mt-1">
                    of {missionStats.statistics.totalExtendedUsers} total users
                  </div>
                </div>

                <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
                  <div className="flex items-center gap-2 mb-2">
                    <Activity className="w-5 h-5 text-purple-400" />
                    <span className="text-sm font-medium text-gray-300">Total Participations</span>
                  </div>
                  <span className="text-2xl font-bold text-purple-400">{missionStats.statistics.totalParticipations}</span>
                  <div className="text-xs text-gray-400 mt-1">
                    {missionStats.statistics.totalParticipations > missionStats.statistics.uniqueParticipants ? 'Multiple entries per user' : 'One entry per user'}
                  </div>
                </div>

                <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-400" />
                    <span className="text-sm font-medium text-gray-300">Completed</span>
                  </div>
                  <span className="text-2xl font-bold text-green-400">{missionStats.statistics.completedParticipations}</span>
                  <div className="text-xs text-gray-400 mt-1">
                    {missionStats.breakdown.completed.percentage.toFixed(1)}% of participations
                  </div>
                </div>

                <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-5 h-5 text-yellow-400" />
                    <span className="text-sm font-medium text-gray-300">Pending</span>
                  </div>
                  <span className="text-2xl font-bold text-yellow-400">{missionStats.statistics.pendingParticipations}</span>
                  <div className="text-xs text-gray-400 mt-1">
                    {missionStats.breakdown.pending.percentage.toFixed(1)}% of participations
                  </div>
                </div>
              </div>

              {/* Rate Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
                  <div className="flex items-center gap-2 mb-3">
                    <BarChart3 className="w-5 h-5 text-primary-500" />
                    <span className="text-sm font-medium text-gray-300">Completion Rate</span>
                  </div>
                  <div className="flex items-end gap-4">
                    <span className="text-3xl font-bold text-primary-500">{missionStats.statistics.completionRate.toFixed(1)}%</span>
                    <div className="flex-1">
                      <div className="w-full bg-dark-700 rounded-full h-2">
                        <div
                          className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${missionStats.statistics.completionRate}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-400 mt-2">
                    {missionStats.statistics.completedParticipations} completed out of {missionStats.statistics.totalParticipations} participations
                  </div>
                </div>

                <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
                  <div className="flex items-center gap-2 mb-3">
                    <Target className="w-5 h-5 text-cyan-400" />
                    <span className="text-sm font-medium text-gray-300">Participation Rate</span>
                  </div>
                  <div className="flex items-end gap-4">
                    <span className="text-3xl font-bold text-cyan-400">{(missionStats.statistics.participationRate).toFixed(1)}%</span>
                    <div className="flex-1">
                      <div className="w-full bg-dark-700 rounded-full h-2">
                        <div
                          className="bg-cyan-400 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${missionStats.statistics.participationRate}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-400 mt-2">
                    {missionStats.statistics.uniqueParticipants} participants out of {missionStats.statistics.totalExtendedUsers} total users
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Participations List */}
          <div className="bg-dark-800 rounded-md border border-dark-600">
            <div className="p-4 border-b border-dark-600">
              <h4 className="text-sm font-medium text-gray-300">Recent Participations</h4>
            </div>

            <div className="max-h-64 overflow-y-auto">
              {console.log('Rendering participations, length:', missionParticipations.length, 'data:', missionParticipations)}
              {missionParticipations.length > 0 ? (
                <div className="divide-y divide-dark-600">
                  {missionParticipations.slice(0, 20).map((participation, index) => {
                    console.log(`Participation ${index}:`, participation);

                    // Safety checks for required fields
                    if (!participation || typeof participation !== 'object') {
                      console.warn(`Invalid participation at index ${index}:`, participation);
                      return null;
                    }

                    const userId = participation.userId || participation.user_id || 'Unknown';
                    const participationId = participation.id || index;
                    const isCompleted = participation.isCompleted ?? participation.is_completed ?? false;
                    const createdAt = participation.createdAt || participation.created_at || new Date().toISOString();

                    return (
                      <div key={participationId} className="p-4 flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-dark-700 rounded-full flex items-center justify-center">
                            <span className="text-xs font-medium text-gray-300">
                              {userId.toString().slice(-2)}
                            </span>
                          </div>
                          <div>
                            <span className="text-sm font-medium text-gray-200">
                              User #{userId}
                            </span>
                            <div className="text-xs text-gray-400">
                              Joined: {new Date(createdAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {isCompleted ? (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Completed
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
                              <Clock className="w-3 h-3 mr-1" />
                              Pending
                            </span>
                          )}
                        </div>
                      </div>
                    );
                  }).filter(Boolean)}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="w-8 h-8 text-gray-500 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">No participations yet</p>
                  <p className="text-gray-500 text-xs">Users will appear here when they join this mission</p>
                </div>
              )}
            </div>

            {missionParticipations.length > 20 && (
              <div className="p-4 border-t border-dark-600 text-center">
                <span className="text-xs text-gray-400">
                  Showing 20 of {missionParticipations.length} participations
                </span>
              </div>
            )}
          </div>

          <div className="flex justify-end pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowParticipationsModal(false);
                setSelectedMission(null);
                setMissionParticipations([]);
                setMissionStats(null);
              }}
              variant="outline"
            >
              Close
            </Button>
          </div>
        </div>
      </Modal>

      {/* Create Mission Rule Modal */}
      <Modal
        isOpen={showCreateRuleModal}
        onClose={() => {
          setShowCreateRuleModal(false);
          resetRuleForm();
          setError('');
        }}
        title="Create Mission Rule"
        size="lg"
      >
        <div className="space-y-6">
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-md p-4">
            <h4 className="text-sm font-medium text-blue-400 mb-2">About Mission Rules</h4>
            <p className="text-xs text-blue-300">
              Mission rules determine which users can participate in missions. Users must meet ALL assigned rules to be eligible.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Rule Type</label>
              <select
                value={ruleFormData.ruleType}
                onChange={(e) => handleRuleInputChange('ruleType', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {RULE_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Comparison</label>
              <select
                value={ruleFormData.compare}
                onChange={(e) => handleRuleInputChange('compare', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {COMPARE_OPERATOR_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <Input
            label="Compare Value"
            value={ruleFormData.compareValue}
            onChange={(e) => handleRuleInputChange('compareValue', e.target.value)}
            placeholder="Enter comparison value..."
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Minimum Date (Optional)"
              type="datetime-local"
              value={ruleFormData.minDate}
              onChange={(e) => handleRuleInputChange('minDate', e.target.value)}
            />

            <Input
              label="Maximum Date (Optional)"
              type="datetime-local"
              value={ruleFormData.maxDate}
              onChange={(e) => handleRuleInputChange('maxDate', e.target.value)}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowCreateRuleModal(false);
                resetRuleForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateRule}
              variant="primary"
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Create Rule
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Mission Rule Modal */}
      <Modal
        isOpen={showEditRuleModal}
        onClose={() => {
          setShowEditRuleModal(false);
          setSelectedRule(null);
          resetRuleForm();
          setError('');
        }}
        title={`Edit Mission Rule #${selectedRule?.id || ''}`}
        size="lg"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Rule Type</label>
              <select
                value={ruleFormData.ruleType}
                onChange={(e) => handleRuleInputChange('ruleType', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {RULE_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Comparison</label>
              <select
                value={ruleFormData.compare}
                onChange={(e) => handleRuleInputChange('compare', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {COMPARE_OPERATOR_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <Input
            label="Compare Value"
            value={ruleFormData.compareValue}
            onChange={(e) => handleRuleInputChange('compareValue', e.target.value)}
            placeholder="Enter comparison value..."
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Minimum Date (Optional)"
              type="datetime-local"
              value={ruleFormData.minDate}
              onChange={(e) => handleRuleInputChange('minDate', e.target.value)}
            />

            <Input
              label="Maximum Date (Optional)"
              type="datetime-local"
              value={ruleFormData.maxDate}
              onChange={(e) => handleRuleInputChange('maxDate', e.target.value)}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowEditRuleModal(false);
                setSelectedRule(null);
                resetRuleForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateRule}
              variant="primary"
              className="flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              Update Rule
            </Button>
          </div>
        </div>
      </Modal>

      {/* Disable Mission Modal */}
      <Modal
        isOpen={showDisableModal}
        onClose={() => {
          setShowDisableModal(false);
          setSelectedMission(null);
          setRemoveCompletely(false);
          setError('');
        }}
        title={`Disable Mission: ${selectedMission?.name || ''}`}
        size="md"
      >
        <div className="space-y-6">
          <div className="bg-orange-500/10 border border-orange-500/20 rounded-md p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-orange-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-orange-400 font-medium mb-2">Disable Mission</h4>
                <p className="text-sm text-gray-300">
                  This will disable the mission "{selectedMission?.name}". Users will no longer be able to participate in this mission.
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="removeCompletely"
              checked={removeCompletely}
              onChange={(e) => setRemoveCompletely(e.target.checked)}
              className="w-4 h-4 text-red-600 bg-dark-700 border-dark-600 rounded focus:ring-red-500 focus:ring-2"
            />
            <label htmlFor="removeCompletely" className="text-sm font-medium text-gray-300">
              Remove the mission completely
            </label>
          </div>

          {removeCompletely && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-md p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-red-400 font-medium mb-2">Warning: Permanent Deletion</h4>
                  <p className="text-sm text-gray-300">
                    This action cannot be undone. The mission and all associated data will be permanently deleted.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowDisableModal(false);
                setSelectedMission(null);
                setRemoveCompletely(false);
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmDisable}
              variant={removeCompletely ? "danger" : "warning"}
              className="flex items-center gap-2"
            >
              <Power className="w-4 h-4" />
              {removeCompletely ? 'Delete Mission' : 'Disable Mission'}
            </Button>
          </div>
        </div>
      </Modal>

    </div>
  );
};

export default Missions;
