import React, { useState, useCallback } from 'react';
import { DollarSign, Download, Filter, RefreshCw, Eye, TrendingUp, AlertCircle, Copy, Search, Percent } from 'lucide-react';
import { fetchDiscountsAdmin, fetchDiscountsSummary, type DiscountData, type DiscountsSearchParams } from '../utils/api';
import { useDataFetching } from '../hooks/useDataFetching';
import Pagination from '../components/ui/Pagination';
import DateRangePicker from '../components/ui/DateRangePicker';
import Modal from '../components/ui/Modal';

interface DiscountDetailsModalProps {
  discount: DiscountData;
}

const DiscountDetailsModal: React.FC<DiscountDetailsModalProps> = ({ discount }) => {
  const [copySuccess, setCopySuccess] = useState(false);

  const handleCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£',
      'TRX': 'TRX'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-gray-100">Discount Details</h3>
        <div className="flex items-center gap-2">
          <span className="px-3 py-1 rounded-full text-sm font-medium bg-green-500/20 text-green-400">
            <Percent size={12} className="inline mr-1" />
            Discount Applied
          </span>
        </div>
      </div>

      {/* Customer Information */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
        <h4 className="text-lg font-semibold text-gray-100 mb-4">Customer Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Customer ID:</span>
              <span className="text-gray-100 font-mono">{discount.customer.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Username:</span>
              <span className="text-gray-100">{discount.customer.username}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Masked Username:</span>
              <span className="text-gray-100">{discount.customer.masked_username}</span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Last Action:</span>
              <span className="text-gray-100">{formatTimestamp(discount.customer.last_action)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Discount Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Discount Details</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Discount ID:</span>
              <span className="text-gray-100 font-mono">{discount.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Currency:</span>
              <span className="text-gray-100">{discount.code}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Discount Amount:</span>
              <span className="text-gray-100 font-semibold text-green-400">
                {formatCurrency(discount.amount, discount.code)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Applied:</span>
              <span className="text-gray-100">{formatTimestamp(discount.timestamp)}</span>
            </div>
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Balance Changes</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Before Balance:</span>
              <span className="text-gray-100">
                {formatCurrency(discount.before_balance, discount.code)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">After Balance:</span>
              <span className="text-gray-100 font-semibold">
                {formatCurrency(discount.after_balance, discount.code)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Net Change:</span>
              <span className="text-green-400 font-semibold">
                +{formatCurrency(discount.amount, discount.code)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Related Transaction */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
        <h4 className="text-lg font-semibold text-gray-100 mb-4">Related Deposit Transaction</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Transaction ID:</span>
              <span className="text-gray-100 font-mono">{discount.transaction.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Deposit Amount:</span>
              <span className="text-gray-100 font-semibold">
                {formatCurrency(discount.depositAmount, discount.code)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">USD Amount:</span>
              <span className="text-gray-100">
                ${parseFloat(discount.transaction.usd_amount).toFixed(2)}
              </span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Transaction Date:</span>
              <span className="text-gray-100">{formatTimestamp(discount.transaction.timestamp)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Provider:</span>
              <span className="text-gray-100">{discount.transaction.provider || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Discount Rate:</span>
              <span className="text-primary-400 font-semibold">
                {((parseFloat(discount.amount) / parseFloat(discount.depositAmount)) * 100).toFixed(2)}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {copySuccess && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg">
          Copied to clipboard!
        </div>
      )}
    </div>
  );
};

const DiscountsAdmin: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedDiscount, setSelectedDiscount] = useState<DiscountData | null>(null);
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<DiscountsSearchParams>>({});
  const [dateRange, setDateRange] = useState<{ from: Date | null; to: Date | null }>({
    from: null,
    to: null
  });

  // Fetch discounts data
  const fetchDiscountsData = useCallback(async () => {
    const result = await fetchDiscountsAdmin(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load discounts');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  // Fetch summary data
  const fetchSummaryData = useCallback(async () => {
    const result = await fetchDiscountsSummary(searchParams);
    
    if (result.success && result.data) {
      return result.data.data.data;
    } else {
      throw new Error(result.error || 'Failed to load discounts summary');
    }
  }, [searchParams]);

  const { data: discountsData, loading, error, refetch } = useDataFetching({
    fetchFn: fetchDiscountsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  const { data: summaryData, loading: summaryLoading } = useDataFetching({
    fetchFn: fetchSummaryData,
    dependencies: [searchParams]
  });

  // Format currency with proper symbol
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£',
      'TRX': 'TRX'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Handle discount modal
  const handleDiscountClick = (discount: DiscountData) => {
    setSelectedDiscount(discount);
    setShowDiscountModal(true);
  };

  const handleCloseModal = () => {
    setShowDiscountModal(false);
    setSelectedDiscount(null);
  };

  // Helper functions
  function handleItemsPerPageChange(newItemsPerPage: number) {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  }

  function handleFilterChange(key: keyof DiscountsSearchParams, value: string) {
    setSearchParams(prev => ({
      ...prev,
      [key]: value || null
    }));
    setCurrentPage(1);
  }

  // Handle date range changes
  const handleDateRangeChange = (range: { from: Date | null; to: Date | null }) => {
    setDateRange(range);
    const newSearchParams = { ...searchParams };

    if (range.from) {
      newSearchParams.from = Math.floor(range.from.getTime() / 1000).toString();
    } else {
      delete newSearchParams.from;
    }

    if (range.to) {
      newSearchParams.to = Math.floor(range.to.getTime() / 1000).toString();
    } else {
      delete newSearchParams.to;
    }

    setSearchParams(newSearchParams);
    setCurrentPage(1);
  };

  function clearFilters() {
    setSearchParams({});
    setDateRange({ from: null, to: null });
    setCurrentPage(1);
  }

  // Export functionality
  const handleExport = () => {
    if (!discountsData?.data || discountsData.data.length === 0) {
      return;
    }

    const csvHeaders = [
      'ID', 'Customer ID', 'Username', 'Currency', 'Discount Amount', 'Deposit Amount',
      'Before Balance', 'After Balance', 'Transaction ID', 'USD Amount', 'Applied Date'
    ];

    const csvData = discountsData.data.map(discount => [
      discount.id,
      discount.customer_id,
      discount.customer.username,
      discount.code,
      discount.amount,
      discount.depositAmount,
      discount.before_balance,
      discount.after_balance,
      discount.transaction.id,
      discount.transaction.usd_amount,
      formatTimestamp(discount.timestamp)
    ]);

    const csvContent = [csvHeaders, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `discounts_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Percent className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Discounts</h1>
            <p className="text-gray-400">Manage discount transactions and analytics</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
              showFilters
                ? 'bg-primary-600 text-white'
                : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
            }`}
          >
            <Filter size={16} />
            Filters
          </button>
          <button
            onClick={refetch}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
          <button
            onClick={handleExport}
            disabled={!discountsData?.data || discountsData.data.length === 0}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      {summaryData && summaryData.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {summaryData.slice(0, 3).map((summary, index) => (
            <div key={`${summary.code}-${index}`} className="bg-dark-700 rounded-lg border border-dark-600 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">{summary.code} Discounts</p>
                  <p className="text-2xl font-bold text-gray-100">{formatCurrency(summary.sum, summary.code)}</p>
                </div>
                <Percent className="w-8 h-8 text-primary-500" />
              </div>
            </div>
          ))}
        </div>
      )}



      {/* Filters */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-semibold text-gray-100 mb-4">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
              <input
                type="text"
                placeholder="Search by username..."
                value={searchParams.username || ''}
                onChange={(e) => handleFilterChange('username', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Currency</label>
              <select
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="TRY">TRY</option>
                <option value="GBP">GBP</option>
                <option value="TRX">TRX</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
              <DateRangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
              />
            </div>
          </div>
          <div className="flex justify-end mt-4">
            <button
              onClick={clearFilters}
              className="px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-500/20 border border-red-500 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-400" />
          <span className="text-red-400">{error}</span>
        </div>
      )}

      {/* Table */}
      {!error && (
        <>
          <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-dark-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">ID</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Customer</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Currency</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Discount Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Deposit Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Discount Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Applied Date</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {loading ? (
                    <tr>
                      <td colSpan={7} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <RefreshCw size={20} className="animate-spin mr-2" />
                          Loading discounts...
                        </div>
                      </td>
                    </tr>
                  ) : discountsData?.data?.length ? (
                    discountsData.data.map((discount) => (
                      <tr
                        key={discount.id}
                        onClick={() => handleDiscountClick(discount)}
                        className="cursor-pointer hover:bg-dark-600/50 transition-colors"
                      >
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-100">#{discount.id}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">#{discount.customer_id}</div>
                            <div className="text-sm text-gray-400">{discount.customer.username}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {discount.code}
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-100">
                            {formatCurrency(discount.amount, discount.code)}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm text-gray-300">
                              {formatCurrency(discount.depositAmount, discount.code)}
                            </div>
                            <div className="text-xs text-gray-400">${parseFloat(discount.transaction.usd_amount).toFixed(2)} USD</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-100">
                            {((parseFloat(discount.amount) / parseFloat(discount.depositAmount)) * 100).toFixed(2)}%
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {formatTimestamp(discount.timestamp)}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="text-center py-8">
                        <p className="text-gray-400">No discounts found.</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil((discountsData?.total || 0) / itemsPerPage)}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            totalItems={discountsData?.total || 0}
          />
        </>
      )}

      {/* Discount Details Modal */}
      {showDiscountModal && selectedDiscount && (
        <Modal
          isOpen={showDiscountModal}
          onClose={handleCloseModal}
          title="Discount Details"
          size="lg"
        >
          <DiscountDetailsModal discount={selectedDiscount} />
        </Modal>
      )}
    </div>
  );
};

export default DiscountsAdmin;
