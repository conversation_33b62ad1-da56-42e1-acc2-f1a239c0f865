import React, { useState } from 'react';
import { Terminal, Send, Copy, RefreshCw, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { sendDebugApiRequest, getExampleRequestBody, type DebugApiResponse } from '../utils/api/debug';

const DebugEbetlabApi: React.FC = () => {
  const [url, setUrl] = useState('https://service.ebetlab.com/api/operator/customers/index/1/20');
  const [method, setMethod] = useState('POST');
  const [requestBody, setRequestBody] = useState('');
  const [response, setResponse] = useState<DebugApiResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Initialize with example request body
  React.useEffect(() => {
    const exampleBody = getExampleRequestBody('/api/operator/customers/index/1/20');
    setRequestBody(JSON.stringify(exampleBody, null, 2));
  }, []);

  const convertToProxyUrl = (originalUrl: string): string => {
    const proxyBaseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000';

    // Convert https://service.ebetlab.com/api/... to http://localhost:3000/debug/api/...
    if (originalUrl.includes('service.ebetlab.com')) {
      return originalUrl.replace('https://service.ebetlab.com', `${proxyBaseUrl}/debug`);
    }

    // If it already has /debug, use as is
    if (originalUrl.includes('/debug')) {
      return originalUrl;
    }

    // If it's a relative path starting with /api, prepend proxy URL with /debug
    if (originalUrl.startsWith('/api')) {
      return `${proxyBaseUrl}/debug${originalUrl}`;
    }

    return originalUrl;
  };

  const handleSendRequest = async () => {
    if (!url.trim()) {
      alert('Please enter a URL');
      return;
    }

    setIsLoading(true);
    setResponse(null);

    try {
      let parsedBody = null;
      if (requestBody.trim() && ['POST', 'PUT', 'PATCH'].includes(method)) {
        try {
          parsedBody = JSON.parse(requestBody);
        } catch (error) {
          alert('Invalid JSON in request body');
          setIsLoading(false);
          return;
        }
      }

      // Convert the URL to use proxy server
      const proxyUrl = convertToProxyUrl(url);
      console.log('Original URL:', url);
      console.log('Proxy URL:', proxyUrl);

      const result = await sendDebugApiRequest(proxyUrl, method, parsedBody);
      setResponse(result.data || null);
    } catch (error) {
      console.error('Request failed:', error);
      setResponse({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyResponse = async () => {
    if (!response) return;

    try {
      await navigator.clipboard.writeText(JSON.stringify(response, null, 2));
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleLoadExample = () => {
    try {
      const urlPath = new URL(url).pathname;
      const exampleBody = getExampleRequestBody(urlPath);
      setRequestBody(JSON.stringify(exampleBody, null, 2));
    } catch (error) {
      // If URL parsing fails, use default example
      const exampleBody = getExampleRequestBody('');
      setRequestBody(JSON.stringify(exampleBody, null, 2));
    }
  };

  const formatJsonString = (str: string) => {
    try {
      const parsed = JSON.parse(str);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return str;
    }
  };

  const handleFormatJson = () => {
    setRequestBody(formatJsonString(requestBody));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-primary-500/20 rounded-lg">
          <Terminal className="w-6 h-6 text-primary-500" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-white">Debug Ebetlab API</h1>
          <p className="text-gray-400">Test API endpoints through the wildcard debug proxy</p>
        </div>
      </div>

      {/* Info Banner */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-blue-200">
            <p className="font-medium mb-1">How it works:</p>
            <p>Enter the actual Ebetlab endpoint URL below. The system will automatically convert it to use your local proxy server.
            For example: <code className="bg-blue-500/20 px-1 rounded">https://service.ebetlab.com/api/...</code> → <code className="bg-blue-500/20 px-1 rounded">{import.meta.env.VITE_API_URL || 'http://localhost:3000'}/debug/api/...</code></p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Request Panel */}
        <div className="bg-dark-700 rounded-lg border border-dark-600">
          <div className="p-4 border-b border-dark-600">
            <h2 className="text-lg font-semibold text-white">Request</h2>
          </div>
          
          <div className="p-4 space-y-4">
            {/* URL Input */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                URL <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://service.ebetlab.com/api/..."
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-white placeholder-gray-500 focus:outline-none focus:border-primary-500"
              />
            </div>

            {/* Method Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Method
              </label>
              <select
                value={method}
                onChange={(e) => setMethod(e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-white focus:outline-none focus:border-primary-500"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="PATCH">PATCH</option>
                <option value="DELETE">DELETE</option>
              </select>
            </div>

            {/* Request Body */}
            {['POST', 'PUT', 'PATCH'].includes(method) && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Request Body (JSON)
                  </label>
                  <div className="flex gap-2">
                    <button
                      onClick={handleLoadExample}
                      className="text-xs text-primary-400 hover:text-primary-300"
                    >
                      Load Example
                    </button>
                    <button
                      onClick={handleFormatJson}
                      className="text-xs text-primary-400 hover:text-primary-300"
                    >
                      Format JSON
                    </button>
                  </div>
                </div>
                <textarea
                  value={requestBody}
                  onChange={(e) => setRequestBody(e.target.value)}
                  placeholder="Enter JSON request body..."
                  rows={12}
                  className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-white placeholder-gray-500 focus:outline-none focus:border-primary-500 font-mono text-sm"
                />
              </div>
            )}

            {/* Send Button */}
            <button
              onClick={handleSendRequest}
              disabled={isLoading}
              className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-600/50 text-white rounded-md transition-colors"
            >
              {isLoading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
              {isLoading ? 'Sending...' : 'Send Request'}
            </button>
          </div>
        </div>

        {/* Response Panel */}
        <div className="bg-dark-700 rounded-lg border border-dark-600">
          <div className="p-4 border-b border-dark-600 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">Response</h2>
            {response && (
              <button
                onClick={handleCopyResponse}
                className="flex items-center gap-2 px-3 py-1 text-sm bg-dark-600 hover:bg-dark-500 text-gray-300 rounded-md transition-colors"
              >
                {copySuccess ? (
                  <CheckCircle className="w-4 h-4 text-green-400" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
                {copySuccess ? 'Copied!' : 'Copy'}
              </button>
            )}
          </div>
          
          <div className="p-4">
            {!response ? (
              <div className="text-center text-gray-500 py-8">
                <Terminal className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>Send a request to see the response</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Status Indicator */}
                <div className="flex items-center gap-2">
                  {response.success ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-400" />
                  )}
                  <span className={`font-medium ${response.success ? 'text-green-400' : 'text-red-400'}`}>
                    {response.status ? `HTTP ${response.status}` : (response.success ? 'Success' : 'Error')}
                  </span>
                  {response.timestamp && (
                    <span className="text-gray-500 text-sm ml-auto">
                      {new Date(response.timestamp).toLocaleTimeString()}
                    </span>
                  )}
                </div>

                {/* Response Data */}
                <div className="bg-dark-800 rounded-md p-3 max-h-96 overflow-y-auto">
                  <pre className="text-sm text-gray-300 whitespace-pre-wrap font-mono">
                    {JSON.stringify(response, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugEbetlabApi;
