import React, { useState, useCallback } from 'react';
import { DollarSign, Download, Filter, RefreshCw, Eye, TrendingUp, TrendingDown, AlertCircle, Copy, Search } from 'lucide-react';
import { fetchCorrections, fetchCorrectionsSummary, type CorrectionData, type CorrectionsSearchParams } from '../utils/api';
import { useDataFetching } from '../hooks/useDataFetching';
import Pagination from '../components/ui/Pagination';
import DateRangePicker from '../components/ui/DateRangePicker';
import Modal from '../components/ui/Modal';

interface CorrectionDetailsModalProps {
  correction: CorrectionData;
}

const CorrectionDetailsModal: React.FC<CorrectionDetailsModalProps> = ({ correction }) => {
  const [copySuccess, setCopySuccess] = useState(false);

  const handleCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-gray-100">Correction Details</h3>
        <div className="flex items-center gap-2">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            correction.way === 'up' 
              ? 'bg-green-500/20 text-green-400' 
              : 'bg-red-500/20 text-red-400'
          }`}>
            {correction.way === 'up' ? 'Credit' : 'Debit'}
          </span>
        </div>
      </div>

      {/* Customer Information */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
        <h4 className="text-lg font-semibold text-gray-100 mb-4">Customer Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Customer ID:</span>
              <span className="text-gray-100 font-mono">{correction.customer.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Username:</span>
              <span className="text-gray-100">{correction.customer.username}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Email:</span>
              <span className="text-gray-100">{correction.customer.email}</span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Rank:</span>
              <span className="text-gray-100 capitalize">{correction.customer.rank}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Last Action:</span>
              <span className="text-gray-100">{formatTimestamp(correction.customer.last_action)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Basic Information</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">ID:</span>
              <span className="text-gray-100 font-mono">{correction.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Model:</span>
              <span className="text-gray-100 capitalize">{correction.model}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Currency:</span>
              <span className="text-gray-100">{correction.currency}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Wallet ID:</span>
              <span className="text-gray-100 font-mono">{correction.wallet_id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Active:</span>
              <span className={`${correction.is_active ? 'text-green-400' : 'text-red-400'}`}>
                {correction.is_active ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Amount Details</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Amount:</span>
              <span className="text-gray-100 font-semibold">
                {formatCurrency(correction.amount, correction.currency)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">USD Amount:</span>
              <span className="text-gray-100 font-semibold">
                ${parseFloat(correction.usd_amount).toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Before Balance:</span>
              <span className="text-gray-100">
                {formatCurrency(correction.before_balance, correction.currency)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">After Balance:</span>
              <span className="text-gray-100">
                {formatCurrency(correction.after_balance, correction.currency)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Wagering Information */}
      {correction.model === 'bonus' && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Wagering Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Required Wager:</span>
                <span className="text-gray-100">${parseFloat(correction.required_wager).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Wagered:</span>
                <span className="text-gray-100">${parseFloat(correction.wagered).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Completed:</span>
                <span className={`${correction.completed ? 'text-green-400' : 'text-yellow-400'}`}>
                  {correction.completed ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Required Wager (Currency):</span>
                <span className="text-gray-100">
                  {formatCurrency(correction.required_wager_currency, correction.currency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Wagered (Currency):</span>
                <span className="text-gray-100">
                  {formatCurrency(correction.wagered_currency, correction.currency)}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Note and Timestamps */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
        <h4 className="text-lg font-semibold text-gray-100 mb-4">Additional Information</h4>
        <div className="space-y-3">
          {correction.note && (
            <div>
              <span className="text-gray-400 block mb-1">Note:</span>
              <div className="flex items-start gap-2">
                <span className="text-gray-100 flex-1">{correction.note}</span>
                <button
                  onClick={() => handleCopyToClipboard(correction.note)}
                  className="text-gray-400 hover:text-gray-200 p-1"
                  title="Copy note"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
          )}
          <div className="flex justify-between">
            <span className="text-gray-400">Created:</span>
            <span className="text-gray-100">{formatTimestamp(correction.timestamp)}</span>
          </div>
          {correction.completed_at && (
            <div className="flex justify-between">
              <span className="text-gray-400">Completed:</span>
              <span className="text-gray-100">{formatTimestamp(correction.completed_at)}</span>
            </div>
          )}
          {correction.operator && (
            <div className="flex justify-between">
              <span className="text-gray-400">Operator:</span>
              <span className="text-gray-100">{correction.operator.username || 'System'}</span>
            </div>
          )}
        </div>
      </div>

      {copySuccess && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg">
          Copied to clipboard!
        </div>
      )}
    </div>
  );
};

const Corrections: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCorrection, setSelectedCorrection] = useState<CorrectionData | null>(null);
  const [showCorrectionModal, setShowCorrectionModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<CorrectionsSearchParams>>({});
  const [dateRange, setDateRange] = useState<{ from: Date | null; to: Date | null }>({
    from: null,
    to: null
  });

  // Fetch corrections data
  const fetchCorrectionsData = useCallback(async () => {
    const result = await fetchCorrections(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load corrections');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  // Fetch summary data
  const fetchSummaryData = useCallback(async () => {
    const result = await fetchCorrectionsSummary();
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load corrections summary');
    }
  }, []);

  const { data: correctionsData, loading, error, refetch } = useDataFetching({
    fetchFn: fetchCorrectionsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  const { data: summaryData, loading: summaryLoading } = useDataFetching({
    fetchFn: fetchSummaryData,
    dependencies: []
  });

  // Format currency with proper symbol
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Handle correction modal
  const handleCorrectionClick = (correction: CorrectionData) => {
    setSelectedCorrection(correction);
    setShowCorrectionModal(true);
  };

  const handleCloseModal = () => {
    setShowCorrectionModal(false);
    setSelectedCorrection(null);
  };

  // Helper functions
  function handleItemsPerPageChange(newItemsPerPage: number) {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  }

  function handleFilterChange(key: keyof CorrectionsSearchParams, value: string) {
    setSearchParams(prev => ({
      ...prev,
      [key]: value || null
    }));
    setCurrentPage(1);
  }

  // Handle date range changes
  const handleDateRangeChange = (range: { from: Date | null; to: Date | null }) => {
    setDateRange(range);
    const newSearchParams = { ...searchParams };

    if (range.from) {
      newSearchParams.from = Math.floor(range.from.getTime() / 1000).toString();
    } else {
      delete newSearchParams.from;
    }

    if (range.to) {
      newSearchParams.to = Math.floor(range.to.getTime() / 1000).toString();
    } else {
      delete newSearchParams.to;
    }

    setSearchParams(newSearchParams);
    setCurrentPage(1);
  };

  function clearFilters() {
    setSearchParams({});
    setDateRange({ from: null, to: null });
    setCurrentPage(1);
  }

  // Export functionality
  const handleExport = () => {
    if (!correctionsData?.data || correctionsData.data.length === 0) {
      return;
    }

    const csvHeaders = [
      'ID', 'Customer ID', 'Username', 'Model', 'Currency', 'Amount', 'USD Amount',
      'Way', 'Before Balance', 'After Balance', 'Note', 'Active', 'Created', 'Completed'
    ];

    const csvData = correctionsData.data.map(correction => [
      correction.id,
      correction.customer_id,
      correction.customer.username,
      correction.model,
      correction.currency,
      correction.amount,
      correction.usd_amount,
      correction.way,
      correction.before_balance,
      correction.after_balance,
      correction.note || '',
      correction.is_active ? 'Yes' : 'No',
      formatTimestamp(correction.timestamp),
      correction.completed_at ? formatTimestamp(correction.completed_at) : ''
    ]);

    const csvContent = [csvHeaders, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `corrections_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <DollarSign className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Balance Corrections</h1>
            <p className="text-gray-400">Manage balance correction transactions</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
              showFilters
                ? 'bg-primary-600 text-white'
                : 'bg-dark-600 text-gray-300 hover:bg-dark-500'
            }`}
          >
            <Filter size={16} />
            Filters
          </button>
          <button
            onClick={refetch}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
          <button
            onClick={handleExport}
            disabled={!correctionsData?.data || correctionsData.data.length === 0}
            className="flex items-center gap-2 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50"
          >
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      {summaryData && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Credits</p>
                <p className="text-2xl font-bold text-gray-100">${parseFloat(summaryData.deposit.deposit).toFixed(2)}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </div>
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Debits</p>
                <p className="text-2xl font-bold text-gray-100">${parseFloat(summaryData.withdraw.withdraw).toFixed(2)}</p>
              </div>
              <TrendingDown className="w-8 h-8 text-red-500" />
            </div>
          </div>
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Net Balance</p>
                <p className="text-2xl font-bold text-gray-100">
                  ${(parseFloat(summaryData.deposit.deposit) - parseFloat(summaryData.withdraw.withdraw)).toFixed(2)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-primary-500" />
            </div>
          </div>
        </div>
      )}



      {/* Filters */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-semibold text-gray-100 mb-4">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
              <input
                type="text"
                placeholder="Search by username..."
                value={searchParams.username || ''}
                onChange={(e) => handleFilterChange('username', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Model</label>
              <select
                value={searchParams.model || ''}
                onChange={(e) => handleFilterChange('model', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Models</option>
                <option value="bonus">Bonus</option>
                <option value="manual">Manual</option>
                <option value="system">System</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Way</label>
              <select
                value={searchParams.way || ''}
                onChange={(e) => handleFilterChange('way', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Ways</option>
                <option value="up">Credit (Up)</option>
                <option value="down">Debit (Down)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Currency</label>
              <select
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="TRY">TRY</option>
                <option value="GBP">GBP</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Min Amount (USD)</label>
              <input
                type="number"
                placeholder="0.00"
                value={searchParams.usd_min || ''}
                onChange={(e) => handleFilterChange('usd_min', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Max Amount (USD)</label>
              <input
                type="number"
                placeholder="0.00"
                value={searchParams.usd_max || ''}
                onChange={(e) => handleFilterChange('usd_max', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
              <DateRangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
              />
            </div>
          </div>
          <div className="flex justify-end mt-4">
            <button
              onClick={clearFilters}
              className="px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-500/20 border border-red-500 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-400" />
          <span className="text-red-400">{error}</span>
        </div>
      )}

      {/* Table */}
      {!error && (
        <>
          <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-dark-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">ID</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Customer</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Model</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Way</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Note</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {loading ? (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <RefreshCw size={20} className="animate-spin mr-2" />
                          Loading corrections...
                        </div>
                      </td>
                    </tr>
                  ) : correctionsData?.data?.length ? (
                    correctionsData.data.map((correction) => (
                      <tr
                        key={correction.id}
                        onClick={() => handleCorrectionClick(correction)}
                        className="cursor-pointer hover:bg-dark-600/50 transition-colors"
                      >
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-100">#{correction.id}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">#{correction.customer_id}</div>
                            <div className="text-sm text-gray-400">{correction.customer.username}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300 capitalize">
                          {correction.model}
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-100">
                              {formatCurrency(correction.amount, correction.currency)}
                            </div>
                            <div className="text-xs text-gray-400">${parseFloat(correction.usd_amount).toFixed(2)} USD</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            correction.way === 'up'
                              ? 'bg-green-500/20 text-green-400'
                              : 'bg-red-500/20 text-red-400'
                          }`}>
                            {correction.way === 'up' ? 'Credit' : 'Debit'}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300 max-w-xs truncate">
                          {correction.note || '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-300">
                          {formatTimestamp(correction.timestamp)}
                        </td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            correction.is_active
                              ? 'bg-green-500/20 text-green-400'
                              : 'bg-gray-500/20 text-gray-400'
                          }`}>
                            {correction.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <p className="text-gray-400">No corrections found.</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil((correctionsData?.total || 0) / itemsPerPage)}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            totalItems={correctionsData?.total || 0}
          />
        </>
      )}

      {/* Correction Details Modal */}
      {showCorrectionModal && selectedCorrection && (
        <Modal
          isOpen={showCorrectionModal}
          onClose={handleCloseModal}
          title="Correction Details"
          size="lg"
        >
          <CorrectionDetailsModal correction={selectedCorrection} />
        </Modal>
      )}
    </div>
  );
};

export default Corrections;
