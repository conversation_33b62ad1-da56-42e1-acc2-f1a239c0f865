import React, { useState, useEffect } from 'react';
import { User, Mail, Shield, Globe, Calendar, MapPin, Settings, Key, Activity } from 'lucide-react';
import { useAuth, type User as UserType } from '../contexts/AuthContext';

const AdminProfile: React.FC = () => {
  const { authData } = useAuth();
  const [user, setUser] = useState<UserType | null>(null);

  useEffect(() => {
    // Get user data from localStorage with key 'betroz_login'
    const storedLoginData = localStorage.getItem('betroz_login');
    if (storedLoginData) {
      try {
        const parsedData = JSON.parse(storedLoginData);
        if (parsedData.user) {
          setUser(parsedData.user);
        }
      } catch (error) {
        console.error('Failed to parse stored login data:', error);
      }
    }
    
    // Fallback to authData if betroz_login is not available
    if (!user && authData?.user) {
      setUser(authData.user);
    }
  }, [authData, user]);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };



  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
        <div className="flex items-center gap-6">
          <div className="relative">
            {user.icon ? (
              <img
                src={user.icon}
                alt={user.name}
                className="w-24 h-24 rounded-full object-cover border-4 border-primary-500"
                onError={(e) => {
                  // Hide image on error and show fallback
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <div className="w-24 h-24 bg-primary-500 rounded-full flex items-center justify-center text-dark-800 border-4 border-primary-500">
                <User size={32} />
              </div>
            )}
            {user.is_active && (
              <div className="absolute bottom-2 right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-dark-700"></div>
            )}
          </div>
          
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-100 mb-2">{user.name}</h1>
            <p className="text-lg text-gray-300 mb-1">@{user.username}</p>
            <div className="flex items-center gap-2 text-sm">
              <Shield className="w-4 h-4 text-primary-500" />
              <span className="text-primary-500 font-medium">Role ID: {user.role_id}</span>
            </div>
          </div>
          
          <div className="text-right">
            <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
              user.is_active 
                ? 'bg-green-500/10 text-green-500 border border-green-500/20' 
                : 'bg-red-500/10 text-red-500 border border-red-500/20'
            }`}>
              <Activity className="w-4 h-4" />
              {user.is_active ? 'Active' : 'Inactive'}
            </div>
          </div>
        </div>
      </div>

      {/* Profile Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Personal Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h2 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
            <User className="w-5 h-5 text-primary-500" />
            Personal Information
          </h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Full Name</label>
              <p className="text-gray-100">{user.name}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
              <p className="text-gray-100">@{user.username}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Email Address</label>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <p className="text-gray-100">{user.email}</p>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">User ID</label>
              <p className="text-gray-100">#{user.id}</p>
            </div>
          </div>
        </div>

        {/* System Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h2 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
            <Settings className="w-5 h-5 text-primary-500" />
            System Information
          </h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Merchant ID</label>
              <p className="text-gray-100">{user.merchant_id}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Website ID</label>
              <p className="text-gray-100">{user.website_id}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Role ID</label>
              <p className="text-gray-100">{user.role_id}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Developer Access</label>
              <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
                user.is_dev 
                  ? 'bg-green-500/10 text-green-500' 
                  : 'bg-gray-500/10 text-gray-500'
              }`}>
                {user.is_dev ? 'Yes' : 'No'}
              </div>
            </div>
          </div>
        </div>

        {/* Security Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h2 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
            <Key className="w-5 h-5 text-primary-500" />
            Security Information
          </h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Two-Factor Authentication</label>
              <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
                user.authenticator_enabled 
                  ? 'bg-green-500/10 text-green-500' 
                  : 'bg-red-500/10 text-red-500'
              }`}>
                <Shield className="w-4 h-4" />
                {user.authenticator_enabled ? 'Enabled' : 'Disabled'}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Last IP Address</label>
              <p className="text-gray-100 font-mono">{user.last_ip}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Last Country</label>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-400" />
                <p className="text-gray-100">{user.last_country}</p>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Full Manager Access</label>
              <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
                user.is_full_manager 
                  ? 'bg-green-500/10 text-green-500' 
                  : 'bg-gray-500/10 text-gray-500'
              }`}>
                {user.is_full_manager ? 'Yes' : 'No'}
              </div>
            </div>
          </div>
        </div>

        {/* Activity Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h2 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
            <Calendar className="w-5 h-5 text-primary-500" />
            Activity Information
          </h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Last Online</label>
              <p className="text-gray-100">{formatDate(user.last_online_at)}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Account Status</label>
              <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
                !user.is_deleted 
                  ? 'bg-green-500/10 text-green-500' 
                  : 'bg-red-500/10 text-red-500'
              }`}>
                {!user.is_deleted ? 'Active Account' : 'Deleted Account'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminProfile;
