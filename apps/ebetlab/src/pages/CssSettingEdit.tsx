import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Settings, AlertCircle, Calendar, User, Globe, Tag, Activity, ExternalLink, Code, Save } from 'lucide-react';
import Button from '../components/ui/Button';
import { fetchCssSetting } from '../utils/api';

interface CssSettingData {
  id: number;
  name: string;
  theme: string;
  merchant_id: number;
  website_id: number;
  operator_id: number;
  path: string;
  last_update: number;
  is_active: boolean;
}

const CssSettingEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [cssSettingData, setCssSettingData] = useState<CssSettingData | null>(null);
  const [error, setError] = useState<string>('');
  const [cssContent, setCssContent] = useState<string>('');
  const [isLoadingCss, setIsLoadingCss] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState<string>('both');
  const [settingName, setSettingName] = useState<string>('');
  const [isActive, setIsActive] = useState<boolean>(true);

  const loadCssSetting = async () => {
    if (!id) {
      setError('Invalid CSS setting ID');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await fetchCssSetting(parseInt(id));

      if (response.success && response.data) {
        setCssSettingData(response.data);
        setSelectedTheme(response.data.theme);
        setSettingName(response.data.name);
        setIsActive(response.data.is_active);

        // Load CSS content from the path URL
        if (response.data.path) {
          await loadCssContent(response.data.path);
        }
      } else {
        setError(response.error || 'Failed to load CSS setting');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCssContent = async (cssUrl: string) => {
    setIsLoadingCss(true);
    try {
      const response = await fetch(cssUrl);
      if (response.ok) {
        const content = await response.text();
        setCssContent(content);
      } else {
        console.error('Failed to load CSS content:', response.statusText);
        setCssContent('/* Failed to load CSS content */');
      }
    } catch (err) {
      console.error('Error loading CSS content:', err);
      setCssContent('/* Error loading CSS content */');
    } finally {
      setIsLoadingCss(false);
    }
  };

  useEffect(() => {
    loadCssSetting();
  }, [id]);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const getThemeColor = (theme: string) => {
    switch (theme.toLowerCase()) {
      case 'both':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      case 'light':
        return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20';
      case 'dark':
        return 'bg-purple-500/10 text-purple-500 border-purple-500/20';
      default:
        return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  const handleBack = () => {
    navigate('/settings/css');
  };

  const handleSave = async () => {
    // TODO: Implement save functionality
    console.log('Saving CSS setting:', {
      id,
      name: settingName,
      theme: selectedTheme,
      isActive,
      cssContent
    });
    // For now, just show an alert
    alert('Save functionality will be implemented next!');
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-12">
          <div className="flex flex-col items-center justify-center text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mb-4"></div>
            <h3 className="text-lg font-medium text-gray-200 mb-2">Loading CSS Setting</h3>
            <p className="text-gray-400">Please wait while we fetch the CSS setting details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Back Button */}
        <Button
          onClick={handleBack}
          variant="outline"
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to CSS Settings
        </Button>

        {/* Error */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading CSS Setting</h4>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!cssSettingData) {
    return null;
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Back Button */}
      <Button
        onClick={handleBack}
        variant="outline"
        className="flex items-center gap-2"
      >
        <ArrowLeft className="w-4 h-4" />
        Back to CSS Settings
      </Button>

      {/* Header */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center">
              <Code className="w-6 h-6 text-dark-800" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-100 mb-1">Edit CSS Setting</h1>
              <p className="text-gray-400">CSS Setting #{cssSettingData.id}</p>
            </div>
          </div>
          <Button
            onClick={handleSave}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            Confirm
          </Button>
        </div>
      </div>

      {/* Main Editing Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* CSS Settings Form */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h2 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
            <Settings className="w-5 h-5 text-primary-500" />
            Settings
          </h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Name</label>
              <input
                type="text"
                value={settingName}
                onChange={(e) => setSettingName(e.target.value)}
                className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded-md text-gray-200 focus:outline-none focus:border-primary-500"
                placeholder="CSS setting name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Theme</label>
              <select
                value={selectedTheme}
                onChange={(e) => setSelectedTheme(e.target.value)}
                className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded-md text-gray-200 focus:outline-none focus:border-primary-500"
              >
                <option value="both">Both (Light & Dark)</option>
                <option value="light">Light Theme Only</option>
                <option value="dark">Dark Theme Only</option>
              </select>
            </div>

            <div>
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)}
                  className="w-4 h-4 text-primary-500 bg-dark-600 border-dark-500 rounded focus:ring-primary-500 focus:ring-2"
                />
                <span className="text-sm font-medium text-gray-400">Active</span>
              </label>
            </div>
          </div>
        </div>

        {/* CSS Code Editor */}
        <div className="lg:col-span-2 bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-100 flex items-center gap-2">
              <Code className="w-5 h-5 text-primary-500" />
              CSS Code
            </h2>
            {isLoadingCss && (
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
                Loading CSS...
              </div>
            )}
          </div>

          <div className="relative">
            <textarea
              value={cssContent}
              onChange={(e) => setCssContent(e.target.value)}
              className="w-full h-96 px-4 py-3 bg-dark-800 border border-dark-600 rounded-md text-gray-200 font-mono text-sm focus:outline-none focus:border-primary-500 resize-none"
              placeholder="/* CSS code will be loaded here... */"
              spellCheck={false}
            />
            <div className="absolute top-2 right-2 text-xs text-gray-500">
              CSS
            </div>
          </div>
        </div>
      </div>

      {/* System Information - De-emphasized */}
      <details className="bg-dark-800 rounded-lg border border-dark-700">
        <summary className="px-6 py-4 cursor-pointer text-gray-400 hover:text-gray-300 transition-colors">
          <span className="text-sm">System Information & CDN Path</span>
        </summary>
        <div className="px-6 pb-4 border-t border-dark-700">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 text-sm">
            <div>
              <span className="text-gray-500">Merchant ID:</span>
              <span className="text-gray-400 ml-2">{cssSettingData.merchant_id}</span>
            </div>
            <div>
              <span className="text-gray-500">Website ID:</span>
              <span className="text-gray-400 ml-2">{cssSettingData.website_id}</span>
            </div>
            <div>
              <span className="text-gray-500">Operator ID:</span>
              <span className="text-gray-400 ml-2">{cssSettingData.operator_id}</span>
            </div>
            <div>
              <span className="text-gray-500">Last Update:</span>
              <span className="text-gray-400 ml-2">{formatDate(cssSettingData.last_update)}</span>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-gray-500 text-sm">CDN Path:</span>
            <div className="flex items-center gap-2 mt-1">
              <code className="text-xs text-gray-500 font-mono flex-1 break-all bg-dark-900 px-2 py-1 rounded">
                {cssSettingData.path}
              </code>
              <Button
                onClick={() => window.open(cssSettingData.path, '_blank')}
                variant="outline"
                size="sm"
                className="flex items-center gap-1 text-xs"
              >
                <ExternalLink className="w-3 h-3" />
                View
              </Button>
            </div>
          </div>
        </div>
      </details>
    </div>
  );
};

export default CssSettingEdit;
