import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Gift, ArrowLeft, Plus, Trash2, AlertCircle, CheckCircle } from 'lucide-react';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { useAppContext } from '../contexts/AppContext';
import { createBonusDrop, type BonusDropCreateData } from '../utils/api';

interface FilterRule {
  field: string;
  operator: string;
  value: string;
}

interface FormData {
  currency: string;
  code: string;
  amount: string;
  count: string;
  required_wager: string;
  required_wager_currency: string;
  date_start: string;
  date_end: string;
  ref_code: string;
  reference_tag: string;
  rules: FilterRule[];
}

const AVAILABLE_FIELDS = [
  { label: "Select Field", value: "" },
  { label: "Ref Code", value: "refCode" },
  { label: "Vip Rank", value: "rank" },
  { label: "KYC Verification Level", value: "kyc" },
  { label: "Registration", value: "registration" },
  { label: "Deposit Amount", value: "depositAmount" },
  { label: "Deposit Currency", value: "depositCurrency" },
  { label: "Deposit Date", value: "depositDate" },
  { label: "First Time Deposit Amount", value: "ftdAmount" },
  { label: "First Time Deposit Currency", value: "ftdCurrency" },
  { label: "First Time Deposit Date", value: "ftdDate" },
  { label: "Total Deposit Amount($)", value: "totalDeposit" },
  { label: "Total Withdraw Amount($)", value: "totalWithdraw" },
  { label: "Last Day USD Turnover", value: "lastDayTurnoverUSD" },
  { label: "Last Week USD Turnover", value: "lastWeekTurnoverUSD" },
  { label: "Last Month USD Turnover", value: "lastMonthTurnoverUSD" },
  { label: "Last Day USD Deposit", value: "lastDayDeposit" },
  { label: "Last Week USD Deposit", value: "lastWeekDeposit" },
  { label: "Last Month USD Deposit", value: "lastMonthDeposit" },
  { label: "Last Day USD Withdraw", value: "lastDayWithdraw" },
  { label: "Last Week USD Withdraw", value: "lastWeekWithdraw" },
  { label: "Last Month USD Withdraw", value: "lastMonthWithdraw" },
  { label: "Last Day USD Net", value: "lastDayNet" },
  { label: "Last Week USD Net", value: "lastWeekNet" },
  { label: "Last Month USD Net", value: "lastMonthNet" },
  { label: "Birthday Bonus", value: "birthday" }
];

const AVAILABLE_OPERATORS = [
  { label: "Select an Operator", value: "" },
  { label: ">", value: ">" },
  { label: "<", value: "<" },
  { label: ">=", value: ">=" },
  { label: "<=", value: "<=" },
  { label: "=", value: "=" }
];

const BonusDropCreate: React.FC = () => {
  const navigate = useNavigate();
  const { configurations } = useAppContext();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState<FormData>({
    currency: '',
    code: '',
    amount: '',
    count: '',
    required_wager: '',
    required_wager_currency: '',
    date_start: '',
    date_end: '',
    ref_code: '',
    reference_tag: '',
    rules: [{ field: '', operator: '', value: '' }]
  });

  // Get currencies from configurations
  const currencies = configurations?.currencies || [];

  // Handle form field changes
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle rule changes
  const handleRuleChange = (index: number, field: keyof FilterRule, value: string) => {
    setFormData(prev => ({
      ...prev,
      rules: prev.rules.map((rule, i) => 
        i === index ? { ...rule, [field]: value } : rule
      )
    }));
  };

  // Add new rule
  const addRule = () => {
    setFormData(prev => ({
      ...prev,
      rules: [...prev.rules, { field: '', operator: '', value: '' }]
    }));
  };

  // Remove rule
  const removeRule = (index: number) => {
    if (formData.rules.length > 1) {
      setFormData(prev => ({
        ...prev,
        rules: prev.rules.filter((_, i) => i !== index)
      }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    if (!formData.currency) {
      setError('Please select a currency');
      return false;
    }
    if (!formData.code.trim()) {
      setError('Please enter a code');
      return false;
    }
    if (!formData.amount.trim()) {
      setError('Please enter bonus amount');
      return false;
    }
    if (!formData.count.trim()) {
      setError('Please enter bonus count');
      return false;
    }
    if (!formData.required_wager.trim()) {
      setError('Please enter required wager');
      return false;
    }
    if (!formData.required_wager_currency.trim()) {
      setError('Please enter required wager USD');
      return false;
    }
    if (!formData.date_start) {
      setError('Please select start date');
      return false;
    }
    if (!formData.date_end) {
      setError('Please select end date');
      return false;
    }

    // Validate date range
    const startDate = new Date(formData.date_start);
    const endDate = new Date(formData.date_end);
    if (endDate <= startDate) {
      setError('End date must be after start date');
      return false;
    }

    // Validate rules
    for (let i = 0; i < formData.rules.length; i++) {
      const rule = formData.rules[i];
      if (!rule.field || !rule.operator || !rule.value.trim()) {
        setError(`Please complete rule ${i + 1}`);
        return false;
      }
    }

    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Convert dates to timestamps
      const fromTimestamp = Math.floor(new Date(formData.date_start).getTime() / 1000);
      const toTimestamp = Math.floor(new Date(formData.date_end).getTime() / 1000);

      const requestBody: BonusDropCreateData = {
        code: formData.code,
        currency: formData.currency,
        amount: formData.amount,
        count: formData.count,
        required_wager: formData.required_wager,
        required_wager_currency: formData.required_wager_currency,
        ref_code: formData.ref_code,
        reference_tag: formData.reference_tag,
        start: fromTimestamp,
        end: toTimestamp,
        rules: formData.rules
      };

      const response = await createBonusDrop(requestBody);
      
      if (response.success) {
        setSuccess('Bonus drop created successfully!');
        setTimeout(() => {
          navigate('/bonus-drops/list');
        }, 2000);
      } else {
        setError(response.error || 'Failed to create bonus drop');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create bonus drop');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          onClick={() => navigate('/bonus-drops/list')}
          variant="ghost"
          size="sm"
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        
        <div className="flex items-center gap-3">
          <Gift className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Create Bonus Drop</h1>
            <p className="text-gray-400">Create a new bonus drop assignment</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
          <span className="text-red-200">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 flex items-center gap-3">
          <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
          <span className="text-green-200">{success}</span>
        </div>
      )}

      {/* Form */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Currency <span className="text-red-400">*</span>
              </label>
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange('currency', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                required
              >
                <option value="">Select currency</option>
                {currencies.map(currency => (
                  <option key={currency} value={currency}>
                    {currency}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Code <span className="text-red-400">*</span>
              </label>
              <Input
                type="text"
                value={formData.code}
                onChange={(e) => handleInputChange('code', e.target.value)}
                placeholder="Enter bonus drop code"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bonus Amount <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                step="0.00000001"
                value={formData.amount}
                onChange={(e) => handleInputChange('amount', e.target.value)}
                placeholder="Enter bonus amount"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bonus Count <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                value={formData.count}
                onChange={(e) => handleInputChange('count', e.target.value)}
                placeholder="Enter bonus count"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Required Wager <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.required_wager}
                onChange={(e) => handleInputChange('required_wager', e.target.value)}
                placeholder="Enter required wager"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Required Wager USD <span className="text-red-400">*</span>
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.required_wager_currency}
                onChange={(e) => handleInputChange('required_wager_currency', e.target.value)}
                placeholder="Enter required wager in USD"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Date Start <span className="text-red-400">*</span>
              </label>
              <Input
                type="datetime-local"
                value={formData.date_start}
                onChange={(e) => handleInputChange('date_start', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Date End <span className="text-red-400">*</span>
              </label>
              <Input
                type="datetime-local"
                value={formData.date_end}
                onChange={(e) => handleInputChange('date_end', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Affiliate Code
              </label>
              <Input
                type="text"
                value={formData.ref_code}
                onChange={(e) => handleInputChange('ref_code', e.target.value)}
                placeholder="Enter affiliate code (optional)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Reference Tag
              </label>
              <Input
                type="text"
                value={formData.reference_tag}
                onChange={(e) => handleInputChange('reference_tag', e.target.value)}
                placeholder="Enter reference tag (optional)"
              />
            </div>
          </div>

          {/* Rules Section */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-200">Filter Rules</h3>
              <Button
                type="button"
                onClick={addRule}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Rule
              </Button>
            </div>

            <div className="space-y-4">
              {formData.rules.map((rule, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-dark-800 rounded-lg border border-dark-600">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Field <span className="text-red-400">*</span>
                    </label>
                    <select
                      value={rule.field}
                      onChange={(e) => handleRuleChange(index, 'field', e.target.value)}
                      className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      required
                    >
                      {AVAILABLE_FIELDS.map(field => (
                        <option key={field.value} value={field.value}>
                          {field.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Operator <span className="text-red-400">*</span>
                    </label>
                    <select
                      value={rule.operator}
                      onChange={(e) => handleRuleChange(index, 'operator', e.target.value)}
                      className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      required
                    >
                      {AVAILABLE_OPERATORS.map(operator => (
                        <option key={operator.value} value={operator.value}>
                          {operator.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Value <span className="text-red-400">*</span>
                    </label>
                    <Input
                      type="text"
                      value={rule.value}
                      onChange={(e) => handleRuleChange(index, 'value', e.target.value)}
                      placeholder="Enter value"
                    />
                  </div>

                  <div className="flex items-end">
                    <Button
                      type="button"
                      onClick={() => removeRule(index)}
                      variant="ghost"
                      size="sm"
                      className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                      disabled={formData.rules.length === 1}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              onClick={() => navigate('/bonus-drops/list')}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
            >
              {isLoading ? 'Creating...' : 'Create Bonus Drop'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BonusDropCreate;
