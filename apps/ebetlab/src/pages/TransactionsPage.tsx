import React from 'react';
import { Search, Filter, ArrowDownLeft, ArrowUpR<PERSON>, Clock, Check, X } from 'lucide-react';

interface TransactionData {
  id: string;
  userId: string;
  type: 'deposit' | 'withdrawal';
  amount: string;
  status: 'completed' | 'pending' | 'failed';
  currency: string;
  date: string;
  method: string;
}

const TransactionsPage: React.FC = () => {
  const transactions: TransactionData[] = [
    {
      id: 'TX-5821',
      userId: '#28491',
      type: 'deposit',
      amount: '0.125',
      status: 'completed',
      currency: 'BTC',
      date: '2023-07-15 14:23',
      method: 'Bitcoin',
    },
    {
      id: 'TX-5835',
      userId: '#19384',
      type: 'withdrawal',
      amount: '0.075',
      status: 'pending',
      currency: 'BTC',
      date: '2023-07-15 15:47',
      method: 'Bitcoin',
    },
    {
      id: 'TX-5842',
      userId: '#45672',
      type: 'deposit',
      amount: '0.250',
      status: 'completed',
      currency: 'BTC',
      date: '2023-07-15 16:32',
      method: 'Bitcoin',
    },
    {
      id: 'TX-5846',
      userId: '#36729',
      type: 'withdrawal',
      amount: '0.180',
      status: 'failed',
      currency: 'BTC',
      date: '2023-07-15 17:09',
      method: 'Bitcoin',
    },
    {
      id: 'TX-5850',
      userId: '#58294',
      type: 'deposit',
      amount: '0.320',
      status: 'completed',
      currency: 'BTC',
      date: '2023-07-15 18:15',
      method: 'Bitcoin',
    },
  ];

  const getStatusIcon = (status: string) => {
    if (status === 'completed') {
      return <Check size={16} className="text-green-500" />;
    } else if (status === 'pending') {
      return <Clock size={16} className="text-yellow-500" />;
    } else {
      return <X size={16} className="text-red-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      completed: 'bg-green-500/20 text-green-500',
      pending: 'bg-yellow-500/20 text-yellow-500',
      failed: 'bg-red-500/20 text-red-500',
    };
    
    return (
      <div className="flex items-center gap-2">
        {getStatusIcon(status)}
        <span className={`px-2 py-1 rounded-md text-xs font-medium ${statusClasses[status as keyof typeof statusClasses]}`}>
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      </div>
    );
  };

  const getTypeDisplay = (type: string) => {
    if (type === 'deposit') {
      return (
        <div className="flex items-center gap-2 text-green-500">
          <ArrowDownLeft size={16} />
          <span>Deposit</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-2 text-red-500">
          <ArrowUpRight size={16} />
          <span>Withdrawal</span>
        </div>
      );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Transactions</h1>
        <div className="flex gap-3">
          <button className="btn btn-outline">
            Export CSV
          </button>
          <button className="btn btn-primary">
            New Manual Transaction
          </button>
        </div>
      </div>
      
      <div className="admin-card">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search transactions by ID, user or amount..."
              className="py-2 pl-10 pr-4 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500 text-sm w-full"
            />
          </div>
          
          <div className="flex gap-3">
            <button className="btn btn-outline flex items-center gap-2">
              <Filter size={16} />
              <span>Filters</span>
            </button>
            
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Types</option>
              <option>Deposits</option>
              <option>Withdrawals</option>
            </select>
            
            <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
              <option>All Status</option>
              <option>Completed</option>
              <option>Pending</option>
              <option>Failed</option>
            </select>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="data-table">
            <thead>
              <tr>
                <th className="rounded-tl-lg">Transaction ID</th>
                <th>User ID</th>
                <th>Type</th>
                <th>Amount</th>
                <th>Currency</th>
                <th>Method</th>
                <th>Date & Time</th>
                <th>Status</th>
                <th className="rounded-tr-lg">Actions</th>
              </tr>
            </thead>
            <tbody>
              {transactions.map((transaction) => (
                <tr key={transaction.id}>
                  <td>{transaction.id}</td>
                  <td>{transaction.userId}</td>
                  <td>{getTypeDisplay(transaction.type)}</td>
                  <td className="font-medium">{transaction.amount}</td>
                  <td>{transaction.currency}</td>
                  <td>{transaction.method}</td>
                  <td>{transaction.date}</td>
                  <td>{getStatusBadge(transaction.status)}</td>
                  <td>
                    <div className="flex gap-2">
                      <button className="btn btn-outline text-xs py-1 px-2">
                        View
                      </button>
                      {transaction.status === 'pending' && (
                        <>
                          <button className="btn btn-secondary text-xs py-1 px-2">
                            Approve
                          </button>
                          <button className="btn btn-outline text-red-500 hover:bg-red-500/10 text-xs py-1 px-2">
                            Reject
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="flex justify-between items-center mt-6">
          <div className="text-sm text-gray-400">
            Showing 1 to 5 of 248 entries
          </div>
          
          <div className="flex gap-2">
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Previous
            </button>
            <button className="px-3 py-1.5 rounded-md border border-primary-500 bg-primary-500/10 text-primary-500">
              1
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              2
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              3
            </button>
            <button className="px-3 py-1.5 rounded-md border border-dark-600 hover:border-primary-500 hover:text-primary-500">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionsPage;