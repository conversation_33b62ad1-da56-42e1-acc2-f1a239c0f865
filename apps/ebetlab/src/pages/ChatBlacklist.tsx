import React, { useState, useEffect, useCallback } from 'react';
import { RefreshCw, Eye, Users } from 'lucide-react';
import { fetchChatBlacklist, type ChatBlacklistData, type ChatBlacklistResponse } from '../utils/api';
import QuickUserInfoModal from '../components/customer-details/QuickUserInfoModal';
import { getVipLevelDisplayName, getVipLevelColor } from '../constants/vipLevels';
import UnifiedTable, { TableColumn } from '../components/ui/UnifiedTable';
import Pagination from '../components/ui/Pagination';

// Table column configuration
interface TableColumn {
  key: string;
  label: string;
  width: string;
  render: (customer: ChatBlacklistData) => React.ReactNode;
}

const ChatBlacklist: React.FC = () => {
  // State management
  const [customers, setCustomers] = useState<ChatBlacklistData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Modal state
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [showQuickInfo, setShowQuickInfo] = useState(false);

  // Format timestamp to readable date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format currency
  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(num);
  };

  // Get rank badge
  const getRankBadge = (rank: string) => {
    const displayName = getVipLevelDisplayName(rank);
    const color = getVipLevelColor(rank);
    
    return (
      <span 
        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
        style={{ backgroundColor: `${color}20`, color: color }}
      >
        {displayName}
      </span>
    );
  };

  // Get status badge
  const getStatusBadge = (status: { id: number; name: string }) => {
    const statusColors: { [key: string]: string } = {
      'STANDART': 'bg-green-100 text-green-800',
      'BLOCKED': 'bg-red-100 text-red-800',
      'SUSPENDED': 'bg-yellow-100 text-yellow-800',
    };

    const colorClass = statusColors[status.name] || 'bg-gray-100 text-gray-800';

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {status.name}
      </span>
    );
  };

  // Get verification level badge
  const getVerificationBadge = (level: number) => {
    const levels: { [key: number]: { label: string; color: string } } = {
      0: { label: 'Unverified', color: 'bg-gray-100 text-gray-800' },
      1: { label: 'Level 1', color: 'bg-blue-100 text-blue-800' },
      2: { label: 'Level 2', color: 'bg-green-100 text-green-800' },
      3: { label: 'Level 3', color: 'bg-purple-100 text-purple-800' },
    };

    const levelInfo = levels[level] || { label: `Level ${level}`, color: 'bg-gray-100 text-gray-800' };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${levelInfo.color}`}>
        {levelInfo.label}
      </span>
    );
  };

  // Table column configuration for UnifiedTable
  const tableColumns: TableColumn<ChatBlacklistData>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '100px',
      render: (customer) => <span className="font-mono text-sm">{customer.id}</span>
    },
    {
      key: 'username',
      label: 'Username',
      width: '150px',
      render: (customer) => <span className="font-medium">{customer.username || '-'}</span>
    },
    {
      key: 'email',
      label: 'Email',
      width: '200px',
      render: (customer) => <span className="text-sm">{customer.email || '-'}</span>
    },
    {
      key: 'name',
      label: 'Full Name',
      width: '180px',
      render: (customer) => (
        <span className="text-sm">
          {customer.profile?.name && customer.profile?.surname 
            ? `${customer.profile.name} ${customer.profile.surname}`
            : '-'
          }
        </span>
      )
    },
    {
      key: 'registration_country',
      label: 'Country',
      width: '100px',
      render: (customer) => <span className="text-sm uppercase">{customer.registration_country || '-'}</span>
    },
    {
      key: 'rank',
      label: 'Rank',
      width: '120px',
      render: (customer) => getRankBadge(customer.rank || 'no-vip')
    },
    {
      key: 'verification_level',
      label: 'Verification',
      width: '120px',
      render: (customer) => getVerificationBadge(customer.profile?.verification_level || 0)
    },
    {
      key: 'status',
      label: 'Status',
      width: '120px',
      render: (customer) => getStatusBadge(customer.status)
    },
    {
      key: 'total_turnover',
      label: 'Total Turnover',
      width: '140px',
      render: (customer) => <span className="font-medium">{formatCurrency(customer.total_turnover)}</span>
    },
    {
      key: 'registration_ts',
      label: 'Registered',
      width: '180px',
      render: (customer) => <span className="text-sm">{formatDate(customer.registration_ts)}</span>
    },
    {
      key: 'last_online_at',
      label: 'Last Online',
      width: '180px',
      render: (customer) => <span className="text-sm">{formatDate(customer.last_online_at)}</span>
    },
    {
      key: 'chat_muted_at',
      label: 'Chat Muted',
      width: '180px',
      render: (customer) => (
        <span className="text-sm">
          {customer.chat_muted_at ? formatDate(customer.chat_muted_at) : '-'}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      render: (customer) => (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleViewCustomer(customer);
          }}
          className="btn btn-outline text-xs py-1 px-2 flex items-center"
          title="View Customer Details"
        >
          <Eye size={14} className="mr-1" />
          View
        </button>
      )
    }
  ];

  // Event handlers
  const handleViewCustomer = (customer: ChatBlacklistData) => {
    setSelectedCustomerId(customer.id.toString());
    setShowQuickInfo(true);
  };

  const handleQuickInfo = (customer: ChatBlacklistData) => {
    setSelectedCustomerId(customer.id.toString());
    setShowQuickInfo(true);
  };

  const handleRefresh = () => {
    loadCustomers(currentPage);
  };

  // API functions
  const loadCustomers = useCallback(async (page: number = 1) => {
    setLoading(true);
    setError(null);

    try {
      const result = await fetchChatBlacklist(page, itemsPerPage);

      if (result.success && result.data) {
        const customerData = result.data.data || [];
        const total = result.data.total || 0;

        setCustomers(customerData);
        setTotalCustomers(total);
      } else {
        setError(result.error || 'Failed to load chat blacklist');
        setCustomers([]);
      }
    } catch (err) {
      console.error('Error loading chat blacklist:', err);
      setError('An unexpected error occurred');
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  }, [itemsPerPage]);

  // Pagination
  const totalPages = Math.ceil(totalCustomers / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadCustomers(page);
  };

  const handleItemsPerPageChange = (newLimit: number) => {
    setItemsPerPage(newLimit);
    setCurrentPage(1);
    loadCustomers(1);
  };

  // Effects
  useEffect(() => {
    loadCustomers(1);
  }, [loadCustomers]);

  return (
    <div className="space-y-6">

      {/* Chat Blacklist Table */}
      <UnifiedTable
        data={customers}
        columns={tableColumns}
        title="Chat Blacklist"
        subtitle={`${totalCustomers} blacklisted customers`}
        isLoading={loading}
        error={error}
        onRowClick={handleQuickInfo}
        onRetry={handleRefresh}
        minWidth="1800px"
        emptyState={{
          icon: <Users className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No blacklisted customers found',
          description: 'There are no customers in the chat blacklist.'
        }}
      />

      {/* Pagination */}
      {totalCustomers > 0 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalCustomers}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        </div>
      )}

      {/* Quick User Info Modal */}
      {showQuickInfo && selectedCustomerId && (
        <QuickUserInfoModal
          isOpen={showQuickInfo}
          onClose={() => {
            setShowQuickInfo(false);
            setSelectedCustomerId(null);
          }}
          customerId={selectedCustomerId}
        />
      )}
    </div>
  );
};

export default ChatBlacklist;
