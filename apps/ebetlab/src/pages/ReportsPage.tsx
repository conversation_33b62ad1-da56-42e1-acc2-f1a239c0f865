import React from 'react';
import Chart from '../components/dashboard/Chart';
import { Download } from 'lucide-react';

const ReportsPage: React.FC = () => {
  const dateLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  const revenueData = {
    labels: dateLabels,
    datasets: [
      {
        label: 'Net Revenue',
        data: [12.3, 15.8, 14.2, 16.5, 19.3, 21.7, 24.5, 23.1, 25.6, 28.4, 30.2, 32.6],
        borderColor: '#18cffb',
        backgroundColor: 'rgba(24, 207, 251, 0.1)',
      },
    ],
  };
  
  const userGrowthData = {
    labels: dateLabels,
    datasets: [
      {
        label: 'New Users',
        data: [521, 632, 587, 742, 851, 942, 1032, 998, 1142, 1287, 1352, 1489],
        backgroundColor: 'rgba(24, 207, 251, 0.7)',
      },
    ],
  };
  
  const betCountData = {
    labels: dateLabels,
    datasets: [
      {
        label: 'Number of Bets',
        data: [34521, 42658, 38954, 47852, 53621, 61482, 72541, 68523, 76421, 85962, 92458, 102541],
        borderColor: '#0ac3c6',
        backgroundColor: 'rgba(10, 195, 198, 0.1)',
      },
    ],
  };
  
  const winRateData = {
    labels: dateLabels,
    datasets: [
      {
        label: 'Win Rate (%)',
        data: [47.2, 48.5, 46.8, 47.9, 46.5, 48.2, 49.1, 47.8, 48.6, 47.2, 48.9, 48.4],
        backgroundColor: 'rgba(10, 195, 198, 0.7)',
      },
    ],
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Analytics & Reports</h1>
        <div className="flex gap-3">
          <select className="bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm">
            <option>Last 12 months</option>
            <option>Last 30 days</option>
            <option>Last 7 days</option>
            <option>Custom Range</option>
          </select>
          <button className="btn btn-outline flex items-center gap-2">
            <Download size={16} />
            <span>Export Report</span>
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="admin-card">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Revenue Summary</h3>
            <div className="flex gap-2">
              <button className="text-xs py-1 px-2 rounded-md bg-primary-500/10 text-primary-500">
                Monthly
              </button>
              <button className="text-xs py-1 px-2 rounded-md hover:bg-dark-700">
                Weekly
              </button>
              <button className="text-xs py-1 px-2 rounded-md hover:bg-dark-700">
                Daily
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-dark-600 rounded-md p-4">
              <div className="text-sm text-gray-400 mb-1">Total Revenue</div>
              <div className="text-2xl font-semibold">₿ 264.76</div>
              <div className="text-xs text-green-500 mt-1">+15.2% from previous year</div>
            </div>
            <div className="bg-dark-600 rounded-md p-4">
              <div className="text-sm text-gray-400 mb-1">Avg. Revenue Per User</div>
              <div className="text-2xl font-semibold">₿ 0.087</div>
              <div className="text-xs text-green-500 mt-1">+3.8% from previous year</div>
            </div>
          </div>
          
          <Chart
            type="line"
            title=""
            labels={revenueData.labels}
            datasets={revenueData.datasets}
            className="bg-transparent"
          />
        </div>
        
        <div className="admin-card">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">User Growth</h3>
            <div className="flex gap-2">
              <button className="text-xs py-1 px-2 rounded-md bg-primary-500/10 text-primary-500">
                Monthly
              </button>
              <button className="text-xs py-1 px-2 rounded-md hover:bg-dark-700">
                Weekly
              </button>
              <button className="text-xs py-1 px-2 rounded-md hover:bg-dark-700">
                Daily
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-dark-600 rounded-md p-4">
              <div className="text-sm text-gray-400 mb-1">Total Users</div>
              <div className="text-2xl font-semibold">24,521</div>
              <div className="text-xs text-green-500 mt-1">+42.6% from previous year</div>
            </div>
            <div className="bg-dark-600 rounded-md p-4">
              <div className="text-sm text-gray-400 mb-1">Active Users</div>
              <div className="text-2xl font-semibold">15,873</div>
              <div className="text-xs text-green-500 mt-1">+36.9% from previous year</div>
            </div>
          </div>
          
          <Chart 
            type="bar"
            title=""
            labels={userGrowthData.labels}
            datasets={userGrowthData.datasets}
          />
        </div>
        
        <div className="admin-card">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Betting Activity</h3>
            <div className="flex gap-2">
              <button className="text-xs py-1 px-2 rounded-md bg-primary-500/10 text-primary-500">
                Monthly
              </button>
              <button className="text-xs py-1 px-2 rounded-md hover:bg-dark-700">
                Weekly
              </button>
              <button className="text-xs py-1 px-2 rounded-md hover:bg-dark-700">
                Daily
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-dark-600 rounded-md p-4">
              <div className="text-sm text-gray-400 mb-1">Total Bets</div>
              <div className="text-2xl font-semibold">1,342,851</div>
              <div className="text-xs text-green-500 mt-1">+52.1% from previous year</div>
            </div>
            <div className="bg-dark-600 rounded-md p-4">
              <div className="text-sm text-gray-400 mb-1">Avg. Bet Size</div>
              <div className="text-2xl font-semibold">₿ 0.0085</div>
              <div className="text-xs text-red-500 mt-1">-2.3% from previous year</div>
            </div>
          </div>
          
          <Chart 
            type="line"
            title=""
            labels={betCountData.labels}
            datasets={betCountData.datasets}
          />
        </div>
        
        <div className="admin-card">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Player Performance</h3>
            <div className="flex gap-2">
              <button className="text-xs py-1 px-2 rounded-md bg-primary-500/10 text-primary-500">
                Monthly
              </button>
              <button className="text-xs py-1 px-2 rounded-md hover:bg-dark-700">
                Weekly
              </button>
              <button className="text-xs py-1 px-2 rounded-md hover:bg-dark-700">
                Daily
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-dark-600 rounded-md p-4">
              <div className="text-sm text-gray-400 mb-1">Avg. Win Rate</div>
              <div className="text-2xl font-semibold">48.2%</div>
              <div className="text-xs text-green-500 mt-1">+0.8% from previous year</div>
            </div>
            <div className="bg-dark-600 rounded-md p-4">
              <div className="text-sm text-gray-400 mb-1">House Edge</div>
              <div className="text-2xl font-semibold">3.42%</div>
              <div className="text-xs text-green-500 mt-1">+0.2% from previous year</div>
            </div>
          </div>
          
          <Chart
            type="bar"
            title=""
            labels={winRateData.labels}
            datasets={winRateData.datasets}
            className="bg-transparent"
          />
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;