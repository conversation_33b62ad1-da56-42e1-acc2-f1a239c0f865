import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { DollarSign, RefreshCw, AlertCircle, Calendar, User, Tag, Hash, Activity, Edit, Trash2, FileText, Ban } from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import { 
  fetchBalanceCorrectionRules, 
  deactivateBalanceCorrectionRule, 
  deleteBalanceCorrectionRule, 
  type BalanceCorrectionRuleData 
} from '../utils/api';

interface BalanceCorrectionRulesData {
  total: number;
  data: BalanceCorrectionRuleData[];
}

const BalanceCorrectionRules: React.FC = () => {
  const navigate = useNavigate();
  const [rulesData, setRulesData] = useState<BalanceCorrectionRulesData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sortState, setSortState] = useState<SortState>({
    field: 'id',
    direction: 'desc'
  });

  const loadBalanceCorrectionRules = useCallback(async (page: number = currentPage, limit: number = itemsPerPage, searchFilters: Record<string, unknown> = filters) => {
    setIsLoading(true);
    setError('');

    try {
      // Include sorting parameters in the request
      const searchParamsWithSort = {
        ...searchFilters,
        sortBy: sortState.field,
        direction: sortState.direction
      };

      const response = await fetchBalanceCorrectionRules(page, limit, searchParamsWithSort);

      if (response.success && response.data) {
        setRulesData(response.data.data);
      } else {
        setError(response.error || 'Failed to load balance correction rules');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load balance correction rules on component mount
  useEffect(() => {
    loadBalanceCorrectionRules();
  }, [loadBalanceCorrectionRules]);

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadBalanceCorrectionRules(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadBalanceCorrectionRules(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadBalanceCorrectionRules(1, itemsPerPage, newFilters);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (rule: BalanceCorrectionRuleData) => {
    // Future: navigate to rule details
    console.log('View rule:', rule.id);
  };

  // Handle deactivate
  const handleDeactivate = async (ruleId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm('Are you sure you want to deactivate this balance correction rule?')) return;

    try {
      const response = await deactivateBalanceCorrectionRule(ruleId);

      if (response.success) {
        loadBalanceCorrectionRules(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to deactivate balance correction rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deactivate balance correction rule');
    }
  };

  // Handle delete
  const handleDelete = async (ruleId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm('Are you sure you want to delete this balance correction rule?')) return;

    try {
      const response = await deleteBalanceCorrectionRule(ruleId);

      if (response.success) {
        loadBalanceCorrectionRules(currentPage, itemsPerPage, filters);
      } else {
        setError(response.error || 'Failed to delete balance correction rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete balance correction rule');
    }
  };

  // Handle edit
  const handleEdit = (ruleId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    // Future: navigate to edit page
    console.log('Edit rule:', ruleId);
  };

  // Handle show claims
  const handleShowClaims = (ruleId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/balance-correction-rules/${ruleId}/claims`);
  };

  // Format timestamp
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Get status badge
  const getStatusBadge = (isActive: number) => {
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isActive
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        <Activity className="w-3 h-3 mr-1" />
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  // Table columns configuration
  const tableColumns: TableColumn<BalanceCorrectionRuleData>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (rule: BalanceCorrectionRuleData) => (
        <span className="font-mono text-sm">#{rule.id}</span>
      )
    },
    {
      key: 'name',
      label: 'Name',
      width: '150px',
      sortable: true,
      render: (rule: BalanceCorrectionRuleData) => (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium">{rule.name}</span>
        </div>
      )
    },
    {
      key: 'redeem_code',
      label: 'Redeem Code',
      width: '120px',
      sortable: true,
      render: (rule: BalanceCorrectionRuleData) => (
        <span className="text-sm font-mono text-gray-200">{rule.redeem_code}</span>
      )
    },
    {
      key: 'operator',
      label: 'Operator',
      width: '120px',
      sortable: true,
      render: (rule: BalanceCorrectionRuleData) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{rule.operator.name}</span>
        </div>
      )
    },
    {
      key: 'amount_percentage',
      label: 'Amount %',
      width: '100px',
      sortable: true,
      render: (rule: BalanceCorrectionRuleData) => (
        <span className="text-sm font-medium">{rule.amount_percentage}%</span>
      )
    },
    {
      key: 'wager_percentage',
      label: 'Wager %',
      width: '100px',
      sortable: true,
      render: (rule: BalanceCorrectionRuleData) => (
        <span className="text-sm font-medium">{rule.wager_percentage}%</span>
      )
    },
    {
      key: 'min_max_usd',
      label: 'USD Range',
      width: '120px',
      sortable: false,
      render: (rule: BalanceCorrectionRuleData) => (
        <div className="text-sm">
          <div>${rule.min_usd} - ${rule.max_usd}</div>
        </div>
      )
    },
    {
      key: 'range',
      label: 'Range',
      width: '80px',
      sortable: true,
      render: (rule: BalanceCorrectionRuleData) => (
        <span className="text-sm capitalize">{rule.range}</span>
      )
    },
    {
      key: 'is_active',
      label: 'Status',
      width: '100px',
      sortable: true,
      render: (rule: BalanceCorrectionRuleData) => getStatusBadge(rule.is_active)
    },
    {
      key: 'timestamp',
      label: 'Created',
      width: '150px',
      sortable: true,
      render: (rule: BalanceCorrectionRuleData) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm">{formatDate(rule.timestamp)}</span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '200px',
      sortable: false,
      render: (rule: BalanceCorrectionRuleData) => (
        <div className="flex gap-1">
          <button
            onClick={(e) => handleDeactivate(rule.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-orange-400 transition-colors"
            title="Deactivate Rule"
          >
            <Ban size={16} />
          </button>
          <button
            onClick={(e) => handleEdit(rule.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500 transition-colors"
            title="Edit Rule"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => handleShowClaims(rule.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-green-400 transition-colors"
            title="Show Claims"
          >
            <FileText size={16} />
          </button>
          <button
            onClick={(e) => handleDelete(rule.id, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-red-400 transition-colors"
            title="Delete Rule"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  // Filter configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Information',
      fields: [
        {
          key: 'id',
          label: 'Rule ID',
          type: 'text',
          placeholder: 'Search by rule ID...'
        },
        {
          key: 'redeem_code',
          label: 'Redeem Code',
          type: 'text',
          placeholder: 'Search by redeem code...'
        },
        {
          key: 'operator_id',
          label: 'Operator ID',
          type: 'text',
          placeholder: 'Search by operator ID...'
        }
      ]
    },
    {
      name: 'status',
      displayName: 'Status & Activity',
      fields: [
        {
          key: 'is_active',
          label: 'Status',
          type: 'select',
          options: [
            { value: '1', label: 'Active' },
            { value: '0', label: 'Inactive' }
          ]
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <DollarSign className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Balance Correction Rules</h1>
            <p className="text-gray-400">Manage balance correction rule assignments</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => loadBalanceCorrectionRules(currentPage, itemsPerPage, filters)}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Balance Correction Rules</h4>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={rulesData?.data || []}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={rulesData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Balance Correction Rules"
        emptyState={{
          icon: <DollarSign className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No balance correction rules found',
          description: 'No rules match your current criteria.',
          action: undefined
        }}
      />
    </div>
  );
};

export default BalanceCorrectionRules;
