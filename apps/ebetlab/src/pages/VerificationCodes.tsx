import React, { useState, useEffect, useCallback } from 'react';
import { RefreshCw, Eye, Shield } from 'lucide-react';
import { fetchVerificationCodes, type VerificationCodeData, type VerificationCodesResponse } from '../utils/api';
import QuickUserInfoModal from '../components/customer-details/QuickUserInfoModal';
import { getVipLevelDisplayName, getVipLevelColor } from '../constants/vipLevels';
import UnifiedTable, { TableColumn } from '../components/ui/UnifiedTable';
import Pagination from '../components/ui/Pagination';

// Remove old interface - using UnifiedTable's TableColumn

const VerificationCodes: React.FC = () => {
  // State management
  const [codes, setCodes] = useState<VerificationCodeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCodes, setTotalCodes] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Modal state
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [showQuickInfo, setShowQuickInfo] = useState(false);

  // Format timestamp to readable date
  const formatDate = (timestamp: string | number) => {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    return new Date(ts * 1000).toLocaleString();
  };

  // Get status badge
  const getStatusBadge = (usedAt: string | null) => {
    if (usedAt === null) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          Pending
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          Completed
        </span>
      );
    }
  };

  // Get rank badge
  const getRankBadge = (rank: string) => {
    const displayName = getVipLevelDisplayName(rank);
    const color = getVipLevelColor(rank);
    
    return (
      <span 
        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
        style={{ backgroundColor: `${color}20`, color: color }}
      >
        {displayName}
      </span>
    );
  };

  // Format verification type
  const formatType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  // Table column configuration for UnifiedTable
  const tableColumns: TableColumn<VerificationCodeData>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '100px',
      render: (code) => <span className="font-mono text-sm">{code.id}</span>
    },
    {
      key: 'code',
      label: 'Code',
      width: '100px',
      render: (code) => <span className="font-mono text-sm font-bold">{code.code}</span>
    },
    {
      key: 'customer_id',
      label: 'Customer ID',
      width: '120px',
      render: (code) => <span className="font-mono text-sm">{code.customer_id}</span>
    },
    {
      key: 'username',
      label: 'Username',
      width: '150px',
      render: (code) => <span className="font-medium">{code.customer?.username || '-'}</span>
    },
    {
      key: 'email',
      label: 'Email',
      width: '200px',
      render: (code) => <span className="text-sm">{code.customer?.email || '-'}</span>
    },
    {
      key: 'rank',
      label: 'Rank',
      width: '120px',
      render: (code) => getRankBadge(code.customer?.rank || 'no-vip')
    },
    {
      key: 'type',
      label: 'Type',
      width: '100px',
      render: (code) => <span className="text-sm">{formatType(code.type)}</span>
    },
    {
      key: 'timestamp',
      label: 'Created',
      width: '180px',
      render: (code) => <span className="text-sm">{formatDate(code.timestamp)}</span>
    },
    {
      key: 'status',
      label: 'Status',
      width: '120px',
      render: (code) => getStatusBadge(code.used_at)
    },
    {
      key: 'used_at',
      label: 'Used At',
      width: '180px',
      render: (code) => (
        <span className="text-sm">
          {code.used_at ? formatDate(code.used_at) : '-'}
        </span>
      )
    },
    {
      key: 'last_action',
      label: 'Last Action',
      width: '180px',
      render: (code) => (
        <span className="text-sm">
          {code.customer?.last_action ? formatDate(code.customer.last_action) : '-'}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      render: (code) => (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleViewCustomer(code);
          }}
          className="btn btn-outline text-xs py-1 px-2 flex items-center"
          title="View Customer Details"
        >
          <Eye size={14} className="mr-1" />
          View
        </button>
      )
    }
  ];

  // Event handlers
  const handleViewCustomer = (code: VerificationCodeData) => {
    setSelectedCustomerId(code.customer_id.toString());
    setShowQuickInfo(true);
  };

  const handleQuickInfo = (code: VerificationCodeData) => {
    setSelectedCustomerId(code.customer_id.toString());
    setShowQuickInfo(true);
  };

  const handleRefresh = () => {
    loadCodes(currentPage);
  };

  // API functions
  const loadCodes = useCallback(async (page: number = 1) => {
    setLoading(true);
    setError(null);

    try {
      const result = await fetchVerificationCodes(page, itemsPerPage);

      if (result.success && result.data) {
        const codeData = result.data.data || [];
        const total = result.data.total || 0;

        setCodes(codeData);
        setTotalCodes(total);
      } else {
        setError(result.error || 'Failed to load verification codes');
        setCodes([]);
      }
    } catch (err) {
      console.error('Error loading verification codes:', err);
      setError('An unexpected error occurred');
      setCodes([]);
    } finally {
      setLoading(false);
    }
  }, [itemsPerPage]);

  // Pagination
  const totalPages = Math.ceil(totalCodes / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadCodes(page);
  };

  const handleItemsPerPageChange = (newLimit: number) => {
    setItemsPerPage(newLimit);
    setCurrentPage(1);
    loadCodes(1);
  };

  // Effects
  useEffect(() => {
    loadCodes(1);
  }, [loadCodes]);

  return (
    <div className="space-y-6">
      {/* Verification Codes Table */}
      <UnifiedTable
        data={codes}
        columns={tableColumns}
        title="Verification Codes"
        subtitle={`${totalCodes} verification codes`}
        isLoading={loading}
        error={error}
        onRowClick={handleQuickInfo}
        onRetry={handleRefresh}
        minWidth="1600px"
        emptyState={{
          icon: <Shield className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No verification codes found',
          description: 'There are no verification codes to display.'
        }}
      />

      {/* Pagination */}
      {totalCodes > 0 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalCodes}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        </div>
      )}

      {/* Quick User Info Modal */}
      {showQuickInfo && selectedCustomerId && (
        <QuickUserInfoModal
          isOpen={showQuickInfo}
          onClose={() => {
            setShowQuickInfo(false);
            setSelectedCustomerId(null);
          }}
          customerId={selectedCustomerId}
        />
      )}
    </div>
  );
};

export default VerificationCodes;
