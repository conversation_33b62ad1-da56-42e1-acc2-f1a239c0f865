import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Star, ArrowLeft, Calendar, User, Tag, Hash, Activity, DollarSign, Globe, MapPin, Edit } from 'lucide-react';
import Button from '../components/ui/Button';
import { fetchWelcomeBonusDetails, type WelcomeBonusData } from '../utils/api';

const WelcomeBonusDetails: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [bonusData, setBonusData] = useState<WelcomeBonusData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Load bonus details on component mount
  useEffect(() => {
    const loadBonusDetails = async () => {
      if (!id) return;
      
      setIsLoading(true);
      setError('');

      try {
        const response = await fetchWelcomeBonusDetails(id);

        if (response.success && response.data) {
          // The API returns a single object in data
          setBonusData(response.data.data);
        } else {
          setError(response.error || 'Failed to load welcome bonus details');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    loadBonusDetails();
  }, [id]);

  // Format timestamp
  const formatDate = (timestamp: string | number) => {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    return new Date(ts * 1000).toLocaleString();
  };

  // Get status badge
  const getStatusBadge = (isActive: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          isActive
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-red-500/20 text-red-400 border border-red-500/30'
        }`}
      >
        <Activity className="w-4 h-4 mr-2" />
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading welcome bonus details...</p>
        </div>
      </div>
    );
  }

  if (error || !bonusData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate('/welcome-bonus')}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Welcome Bonuses
          </Button>
        </div>
        
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error Loading Bonus Details</h4>
              <p className="text-red-400 text-sm">{error || 'Bonus not found'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate('/welcome-bonus')}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Welcome Bonuses
          </Button>
          
          <div className="flex items-center gap-3">
            <Star className="w-8 h-8 text-primary-500" />
            <div>
              <h1 className="text-2xl font-bold text-gray-100">Welcome Bonus Details</h1>
              <p className="text-gray-400">Bonus ID: {bonusData.id}</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => navigate(`/welcome-bonus/edit/${bonusData.id}`)}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Edit className="w-4 h-4" />
            Edit Bonus
          </Button>
        </div>
      </div>

      {/* Bonus Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Basic Information</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Status</span>
              {getStatusBadge(bonusData.is_active)}
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Code</span>
              <div className="flex items-center gap-2">
                <Tag className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200 font-mono">{bonusData.code}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Reference Code</span>
              <span className="text-gray-200 font-mono">{bonusData.ref_code}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Operator</span>
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">
                  {bonusData.operator?.name || `ID: ${bonusData.operator_id}`}
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Created</span>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{formatDate(bonusData.timestamp)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Amount Information */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Amount Information</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Amount</span>
              <div className="text-right">
                <div className="text-gray-200 font-medium">{bonusData.amount} {bonusData.currency_code}</div>
                <div className="text-gray-400 text-sm">${bonusData.usd_amount} USD</div>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Currency</span>
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200 font-medium">{bonusData.currency_code}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Required Wager</span>
              <div className="text-right">
                <div className="text-gray-200 font-medium">{bonusData.required_wager}</div>
                <div className="text-gray-400 text-sm">{bonusData.required_wager_currency}</div>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Total</span>
              <div className="flex items-center gap-2">
                <Hash className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200 font-medium">{bonusData.total}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Redeems Count</span>
              <span className="text-gray-200 font-medium">{bonusData.redeems_count}</span>
            </div>
          </div>
        </div>

        {/* Validity Period */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Validity Period</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">From</span>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{formatDate(bonusData.from)}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">To</span>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{formatDate(bonusData.to)}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Duration</span>
              <span className="text-gray-200">
                {Math.ceil((bonusData.to - bonusData.from) / (24 * 60 * 60))} days
              </span>
            </div>
          </div>
        </div>

        {/* Location & Technical Details */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Location & Technical Details</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Country</span>
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{bonusData.details.country}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">City</span>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-400" />
                <span className="text-gray-200">{bonusData.details.city}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">IP Address</span>
              <span className="text-gray-200 font-mono">{bonusData.details.real_ip}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Merchant ID</span>
              <span className="text-gray-200 font-mono">#{bonusData.merchant_id}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Website ID</span>
              <span className="text-gray-200 font-mono">#{bonusData.website_id}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Currency ID</span>
              <span className="text-gray-200 font-mono">
                {bonusData.currency_id ? `#${bonusData.currency_id}` : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeBonusDetails;
