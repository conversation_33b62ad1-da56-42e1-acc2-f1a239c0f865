import React, { useState } from 'react';
import { Server, Send, Copy, RefreshCw, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { getAuthHeaders } from '../utils/api/common';

interface BackendApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  status?: number;
  headers?: Record<string, string>;
  timestamp?: string;
}

const DebugBackendApi: React.FC = () => {
  const [url, setUrl] = useState('http://localhost:3001/api/some-endpoint');
  const [method, setMethod] = useState('POST');
  const [requestBody, setRequestBody] = useState('');
  const [response, setResponse] = useState<BackendApiResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Initialize with example request body
  React.useEffect(() => {
    const exampleBody = {
      page: 1,
      limit: 20,
      rt: Math.floor(Date.now() / 1000)
    };
    setRequestBody(JSON.stringify(exampleBody, null, 2));
  }, []);

  const handleSendRequest = async () => {
    if (!url.trim()) {
      alert('Please enter a URL');
      return;
    }

    setIsLoading(true);

    try {
      let parsedBody = null;
      if (requestBody.trim() && ['POST', 'PUT', 'PATCH'].includes(method)) {
        try {
          parsedBody = JSON.parse(requestBody);
        } catch (error) {
          alert('Invalid JSON in request body');
          setIsLoading(false);
          return;
        }
      }

      console.log('Backend API Request:', {
        url,
        method: method.toUpperCase(),
        body: parsedBody
      });

      // Prepare the request options
      const requestOptions: RequestInit = {
        method: method.toUpperCase(),
        headers: getAuthHeaders(),
      };

      // Add body for methods that support it
      if (parsedBody && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
        requestOptions.body = JSON.stringify(parsedBody);
      }

      const startTime = Date.now();
      const response = await fetch(url, requestOptions);
      const endTime = Date.now();

      // Get response headers
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      let responseData;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      const backendResponse: BackendApiResponse = {
        success: response.ok,
        data: responseData,
        status: response.status,
        headers: responseHeaders,
        timestamp: new Date().toISOString(),
      };

      if (!response.ok) {
        backendResponse.error = `HTTP ${response.status}: ${response.statusText}`;
      }

      console.log('Backend API Response:', {
        status: response.status,
        statusText: response.statusText,
        duration: `${endTime - startTime}ms`,
        data: responseData
      });

      setResponse(backendResponse);
    } catch (error) {
      console.error('Backend API Error:', error);
      
      const errorResponse: BackendApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString(),
      };

      setResponse(errorResponse);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyResponse = async () => {
    if (!response) return;

    try {
      await navigator.clipboard.writeText(JSON.stringify(response, null, 2));
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatJsonString = (str: string) => {
    try {
      const parsed = JSON.parse(str);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return str;
    }
  };

  const handleFormatJson = () => {
    setRequestBody(formatJsonString(requestBody));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-green-500/20 rounded-lg">
          <Server className="w-6 h-6 text-green-500" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-white">Debug Backend API</h1>
          <p className="text-gray-400">Test API endpoints directly on your backend server</p>
        </div>
      </div>

      {/* Info Banner */}
      <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-green-200">
            <p className="font-medium mb-1">How it works:</p>
            <p>Enter the full backend server URL including host and port below. No URL substitution will be performed - requests are sent directly to your backend server.
            For example: <code className="bg-green-500/20 px-1 rounded">http://localhost:3001/api/some-endpoint</code></p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Request Panel */}
        <div className="bg-dark-700 rounded-lg border border-dark-600">
          <div className="p-4 border-b border-dark-600">
            <h2 className="text-lg font-semibold text-white">Request</h2>
          </div>
          
          <div className="p-4 space-y-4">
            {/* URL Input */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                URL <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="http://localhost:3001/api/..."
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-white placeholder-gray-500 focus:outline-none focus:border-green-500"
              />
            </div>

            {/* Method Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Method
              </label>
              <select
                value={method}
                onChange={(e) => setMethod(e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-white focus:outline-none focus:border-green-500"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="PATCH">PATCH</option>
                <option value="DELETE">DELETE</option>
              </select>
            </div>

            {/* Request Body */}
            {['POST', 'PUT', 'PATCH'].includes(method) && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-300">
                    Request Body (JSON)
                  </label>
                  <button
                    onClick={handleFormatJson}
                    className="text-xs text-green-400 hover:text-green-300 flex items-center gap-1"
                  >
                    <RefreshCw size={12} />
                    Format
                  </button>
                </div>
                <textarea
                  value={requestBody}
                  onChange={(e) => setRequestBody(e.target.value)}
                  placeholder="Enter JSON request body..."
                  rows={12}
                  className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-white placeholder-gray-500 focus:outline-none focus:border-green-500 font-mono text-sm"
                />
              </div>
            )}

            {/* Send Button */}
            <button
              onClick={handleSendRequest}
              disabled={isLoading}
              className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-600/50 text-white rounded-md transition-colors"
            >
              {isLoading ? (
                <RefreshCw size={16} className="animate-spin" />
              ) : (
                <Send size={16} />
              )}
              {isLoading ? 'Sending...' : 'Send Request'}
            </button>
          </div>
        </div>

        {/* Response Panel */}
        <div className="bg-dark-700 rounded-lg border border-dark-600">
          <div className="p-4 border-b border-dark-600 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">Response</h2>
            {response && (
              <button
                onClick={handleCopyResponse}
                className="flex items-center gap-2 px-3 py-1 text-sm bg-dark-600 hover:bg-dark-500 text-gray-300 rounded-md transition-colors"
              >
                {copySuccess ? (
                  <>
                    <CheckCircle size={14} className="text-green-400" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy size={14} />
                    Copy
                  </>
                )}
              </button>
            )}
          </div>

          <div className="p-4">
            {!response ? (
              <div className="text-center text-gray-400 py-8">
                <Server size={48} className="mx-auto mb-4 opacity-50" />
                <p>No response yet. Send a request to see the response here.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Status */}
                <div className="flex items-center gap-2">
                  {response.success ? (
                    <CheckCircle size={16} className="text-green-400" />
                  ) : (
                    <AlertCircle size={16} className="text-red-400" />
                  )}
                  <span className={`font-medium ${response.success ? 'text-green-400' : 'text-red-400'}`}>
                    {response.status ? `HTTP ${response.status}` : 'Request Failed'}
                  </span>
                  {response.timestamp && (
                    <span className="text-xs text-gray-500 ml-auto">
                      {new Date(response.timestamp).toLocaleTimeString()}
                    </span>
                  )}
                </div>

                {/* Error */}
                {response.error && (
                  <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-md">
                    <p className="text-red-400 text-sm">{response.error}</p>
                  </div>
                )}

                {/* Response Data */}
                {response.data && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-300 mb-2">Response Data:</h3>
                    <pre className="bg-dark-800 p-3 rounded-md text-sm text-gray-300 overflow-auto max-h-96 border border-dark-600">
                      {typeof response.data === 'string'
                        ? response.data
                        : JSON.stringify(response.data, null, 2)
                      }
                    </pre>
                  </div>
                )}

                {/* Headers */}
                {response.headers && Object.keys(response.headers).length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-300 mb-2">Response Headers:</h3>
                    <pre className="bg-dark-800 p-3 rounded-md text-sm text-gray-300 overflow-auto max-h-32 border border-dark-600">
                      {JSON.stringify(response.headers, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugBackendApi;
