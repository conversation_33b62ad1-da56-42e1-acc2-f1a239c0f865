import React, { useState, useEffect, useCallback } from 'react';
import {
  Award, Plus, RefreshCw, AlertCircle, Edit, Trash2, Eye, Calendar,
  TrendingUp, TrendingDown, Users, BarChart3, Settings, Clock, CheckCircle,
  XCircle, Filter, Search, X, UserPlus, Coins, Target, Activity
} from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import Modal from '../components/ui/Modal';
import Input from '../components/ui/Input';
import {
  fetchExtendedUsers,
  fetchExtendedUserByExternalId,
  createExtendedUser,
  updateExtendedUser,
  deleteExtendedUser,
  fetchExtendedUserStatistics,
  type ExtendedUser,
  type CreateExtendedUserRequest,
  type UpdateExtendedUserRequest,
  type ExtendedUserStatistics
} from '../utils/api/extended-users';
import { type ExtendedUsersQueryParams } from '../utils/api/common';
import {
  fetchMissionParticipations,
  fetchUserStatistics,
  type MissionParticipation
} from '../utils/api/mission-participations';

interface ExtendedUsersData {
  total: number;
  data: ExtendedUser[];
}

interface UserFormData {
  externalId: number;
  points: number;
}

interface PointsOperationData {
  operation: 'set' | 'add' | 'subtract';
  amount: number;
  reason?: string;
}

interface UserStats {
  totalParticipations: number;
  completedMissions: number;
  completionRate: number;
}

const MissionsPoints: React.FC = () => {
  // State management
  const [usersData, setUsersData] = useState<ExtendedUsersData>({ total: 0, data: [] });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<Record<string, unknown>>({});
  const [sortState, setSortState] = useState<SortState>({
    field: 'id',
    direction: 'desc'
  });

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showPointsModal, setShowPointsModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);

  // Selected user and form data
  const [selectedUser, setSelectedUser] = useState<ExtendedUser | null>(null);
  const [formData, setFormData] = useState<UserFormData>({
    externalId: 0,
    points: 0
  });
  const [pointsOperation, setPointsOperation] = useState<PointsOperationData>({
    operation: 'add',
    amount: 0,
    reason: ''
  });

  // Additional data
  const [userParticipations, setUserParticipations] = useState<MissionParticipation[]>([]);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [statistics, setStatistics] = useState<ExtendedUserStatistics>({
    totalUsers: 0,
    totalPoints: 0,
    averagePoints: 0
  });

  // Load statistics from API
  const loadStatistics = useCallback(async () => {
    try {
      const response = await fetchExtendedUserStatistics();
      if (response.success && response.data) {
        setStatistics(response.data);
      }
    } catch (err) {
      console.error('Failed to load statistics:', err);
      // Don't set error state for statistics failure, just log it
    }
  }, []);

  // Load users data
  const loadUsers = useCallback(async (
    page: number = currentPage,
    limit: number = itemsPerPage,
    searchFilters: Record<string, unknown> = filters
  ) => {
    setIsLoading(true);
    setError('');

    try {
      // Build query parameters for server-side filtering
      const queryParams: ExtendedUsersQueryParams = {
        page,
        limit,
        sortBy: sortState.field || 'createdAt',
        sortOrder: sortState.direction?.toUpperCase() || 'DESC'
      };

      // Add filtering parameters
      if (searchFilters.externalId) {
        queryParams.externalId = parseInt(searchFilters.externalId as string);
      }

      if (searchFilters.points) {
        queryParams.points = parseInt(searchFilters.points as string);
      }

      if (searchFilters.minPoints) {
        queryParams.minPoints = parseInt(searchFilters.minPoints as string);
      }

      if (searchFilters.maxPoints) {
        queryParams.maxPoints = parseInt(searchFilters.maxPoints as string);
      }

      if (searchFilters.externalUsername) {
        queryParams.externalUsername = searchFilters.externalUsername as string;
      }

      if (searchFilters.search) {
        queryParams.search = searchFilters.search as string;
      }

      const response = await fetchExtendedUsers(queryParams);

      if (response.success && response.data) {
        const userData = response.data || [];
        const totalCount = response.meta?.total || 0;

        setUsersData({
          total: totalCount,
          data: userData
        });
      } else {
        setError(response.error || 'Failed to load users');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load users and statistics on component mount
  useEffect(() => {
    loadUsers();
    loadStatistics();
  }, [loadUsers, loadStatistics]);

  // Reload data when sort state changes
  useEffect(() => {
    if (sortState.field) {
      setCurrentPage(1);
      loadUsers(1, itemsPerPage, filters);
    }
  }, [sortState.field, sortState.direction, itemsPerPage, filters]);

  // Utility functions
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatPoints = (points: number) => {
    return points.toLocaleString();
  };

  const getPointsBadge = (points: number) => {
    let colorClass = 'bg-gray-500/20 text-gray-400 border-gray-500/30';

    if (points > 10000) {
      colorClass = 'bg-purple-500/20 text-purple-400 border-purple-500/30';
    } else if (points > 5000) {
      colorClass = 'bg-blue-500/20 text-blue-400 border-blue-500/30';
    } else if (points > 1000) {
      colorClass = 'bg-green-500/20 text-green-400 border-green-500/30';
    } else if (points > 0) {
      colorClass = 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    } else {
      colorClass = 'bg-red-500/20 text-red-400 border-red-500/30';
    }

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}>
        <Coins className="w-3 h-3 mr-1" />
        {formatPoints(points)}
      </span>
    );
  };

  const getActivityBadge = (user: ExtendedUser) => {
    const daysSinceUpdate = Math.floor((Date.now() - new Date(user.updatedAt).getTime()) / (1000 * 60 * 60 * 24));

    if (daysSinceUpdate <= 1) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
          <Activity className="w-3 h-3 mr-1" />
          Active
        </span>
      );
    } else if (daysSinceUpdate <= 7) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
          <Clock className="w-3 h-3 mr-1" />
          Recent
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-500/20 text-gray-400 border border-gray-500/30">
          <Clock className="w-3 h-3 mr-1" />
          Inactive
        </span>
      );
    }
  };

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadUsers(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadUsers(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadUsers(1, itemsPerPage, newFilters);
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (user: ExtendedUser) => {
    setSelectedUser(user);
    setShowDetailsModal(true);
  };

  // Form handlers
  const resetForm = () => {
    setFormData({
      externalId: 0,
      points: 0
    });
  };

  const resetPointsOperation = () => {
    setPointsOperation({
      operation: 'add',
      amount: 0,
      reason: ''
    });
  };

  const handleInputChange = (field: keyof UserFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePointsInputChange = (field: keyof PointsOperationData, value: string | number) => {
    setPointsOperation(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): string | null => {
    if (!formData.externalId || formData.externalId <= 0) return 'External ID is required and must be positive';
    if (formData.points < 0) return 'Points cannot be negative';
    return null;
  };

  const validatePointsOperation = (): string | null => {
    if (!pointsOperation.amount || pointsOperation.amount <= 0) return 'Amount must be greater than 0';
    if (pointsOperation.operation === 'subtract' && selectedUser && pointsOperation.amount > selectedUser.points) {
      return 'Cannot subtract more points than user currently has';
    }
    return null;
  };

  // CRUD operations
  const handleCreate = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      const userData: CreateExtendedUserRequest = {
        externalId: formData.externalId,
        points: formData.points
      };

      const response = await createExtendedUser(userData);

      if (response.success) {
        setShowCreateModal(false);
        resetForm();
        loadUsers(currentPage, itemsPerPage, filters);
        loadStatistics(); // Reload statistics after creating user
      } else {
        setError(response.error || 'Failed to create user');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create user');
    }
  };

  const handleEdit = (user: ExtendedUser, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedUser(user);
    setFormData({
      externalId: user.externalId,
      points: user.points
    });
    setShowEditModal(true);
  };

  const handleUpdate = async () => {
    if (!selectedUser) return;

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      // Use the new updateExtendedUser approach instead of deprecated upsert
      const userData: UpdateExtendedUserRequest = {
        externalId: formData.externalId,
        points: formData.points
      };

      const response = await updateExtendedUser(selectedUser.id, userData);

      if (response.success) {
        setShowEditModal(false);
        setSelectedUser(null);
        resetForm();
        loadUsers(currentPage, itemsPerPage, filters);
        loadStatistics(); // Reload statistics after updating user
      } else {
        setError(response.error || 'Failed to update user');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update user');
    }
  };

  const handleDelete = async (user: ExtendedUser, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!window.confirm(`Are you sure you want to delete user with External ID ${user.externalId}?`)) return;

    try {
      const response = await deleteExtendedUser(user.id);

      if (response.success) {
        loadUsers(currentPage, itemsPerPage, filters);
        loadStatistics(); // Reload statistics after deleting user
      } else {
        setError(response.error || 'Failed to delete user');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete user');
    }
  };

  const handleView = (user: ExtendedUser, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedUser(user);
    setShowDetailsModal(true);
  };

  const handleManagePoints = async (user: ExtendedUser, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedUser(user);
    resetPointsOperation();
    setShowPointsModal(true);
  };

  const handlePointsOperation = async () => {
    if (!selectedUser) return;

    const validationError = validatePointsOperation();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      let response;
      let newPoints: number;

      // Calculate the new absolute points value on the frontend
      if (pointsOperation.operation === 'set') {
        newPoints = pointsOperation.amount;
      } else if (pointsOperation.operation === 'add') {
        newPoints = selectedUser.points + pointsOperation.amount;
      } else { // subtract
        newPoints = selectedUser.points - pointsOperation.amount;
      }

      // Ensure points don't go below 0
      newPoints = Math.max(0, newPoints);

      // Always use absolute value for all operations
      response = await updateExtendedUser(selectedUser.id, {
        points: newPoints
      });

      if (response.success) {
        // Update the selected user's points in state for immediate UI feedback
        setSelectedUser(prev => prev ? { ...prev, points: newPoints } : null);

        setShowPointsModal(false);
        setSelectedUser(null);
        resetPointsOperation();
        loadUsers(currentPage, itemsPerPage, filters);
        loadStatistics(); // Reload statistics after points operation

        // Show success message with operation details
        const operationText = pointsOperation.operation === 'set' ? 'set to' :
                             pointsOperation.operation === 'add' ? 'increased by' : 'decreased by';
        console.log(`Points successfully ${operationText} ${pointsOperation.amount.toLocaleString()}. New total: ${newPoints.toLocaleString()}`);
      } else {
        setError(response.error || 'Failed to update points');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update points');
    }
  };

  const handleViewDetails = async (user: ExtendedUser, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedUser(user);

    try {
      // Load user participations and statistics using new query parameter approach
      const [participationsResponse, statsResponse] = await Promise.all([
        fetchMissionParticipations({ userId: user.externalId }),
        fetchUserStatistics(user.externalId)
      ]);

      if (participationsResponse.success) {
        // fetchMissionParticipations returns paginated data, so we need to access the data array
        setUserParticipations(participationsResponse.data || []);
      }

      if (statsResponse.success) {
        setUserStats(statsResponse.data);
      }

      setShowDetailsModal(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load user details');
    }
  };

  // Table configuration
  const tableColumns: TableColumn<ExtendedUser>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (user) => <span className="font-mono text-sm">#{user.id}</span>
    },
    {
      key: 'externalId',
      label: 'External ID',
      width: '120px',
      sortable: true,
      render: (user) => (
        <div className="flex items-center gap-2">
          <Users className="w-4 h-4 text-gray-400" />
          <span className="font-medium text-sm">{user.externalId}</span>
        </div>
      )
    },
    {
      key: 'externalUsername',
      label: 'Username',
      width: '150px',
      sortable: false,
      render: (user) => (
        <div className="flex items-center gap-2">
          <UserPlus className="w-4 h-4 text-gray-400" />
          <span className="text-sm">
            {user.externalUsername || (
              <span className="text-gray-500 italic">N/A</span>
            )}
          </span>
        </div>
      )
    },
    {
      key: 'points',
      label: 'Points',
      width: '150px',
      sortable: true,
      render: (user) => getPointsBadge(user.points)
    },
    {
      key: 'createdAt',
      label: 'Created',
      width: '160px',
      sortable: true,
      render: (user) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm whitespace-nowrap">{formatDate(user.createdAt)}</span>
        </div>
      )
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      width: '160px',
      sortable: true,
      render: (user) => (
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-gray-400" />
          <span className="text-sm whitespace-nowrap">{formatDate(user.updatedAt)}</span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '200px',
      sortable: false,
      render: (user) => (
        <div className="flex gap-1">
          <button
            onClick={(e) => handleViewDetails(user, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-blue-400 transition-colors"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={(e) => handleEdit(user, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-primary-500 transition-colors"
            title="Edit User"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => handleManagePoints(user, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-green-400 transition-colors"
            title="Manage Points"
          >
            <Coins size={16} />
          </button>
          <button
            onClick={(e) => handleDelete(user, e)}
            className="p-1.5 rounded-md text-gray-300 hover:bg-dark-700 hover:text-red-400 transition-colors"
            title="Delete User"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  // Filter configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Information',
      fields: [
        {
          key: 'externalId',
          label: 'External ID (Exact)',
          type: 'number',
          placeholder: 'Enter exact external ID...'
        },
        {
          key: 'search',
          label: 'General Search',
          type: 'text',
          placeholder: 'Search external ID or username (partial match)...'
        },
        {
          key: 'externalUsername',
          label: 'Username (Exact)',
          type: 'text',
          placeholder: 'Enter exact username...'
        }
      ]
    },
    {
      name: 'points',
      displayName: 'Points Filtering',
      fields: [
        {
          key: 'points',
          label: 'Exact Points',
          type: 'number',
          placeholder: 'e.g., 500',
          min: 0
        },
        {
          key: 'minPoints',
          label: 'Minimum Points',
          type: 'number',
          placeholder: 'e.g., 100',
          min: 0
        },
        {
          key: 'maxPoints',
          label: 'Maximum Points',
          type: 'number',
          placeholder: 'e.g., 10000',
          min: 0
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Award className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Points Management</h1>
            <p className="text-gray-400">Manage user points and track activity</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => {
              loadUsers(currentPage, itemsPerPage, filters);
              loadStatistics();
            }}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>

          <Button
            onClick={() => {
              resetForm();
              setShowCreateModal(true);
            }}
            variant="primary"
            className="flex items-center gap-2"
          >
            <UserPlus className="w-4 h-4" />
            Add User
          </Button>
        </div>
      </div>

      {/* Statistics Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-5 h-5 text-blue-400" />
            <span className="text-sm font-medium text-gray-300">Total Users</span>
          </div>
          <span className="text-2xl font-bold text-blue-400">{statistics.totalUsers.toLocaleString()}</span>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center gap-2 mb-2">
            <Coins className="w-5 h-5 text-yellow-400" />
            <span className="text-sm font-medium text-gray-300">Total Points</span>
          </div>
          <span className="text-2xl font-bold text-yellow-400">{statistics.totalPoints.toLocaleString()}</span>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center gap-2 mb-2">
            <BarChart3 className="w-5 h-5 text-green-400" />
            <span className="text-sm font-medium text-gray-300">Average Points</span>
          </div>
          <span className="text-2xl font-bold text-green-400">{Math.round(statistics.averagePoints).toLocaleString()}</span>
        </div>
      </div>

      {/* Error Section */}
      {error && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="flex items-start gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-500 font-medium mb-1">Error</h4>
              <p className="text-red-400 text-sm">{error}</p>
              <Button
                onClick={() => setError('')}
                variant="ghost"
                size="sm"
                className="mt-2 text-red-400 hover:text-red-300"
              >
                Dismiss
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={usersData.data}
        columns={tableColumns}
        filterGroups={filterGroups}
        total={usersData.total}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        filters={filters}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onFilterChange={handleFilterChange}
        onRowClick={handleRowClick}
        title="Extended Users"
        emptyState={{
          icon: <Users className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No users found',
          description: 'Get started by adding your first user.',
          action: (
            <Button
              onClick={() => {
                resetForm();
                setShowCreateModal(true);
              }}
              variant="primary"
            >
              Add User
            </Button>
          )
        }}
      />

      {/* Create User Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          resetForm();
          setError('');
        }}
        title="Add New User"
        size="md"
      >
        <div className="space-y-6">
          <Input
            label="External ID"
            type="number"
            value={formData.externalId}
            onChange={(e) => handleInputChange('externalId', parseInt(e.target.value) || 0)}
            placeholder="Enter external user ID..."
            min="1"
            required
          />

          <Input
            label="Initial Points"
            type="number"
            value={formData.points}
            onChange={(e) => handleInputChange('points', parseInt(e.target.value) || 0)}
            placeholder="Enter initial points..."
            min="0"
            required
          />

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowCreateModal(false);
                resetForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreate}
              variant="primary"
              className="flex items-center gap-2"
            >
              <UserPlus className="w-4 h-4" />
              Add User
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit User Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedUser(null);
          resetForm();
          setError('');
        }}
        title={`Edit User: ${selectedUser?.externalId || ''}`}
        size="md"
      >
        <div className="space-y-6">
          <Input
            label="External ID"
            type="number"
            value={formData.externalId}
            onChange={(e) => handleInputChange('externalId', parseInt(e.target.value) || 0)}
            placeholder="Enter external user ID..."
            min="1"
            required
          />

          <Input
            label="Points"
            type="number"
            value={formData.points}
            onChange={(e) => handleInputChange('points', parseInt(e.target.value) || 0)}
            placeholder="Enter points..."
            min="0"
            required
          />

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowEditModal(false);
                setSelectedUser(null);
                resetForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdate}
              variant="primary"
              className="flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              Update User
            </Button>
          </div>
        </div>
      </Modal>

      {/* Points Management Modal */}
      <Modal
        isOpen={showPointsModal}
        onClose={() => {
          setShowPointsModal(false);
          setSelectedUser(null);
          resetPointsOperation();
          setError('');
        }}
        title={`Manage Points: User ${selectedUser?.externalId || ''}`}
        size="md"
      >
        <div className="space-y-6">
          {selectedUser && (
            <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-300">Current Points</span>
                {getPointsBadge(selectedUser.points)}
              </div>
              <div className="text-xs text-gray-400">
                Last updated: {formatDate(selectedUser.updatedAt)}
              </div>
            </div>
          )}

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Operation</label>
            <select
              value={pointsOperation.operation}
              onChange={(e) => handlePointsInputChange('operation', e.target.value as 'set' | 'add' | 'subtract')}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="add">Add Points</option>
              <option value="subtract">Subtract Points</option>
              <option value="set">Set Absolute Points</option>
            </select>
          </div>

          <Input
            label="Amount"
            type="number"
            value={pointsOperation.amount}
            onChange={(e) => handlePointsInputChange('amount', parseInt(e.target.value) || 0)}
            placeholder="Enter amount..."
            min="1"
            required
          />

          {/* Points calculation preview */}
          {selectedUser && pointsOperation.amount > 0 && (
            <div className="bg-dark-800 border border-dark-600 rounded-md p-3">
              <div className="text-sm text-gray-300 mb-1">Preview:</div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Current Points:</span>
                <span className="text-yellow-400 font-medium">{selectedUser.points.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">
                  {pointsOperation.operation === 'set' ? 'New Points:' :
                   pointsOperation.operation === 'add' ? 'After Adding:' : 'After Subtracting:'}
                </span>
                <span className={`font-medium ${
                  pointsOperation.operation === 'set' ? 'text-blue-400' :
                  pointsOperation.operation === 'add' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {(() => {
                    let newPoints: number;
                    if (pointsOperation.operation === 'set') {
                      newPoints = pointsOperation.amount;
                    } else if (pointsOperation.operation === 'add') {
                      newPoints = selectedUser.points + pointsOperation.amount;
                    } else {
                      newPoints = Math.max(0, selectedUser.points - pointsOperation.amount);
                    }
                    return newPoints.toLocaleString();
                  })()}
                </span>
              </div>
              {pointsOperation.operation === 'subtract' &&
               selectedUser.points - pointsOperation.amount < 0 && (
                <div className="text-xs text-orange-400 mt-1">
                  ⚠️ Points will be set to 0 (cannot go negative)
                </div>
              )}
            </div>
          )}

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Reason (Optional)</label>
            <textarea
              value={pointsOperation.reason}
              onChange={(e) => handlePointsInputChange('reason', e.target.value)}
              placeholder="Enter reason for this operation..."
              rows={2}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
            />
          </div>

          {selectedUser && pointsOperation.operation === 'set' && (
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-md p-3">
              <div className="text-sm text-blue-400">
                <strong>Preview:</strong> User will have {pointsOperation.amount.toLocaleString()} points
              </div>
            </div>
          )}

          {selectedUser && pointsOperation.operation === 'add' && (
            <div className="bg-green-500/10 border border-green-500/20 rounded-md p-3">
              <div className="text-sm text-green-400">
                <strong>Preview:</strong> User will have {(selectedUser.points + pointsOperation.amount).toLocaleString()} points
              </div>
            </div>
          )}

          {selectedUser && pointsOperation.operation === 'subtract' && (
            <div className="bg-orange-500/10 border border-orange-500/20 rounded-md p-3">
              <div className="text-sm text-orange-400">
                <strong>Preview:</strong> User will have {Math.max(0, selectedUser.points - pointsOperation.amount).toLocaleString()} points
              </div>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowPointsModal(false);
                setSelectedUser(null);
                resetPointsOperation();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handlePointsOperation}
              variant="primary"
              className="flex items-center gap-2"
            >
              <Coins className="w-4 h-4" />
              {pointsOperation.operation === 'set' ? 'Set Points' :
               pointsOperation.operation === 'add' ? 'Add Points' : 'Subtract Points'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* User Details Modal */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedUser(null);
          setUserParticipations([]);
          setUserStats(null);
        }}
        title={`User Details: ${selectedUser?.externalId || ''}`}
        size="xl"
      >
        {selectedUser && (
          <div className="space-y-6">
            {/* User Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Internal ID</label>
                  <span className="font-mono text-sm">#{selectedUser.id}</span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">External ID</label>
                  <span className="text-gray-100">{selectedUser.externalId}</span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Points</label>
                  {getPointsBadge(selectedUser.points)}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Activity Status</label>
                  {getActivityBadge(selectedUser)}
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Created</label>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-100">{formatDate(selectedUser.createdAt)}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Last Updated</label>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-100">{formatDate(selectedUser.updatedAt)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Mission Statistics */}
            {userStats && (
              <div className="bg-dark-800 rounded-md border border-dark-600 p-4">
                <h4 className="text-sm font-medium text-gray-300 mb-4">Mission Statistics</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-1">
                      <Target className="w-4 h-4 text-blue-400" />
                      <span className="text-sm font-medium text-gray-300">Total Participations</span>
                    </div>
                    <span className="text-xl font-bold text-blue-400">{userStats.totalParticipations}</span>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-1">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-sm font-medium text-gray-300">Completed</span>
                    </div>
                    <span className="text-xl font-bold text-green-400">{userStats.completedMissions}</span>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-1">
                      <BarChart3 className="w-4 h-4 text-primary-500" />
                      <span className="text-sm font-medium text-gray-300">Success Rate</span>
                    </div>
                    <span className="text-xl font-bold text-primary-500">{userStats.completionRate.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            )}

            {/* Recent Participations */}
            <div className="bg-dark-800 rounded-md border border-dark-600">
              <div className="p-4 border-b border-dark-600">
                <h4 className="text-sm font-medium text-gray-300">Recent Mission Participations</h4>
              </div>

              <div className="max-h-64 overflow-y-auto">
                {userParticipations.length > 0 ? (
                  <div className="divide-y divide-dark-600">
                    {userParticipations.slice(0, 10).map((participation) => (
                      <div key={participation.id} className="p-4 flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-dark-700 rounded-full flex items-center justify-center">
                            <Target className="w-4 h-4 text-gray-400" />
                          </div>
                          <div>
                            <span className="text-sm font-medium text-gray-200">
                              Mission #{participation.missionId}
                            </span>
                            <div className="text-xs text-gray-400">
                              Joined: {new Date(participation.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {participation.isCompleted ? (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Completed
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
                              <Clock className="w-3 h-3 mr-1" />
                              Pending
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Target className="w-8 h-8 text-gray-500 mx-auto mb-2" />
                    <p className="text-gray-400 text-sm">No mission participations</p>
                    <p className="text-gray-500 text-xs">User hasn't joined any missions yet</p>
                  </div>
                )}
              </div>

              {userParticipations.length > 10 && (
                <div className="p-4 border-t border-dark-600 text-center">
                  <span className="text-xs text-gray-400">
                    Showing 10 of {userParticipations.length} participations
                  </span>
                </div>
              )}
            </div>

            <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
              <Button
                onClick={() => {
                  setShowDetailsModal(false);
                  handleEdit(selectedUser, { stopPropagation: () => {} } as React.MouseEvent);
                }}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                Edit User
              </Button>

              <Button
                onClick={() => {
                  setShowDetailsModal(false);
                  handleManagePoints(selectedUser, { stopPropagation: () => {} } as React.MouseEvent);
                }}
                variant="primary"
                className="flex items-center gap-2"
              >
                <Coins className="w-4 h-4" />
                Manage Points
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default MissionsPoints;
