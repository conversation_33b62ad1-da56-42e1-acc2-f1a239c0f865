import React, { useState, useEffect, useCallback } from 'react';
import { RefreshCw, Eye, UserX } from 'lucide-react';
import { fetchSelfExclusions, type SelfExclusionData, type SelfExclusionsResponse } from '../utils/api';
import QuickUserInfoModal from '../components/customer-details/QuickUserInfoModal';
import UnifiedTable, { TableColumn } from '../components/ui/UnifiedTable';
import Pagination from '../components/ui/Pagination';

// Remove old interface - using UnifiedTable's TableColumn

const SelfExclusionList: React.FC = () => {
  // State management
  const [exclusions, setExclusions] = useState<SelfExclusionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalExclusions, setTotalExclusions] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Modal state
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [showQuickInfo, setShowQuickInfo] = useState(false);

  // Format timestamp to readable date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format step display
  const getStepDisplay = (step: number) => {
    const stepLabels: { [key: number]: string } = {
      1: 'Step 1',
      2: 'Step 2',
      3: 'Step 3',
      4: 'Step 4',
      5: 'Step 5'
    };
    return stepLabels[step] || `Step ${step}`;
  };

  // Get status badge
  const getStatusBadge = (isActive: number) => {
    if (isActive === 1) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Active
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          Inactive
        </span>
      );
    }
  };

  // Table column configuration for UnifiedTable
  const tableColumns: TableColumn<SelfExclusionData>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '100px',
      render: (exclusion) => <span className="font-mono text-sm">{exclusion.id}</span>
    },
    {
      key: 'customer_id',
      label: 'Customer ID',
      width: '120px',
      render: (exclusion) => <span className="font-mono text-sm">{exclusion.customer_id}</span>
    },
    {
      key: 'username',
      label: 'Username',
      width: '150px',
      render: (exclusion) => <span className="font-medium">{exclusion.customer?.username || '-'}</span>
    },
    {
      key: 'masked_username',
      label: 'Masked Username',
      width: '150px',
      render: (exclusion) => <span className="text-sm text-gray-400">{exclusion.customer?.masked_username || '-'}</span>
    },
    {
      key: 'step',
      label: 'Step',
      width: '100px',
      render: (exclusion) => <span className="text-sm">{getStepDisplay(exclusion.step)}</span>
    },
    {
      key: 'status',
      label: 'Status',
      width: '100px',
      render: (exclusion) => getStatusBadge(exclusion.is_active)
    },
    {
      key: 'timestamp',
      label: 'Created',
      width: '180px',
      render: (exclusion) => <span className="text-sm">{formatDate(exclusion.timestamp)}</span>
    },
    {
      key: 'excluded_till',
      label: 'Excluded Until',
      width: '180px',
      render: (exclusion) => <span className="text-sm">{formatDate(exclusion.excluded_till)}</span>
    },
    {
      key: 'last_action',
      label: 'Last Action',
      width: '180px',
      render: (exclusion) => <span className="text-sm">{exclusion.customer?.last_action ? formatDate(exclusion.customer.last_action) : '-'}</span>
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      render: (exclusion) => (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleViewCustomer(exclusion);
          }}
          className="btn btn-outline text-xs py-1 px-2 flex items-center"
          title="View Customer Details"
        >
          <Eye size={14} className="mr-1" />
          View
        </button>
      )
    }
  ];

  // Event handlers
  const handleViewCustomer = (exclusion: SelfExclusionData) => {
    setSelectedCustomerId(exclusion.customer_id.toString());
    setShowQuickInfo(true);
  };

  const handleQuickInfo = (exclusion: SelfExclusionData) => {
    setSelectedCustomerId(exclusion.customer_id.toString());
    setShowQuickInfo(true);
  };

  const handleRefresh = () => {
    loadExclusions(currentPage);
  };

  // API functions
  const loadExclusions = useCallback(async (page: number = 1) => {
    setLoading(true);
    setError(null);

    try {
      const result = await fetchSelfExclusions(page, itemsPerPage);

      if (result.success && result.data) {
        const exclusionData = result.data.data || [];
        const total = result.data.total || 0;

        setExclusions(exclusionData);
        setTotalExclusions(total);
      } else {
        setError(result.error || 'Failed to load self-exclusions');
        setExclusions([]);
      }
    } catch (err) {
      console.error('Error loading self-exclusions:', err);
      setError('An unexpected error occurred');
      setExclusions([]);
    } finally {
      setLoading(false);
    }
  }, [itemsPerPage]);

  // Pagination
  const totalPages = Math.ceil(totalExclusions / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadExclusions(page);
  };

  const handleItemsPerPageChange = (newLimit: number) => {
    setItemsPerPage(newLimit);
    setCurrentPage(1);
    loadExclusions(1);
  };

  // Effects
  useEffect(() => {
    loadExclusions(1);
  }, [loadExclusions]);

  return (
    <div className="space-y-6">
      {/* Self-Exclusion Table */}
      <UnifiedTable
        data={exclusions}
        columns={tableColumns}
        title="Self Exclusion List"
        subtitle={`${totalExclusions} self-exclusions`}
        isLoading={loading}
        error={error}
        onRowClick={handleQuickInfo}
        onRetry={handleRefresh}
        minWidth="1400px"
        emptyState={{
          icon: <UserX className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No self-exclusions found',
          description: 'There are no self-exclusions to display.'
        }}
      />

      {/* Pagination */}
      {totalExclusions > 0 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalExclusions}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        </div>
      )}

      {/* Quick User Info Modal */}
      {showQuickInfo && selectedCustomerId && (
        <QuickUserInfoModal
          isOpen={showQuickInfo}
          onClose={() => {
            setShowQuickInfo(false);
            setSelectedCustomerId(null);
          }}
          customerId={selectedCustomerId}
        />
      )}
    </div>
  );
};

export default SelfExclusionList;
