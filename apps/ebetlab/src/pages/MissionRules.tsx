import React, { useState, useEffect, useCallback } from 'react';
import { Settings, Plus, Edit, Trash2, RefreshCw } from 'lucide-react';
import {
  fetchMissionRules,
  createMissionRule,
  updateMissionRule,
  deleteMissionRule,
  MissionRule,
  CreateMissionRuleRequest,
  UpdateMissionRuleRequest,
  RULE_TYPE_OPTIONS,
  COMPARE_OPERATOR_OPTIONS,
  MISSION_RULES_SORT_FIELDS,
  MissionRulesQueryParams
} from '../utils/api/mission-rules';
import DataTable from '../components/ui/DataTable';
import Modal from '../components/ui/Modal';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import MissionRulesFilter from '../components/mission-rules/MissionRulesFilter';

interface MissionRulesData {
  total: number;
  data: MissionRule[];
}

interface MissionRuleFormData {
  ruleType: string;
  compare: string;
  compareValue: string;
  minDate: string;
  maxDate: string;
}

const MissionRules: React.FC = () => {
  // Data states
  const [missionRulesData, setMissionRulesData] = useState<MissionRulesData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [filters, setFilters] = useState<any>({});
  const [sortState, setSortState] = useState<{ field: string; direction: 'asc' | 'desc' }>({
    field: 'id',
    direction: 'desc'
  });

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedRule, setSelectedRule] = useState<MissionRule | null>(null);

  // Form data
  const [formData, setFormData] = useState<MissionRuleFormData>({
    ruleType: 'joinedAt',
    compare: 'eq',
    compareValue: '',
    minDate: '',
    maxDate: ''
  });

  // Load mission rules
  const loadMissionRules = useCallback(async (
    page: number = currentPage,
    limit: number = itemsPerPage,
    currentFilters: any = filters
  ) => {
    setIsLoading(true);
    setError('');

    try {
      // Build query parameters
      const queryParams: MissionRulesQueryParams = {
        page,
        limit,
        sortBy: sortState.field,
        sortOrder: sortState.direction === 'asc' ? 'ASC' : 'DESC',
        ...currentFilters
      };

      const response = await fetchMissionRules(queryParams);

      if (response.success && response.data) {
        setMissionRulesData({
          total: response.meta.total,
          data: response.data
        });
      } else {
        setError(response.error || 'Failed to load mission rules');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, itemsPerPage, filters, sortState.field, sortState.direction]);

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadMissionRules();
  }, [loadMissionRules]);

  // Event handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadMissionRules(page, itemsPerPage, filters);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    loadMissionRules(1, newItemsPerPage, filters);
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
    setCurrentPage(1);
    loadMissionRules(1, itemsPerPage, newFilters);
  };

  const handleClearFilters = () => {
    setFilters({});
    setCurrentPage(1);
    loadMissionRules(1, itemsPerPage, {});
  };

  const handleSort = (field: string) => {
    setSortState(prevState => ({
      field,
      direction: prevState.field === field && prevState.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (rule: MissionRule) => {
    setSelectedRule(rule);
    setFormData({
      ruleType: rule.ruleType,
      compare: rule.compare,
      compareValue: rule.compareValue,
      minDate: rule.minDate ? new Date(rule.minDate * 1000).toISOString().slice(0, 16) : '',
      maxDate: rule.maxDate ? new Date(rule.maxDate * 1000).toISOString().slice(0, 16) : ''
    });
    setShowEditModal(true);
  };

  // Form handlers
  const resetForm = () => {
    setFormData({
      ruleType: 'joinedAt',
      compare: 'eq',
      compareValue: '',
      minDate: '',
      maxDate: ''
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const parseInputDate = (dateString: string): number | null => {
    if (!dateString) return null;
    return Math.floor(new Date(dateString).getTime() / 1000);
  };

  const handleCreate = async () => {
    try {
      const ruleData: CreateMissionRuleRequest = {
        ruleType: formData.ruleType as any,
        compare: formData.compare as any,
        compareValue: formData.compareValue,
        minDate: parseInputDate(formData.minDate),
        maxDate: parseInputDate(formData.maxDate)
      };

      const response = await createMissionRule(ruleData);

      if (response.success) {
        setShowCreateModal(false);
        resetForm();
        loadMissionRules();
      } else {
        setError(response.error || 'Failed to create mission rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create mission rule');
    }
  };

  const handleUpdate = async () => {
    if (!selectedRule) return;

    try {
      const ruleData: UpdateMissionRuleRequest = {
        ruleType: formData.ruleType as any,
        compare: formData.compare as any,
        compareValue: formData.compareValue,
        minDate: parseInputDate(formData.minDate),
        maxDate: parseInputDate(formData.maxDate)
      };

      const response = await updateMissionRule(selectedRule.id, ruleData);

      if (response.success) {
        setShowEditModal(false);
        setSelectedRule(null);
        resetForm();
        loadMissionRules();
      } else {
        setError(response.error || 'Failed to update mission rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update mission rule');
    }
  };

  const handleDelete = async (rule: MissionRule) => {
    if (!confirm(`Are you sure you want to delete the rule "${rule.ruleType} ${rule.compare} ${rule.compareValue}"?`)) {
      return;
    }

    try {
      const response = await deleteMissionRule(rule.id);

      if (response.success) {
        loadMissionRules();
      } else {
        setError(response.error || 'Failed to delete mission rule');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete mission rule');
    }
  };

  // Table configuration
  const tableColumns = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true
    },
    {
      key: 'ruleType',
      label: 'Rule Type',
      width: '140px',
      sortable: true,
      render: (rule: MissionRule) => (
        <span className="font-medium text-blue-400">
          {RULE_TYPE_OPTIONS.find(opt => opt.value === rule.ruleType)?.label || rule.ruleType}
        </span>
      )
    },
    {
      key: 'compare',
      label: 'Operator',
      width: '100px',
      sortable: true,
      render: (rule: MissionRule) => (
        <span className="font-medium text-purple-400">
          {COMPARE_OPERATOR_OPTIONS.find(opt => opt.value === rule.compare)?.label || rule.compare}
        </span>
      )
    },
    {
      key: 'compareValue',
      label: 'Compare Value',
      width: '120px',
      sortable: true,
      render: (rule: MissionRule) => (
        <span className="font-mono text-green-400">{rule.compareValue}</span>
      )
    },
    {
      key: 'minDate',
      label: 'Min Date',
      width: '140px',
      sortable: true,
      render: (rule: MissionRule) => (
        <span className="text-gray-300">
          {rule.minDate ? new Date(rule.minDate * 1000).toLocaleDateString() : '-'}
        </span>
      )
    },
    {
      key: 'maxDate',
      label: 'Max Date',
      width: '140px',
      sortable: true,
      render: (rule: MissionRule) => (
        <span className="text-gray-300">
          {rule.maxDate ? new Date(rule.maxDate * 1000).toLocaleDateString() : '-'}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      render: (rule: MissionRule) => (
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleRowClick(rule);
            }}
            className="p-1 text-blue-400 hover:text-blue-300"
            title="Edit"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(rule);
            }}
            className="p-1 text-red-400 hover:text-red-300"
            title="Delete"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Settings className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Mission Rules</h1>
            <p className="text-gray-400">Manage rules that determine mission eligibility</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={() => loadMissionRules()}
            variant="outline"
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            Refresh
          </Button>
          <Button
            onClick={() => {
              resetForm();
              setShowCreateModal(true);
            }}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Plus size={16} />
            Create Rule
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* Filters */}
      <MissionRulesFilter
        filters={filters}
        onFiltersChange={handleFilterChange}
        onClearFilters={handleClearFilters}
      />

      {/* Data Table */}
      <DataTable
        data={missionRulesData?.data || []}
        columns={tableColumns}
        total={missionRulesData?.total || 0}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onRowClick={handleRowClick}
        title="Mission Rules"
        emptyState={{
          icon: <Settings className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No mission rules found',
          description: 'Get started by creating your first mission rule.',
          action: (
            <Button
              onClick={() => {
                resetForm();
                setShowCreateModal(true);
              }}
              variant="primary"
            >
              Create Rule
            </Button>
          )
        }}
      />

      {/* Create Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          resetForm();
          setError('');
        }}
        title="Create Mission Rule"
        size="md"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Rule Type</label>
              <select
                value={formData.ruleType}
                onChange={(e) => handleInputChange('ruleType', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {RULE_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Comparison</label>
              <select
                value={formData.compare}
                onChange={(e) => handleInputChange('compare', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {COMPARE_OPERATOR_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <Input
            label="Compare Value"
            value={formData.compareValue}
            onChange={(e) => handleInputChange('compareValue', e.target.value)}
            placeholder="Enter comparison value..."
            required
          />

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Min Date (Optional)"
              type="datetime-local"
              value={formData.minDate}
              onChange={(e) => handleInputChange('minDate', e.target.value)}
            />

            <Input
              label="Max Date (Optional)"
              type="datetime-local"
              value={formData.maxDate}
              onChange={(e) => handleInputChange('maxDate', e.target.value)}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowCreateModal(false);
                resetForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreate}
              variant="primary"
              disabled={!formData.ruleType || !formData.compare || !formData.compareValue}
            >
              Create Rule
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedRule(null);
          resetForm();
          setError('');
        }}
        title={`Edit Mission Rule: ${selectedRule?.ruleType || ''}`}
        size="md"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Rule Type</label>
              <select
                value={formData.ruleType}
                onChange={(e) => handleInputChange('ruleType', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {RULE_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Comparison</label>
              <select
                value={formData.compare}
                onChange={(e) => handleInputChange('compare', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {COMPARE_OPERATOR_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <Input
            label="Compare Value"
            value={formData.compareValue}
            onChange={(e) => handleInputChange('compareValue', e.target.value)}
            placeholder="Enter comparison value..."
            required
          />

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Min Date (Optional)"
              type="datetime-local"
              value={formData.minDate}
              onChange={(e) => handleInputChange('minDate', e.target.value)}
            />

            <Input
              label="Max Date (Optional)"
              type="datetime-local"
              value={formData.maxDate}
              onChange={(e) => handleInputChange('maxDate', e.target.value)}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={() => {
                setShowEditModal(false);
                setSelectedRule(null);
                resetForm();
                setError('');
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdate}
              variant="primary"
              disabled={!formData.ruleType || !formData.compare || !formData.compareValue}
            >
              Update Rule
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default MissionRules;
