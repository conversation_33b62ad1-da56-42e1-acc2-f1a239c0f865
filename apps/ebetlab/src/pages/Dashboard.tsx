import React, { useState, useEffect, useCallback } from 'react';
import StatCard from '../components/dashboard/StatCard';
import Chart from '../components/dashboard/Chart';
import PieChart from '../components/dashboard/PieChart';
import DonutChart from '../components/dashboard/DonutChart';
import HorizontalBarChart from '../components/dashboard/HorizontalBarChart';
import BigWinLoseTable from '../components/dashboard/BigWinLoseTable';
import { CreditCard, TrendingUp, DollarSign, RefreshCw, Activity, Target, UserPlus, LogIn, User, Globe } from 'lucide-react';
import DateRangePicker from '../components/ui/DateRangePicker';
import { useCurrentUser, useWebsiteConfig, useCurrencies } from '../hooks/useAppData';
import {
  fetchTransactionSummary,
  fetchGameWinLose,
  fetchBigWinLose,
  fetchDashboardStats,
  fetchTips,
  type TransactionSummaryItem,
  type BigWinLoseItem,
  type GameWinItem,
  type GameLoseItem,
  type DashboardStatsData
} from '../utils/api';

const Dashboard: React.FC = () => {
  // App context data
  const currentUser = useCurrentUser();
  const websiteConfig = useWebsiteConfig();
  const currencies = useCurrencies();

  const [transactionData, setTransactionData] = useState<TransactionSummaryItem[]>([]);
  const [gameWinLoseData, setGameWinLoseData] = useState<{ win: GameWinItem[], lose: GameLoseItem[] }>({ win: [], lose: [] });
  const [bigWinLoseData, setBigWinLoseData] = useState<{ win: BigWinLoseItem[], lose: BigWinLoseItem[] }>({ win: [], lose: [] });
  const [dashboardStats, setDashboardStats] = useState<DashboardStatsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGameWinLoseLoading, setIsGameWinLoseLoading] = useState(true);
  const [isBigWinLoseLoading, setIsBigWinLoseLoading] = useState(true);
  const [isStatsLoading, setIsStatsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [gameWinLoseError, setGameWinLoseError] = useState<string | null>(null);
  const [bigWinLoseError, setBigWinLoseError] = useState<string | null>(null);



  // Date range state for the new stats endpoint
  const [dateRange, setDateRange] = useState(() => {
    const to = new Date();
    to.setHours(23, 59, 59, 999);
    const from = new Date();
    from.setDate(from.getDate() - 6); // Last 7 days
    from.setHours(0, 0, 0, 0);
    return { from, to };
  });

  // Utility function to round numbers to 2 decimal places
  const roundToTwo = (num: number): number => {
    return Math.round((num + Number.EPSILON) * 100) / 100;
  };

  // Utility function to format currency with 2 decimal places
  const formatCurrency = (num: number): string => {
    return `$${roundToTwo(num).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // Calculate key metrics from dashboard stats (preferred) or fallback to calculated data
  const calculateMetrics = () => {
    if (dashboardStats) {
      // Use data from the proper stats endpoint
      const totalDeposits = parseFloat(dashboardStats.total_deposit);
      const totalWithdrawals = parseFloat(dashboardStats.total_withdraw);
      const totalNet = totalDeposits - totalWithdrawals;

      return {
        totalDeposits: roundToTwo(totalDeposits),
        totalWithdrawals: roundToTwo(totalWithdrawals),
        totalNet: roundToTwo(totalNet),
        totalTransactionCount: dashboardStats.total_deposit_count + dashboardStats.total_withdraw_count,
        playersLoggedIn: dashboardStats.players_logged_in,
        registeredPlayers: dashboardStats.registered_players,
        depositPlayers: dashboardStats.deposit_players,
        withdrawPlayers: dashboardStats.withdraw_players,
        totalVolume: roundToTwo(totalDeposits + totalWithdrawals),
        // Keep some fallback calculations for charts
        totalGames: new Set([...gameWinLoseData.win, ...gameWinLoseData.lose].map(item => item.game_id)).size,
        bigWinTotal: roundToTwo(bigWinLoseData.win.reduce((sum, item) => sum + parseFloat(item.income), 0)),
      };
    } else {
      // Fallback to old calculation method
      const totalDeposits = transactionData.reduce((sum, item) => sum + item.deposit, 0);
      const totalWithdrawals = transactionData.reduce((sum, item) => sum + item.withdraw, 0);
      const totalNet = transactionData.reduce((sum, item) => sum + item.net, 0);
      const totalTransactionCount = transactionData.reduce((sum, item) => sum + item.deposit_count + item.withdraw_count, 0);

      const uniqueCustomers = new Set([...bigWinLoseData.win, ...bigWinLoseData.lose].map(item => item.customer_id));
      const playerCount = uniqueCustomers.size;

      const totalGames = new Set([...gameWinLoseData.win, ...gameWinLoseData.lose].map(item => item.game_id)).size;
      const bigWinTotal = bigWinLoseData.win.reduce((sum, item) => sum + parseFloat(item.income), 0);

      return {
        totalDeposits: roundToTwo(totalDeposits),
        totalWithdrawals: roundToTwo(totalWithdrawals),
        totalNet: roundToTwo(totalNet),
        totalTransactionCount,
        playersLoggedIn: playerCount, // Fallback
        registeredPlayers: 0, // Not available in old data
        depositPlayers: 0, // Not available in old data
        withdrawPlayers: 0, // Not available in old data
        totalVolume: roundToTwo(totalDeposits + totalWithdrawals),
        totalGames,
        bigWinTotal: roundToTwo(bigWinTotal),
      };
    }
  };

  const metrics = calculateMetrics();

  // Helper function to generate blue/aqua colors for pie chart legend
  const getPieChartColor = (index: number, _total: number, type: 'win' | 'lose') => {
    const primaryShades = [
      '#18cffb', '#33dffa', '#66e7fc', '#99effd', '#ccf7fe',
      '#0ca5c8', '#097c96', '#065264'
    ];

    const secondaryShades = [
      '#18cffb', '#33dffa', '#66e7fc', '#99effd', '#ccf7fe',
      '#0ca5c8', '#097c96', '#065264'
    ];

    const colors = type === 'win' ? primaryShades : secondaryShades;
    return colors[index % colors.length];
  };

  const loadTransactionData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetchTransactionSummary('day', -3); // Default timezone
      if (response.success && response.data) {
        setTransactionData(response.data.data);
      } else {
        setError(response.error || 'Failed to load transaction data');
      }
    } catch {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const loadGameWinLoseData = useCallback(async () => {
    setIsGameWinLoseLoading(true);
    setGameWinLoseError(null);

    try {
      const response = await fetchGameWinLose();
      if (response.success && response.data) {
        setGameWinLoseData(response.data.data);
      } else {
        setGameWinLoseError(response.error || 'Failed to load game win/lose data');
      }
    } catch {
      setGameWinLoseError('An unexpected error occurred');
    } finally {
      setIsGameWinLoseLoading(false);
    }
  }, []);

  const loadBigWinLoseData = useCallback(async () => {
    setIsBigWinLoseLoading(true);
    setBigWinLoseError(null);

    try {
      const response = await fetchBigWinLose();
      if (response.success && response.data) {
        setBigWinLoseData(response.data.data);
      } else {
        setBigWinLoseError(response.error || 'Failed to load big win/lose data');
      }
    } catch {
      setBigWinLoseError('An unexpected error occurred');
    } finally {
      setIsBigWinLoseLoading(false);
    }
  }, []);

  const loadDashboardStats = useCallback(async () => {
    setIsStatsLoading(true);

    try {
      // Convert dates to Unix timestamps - only if both dates exist
      if (dateRange.from && dateRange.to) {
        const fromTimestamp = Math.floor(dateRange.from.getTime() / 1000);
        const toTimestamp = Math.floor(dateRange.to.getTime() / 1000);

        const response = await fetchDashboardStats(fromTimestamp, toTimestamp);
        if (response.success && response.data) {
          setDashboardStats(response.data.data);
        }
      }
    } catch {
      // Silently fail - will use fallback metrics
    } finally {
      setIsStatsLoading(false);
    }
  }, [dateRange]);

  // Function to handle tips API ping
  const handleTipsPing = useCallback(async () => {
    console.log('Tips Ping Button clicked - Starting API call...');

    try {
      const response = await fetchTips(1, 20);
      console.log('Tips API Response:', response);

      if (response.success) {
        console.log('Tips API call successful!', response.data);
      } else {
        console.error('Tips API call failed:', response.error);
      }
    } catch (error) {
      console.error('Tips API call error:', error);
    }
  }, []);

  useEffect(() => {
    loadTransactionData();
  }, [loadTransactionData]);

  useEffect(() => {
    loadGameWinLoseData();
  }, [loadGameWinLoseData]);

  useEffect(() => {
    loadBigWinLoseData();
  }, [loadBigWinLoseData]);

  useEffect(() => {
    loadDashboardStats();
  }, [loadDashboardStats]);

  // Prepare chart data from transaction summary
  const chartLabels = transactionData.map(item => {
    const date = new Date(item.date);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  });

  // Chart will calculate the range internally for centering Y-axis at 0

  const chartDatasets = [
    {
      label: 'Deposits',
      data: transactionData.map(item => item.deposit),
      backgroundColor: 'rgba(24, 207, 251, 0.4)', // Primary blue with opacity
      borderColor: '#18cffb', // Primary blue
      borderWidth: 1,
      type: 'bar' as const,
      yAxisID: 'y',
      barThickness: 20, // Tighter bars
      maxBarThickness: 25,
    },
    {
      label: 'Withdrawals',
      data: transactionData.map(item => item.withdraw),
      backgroundColor: 'rgba(10, 195, 198, 0.4)', // Secondary aqua with opacity
      borderColor: '#0ac3c6', // Secondary aqua
      borderWidth: 1,
      type: 'bar' as const,
      yAxisID: 'y',
      barThickness: 20, // Tighter bars
      maxBarThickness: 25,
    },
    {
      label: 'Net',
      data: transactionData.map(item => item.net),
      borderColor: '#66e7fc', // Lighter primary blue for line
      backgroundColor: 'rgba(102, 231, 252, 0.2)', // Light primary blue with opacity
      type: 'line' as const,
      yAxisID: 'y1',
      tension: 0.2,
      borderWidth: 4, // Thicker line
      pointBackgroundColor: '#66e7fc',
      pointBorderColor: '#ffffff',
      pointBorderWidth: 3,
      pointRadius: 6,
      pointHoverRadius: 8,
      fill: false,
    },
  ];

  // Transaction Count Chart Data
  const transactionCountDatasets = [
    {
      label: 'Deposit Count',
      data: transactionData.map(item => item.deposit_count),
      backgroundColor: 'rgba(24, 207, 251, 0.6)',
      borderColor: '#18cffb',
      borderWidth: 2,
      type: 'bar' as const,
    },
    {
      label: 'Withdrawal Count',
      data: transactionData.map(item => item.withdraw_count),
      backgroundColor: 'rgba(10, 195, 198, 0.6)',
      borderColor: '#0ac3c6',
      borderWidth: 2,
      type: 'bar' as const,
    },
  ];

  // Game Performance Chart Data (Top 10 games by total volume)
  const gamePerformanceData = () => {
    const allGames = [...gameWinLoseData.win, ...gameWinLoseData.lose];
    const gameMap = new Map();

    // Combine win and lose data for each game
    allGames.forEach(game => {
      const gameId = game.game_id;
      const gameName = game.game_name;

      if (!gameMap.has(gameId)) {
        gameMap.set(gameId, {
          name: gameName,
          totalWin: 0,
          totalLoss: 0,
        });
      }

      const gameData = gameMap.get(gameId);
      if ('total_win' in game) {
        gameData.totalWin = roundToTwo(parseFloat(game.total_win));
      }
      if ('total_loss' in game) {
        gameData.totalLoss = roundToTwo(parseFloat(game.total_loss));
      }
    });

    // Convert to array and sort by total volume (win + loss)
    const gameArray = Array.from(gameMap.values())
      .map(game => ({
        ...game,
        totalVolume: roundToTwo(game.totalWin + game.totalLoss)
      }))
      .sort((a, b) => b.totalVolume - a.totalVolume)
      .slice(0, 10); // Top 10 games

    return {
      labels: gameArray.map(game => game.name),
      datasets: [
        {
          label: 'Total Wins',
          data: gameArray.map(game => game.totalWin),
          backgroundColor: 'rgba(24, 207, 251, 0.7)',
          borderColor: '#18cffb',
          borderWidth: 2,
        },
        {
          label: 'Total Losses',
          data: gameArray.map(game => game.totalLoss),
          backgroundColor: 'rgba(10, 195, 198, 0.7)',
          borderColor: '#0ac3c6',
          borderWidth: 2,
        },
      ],
    };
  };

  // Big Win/Lose Timeline Data (Last 20 transactions)
  const bigWinLoseTimelineData = () => {
    const allTransactions = [...bigWinLoseData.win, ...bigWinLoseData.lose]
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 20);

    return {
      labels: allTransactions.map(item =>
        new Date(item.timestamp * 1000).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      ),
      datasets: [
        {
          label: 'Transaction Amount (USD)',
          data: allTransactions.map(item => roundToTwo(parseFloat(item.amount_usd))),
          borderColor: '#66e7fc',
          backgroundColor: 'rgba(102, 231, 252, 0.3)',
          borderWidth: 3,
          pointBackgroundColor: '#66e7fc',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6,
          tension: 0.3,
        },
      ],
    };
  };

  // Currency Distribution Data
  const currencyDistributionData = () => {
    const currencyMap = new Map();

    // Count currencies from big win/lose data
    [...bigWinLoseData.win, ...bigWinLoseData.lose].forEach(item => {
      const currency = 'USD'; // Since we don't have currency info in the new structure, default to USD
      const amount = roundToTwo(parseFloat(item.amount));

      if (!currencyMap.has(currency)) {
        currencyMap.set(currency, { count: 0, totalAmount: 0 });
      }

      const currencyData = currencyMap.get(currency);
      currencyData.count += 1;
      currencyData.totalAmount = roundToTwo(currencyData.totalAmount + amount);
    });

    // Convert to array and sort by total amount
    const currencyArray = Array.from(currencyMap.entries())
      .map(([currency, data]) => ({
        label: currency,
        value: roundToTwo(data.totalAmount),
        count: data.count,
      }))
      .sort((a, b) => b.value - a.value);

    const totalAmount = currencyArray.reduce((sum, item) => sum + item.value, 0);

    return {
      data: currencyArray,
      totalAmount,
    };
  };

  // Net Revenue Trend Data (focused view)
  const netRevenueDatasets = [
    {
      label: 'Net Revenue',
      data: transactionData.map(item => item.net),
      borderColor: '#18cffb',
      backgroundColor: 'rgba(24, 207, 251, 0.1)',
      borderWidth: 3,
      pointBackgroundColor: '#18cffb',
      pointBorderColor: '#ffffff',
      pointBorderWidth: 2,
      pointRadius: 5,
      pointHoverRadius: 7,
      tension: 0.4,
      fill: true,
    },
  ];

  // Player Activity Data (based on customer ranks from big win/lose)
  const playerActivityData = () => {
    const rankMap = new Map();

    [...bigWinLoseData.win, ...bigWinLoseData.lose].forEach(item => {
      const rank = item.customer?.rank || 'Unknown';
      const amount = roundToTwo(parseFloat(item.amount_usd));

      if (!rankMap.has(rank)) {
        rankMap.set(rank, { count: 0, totalAmount: 0 });
      }

      const rankData = rankMap.get(rank);
      rankData.count += 1;
      rankData.totalAmount = roundToTwo(rankData.totalAmount + amount);
    });

    // Convert to array and sort by count
    const rankArray = Array.from(rankMap.entries())
      .map(([rank, data]) => ({
        label: rank,
        value: data.count,
        totalAmount: roundToTwo(data.totalAmount),
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 8); // Top 8 ranks

    return {
      data: rankArray,
    };
  };

  // Top Customers Data (based on total transaction volume)
  const topCustomersData = () => {
    const customerMap = new Map();

    [...bigWinLoseData.win, ...bigWinLoseData.lose].forEach(item => {
      const customerId = item.customer_id;
      const customerName = item.customer?.username || item.customer?.masked_username || `Customer ${customerId}`;
      const amount = roundToTwo(parseFloat(item.amount_usd));

      if (!customerMap.has(customerId)) {
        customerMap.set(customerId, {
          name: customerName,
          totalAmount: 0,
          transactionCount: 0,
          rank: item.customer?.rank || 'Unknown'
        });
      }

      const customerData = customerMap.get(customerId);
      customerData.totalAmount = roundToTwo(customerData.totalAmount + amount);
      customerData.transactionCount += 1;
    });

    // Convert to array and sort by total amount
    const customerArray = Array.from(customerMap.values())
      .map(customer => ({
        label: customer.name,
        value: roundToTwo(customer.totalAmount),
        additionalInfo: `${customer.transactionCount} transactions, Rank: ${customer.rank}`
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 customers

    return customerArray;
  };

  return (
    <div className="space-y-6">
      {/* App Context Info */}
      {(currentUser || websiteConfig) && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {currentUser && (
                <div className="flex items-center gap-2">
                  <User className="w-5 h-5 text-primary-500" />
                  <div>
                    <span className="text-sm font-medium text-gray-200">{currentUser.name}</span>
                    <span className="text-xs text-gray-400 ml-2">({currentUser.email})</span>
                  </div>
                </div>
              )}
              {websiteConfig && (
                <div className="flex items-center gap-2">
                  <Globe className="w-5 h-5 text-primary-500" />
                  <span className="text-sm text-gray-200">{websiteConfig.domain}</span>
                </div>
              )}
            </div>
            {currencies.length > 0 && (
              <div className="text-xs text-gray-400">
                {currencies.length} currencies available
              </div>
            )}
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-sm text-gray-400 mt-1">
            Stats: {dateRange.from && dateRange.to
              ? `${dateRange.from.toLocaleDateString()} - ${dateRange.to.toLocaleDateString()}`
              : 'Select date range'} •
            Charts: Last 30 days
          </p>
        </div>
        <div className="flex gap-3">
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
          />
          <button
            className="btn btn-outline flex items-center gap-2"
            onClick={() => {
              loadTransactionData();
              loadGameWinLoseData();
              loadBigWinLoseData();
              loadDashboardStats();
            }}
            disabled={isLoading || isGameWinLoseLoading || isBigWinLoseLoading || isStatsLoading}
          >
            <RefreshCw size={16} className={(isLoading || isGameWinLoseLoading || isBigWinLoseLoading || isStatsLoading) ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>
      
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Players Logged In"
          value={metrics.playersLoggedIn.toLocaleString()}
          icon={<LogIn size={18} className="text-primary-500" />}
        />
        <StatCard
          title="Total Volume"
          value={formatCurrency(metrics.totalVolume)}
          icon={<DollarSign size={18} className="text-secondary-500" />}
        />
        <StatCard
          title="Net Revenue"
          value={formatCurrency(metrics.totalNet)}
          icon={<TrendingUp size={18} className={metrics.totalNet >= 0 ? "text-primary-500" : "text-red-500"} />}
        />
        <StatCard
          title="Total Transactions"
          value={metrics.totalTransactionCount.toLocaleString()}
          icon={<CreditCard size={18} className="text-primary-500" />}
        />
      </div>

      {/* Additional Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Deposits"
          value={formatCurrency(metrics.totalDeposits)}
          icon={<CreditCard size={18} className="text-primary-500" />}
        />
        <StatCard
          title="Total Withdrawals"
          value={formatCurrency(metrics.totalWithdrawals)}
          icon={<Activity size={18} className="text-secondary-500" />}
        />
        <StatCard
          title="Registered Players"
          value={metrics.registeredPlayers.toLocaleString()}
          icon={<UserPlus size={18} className="text-primary-500" />}
        />
        <StatCard
          title="Deposit Players"
          value={metrics.depositPlayers.toLocaleString()}
          icon={<Target size={18} className="text-primary-500" />}
        />
      </div>

      <div className="grid grid-cols-1 gap-6">
        {error ? (
          <div className="admin-card">
            <div className="text-center py-8">
              <div className="text-red-500 mb-2">Error loading transaction data</div>
              <div className="text-gray-400 text-sm">{error}</div>
              <button
                className="btn btn-primary mt-4"
                onClick={loadTransactionData}
              >
                Try Again
              </button>
            </div>
          </div>
        ) : isLoading ? (
          <div className="admin-card">
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
              <div className="text-gray-400">Loading transaction data...</div>
            </div>
          </div>
        ) : transactionData.length > 0 ? (
          <Chart
            type="mixed"
            title="Transaction Summary (Last 30 days)"
            labels={chartLabels}
            datasets={chartDatasets}
            className="admin-card"
          />
        ) : (
          <div className="admin-card">
            <div className="text-center py-8">
              <div className="text-gray-400">No transaction data available</div>
            </div>
          </div>
        )}
      </div>

      {/* Additional Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Transaction Count Chart */}
        {transactionData.length > 0 && (
          <Chart
            type="bar"
            title="Transaction Count Trends (Last 30 days)"
            labels={chartLabels}
            datasets={transactionCountDatasets}
            className="admin-card"
          />
        )}

        {/* Game Performance Chart */}
        {(gameWinLoseData.win.length > 0 || gameWinLoseData.lose.length > 0) && (
          <Chart
            type="bar"
            title="Top 10 Games Performance"
            labels={gamePerformanceData().labels}
            datasets={gamePerformanceData().datasets}
            className="admin-card"
          />
        )}
      </div>

      {/* Big Win/Lose Timeline Chart */}
      {(bigWinLoseData.win.length > 0 || bigWinLoseData.lose.length > 0) && (
        <Chart
          type="line"
          title="Recent Big Win/Lose Timeline"
          labels={bigWinLoseTimelineData().labels}
          datasets={bigWinLoseTimelineData().datasets}
          className="admin-card"
        />
      )}

      {/* Additional Analytics Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Net Revenue Trend */}
        {transactionData.length > 0 && (
          <Chart
            type="line"
            title="Net Revenue Trend (Last 30 days)"
            labels={chartLabels}
            datasets={netRevenueDatasets}
            height="400px"
            className="admin-card"
          />
        )}

        {/* Currency Distribution */}
        {(bigWinLoseData.win.length > 0 || bigWinLoseData.lose.length > 0) && (
          <div className="admin-card">
            <div style={{ height: '400px' }}>
              <DonutChart
                title="Currency Distribution"
                data={currencyDistributionData().data}
                centerText={formatCurrency(currencyDistributionData().totalAmount)}
                centerSubtext="Total Volume"
              />
            </div>
          </div>
        )}

        {/* Player Activity by Rank */}
        {(bigWinLoseData.win.length > 0 || bigWinLoseData.lose.length > 0) && (
          <div className="admin-card">
            <div style={{ height: '400px' }}>
              <DonutChart
                title="Player Activity by Rank"
                data={playerActivityData().data}
                centerText={playerActivityData().data.reduce((sum, item) => sum + item.value, 0).toString()}
                centerSubtext="Total Transactions"
              />
            </div>
          </div>
        )}
      </div>

      {/* Top Customers Section */}
      {(bigWinLoseData.win.length > 0 || bigWinLoseData.lose.length > 0) && (
        <HorizontalBarChart
          title="Top Customers by Transaction Volume"
          data={topCustomersData()}
          valueLabel="Total Volume (USD)"
          maxItems={10}
        />
      )}

      {/* Game Win/Lose Pie Charts Section */}
      <div className="admin-card">
        <h3 className="text-lg font-semibold mb-6">Winning / Losing</h3>
        {gameWinLoseError ? (
          <div className="text-center py-8">
            <div className="text-red-500 mb-2">Error loading game win/lose data</div>
            <div className="text-gray-400 text-sm">{gameWinLoseError}</div>
            <button
              className="btn btn-primary mt-4"
              onClick={loadGameWinLoseData}
            >
              Try Again
            </button>
          </div>
        ) : isGameWinLoseLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
            <div className="text-gray-400">Loading game win/lose data...</div>
          </div>
        ) : (
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Pie Charts Container */}
            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Game Wins Pie Chart */}
              <div className="h-[400px]">
                <PieChart
                  title="Game Wins"
                  data={[...gameWinLoseData.win]
                    .sort((a, b) => parseFloat(b.total_win) - parseFloat(a.total_win))
                    .map(item => ({
                      game_name: item.game_name,
                      amount_usd: item.total_win
                    }))}
                  type="win"
                  showLegend={true}
                />
              </div>

              {/* Game Losses Pie Chart */}
              <div className="h-[400px]">
                <PieChart
                  title="Game Losses"
                  data={[...gameWinLoseData.lose]
                    .sort((a, b) => parseFloat(b.total_loss) - parseFloat(a.total_loss))
                    .map(item => ({
                      game_name: item.game_name,
                      amount_usd: item.total_loss
                    }))}
                  type="lose"
                  showLegend={true}
                />
              </div>
            </div>

            {/* Legend Table */}
            <div className="w-full lg:w-80 bg-dark-800 rounded-lg p-4">
              <h4 className="text-md font-semibold mb-4">Legend</h4>
              <div className="space-y-4 max-h-[400px] overflow-y-auto">
                {/* Wins Section */}
                {gameWinLoseData.win.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium text-primary-400 mb-2">Game Wins</h5>
                    <div className="space-y-2">
                      {[...gameWinLoseData.win]
                        .sort((a, b) => parseFloat(b.total_win) - parseFloat(a.total_win))
                        .map((item, index) => (
                          <div key={`win-${item.game_id}`} className="flex items-center justify-between text-sm">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{
                                  backgroundColor: getPieChartColor(index, gameWinLoseData.win.length, 'win')
                                }}
                              ></div>
                              <span className="text-primary-300 truncate">{item.game_name}</span>
                            </div>
                            <span className="text-primary-400 font-medium ml-2">${item.total_win}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                {/* Losses Section */}
                {gameWinLoseData.lose.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium text-secondary-400 mb-2">Game Losses</h5>
                    <div className="space-y-2">
                      {[...gameWinLoseData.lose]
                        .sort((a, b) => parseFloat(b.total_loss) - parseFloat(a.total_loss))
                        .map((item, index) => (
                          <div key={`lose-${item.game_id}`} className="flex items-center justify-between text-sm">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{
                                  backgroundColor: getPieChartColor(index, gameWinLoseData.lose.length, 'lose')
                                }}
                              ></div>
                              <span className="text-secondary-300 truncate">{item.game_name}</span>
                            </div>
                            <span className="text-secondary-400 font-medium ml-2">${item.total_loss}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Big Win/Lose Tables Section */}
      <BigWinLoseTable
        winData={bigWinLoseData.win}
        loseData={bigWinLoseData.lose}
        isLoading={isBigWinLoseLoading}
        error={bigWinLoseError}
        onRetry={loadBigWinLoseData}
      />
    </div>
  );
};

export default Dashboard;
