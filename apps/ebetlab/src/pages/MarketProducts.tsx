import React, { useState, useEffect, useCallback } from 'react';
import {
  ShoppingCart, Plus, RefreshCw, AlertCircle, Edit, Trash2, Eye, Search,
  Filter, X, Download, Upload, Settings, DollarSign, Package, Tag,
  Image, Globe, Users, TrendingUp, Clock, CheckCircle, XCircle, AlertTriangle
} from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import Modal from '../components/ui/Modal';
import Input from '../components/ui/Input';
import MarketProductForm from '../components/market-products/MarketProductForm';
import MarketProductDetailsModal from '../components/market-products/MarketProductDetailsModal';
import MarketProductsErrorBoundary from '../components/market-products/MarketProductsErrorBoundary';
import {
  fetchMarketProducts,
  createMarketProduct,
  updateMarketProduct,
  deleteMarketProduct,
  getProductTypeDisplay,
  getProductCategoryDisplay,
  formatPrice,
  getAvailabilityDisplay,
  getAvailabilityStatus,
  PRODUCT_TYPE_OPTIONS,
  PRODUCT_CATEGORY_OPTIONS,
  MARKET_PRODUCTS_SORT_FIELDS,
  type MarketProduct,
  type CreateMarketProductRequest,
  type UpdateMarketProductRequest,
  type MarketProductsQueryParams,
  type ProductType,
  type ProductCategory
} from '../utils/api/market-products';

interface MarketProductsData {
  total: number;
  data: MarketProduct[];
}

const MarketProducts: React.FC = () => {
  // State management
  const [data, setData] = useState<MarketProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [sortState, setSortState] = useState<SortState>({ field: 'createdAt', direction: 'desc' });
  
  // Filter states (for UI inputs)
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<ProductType | ''>('');
  const [categoryFilter, setCategoryFilter] = useState<ProductCategory | ''>('');
  const [minPrice, setMinPrice] = useState<string>('');
  const [maxPrice, setMaxPrice] = useState<string>('');
  const [minAmount, setMinAmount] = useState<string>('');
  const [maxAmount, setMaxAmount] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);

  // Applied filters (for API calls)
  const [appliedFilters, setAppliedFilters] = useState({
    search: '',
    type: '' as ProductType | '',
    category: '' as ProductCategory | '',
    minPrice: '',
    maxPrice: '',
    minAmount: '',
    maxAmount: ''
  });
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<MarketProduct | null>(null);

  // Bulk operations
  const [selectedProducts, setSelectedProducts] = useState<Set<number>>(new Set());
  const [bulkOperationLoading, setBulkOperationLoading] = useState(false);
  
  // Form states are managed internally by the form components

  // Load data function
  const loadData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const queryParams: MarketProductsQueryParams = {
        page: currentPage,
        limit: itemsPerPage,
        sortBy: sortState.field,
        sortOrder: sortState.direction.toUpperCase() as 'ASC' | 'DESC'
      };
      
      // Add filters
      if (appliedFilters.search) queryParams.search = appliedFilters.search;
      if (appliedFilters.type) queryParams.type = appliedFilters.type;
      if (appliedFilters.category) queryParams.category = appliedFilters.category;
      if (appliedFilters.minPrice) queryParams.minPrice = parseFloat(appliedFilters.minPrice);
      if (appliedFilters.maxPrice) queryParams.maxPrice = parseFloat(appliedFilters.maxPrice);
      if (appliedFilters.minAmount) queryParams.minAmount = parseInt(appliedFilters.minAmount);
      if (appliedFilters.maxAmount) queryParams.maxAmount = parseInt(appliedFilters.maxAmount);
      
      const result = await fetchMarketProducts(queryParams);
      
      if (result.success) {
        setData(result.data);
        setTotalItems(result.meta.total);
        setTotalPages(result.meta.totalPages);
      } else {
        setError(result.error || 'Failed to load market products');
        setData([]);
        setTotalItems(0);
        setTotalPages(0);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setData([]);
      setTotalItems(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, itemsPerPage, sortState, appliedFilters]);

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + R for refresh
      if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        loadData();
      }

      // Ctrl/Cmd + F for filters
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        setShowFilters(!showFilters);
      }

      // Escape to close modals
      if (e.key === 'Escape') {
        setShowCreateModal(false);
        setShowEditModal(false);
        setShowDeleteModal(false);
        setShowDetailsModal(false);
        setShowBulkDeleteModal(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showFilters]);

  // Recently viewed products (localStorage)
  const addToRecentlyViewed = (product: MarketProduct) => {
    try {
      const recent = JSON.parse(localStorage.getItem('recentlyViewedProducts') || '[]');
      const filtered = recent.filter((p: any) => p.id !== product.id);
      const updated = [{ id: product.id, name: product.name, viewedAt: Date.now() }, ...filtered].slice(0, 5);
      localStorage.setItem('recentlyViewedProducts', JSON.stringify(updated));
    } catch (error) {
      console.error('Failed to save recently viewed product:', error);
    }
  };

  const getRecentlyViewed = (): Array<{ id: number; name: string; viewedAt: number }> => {
    try {
      return JSON.parse(localStorage.getItem('recentlyViewedProducts') || '[]');
    } catch (error) {
      return [];
    }
  };

  // Handle create product
  const handleCreate = async (formData: CreateMarketProductRequest) => {
    try {
      const result = await createMarketProduct(formData);

      if (result.success) {
        setShowCreateModal(false);
        loadData();
      } else {
        setError(result.error || 'Failed to create product');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create product');
    }
  };

  // Handle edit product
  const handleEdit = async (formData: UpdateMarketProductRequest) => {
    if (!selectedProduct) return;

    try {
      const result = await updateMarketProduct(selectedProduct.id, formData);

      if (result.success) {
        setShowEditModal(false);
        setSelectedProduct(null);
        loadData();
      } else {
        setError(result.error || 'Failed to update product');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update product');
    }
  };

  // Handle delete product
  const handleDelete = async () => {
    if (!selectedProduct) return;
    
    try {
      const result = await deleteMarketProduct(selectedProduct.id);
      
      if (result.success) {
        setShowDeleteModal(false);
        setSelectedProduct(null);
        loadData();
      } else {
        setError(result.error || 'Failed to delete product');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete product');
    }
  };

  // Open modals
  const openEditModal = (product: MarketProduct) => {
    setSelectedProduct(product);
    setShowEditModal(true);
  };

  const openDeleteModal = (product: MarketProduct) => {
    setSelectedProduct(product);
    setShowDeleteModal(true);
  };

  const openDetailsModal = (product: MarketProduct) => {
    setSelectedProduct(product);
    setShowDetailsModal(true);
    addToRecentlyViewed(product);
  };

  // Apply filters
  const applyFilters = () => {
    setAppliedFilters({
      search: searchTerm,
      type: typeFilter,
      category: categoryFilter,
      minPrice,
      maxPrice,
      minAmount,
      maxAmount
    });
    setCurrentPage(1);
  };

  // Clear filters
  const clearFilters = () => {
    setSearchTerm('');
    setTypeFilter('');
    setCategoryFilter('');
    setMinPrice('');
    setMaxPrice('');
    setMinAmount('');
    setMaxAmount('');
    setAppliedFilters({
      search: '',
      type: '',
      category: '',
      minPrice: '',
      maxPrice: '',
      minAmount: '',
      maxAmount: ''
    });
    setCurrentPage(1);
  };

  // Bulk operations
  const toggleProductSelection = (productId: number) => {
    setSelectedProducts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(productId)) {
        newSet.delete(productId);
      } else {
        newSet.add(productId);
      }
      return newSet;
    });
  };

  const toggleSelectAll = () => {
    if (selectedProducts.size === data.length) {
      setSelectedProducts(new Set());
    } else {
      setSelectedProducts(new Set(data.map(product => product.id)));
    }
  };

  const handleBulkDelete = async () => {
    setBulkOperationLoading(true);
    try {
      const deletePromises = Array.from(selectedProducts).map(id => deleteMarketProduct(id));
      const results = await Promise.all(deletePromises);

      const failedDeletes = results.filter(result => !result.success);
      if (failedDeletes.length > 0) {
        setError(`Failed to delete ${failedDeletes.length} products`);
      }

      setShowBulkDeleteModal(false);
      setSelectedProducts(new Set());
      loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete products');
    } finally {
      setBulkOperationLoading(false);
    }
  };

  // Export functionality
  const exportToJSON = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      totalProducts: totalItems,
      filters: appliedFilters,
      products: data
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `market-products-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportToCSV = () => {
    const headers = [
      'ID', 'Name', 'Slug', 'Type', 'Category', 'Price', 'Available Amount',
      'Multi Purchase', 'Created At', 'Updated At'
    ];

    const csvData = data.map(product => [
      product.id,
      product.name,
      product.slug,
      product.type,
      product.category,
      product.price,
      product.availableAmount || 'Unlimited',
      product.isMultiPerBuyer ? 'Yes' : 'No',
      new Date(product.createdAt).toLocaleString(),
      new Date(product.updatedAt).toLocaleString()
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `market-products-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Get availability badge color
  const getAvailabilityBadgeColor = (status: string) => {
    switch (status) {
      case 'unlimited': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'available': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'low': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'out': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Table columns definition
  const columns: TableColumn<MarketProduct>[] = [
    {
      key: 'select',
      label: (
        <input
          type="checkbox"
          checked={selectedProducts.size === data.length && data.length > 0}
          onChange={toggleSelectAll}
          className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500"
        />
      ),
      width: '50px',
      render: (product: MarketProduct) => (
        <input
          type="checkbox"
          checked={selectedProducts.has(product.id)}
          onChange={() => toggleProductSelection(product.id)}
          className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500"
        />
      )
    },
    {
      key: 'image',
      label: 'Image',
      width: '80px',
      render: (product: MarketProduct) => (
        <div className="w-12 h-12 rounded-lg overflow-hidden bg-dark-700 flex items-center justify-center">
          {product.photoUrl ? (
            <img 
              src={product.photoUrl} 
              alt={product.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <Image className="w-6 h-6 text-gray-500" />
        </div>
      )
    },
    {
      key: 'name',
      label: 'Product Name',
      width: '200px',
      render: (product: MarketProduct) => (
        <div>
          <div className="font-medium text-gray-200">{product.name}</div>
          <div className="text-sm text-gray-400">{product.slug}</div>
        </div>
      )
    },
    {
      key: 'type',
      label: 'Type',
      width: '100px',
      render: (product: MarketProduct) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${
          product.type === 'slots' 
            ? 'bg-purple-500/20 text-purple-400 border-purple-500/30'
            : 'bg-blue-500/20 text-blue-400 border-blue-500/30'
        }`}>
          {getProductTypeDisplay(product.type)}
        </span>
      )
    },
    {
      key: 'category',
      label: 'Category',
      width: '120px',
      render: (product: MarketProduct) => (
        <span className="text-gray-300">
          {getProductCategoryDisplay(product.category)}
        </span>
      )
    },
    {
      key: 'price',
      label: 'Price',
      width: '100px',
      render: (product: MarketProduct) => (
        <div className="font-medium text-green-400">
          {formatPrice(product.price)}
        </div>
      )
    },
    {
      key: 'availability',
      label: 'Availability',
      width: '120px',
      render: (product: MarketProduct) => {
        const status = getAvailabilityStatus(product.availableAmount);
        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getAvailabilityBadgeColor(status)}`}>
            {getAvailabilityDisplay(product.availableAmount)}
          </span>
        );
      }
    },
    {
      key: 'multiPurchase',
      label: 'Multi Purchase',
      width: '120px',
      render: (product: MarketProduct) => (
        <div className="flex items-center">
          {product.isMultiPerBuyer ? (
            <CheckCircle className="w-4 h-4 text-green-400" />
          ) : (
            <XCircle className="w-4 h-4 text-red-400" />
          )}
          <span className="ml-2 text-sm text-gray-300">
            {product.isMultiPerBuyer ? 'Yes' : 'No'}
          </span>
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'Created',
      width: '140px',
      render: (product: MarketProduct) => (
        <span className="text-gray-300 text-sm">
          {new Date(product.createdAt).toLocaleDateString()}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '120px',
      render: (product: MarketProduct) => (
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              openDetailsModal(product);
            }}
            className="p-1 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              openEditModal(product);
            }}
            className="p-1 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400/10 rounded"
            title="Edit"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              openDeleteModal(product);
            }}
            className="p-1 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded"
            title="Delete"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  return (
    <MarketProductsErrorBoundary>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white flex items-center gap-3">
            <ShoppingCart className="w-8 h-8 text-primary-500" />
            Market Products
          </h1>
          <p className="text-gray-400 mt-1">
            Manage bonuses available for purchase in the marketplace
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* Bulk Operations */}
          {selectedProducts.size > 0 && (
            <div className="flex items-center gap-2 px-3 py-2 bg-primary-500/10 border border-primary-500/20 rounded-lg">
              <span className="text-sm text-primary-400">
                {selectedProducts.size} selected
              </span>
              <Button
                onClick={() => setShowBulkDeleteModal(true)}
                variant="danger"
                size="sm"
                className="flex items-center gap-1"
              >
                <Trash2 size={14} />
                Delete
              </Button>
            </div>
          )}

          {/* Export Options */}
          <div className="relative group">
            <Button
              variant="secondary"
              className="flex items-center gap-2"
            >
              <Download size={16} />
              Export
            </Button>
            <div className="absolute right-0 top-full mt-1 bg-dark-800 border border-dark-600 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
              <div className="p-2 space-y-1 min-w-[120px]">
                <button
                  onClick={exportToJSON}
                  className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:bg-dark-700 rounded"
                >
                  Export JSON
                </button>
                <button
                  onClick={exportToCSV}
                  className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:bg-dark-700 rounded"
                >
                  Export CSV
                </button>
              </div>
            </div>
          </div>

          <Button
            onClick={() => setShowFilters(!showFilters)}
            variant="secondary"
            className="flex items-center gap-2"
          >
            <Filter size={16} />
            Filters
            {(appliedFilters.search || appliedFilters.type || appliedFilters.category || appliedFilters.minPrice || appliedFilters.maxPrice || appliedFilters.minAmount || appliedFilters.maxAmount) && (
              <span className="bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                !
              </span>
            )}
          </Button>
          <Button
            onClick={loadData}
            variant="secondary"
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </Button>
          <Button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center gap-2"
          >
            <Plus size={16} />
            Create Product
          </Button>
        </div>
      </div>

      {/* Tutorial Box */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Package className="w-5 h-5 text-blue-400 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-400 mb-1">Market Products Overview</h3>
            <p className="text-sm text-gray-300">
              Market products are bonuses that users can purchase using their points.
              Products can be general items or slot-related bonuses like free spins, cash rewards, reloads, or scatter bonuses.
              Configure pricing, availability, and provider restrictions for each product.
            </p>
            <div className="mt-3 text-xs text-gray-400">
              <strong>Keyboard Shortcuts:</strong> Ctrl+R (Refresh), Ctrl+F (Filters), Esc (Close modals)
            </div>
          </div>
        </div>
      </div>

      {/* Recently Viewed Products */}
      {getRecentlyViewed().length > 0 && (
        <div className="bg-dark-800 border border-dark-600 rounded-lg p-4">
          <h3 className="font-medium text-gray-200 mb-3 flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Recently Viewed Products
          </h3>
          <div className="flex flex-wrap gap-2">
            {getRecentlyViewed().map(recent => {
              const product = data.find(p => p.id === recent.id);
              return (
                <button
                  key={recent.id}
                  onClick={() => {
                    if (product) {
                      openDetailsModal(product);
                    }
                  }}
                  className="px-3 py-2 bg-dark-700 hover:bg-dark-600 border border-dark-600 rounded-lg text-sm text-gray-300 transition-colors"
                  disabled={!product}
                >
                  {recent.name}
                  {!product && (
                    <span className="ml-2 text-xs text-gray-500">(not in current view)</span>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Filters Panel */}
      {showFilters && (
        <div className="bg-dark-800 border border-dark-600 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-200">Filters</h3>
            <button
              onClick={() => setShowFilters(false)}
              className="p-1 text-gray-400 hover:text-gray-300"
            >
              <X size={16} />
            </button>
          </div>

          <div className="space-y-6">
            {/* First Row - Basic Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Search */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Search
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search products..."
                    className="w-full pl-10 pr-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>

              {/* Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Product Type
                </label>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value as ProductType | '')}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Types</option>
                  {PRODUCT_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Category
                </label>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value as ProductCategory | '')}
                  className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Categories</option>
                  {PRODUCT_CATEGORY_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Second Row - Range Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Price Range
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    value={minPrice}
                    onChange={(e) => setMinPrice(e.target.value)}
                    placeholder="Min price"
                    min="0"
                    step="0.01"
                    className="flex-1 px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500"
                  />
                  <span className="text-gray-400 text-sm">to</span>
                  <input
                    type="number"
                    value={maxPrice}
                    onChange={(e) => setMaxPrice(e.target.value)}
                    placeholder="Max price"
                    min="0"
                    step="0.01"
                    className="flex-1 px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500"
                  />
                </div>
              </div>

              {/* Amount Range */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Available Amount Range
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    value={minAmount}
                    onChange={(e) => setMinAmount(e.target.value)}
                    placeholder="Min amount"
                    min="0"
                    className="flex-1 px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500"
                  />
                  <span className="text-gray-400 text-sm">to</span>
                  <input
                    type="number"
                    value={maxAmount}
                    onChange={(e) => setMaxAmount(e.target.value)}
                    placeholder="Max amount"
                    min="0"
                    className="flex-1 px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500"
                  />
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  Leave empty to include unlimited products. Use 0 for out-of-stock products.
                </p>
              </div>
            </div>

            {/* Filter Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-dark-600">
              <Button
                onClick={clearFilters}
                variant="secondary"
                size="sm"
                className="flex items-center gap-2"
              >
                <X size={16} />
                Clear All
              </Button>
              <Button
                onClick={applyFilters}
                className="flex items-center gap-2"
              >
                <Filter size={16} />
                Apply Filters
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <div>
              <h3 className="font-medium text-red-400">Error</h3>
              <p className="text-sm text-gray-300 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={data}
        columns={columns}
        loading={loading}
        error={error}
        onRetry={loadData}
        sortState={sortState}
        onSortChange={setSortState}
        pagination={{
          currentPage,
          totalPages,
          totalItems,
          itemsPerPage,
          onPageChange: setCurrentPage,
          onItemsPerPageChange: setItemsPerPage
        }}
        emptyState={{
          icon: <ShoppingCart className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No market products found',
          description: appliedFilters.search || appliedFilters.type || appliedFilters.category || appliedFilters.minPrice || appliedFilters.maxPrice || appliedFilters.minAmount || appliedFilters.maxAmount
            ? 'No products match your current filters. Try adjusting your search criteria.'
            : 'Create your first market product to get started.',
          action: (
            <Button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center gap-2"
            >
              <Plus size={16} />
              Create Product
            </Button>
          )
        }}
      />

      {/* Create Product Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create New Market Product"
        size="xl"
      >
        <MarketProductForm
          onSubmit={handleCreate}
          onCancel={() => setShowCreateModal(false)}
          loading={loading}
        />
      </Modal>

      {/* Edit Product Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Market Product"
        size="xl"
      >
        {selectedProduct && (
          <MarketProductForm
            isEdit
            initialData={selectedProduct}
            onSubmit={handleEdit}
            onCancel={() => setShowEditModal(false)}
            loading={loading}
          />
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Market Product"
      >
        {selectedProduct && (
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-6 h-6 text-red-400 mt-1" />
              <div>
                <h3 className="font-medium text-gray-200 mb-2">
                  Are you sure you want to delete this product?
                </h3>
                <p className="text-gray-400 text-sm mb-4">
                  This action cannot be undone. The product "{selectedProduct.name}" will be permanently removed from the marketplace.
                </p>
                <div className="bg-dark-800 rounded-lg p-3">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-dark-700 flex items-center justify-center">
                      {selectedProduct.photoUrl ? (
                        <img
                          src={selectedProduct.photoUrl}
                          alt={selectedProduct.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Image className="w-6 h-6 text-gray-500" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-gray-200">{selectedProduct.name}</div>
                      <div className="text-sm text-gray-400">{formatPrice(selectedProduct.price)}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end gap-3 pt-4 border-t border-dark-600">
              <Button
                variant="secondary"
                onClick={() => setShowDeleteModal(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDelete}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Trash2 size={16} />
                {loading ? 'Deleting...' : 'Delete Product'}
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* Bulk Delete Confirmation Modal */}
      <Modal
        isOpen={showBulkDeleteModal}
        onClose={() => setShowBulkDeleteModal(false)}
        title="Bulk Delete Products"
      >
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-6 h-6 text-red-400 mt-1" />
            <div>
              <h3 className="font-medium text-gray-200 mb-2">
                Are you sure you want to delete {selectedProducts.size} products?
              </h3>
              <p className="text-gray-400 text-sm mb-4">
                This action cannot be undone. All selected products will be permanently removed from the marketplace.
              </p>
              <div className="bg-dark-800 rounded-lg p-3 max-h-32 overflow-y-auto">
                <div className="text-sm text-gray-300 space-y-1">
                  {data
                    .filter(product => selectedProducts.has(product.id))
                    .map(product => (
                      <div key={product.id} className="flex items-center gap-2">
                        <span className="text-gray-500">#{product.id}</span>
                        <span>{product.name}</span>
                        <span className="text-gray-500">-</span>
                        <span className="text-green-400">{formatPrice(product.price)}</span>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              variant="secondary"
              onClick={() => setShowBulkDeleteModal(false)}
              disabled={bulkOperationLoading}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleBulkDelete}
              disabled={bulkOperationLoading}
              className="flex items-center gap-2"
            >
              <Trash2 size={16} />
              {bulkOperationLoading ? 'Deleting...' : `Delete ${selectedProducts.size} Products`}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Product Details Modal */}
      <MarketProductDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        product={selectedProduct}
      />
      </div>
    </MarketProductsErrorBoundary>
  );
};

export default MarketProducts;
