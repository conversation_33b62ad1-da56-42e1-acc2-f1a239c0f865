import React, { useState, useEffect, useCallback } from 'react';
import {
  ShoppingCart, RefreshCw, AlertCircle, Eye, Search, Filter, X, Download,
  CheckCircle, XCircle, RotateCcw, Trash2, Clock, Coins, User,
  Package, Calendar, TrendingUp, AlertTriangle, FileText, ChevronDown, ChevronRight
} from 'lucide-react';
import Button from '../components/ui/Button';
import DataTable, { type TableColumn, type FilterGroup, type SortState } from '../components/ui/DataTable';
import Modal from '../components/ui/Modal';
import Input from '../components/ui/Input';
import Select from '../components/ui/Select';
import DateRangePicker from '../components/ui/DateRangePicker';
import {
  fetchMarketProductRequests,
  rejectMarketProductRequest,
  completeMarketProductRequest,
  refundMarketProductRequest,
  deleteMarketProductRequest,
  getStatusDisplay,
  getStatusColor,
  getProductTypeDisplay,
  getProductCategoryDisplay,
  formatPrice,
  formatTimestamp,
  canReject,
  canComplete,
  canRefund,
  MARKET_PRODUCT_REQUEST_STATUS_OPTIONS,
  PRODUCT_TYPE_OPTIONS,
  PRODUCT_CATEGORY_OPTIONS,
  MARKET_PRODUCT_REQUESTS_SORT_FIELDS,
  type MarketProductRequest,
  type MarketProductRequestsQueryParams,
  type RejectMarketProductRequestRequest,
  type RefundMarketProductRequestRequest
} from '../utils/api/market-product-requests';

interface MarketProductRequestsData {
  total: number;
  data: MarketProductRequest[];
}

// Truncated display component for large content
interface TruncatedDisplayProps {
  items: string[];
  maxItems?: number;
  itemLabel?: string;
}

const TruncatedDisplay: React.FC<TruncatedDisplayProps> = ({
  items,
  maxItems = 3,
  itemLabel = 'items'
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (items.length === 0) {
    return <span className="text-gray-500">-</span>;
  }

  const displayItems = isExpanded ? items : items.slice(0, maxItems);
  const hasMore = items.length > maxItems;

  return (
    <div className="text-sm text-gray-300">
      <div className="space-y-1">
        {displayItems.map((item, index) => (
          <div key={index} className="text-xs">
            {item}
          </div>
        ))}
      </div>
      {hasMore && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            setIsExpanded(!isExpanded);
          }}
          className="flex items-center gap-1 text-xs text-primary-400 hover:text-primary-300 mt-1"
        >
          <ChevronRight
            size={12}
            className={`transition-transform ${isExpanded ? 'rotate-90' : ''}`}
          />
          {isExpanded
            ? 'Show less'
            : `+${items.length - maxItems} more ${itemLabel}`
          }
        </button>
      )}
    </div>
  );
};

const MissionsMarketRequests: React.FC = () => {
  // State management
  const [data, setData] = useState<MarketProductRequestsData>({ total: 0, data: [] });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [sortState, setSortState] = useState<SortState>({
    field: 'createdAt',
    direction: 'desc'
  });

  // Filter states
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [productTypeFilter, setProductTypeFilter] = useState<string>('');
  const [productCategoryFilter, setProductCategoryFilter] = useState<string>('');
  const [currencyFilter, setCurrencyFilter] = useState<string>('');
  const [userIdFilter, setUserIdFilter] = useState<string>('');
  const [productIdFilter, setProductIdFilter] = useState<string>('');
  const [minPriceFilter, setMinPriceFilter] = useState<string>('');
  const [maxPriceFilter, setMaxPriceFilter] = useState<string>('');
  const [hasRejectReasonFilter, setHasRejectReasonFilter] = useState<string>('');
  const [createdDateRange, setCreatedDateRange] = useState<{ from: Date | null; to: Date | null }>({
    from: null,
    to: null
  });
  const [updatedDateRange, setUpdatedDateRange] = useState<{ from: Date | null; to: Date | null }>({
    from: null,
    to: null
  });

  // Applied filters (for API requests)
  const [appliedFilters, setAppliedFilters] = useState({
    searchTerm: '',
    statusFilter: '',
    productTypeFilter: '',
    productCategoryFilter: '',
    currencyFilter: '',
    userIdFilter: '',
    productIdFilter: '',
    minPriceFilter: '',
    maxPriceFilter: '',
    hasRejectReasonFilter: '',
    createdDateRange: { from: null as Date | null, to: null as Date | null },
    updatedDateRange: { from: null as Date | null, to: null as Date | null }
  });

  // Modal states
  const [selectedRequest, setSelectedRequest] = useState<MarketProductRequest | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [shouldRefundPoints, setShouldRefundPoints] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  // Bulk operations state
  const [selectedRequests, setSelectedRequests] = useState<Set<number>>(new Set());
  const [showBulkRejectModal, setShowBulkRejectModal] = useState(false);
  const [showBulkCompleteModal, setShowBulkCompleteModal] = useState(false);
  const [bulkRejectReason, setBulkRejectReason] = useState('');
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  // Statistics state
  const [statistics, setStatistics] = useState({
    total: 0,
    pending: 0,
    completed: 0,
    rejected: 0,
    refunded: 0,
    totalValue: 0
  });

  // Build query parameters
  const buildQueryParams = useCallback((): MarketProductRequestsQueryParams => {
    const params: MarketProductRequestsQueryParams = {
      page: currentPage,
      limit: itemsPerPage,
      sortBy: sortState.field as any,
      sortOrder: sortState.direction === 'asc' ? 'ASC' : 'DESC'
    };

    if (appliedFilters.searchTerm.trim()) params.search = appliedFilters.searchTerm.trim();
    if (appliedFilters.statusFilter) params.status = appliedFilters.statusFilter as any;
    if (appliedFilters.productTypeFilter) params.productType = appliedFilters.productTypeFilter as any;
    if (appliedFilters.productCategoryFilter) params.productCategory = appliedFilters.productCategoryFilter as any;
    if (appliedFilters.currencyFilter) params.currency = appliedFilters.currencyFilter;
    if (appliedFilters.userIdFilter && !isNaN(Number(appliedFilters.userIdFilter))) params.userId = Number(appliedFilters.userIdFilter);
    if (appliedFilters.productIdFilter && !isNaN(Number(appliedFilters.productIdFilter))) params.productId = Number(appliedFilters.productIdFilter);
    if (appliedFilters.minPriceFilter && !isNaN(Number(appliedFilters.minPriceFilter))) params.minPrice = Number(appliedFilters.minPriceFilter);
    if (appliedFilters.maxPriceFilter && !isNaN(Number(appliedFilters.maxPriceFilter))) params.maxPrice = Number(appliedFilters.maxPriceFilter);
    if (appliedFilters.hasRejectReasonFilter) params.hasRejectReason = appliedFilters.hasRejectReasonFilter === 'true';
    if (appliedFilters.createdDateRange.from) params.createdFrom = appliedFilters.createdDateRange.from.toISOString();
    if (appliedFilters.createdDateRange.to) params.createdTo = appliedFilters.createdDateRange.to.toISOString();
    if (appliedFilters.updatedDateRange.from) params.updatedFrom = appliedFilters.updatedDateRange.from.toISOString();
    if (appliedFilters.updatedDateRange.to) params.updatedTo = appliedFilters.updatedDateRange.to.toISOString();

    return params;
  }, [
    currentPage, itemsPerPage, sortState, appliedFilters
  ]);

  // Load data
  const loadData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const queryParams = buildQueryParams();
      const response = await fetchMarketProductRequests(queryParams);

      if (response.success && response.data) {
        setData({
          total: response.meta.total,
          data: response.data
        });

        // Calculate statistics
        const stats = response.data.reduce((acc, request) => {
          acc.total++;
          acc[request.status as keyof typeof acc]++;
          acc.totalValue += request.product.price;
          return acc;
        }, {
          total: 0,
          pending: 0,
          completed: 0,
          rejected: 0,
          refunded: 0,
          totalValue: 0
        });

        setStatistics(stats);
      } else {
        throw new Error(response.error || 'Failed to load market product requests');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Error loading market product requests:', err);
    } finally {
      setIsLoading(false);
    }
  }, [buildQueryParams]);

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1);
  };

  // Handle sorting
  const handleSort = (field: string, direction: 'asc' | 'desc') => {
    setSortState({ field, direction });
    setCurrentPage(1);
  };

  // Handle row click
  const handleRowClick = (request: MarketProductRequest) => {
    setSelectedRequest(request);
    setShowDetailsModal(true);
  };

  // Action handlers
  const handleRejectOrRefund = async () => {
    if (!selectedRequest || !rejectReason.trim()) return;

    setActionLoading(true);
    try {
      const response = shouldRefundPoints
        ? await refundMarketProductRequest(selectedRequest.id, { reason: rejectReason.trim() })
        : await rejectMarketProductRequest(selectedRequest.id, { reason: rejectReason.trim() });

      if (response.success) {
        setShowRejectModal(false);
        setRejectReason('');
        setShouldRefundPoints(false);
        setSelectedRequest(null);
        await loadData();
      } else {
        throw new Error(response.error || `Failed to ${shouldRefundPoints ? 'refund' : 'reject'} request`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  const handleComplete = async (request: MarketProductRequest) => {
    setActionLoading(true);
    try {
      const response = await completeMarketProductRequest(request.id);

      if (response.success) {
        await loadData();
      } else {
        throw new Error(response.error || 'Failed to complete request');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };



  const handleDelete = async () => {
    if (!selectedRequest) return;

    setActionLoading(true);
    try {
      const response = await deleteMarketProductRequest(selectedRequest.id);

      if (response.success) {
        setShowDeleteModal(false);
        setSelectedRequest(null);
        await loadData();
      } else {
        throw new Error(response.error || 'Failed to delete request');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('');
    setProductTypeFilter('');
    setProductCategoryFilter('');
    setCurrencyFilter('');
    setUserIdFilter('');
    setProductIdFilter('');
    setMinPriceFilter('');
    setMaxPriceFilter('');
    setHasRejectReasonFilter('');
    setCreatedDateRange({ from: null, to: null });
    setUpdatedDateRange({ from: null, to: null });

    // Also clear applied filters
    setAppliedFilters({
      searchTerm: '',
      statusFilter: '',
      productTypeFilter: '',
      productCategoryFilter: '',
      currencyFilter: '',
      userIdFilter: '',
      productIdFilter: '',
      minPriceFilter: '',
      maxPriceFilter: '',
      hasRejectReasonFilter: '',
      createdDateRange: { from: null, to: null },
      updatedDateRange: { from: null, to: null }
    });

    setCurrentPage(1);
  };

  // Apply filters
  const applyFilters = () => {
    setAppliedFilters({
      searchTerm,
      statusFilter,
      productTypeFilter,
      productCategoryFilter,
      currencyFilter,
      userIdFilter,
      productIdFilter,
      minPriceFilter,
      maxPriceFilter,
      hasRejectReasonFilter,
      createdDateRange,
      updatedDateRange
    });
    setCurrentPage(1);
  };

  // Export functionality
  const exportData = async () => {
    try {
      setActionLoading(true);

      // Fetch all data with current filters (no pagination)
      const queryParams = buildQueryParams();
      delete queryParams.page;
      delete queryParams.limit;

      const response = await fetchMarketProductRequests(queryParams);

      if (response.success && response.data) {
        const exportData = response.data.map(request => ({
          id: request.id,
          status: request.status,
          userId: request.user.externalId,
          userPoints: request.user.points,
          productId: request.product.id,
          productName: request.product.name,
          productType: request.product.type,
          productCategory: request.product.category,
          productPrice: request.product.price,
          providers: request.providers ? JSON.stringify(request.providers) : null,
          rejectReason: request.rejectReason,
          createdAt: request.createdAt,
          updatedAt: request.updatedAt,
          historyCount: request.history.length
        }));

        // Create CSV content
        const headers = Object.keys(exportData[0] || {});
        const csvContent = [
          headers.join(','),
          ...exportData.map(row =>
            headers.map(header => {
              const value = row[header as keyof typeof row];
              return typeof value === 'string' && value.includes(',')
                ? `"${value.replace(/"/g, '""')}"`
                : value;
            }).join(',')
          )
        ].join('\n');

        // Download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `market-product-requests-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Export failed';
      setError(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  // Bulk operations
  const handleSelectRequest = (requestId: number, selected: boolean) => {
    const newSelected = new Set(selectedRequests);
    if (selected) {
      newSelected.add(requestId);
    } else {
      newSelected.delete(requestId);
    }
    setSelectedRequests(newSelected);
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedRequests(new Set(data.data.map(request => request.id)));
    } else {
      setSelectedRequests(new Set());
    }
  };

  const handleBulkComplete = async () => {
    setBulkActionLoading(true);
    try {
      const promises = Array.from(selectedRequests).map(id =>
        completeMarketProductRequest(id)
      );

      const results = await Promise.allSettled(promises);
      const failed = results.filter(result => result.status === 'rejected').length;

      if (failed > 0) {
        setError(`${failed} requests failed to complete`);
      }

      setShowBulkCompleteModal(false);
      setSelectedRequests(new Set());
      await loadData();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Bulk complete failed';
      setError(errorMessage);
    } finally {
      setBulkActionLoading(false);
    }
  };

  const handleBulkReject = async () => {
    if (!bulkRejectReason.trim()) return;

    setBulkActionLoading(true);
    try {
      const promises = Array.from(selectedRequests).map(id =>
        rejectMarketProductRequest(id, { reason: bulkRejectReason.trim() })
      );

      const results = await Promise.allSettled(promises);
      const failed = results.filter(result => result.status === 'rejected').length;

      if (failed > 0) {
        setError(`${failed} requests failed to reject`);
      }

      setShowBulkRejectModal(false);
      setBulkRejectReason('');
      setSelectedRequests(new Set());
      await loadData();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Bulk reject failed';
      setError(errorMessage);
    } finally {
      setBulkActionLoading(false);
    }
  };

  // Table columns
  const tableColumns: TableColumn<MarketProductRequest>[] = [
    {
      key: 'select',
      label: (
        <input
          type="checkbox"
          checked={selectedRequests.size === data.data.length && data.data.length > 0}
          onChange={(e) => handleSelectAll(e.target.checked)}
          className="rounded border-gray-600 bg-dark-700 text-primary-500 focus:ring-primary-500"
        />
      ),
      render: (request) => (
        <input
          type="checkbox"
          checked={selectedRequests.has(request.id)}
          onChange={(e) => handleSelectRequest(request.id, e.target.checked)}
          onClick={(e) => e.stopPropagation()}
          className="rounded border-gray-600 bg-dark-700 text-primary-500 focus:ring-primary-500"
        />
      )
    },
    {
      key: 'id',
      label: 'ID',
      sortable: true,
      render: (request) => (
        <span className="font-mono text-sm text-gray-300">#{request.id}</span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (request) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
          {getStatusDisplay(request.status)}
        </span>
      )
    },
    {
      key: 'user',
      label: 'User',
      sortable: true,
      render: (request) => (
        <div className="flex flex-col">
          <span className="text-sm text-gray-300">ID: {request.user.externalId}</span>
          <span className="text-xs text-gray-500">{formatPrice(request.user.points)}</span>
        </div>
      )
    },
    {
      key: 'product',
      label: 'Product',
      sortable: true,
      render: (request) => (
        <div className="flex flex-col">
          <span
            className="text-sm text-gray-300 font-medium truncate max-w-[200px]"
            title={request.product.name}
          >
            {request.product.name}
          </span>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span>{getProductTypeDisplay(request.product.type)}</span>
            <span>•</span>
            <span>{getProductCategoryDisplay(request.product.category)}</span>
          </div>
        </div>
      )
    },
    {
      key: 'price',
      label: 'Price',
      sortable: true,
      render: (request) => (
        <span className="text-sm text-gray-300 font-medium">{formatPrice(request.product.price)}</span>
      )
    },
    {
      key: 'providers',
      label: 'Providers',
      render: (request) => {
        if (!request.providers) {
          return <span className="text-gray-500">-</span>;
        }

        const providerItems = Object.entries(request.providers).map(([provider, games]) => {
          const gameCount = games.length > 0 ? ` (${games.length} games)` : '';
          return `${provider}${gameCount}`;
        });

        return <TruncatedDisplay items={providerItems} maxItems={2} itemLabel="providers" />;
      }
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (request) => (
        <div className="flex flex-col">
          <span className="text-sm text-gray-300">
            {new Date(request.createdAt).toLocaleDateString()}
          </span>
          <span className="text-xs text-gray-500">
            {new Date(request.createdAt).toLocaleTimeString()}
          </span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (request) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedRequest(request);
              setShowDetailsModal(true);
            }}
            className="text-gray-400 hover:text-gray-300"
          >
            <Eye size={16} />
          </Button>

          {canComplete(request.status) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleComplete(request);
              }}
              className="text-green-400 hover:text-green-300"
              disabled={actionLoading}
            >
              <CheckCircle size={16} />
            </Button>
          )}

          {(canReject(request.status) || canRefund(request.status)) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedRequest(request);
                setShouldRefundPoints(false); // Default to reject mode
                setShowRejectModal(true);
              }}
              className="text-red-400 hover:text-red-300"
              disabled={actionLoading}
            >
              <XCircle size={16} />
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedRequest(request);
              setShowDeleteModal(true);
            }}
            className="text-red-400 hover:text-red-300"
            disabled={actionLoading}
          >
            <Trash2 size={16} />
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <ShoppingCart size={24} className="text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Market Product Requests</h1>
            <p className="text-gray-400">View and manage user product purchase requests</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {selectedRequests.size > 0 && (
            <>
              <span className="text-sm text-gray-400">
                {selectedRequests.size} selected
              </span>

              <Button
                variant="ghost"
                onClick={() => setShowBulkCompleteModal(true)}
                disabled={bulkActionLoading}
                className="flex items-center gap-2 text-green-400 hover:text-green-300"
              >
                <CheckCircle size={16} />
                Complete Selected
              </Button>

              <Button
                variant="ghost"
                onClick={() => setShowBulkRejectModal(true)}
                disabled={bulkActionLoading}
                className="flex items-center gap-2 text-red-400 hover:text-red-300"
              >
                <XCircle size={16} />
                Reject Selected
              </Button>
            </>
          )}

          <Button
            variant="ghost"
            onClick={exportData}
            disabled={isLoading || actionLoading}
            className="flex items-center gap-2"
          >
            <Download size={16} />
            Export
          </Button>

          <Button
            variant="ghost"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter size={16} />
            Filters
            <ChevronDown size={16} className={`transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </Button>

          <Button
            variant="ghost"
            onClick={loadData}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <div className="admin-card p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500/10 rounded-lg">
              <FileText size={20} className="text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Total Requests</p>
              <p className="text-xl font-bold text-gray-100">{statistics.total}</p>
            </div>
          </div>
        </div>

        <div className="admin-card p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-500/10 rounded-lg">
              <Clock size={20} className="text-yellow-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Pending</p>
              <p className="text-xl font-bold text-gray-100">{statistics.pending}</p>
            </div>
          </div>
        </div>

        <div className="admin-card p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-500/10 rounded-lg">
              <CheckCircle size={20} className="text-green-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Completed</p>
              <p className="text-xl font-bold text-gray-100">{statistics.completed}</p>
            </div>
          </div>
        </div>

        <div className="admin-card p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-500/10 rounded-lg">
              <XCircle size={20} className="text-red-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Rejected</p>
              <p className="text-xl font-bold text-gray-100">{statistics.rejected}</p>
            </div>
          </div>
        </div>

        <div className="admin-card p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500/10 rounded-lg">
              <RotateCcw size={20} className="text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Refunded</p>
              <p className="text-xl font-bold text-gray-100">{statistics.refunded}</p>
            </div>
          </div>
        </div>

        <div className="admin-card p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary-500/10 rounded-lg">
              <Coins size={20} className="text-primary-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Total Value</p>
              <p className="text-xl font-bold text-gray-100">{formatPrice(statistics.totalValue)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="admin-card p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-100">Filters</h3>
            <Button
              variant="ghost"
              onClick={clearFilters}
              className="text-gray-400 hover:text-gray-300"
            >
              <X size={16} className="mr-2" />
              Clear All
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Search</label>
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search requests..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Status</label>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                options={[
                  { value: '', label: 'All Statuses' },
                  ...MARKET_PRODUCT_REQUEST_STATUS_OPTIONS
                ]}
              />
            </div>

            {/* Product Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Product Type</label>
              <Select
                value={productTypeFilter}
                onChange={(e) => setProductTypeFilter(e.target.value)}
                options={[
                  { value: '', label: 'All Types' },
                  ...PRODUCT_TYPE_OPTIONS
                ]}
              />
            </div>

            {/* Product Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Product Category</label>
              <Select
                value={productCategoryFilter}
                onChange={(e) => setProductCategoryFilter(e.target.value)}
                options={[
                  { value: '', label: 'All Categories' },
                  ...PRODUCT_CATEGORY_OPTIONS
                ]}
              />
            </div>

            {/* Currency Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Currency</label>
              <Input
                type="text"
                placeholder="e.g., USD, EUR"
                value={currencyFilter}
                onChange={(e) => setCurrencyFilter(e.target.value)}
              />
            </div>

            {/* User ID Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">User ID</label>
              <Input
                type="number"
                placeholder="Enter user ID"
                value={userIdFilter}
                onChange={(e) => setUserIdFilter(e.target.value)}
              />
            </div>

            {/* Product ID Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Product ID</label>
              <Input
                type="number"
                placeholder="Enter product ID"
                value={productIdFilter}
                onChange={(e) => setProductIdFilter(e.target.value)}
              />
            </div>

            {/* Has Reject Reason Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Has Reject Reason</label>
              <Select
                value={hasRejectReasonFilter}
                onChange={(e) => setHasRejectReasonFilter(e.target.value)}
                options={[
                  { value: '', label: 'All' },
                  { value: 'true', label: 'Yes' },
                  { value: 'false', label: 'No' }
                ]}
              />
            </div>
          </div>

          {/* Price Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Price Range</label>
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  placeholder="Min price"
                  value={minPriceFilter}
                  onChange={(e) => setMinPriceFilter(e.target.value)}
                  step="0.01"
                />
                <span className="text-gray-400">to</span>
                <Input
                  type="number"
                  placeholder="Max price"
                  value={maxPriceFilter}
                  onChange={(e) => setMaxPriceFilter(e.target.value)}
                  step="0.01"
                />
              </div>
            </div>
          </div>

          {/* Date Ranges */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Created Date Range</label>
              <DateRangePicker
                value={createdDateRange}
                onChange={setCreatedDateRange}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Updated Date Range</label>
              <DateRangePicker
                value={updatedDateRange}
                onChange={setUpdatedDateRange}
              />
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              onClick={applyFilters}
              className="flex items-center gap-2"
            >
              <Filter size={16} />
              Apply Filters
            </Button>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="admin-card p-4 border-red-500/20 bg-red-500/5">
          <div className="flex items-center gap-3">
            <AlertCircle size={20} className="text-red-400" />
            <div>
              <p className="text-red-400 font-medium">Error</p>
              <p className="text-gray-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <DataTable
        data={data.data}
        columns={tableColumns}
        total={data.total}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        onSort={handleSort}
        onRowClick={handleRowClick}
        title="Market Product Requests"
        emptyState={{
          icon: <ShoppingCart className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No market product requests found',
          description: 'No requests match your current filters.',
          action: showFilters ? (
            <Button onClick={clearFilters} variant="primary">
              Clear Filters
            </Button>
          ) : undefined
        }}
      />

      {/* Details Modal */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedRequest(null);
        }}
        title="Request Details"
        size="lg"
      >
        {selectedRequest && (
          <div className="space-y-6">
            {/* Request Info */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Request ID</label>
                <p className="text-gray-100 font-mono">#{selectedRequest.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Status</label>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedRequest.status)}`}>
                  {getStatusDisplay(selectedRequest.status)}
                </span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Created</label>
                <p className="text-gray-100">{new Date(selectedRequest.createdAt).toLocaleString()}</p>
              </div>
            </div>

            {/* User Info */}
            <div>
              <h4 className="text-lg font-semibold text-gray-100 mb-3">User Information</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">User ID</label>
                  <p className="text-gray-100">{selectedRequest.user.externalId}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Current Points</label>
                  <p className="text-gray-100">{formatPrice(selectedRequest.user.points)}</p>
                </div>
              </div>
            </div>

            {/* Product Info */}
            <div>
              <h4 className="text-lg font-semibold text-gray-100 mb-3">Product Information</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Product Name</label>
                  <p className="text-gray-100">{selectedRequest.product.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Price</label>
                  <p className="text-gray-100">{formatPrice(selectedRequest.product.price)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Type</label>
                  <p className="text-gray-100">{getProductTypeDisplay(selectedRequest.product.type)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Category</label>
                  <p className="text-gray-100">{getProductCategoryDisplay(selectedRequest.product.category)}</p>
                </div>
              </div>
            </div>

            {/* Request Details */}
            <div>
              <h4 className="text-lg font-semibold text-gray-100 mb-3">Request Details</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Currency</label>
                  <span className="px-2 py-1 bg-green-500/20 text-green-400 border border-green-500/30 rounded text-sm font-medium">
                    {selectedRequest.currency}
                  </span>
                </div>
              </div>
            </div>

            {/* Providers */}
            {selectedRequest.providers && (
              <div>
                <h4 className="text-lg font-semibold text-gray-100 mb-3">Providers</h4>
                <div className="space-y-2">
                  {Array.isArray(selectedRequest.providers) ? (
                    // New structure: array of MarketSelectedProvider objects
                    selectedRequest.providers.map((selectedProvider) => (
                      <div key={selectedProvider.provider.id} className="p-3 bg-dark-700 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {selectedProvider.provider.image && (
                              <img
                                src={selectedProvider.provider.image}
                                alt={selectedProvider.provider.name}
                                className="w-6 h-6 rounded"
                              />
                            )}
                            <span className="text-gray-100 font-medium">{selectedProvider.provider.name}</span>
                          </div>
                          <span className="text-gray-400">
                            {selectedProvider.games.length > 0 ? `${selectedProvider.games.length} games` : 'All games'}
                          </span>
                        </div>
                        {selectedProvider.games.length > 0 && (
                          <div className="mt-2">
                            <span className="text-xs text-gray-500 mb-1 block">Game IDs:</span>
                            <div className="flex flex-wrap gap-1">
                              {selectedProvider.games.map((game) => (
                                <span key={game.id} className="px-2 py-1 bg-dark-600 text-gray-300 rounded text-xs font-mono">
                                  {game.id}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    // Legacy structure: Record<string, number[]> - matches public API format
                    Object.entries(selectedRequest.providers).map(([providerId, gameIds]) => (
                      <div key={providerId} className="p-3 bg-dark-700 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-gray-100 font-medium">Provider ID: {providerId}</span>
                          <span className="text-gray-400">
                            {gameIds.length > 0 ? `${gameIds.length} games` : 'All games'}
                          </span>
                        </div>
                        {gameIds.length > 0 && (
                          <div className="mt-2">
                            <span className="text-xs text-gray-500 mb-1 block">Game IDs:</span>
                            <div className="flex flex-wrap gap-1">
                              {gameIds.map((gameId) => (
                                <span key={gameId} className="px-2 py-1 bg-dark-600 text-gray-300 rounded text-xs font-mono">
                                  {gameId}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}

            {/* Reject Reason */}
            {selectedRequest.rejectReason && (
              <div>
                <h4 className="text-lg font-semibold text-gray-100 mb-3">Reject Reason</h4>
                <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                  <p className="text-gray-100">{selectedRequest.rejectReason}</p>
                </div>
              </div>
            )}

            {/* History */}
            <div>
              <h4 className="text-lg font-semibold text-gray-100 mb-3">History</h4>
              <div className="space-y-3">
                {selectedRequest.history.map((entry, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-dark-700 rounded-lg">
                    <div className={`p-1 rounded-full ${getStatusColor(entry.status)}`}>
                      <div className="w-2 h-2 rounded-full bg-current"></div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-100 font-medium">{getStatusDisplay(entry.status)}</span>
                        <span className="text-gray-400 text-sm">{formatTimestamp(entry.timestamp)}</span>
                      </div>
                      <p className="text-gray-300 text-sm mt-1">{entry.message}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-4 border-t border-dark-700">
              {canComplete(selectedRequest.status) && (
                <Button
                  onClick={() => handleComplete(selectedRequest)}
                  disabled={actionLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle size={16} className="mr-2" />
                  Complete
                </Button>
              )}

              {(canReject(selectedRequest.status) || canRefund(selectedRequest.status)) && (
                <Button
                  onClick={() => {
                    setShowDetailsModal(false);
                    setShouldRefundPoints(false); // Default to reject mode
                    setShowRejectModal(true);
                  }}
                  disabled={actionLoading}
                  className="bg-red-600 hover:bg-red-700"
                >
                  <XCircle size={16} className="mr-2" />
                  Reject / Refund
                </Button>
              )}
            </div>
          </div>
        )}
      </Modal>

      {/* Reject/Refund Modal */}
      <Modal
        isOpen={showRejectModal}
        onClose={() => {
          setShowRejectModal(false);
          setRejectReason('');
          setShouldRefundPoints(false);
          setSelectedRequest(null);
        }}
        title={shouldRefundPoints ? "Refund Request" : "Reject Request"}
      >
        {selectedRequest && (
          <div className="space-y-4">
            <div className={`p-4 border rounded-lg ${
              shouldRefundPoints
                ? 'bg-blue-500/10 border-blue-500/20'
                : 'bg-red-500/10 border-red-500/20'
            }`}>
              <div className="flex items-center gap-2 mb-2">
                {shouldRefundPoints ? (
                  <RotateCcw size={20} className="text-blue-400" />
                ) : (
                  <AlertTriangle size={20} className="text-red-400" />
                )}
                <span className={`font-medium ${
                  shouldRefundPoints ? 'text-blue-400' : 'text-red-400'
                }`}>
                  {shouldRefundPoints ? 'Refund' : 'Reject'} Request #{selectedRequest.id}
                </span>
              </div>
              <p className="text-gray-300">
                {shouldRefundPoints
                  ? 'You are about to refund this request. The points will be returned to the user\'s account.'
                  : 'You are about to reject this request. The user\'s points will remain charged.'
                }
                Please provide a reason for {shouldRefundPoints ? 'the refund' : 'rejection'}.
              </p>
            </div>

            {/* Refund Points Checkbox */}
            {(canReject(selectedRequest.status) || canRefund(selectedRequest.status)) && (
              <div className="flex items-center gap-3 p-3 bg-dark-700 rounded-lg">
                <input
                  type="checkbox"
                  id="refundPoints"
                  checked={shouldRefundPoints}
                  onChange={(e) => setShouldRefundPoints(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-dark-600 border-dark-500 rounded focus:ring-blue-500 focus:ring-2"
                />
                <label htmlFor="refundPoints" className="text-sm font-medium text-gray-300 cursor-pointer">
                  Refund points to user
                </label>
                <span className="text-xs text-gray-500">
                  (Check this to refund points instead of just rejecting)
                </span>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {shouldRefundPoints ? 'Refund Reason' : 'Rejection Reason'}
              </label>
              <textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                placeholder={`Enter reason for ${shouldRefundPoints ? 'refund' : 'rejection'}...`}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={4}
                required
              />
            </div>

            <div className="flex justify-end gap-3">
              <Button
                variant="ghost"
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectReason('');
                  setShouldRefundPoints(false);
                  setSelectedRequest(null);
                }}
                disabled={actionLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleRejectOrRefund}
                disabled={actionLoading || !rejectReason.trim()}
                className={shouldRefundPoints ? "bg-blue-600 hover:bg-blue-700" : "bg-red-600 hover:bg-red-700"}
              >
                {actionLoading
                  ? (shouldRefundPoints ? 'Processing Refund...' : 'Rejecting...')
                  : (shouldRefundPoints ? 'Process Refund' : 'Reject Request')
                }
              </Button>
            </div>
          </div>
        )}
      </Modal>


      {/* Delete Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setSelectedRequest(null);
        }}
        title="Delete Request"
      >
        {selectedRequest && (
          <div className="space-y-4">
            <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle size={20} className="text-red-400" />
                <span className="text-red-400 font-medium">Delete Request #{selectedRequest.id}</span>
              </div>
              <p className="text-gray-300">
                You are about to permanently delete this request. This action cannot be undone.
                Are you sure you want to continue?
              </p>
            </div>

            <div className="bg-dark-700 p-4 rounded-lg">
              <h4 className="text-gray-100 font-medium mb-2">Request Details:</h4>
              <div className="space-y-1 text-sm">
                <p className="text-gray-300">Product: {selectedRequest.product.name}</p>
                <p className="text-gray-300">User ID: {selectedRequest.user.externalId}</p>
                <p className="text-gray-300">Price: {formatPrice(selectedRequest.product.price)}</p>
                <p className="text-gray-300">Status: {getStatusDisplay(selectedRequest.status)}</p>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                variant="ghost"
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedRequest(null);
                }}
                disabled={actionLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleDelete}
                disabled={actionLoading}
                className="bg-red-600 hover:bg-red-700"
              >
                {actionLoading ? 'Deleting...' : 'Delete Request'}
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* Bulk Complete Modal */}
      <Modal
        isOpen={showBulkCompleteModal}
        onClose={() => {
          setShowBulkCompleteModal(false);
        }}
        title="Bulk Complete Requests"
      >
        <div className="space-y-4">
          <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle size={20} className="text-green-400" />
              <span className="text-green-400 font-medium">Complete {selectedRequests.size} Requests</span>
            </div>
            <p className="text-gray-300">
              You are about to mark {selectedRequests.size} requests as completed.
              This indicates that the products have been delivered to the users.
            </p>
          </div>

          <div className="bg-dark-700 p-4 rounded-lg">
            <h4 className="text-gray-100 font-medium mb-2">Selected Requests:</h4>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {Array.from(selectedRequests).map(id => {
                const request = data.data.find(r => r.id === id);
                return request ? (
                  <div key={id} className="text-sm text-gray-300">
                    #{request.id} - {request.product.name} ({request.user.externalId})
                  </div>
                ) : null;
              })}
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="ghost"
              onClick={() => setShowBulkCompleteModal(false)}
              disabled={bulkActionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleBulkComplete}
              disabled={bulkActionLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {bulkActionLoading ? 'Completing...' : 'Complete All'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Bulk Reject Modal */}
      <Modal
        isOpen={showBulkRejectModal}
        onClose={() => {
          setShowBulkRejectModal(false);
          setBulkRejectReason('');
        }}
        title="Bulk Reject Requests"
      >
        <div className="space-y-4">
          <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <XCircle size={20} className="text-red-400" />
              <span className="text-red-400 font-medium">Reject {selectedRequests.size} Requests</span>
            </div>
            <p className="text-gray-300">
              You are about to reject {selectedRequests.size} requests.
              The users' points will remain charged. Please provide a reason for rejection.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Rejection Reason</label>
            <textarea
              value={bulkRejectReason}
              onChange={(e) => setBulkRejectReason(e.target.value)}
              placeholder="Enter reason for bulk rejection..."
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500"
              rows={4}
              required
            />
          </div>

          <div className="bg-dark-700 p-4 rounded-lg">
            <h4 className="text-gray-100 font-medium mb-2">Selected Requests:</h4>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {Array.from(selectedRequests).map(id => {
                const request = data.data.find(r => r.id === id);
                return request ? (
                  <div key={id} className="text-sm text-gray-300">
                    #{request.id} - {request.product.name} ({request.user.externalId})
                  </div>
                ) : null;
              })}
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="ghost"
              onClick={() => {
                setShowBulkRejectModal(false);
                setBulkRejectReason('');
              }}
              disabled={bulkActionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleBulkReject}
              disabled={bulkActionLoading || !bulkRejectReason.trim()}
              className="bg-red-600 hover:bg-red-700"
            >
              {bulkActionLoading ? 'Rejecting...' : 'Reject All'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default MissionsMarketRequests;
