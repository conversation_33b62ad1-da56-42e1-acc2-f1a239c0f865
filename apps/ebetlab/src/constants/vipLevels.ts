// VIP Levels Constants
// This enum defines all possible VIP levels in the system
// Use this enum whenever referencing VIP levels in the frontend

export enum VipLevel {
  NO_VIP = 'no-vip',
  IRON = 'iron',
  COPPER = 'copper',
  BRONZE = 'bronze',
  BRASS = 'brass',
  NICKEL = 'nickel',
  STEEL = 'steel',
  COBALT = 'cobalt',
  TITANIUM = 'titanium',
  TUNGSTEN = 'tungsten',
  SILVER = 'silver',
  GOLD = 'gold',
  PLATINUM = 'platinum',
  PALLADIUM = 'palladium',
  RHODIUM = 'rhodium',
  OSMIUM = 'osmium',
  IRIDIUM = 'iridium',
  MITHRIL = 'mithril',
  ADAMANTITE = 'adamantite',
  ORICHALCUM = 'orichalcum',
  VIBRANIUM = 'vibranium',
  UNOBTANIUM = 'unobtanium',
  ETERNIUM = 'eternium'
}

// Array of all VIP levels for easy iteration
export const VIP_LEVELS = Object.values(VipLevel);

// Display names for VIP levels (formatted for UI)
export const VIP_LEVEL_DISPLAY_NAMES: Record<VipLevel, string> = {
  [VipLevel.NO_VIP]: 'NO-VIP',
  [VipLevel.IRON]: 'IRON',
  [VipLevel.COPPER]: 'COPPER',
  [VipLevel.BRONZE]: 'BRONZE',
  [VipLevel.BRASS]: 'BRASS',
  [VipLevel.NICKEL]: 'NICKEL',
  [VipLevel.STEEL]: 'STEEL',
  [VipLevel.COBALT]: 'COBALT',
  [VipLevel.TITANIUM]: 'TITANIUM',
  [VipLevel.TUNGSTEN]: 'TUNGSTEN',
  [VipLevel.SILVER]: 'SILVER',
  [VipLevel.GOLD]: 'GOLD',
  [VipLevel.PLATINUM]: 'PLATINUM',
  [VipLevel.PALLADIUM]: 'PALLADIUM',
  [VipLevel.RHODIUM]: 'RHODIUM',
  [VipLevel.OSMIUM]: 'OSMIUM',
  [VipLevel.IRIDIUM]: 'IRIDIUM',
  [VipLevel.MITHRIL]: 'MITHRIL',
  [VipLevel.ADAMANTITE]: 'ADAMANTITE',
  [VipLevel.ORICHALCUM]: 'ORICHALCUM',
  [VipLevel.VIBRANIUM]: 'VIBRANIUM',
  [VipLevel.UNOBTANIUM]: 'UNOBTANIUM',
  [VipLevel.ETERNIUM]: 'ETERNIUM'
};

// Color configurations for VIP level badges
export const VIP_LEVEL_COLORS: Record<VipLevel, string> = {
  [VipLevel.NO_VIP]: 'bg-gray-500/20 text-gray-400',
  [VipLevel.IRON]: 'bg-gray-600/20 text-gray-300',
  [VipLevel.COPPER]: 'bg-orange-700/20 text-orange-400',
  [VipLevel.BRONZE]: 'bg-amber-600/20 text-amber-400',
  [VipLevel.BRASS]: 'bg-yellow-600/20 text-yellow-300',
  [VipLevel.NICKEL]: 'bg-gray-500/20 text-gray-300',
  [VipLevel.STEEL]: 'bg-slate-500/20 text-slate-300',
  [VipLevel.COBALT]: 'bg-blue-600/20 text-blue-400',
  [VipLevel.TITANIUM]: 'bg-slate-400/20 text-slate-200',
  [VipLevel.TUNGSTEN]: 'bg-gray-700/20 text-gray-200',
  [VipLevel.SILVER]: 'bg-gray-500/20 text-gray-300',
  [VipLevel.GOLD]: 'bg-yellow-500/20 text-yellow-400',
  [VipLevel.PLATINUM]: 'bg-purple-500/20 text-purple-400',
  [VipLevel.PALLADIUM]: 'bg-indigo-500/20 text-indigo-400',
  [VipLevel.RHODIUM]: 'bg-pink-500/20 text-pink-400',
  [VipLevel.OSMIUM]: 'bg-purple-600/20 text-purple-300',
  [VipLevel.IRIDIUM]: 'bg-violet-500/20 text-violet-400',
  [VipLevel.MITHRIL]: 'bg-cyan-500/20 text-cyan-400',
  [VipLevel.ADAMANTITE]: 'bg-emerald-500/20 text-emerald-400',
  [VipLevel.ORICHALCUM]: 'bg-amber-500/20 text-amber-300',
  [VipLevel.VIBRANIUM]: 'bg-purple-700/20 text-purple-300',
  [VipLevel.UNOBTANIUM]: 'bg-blue-500/20 text-blue-400',
  [VipLevel.ETERNIUM]: 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400'
};

// Helper function to get VIP level display name
export const getVipLevelDisplayName = (level: string): string => {
  const vipLevel = level?.toLowerCase() as VipLevel;
  return VIP_LEVEL_DISPLAY_NAMES[vipLevel] || VIP_LEVEL_DISPLAY_NAMES[VipLevel.NO_VIP];
};

// Helper function to get VIP level color class
export const getVipLevelColor = (level: string): string => {
  const vipLevel = level?.toLowerCase() as VipLevel;
  return VIP_LEVEL_COLORS[vipLevel] || VIP_LEVEL_COLORS[VipLevel.NO_VIP];
};

// Helper function to check if a string is a valid VIP level
export const isValidVipLevel = (level: string): level is VipLevel => {
  return VIP_LEVELS.includes(level?.toLowerCase() as VipLevel);
};

// Helper function to create a VIP level badge component props
export const getVipLevelBadgeProps = (level: string) => {
  return {
    colorClass: getVipLevelColor(level),
    displayText: getVipLevelDisplayName(level),
    isValid: isValidVipLevel(level)
  };
};

// Helper function to get all VIP levels as options for select components
export const getVipLevelOptions = () => {
  return VIP_LEVELS.map(level => ({
    value: level,
    label: getVipLevelDisplayName(level)
  }));
};
