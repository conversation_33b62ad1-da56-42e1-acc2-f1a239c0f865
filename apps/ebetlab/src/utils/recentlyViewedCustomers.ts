// Recently Viewed Customers utility
// Handles localStorage operations for tracking recently viewed customers

export interface RecentlyViewedCustomer {
  id: number;
  name: string;
  username: string;
  viewedAt: number;
}

const STORAGE_KEY = 'recentlyViewedCustomers';
const MAX_RECENT_CUSTOMERS = 10;

export const getRecentlyViewedCustomers = (): RecentlyViewedCustomer[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return [];
    
    const customers: RecentlyViewedCustomer[] = JSON.parse(stored);
    
    // Sort by most recently viewed
    return customers.sort((a, b) => b.viewedAt - a.viewedAt);
  } catch (error) {
    console.error('Error loading recently viewed customers:', error);
    return [];
  }
};

export const addRecentlyViewedCustomer = (customer: {
  id: number;
  name?: string;
  surname?: string;
  username?: string;
}): void => {
  try {
    const existing = getRecentlyViewedCustomers();
    
    // Create display name
    const displayName = customer.name && customer.surname 
      ? `${customer.name} ${customer.surname}`
      : customer.username || `Customer #${customer.id}`;
    
    const newCustomer: RecentlyViewedCustomer = {
      id: customer.id,
      name: displayName,
      username: customer.username || '',
      viewedAt: Date.now()
    };
    
    // Remove existing entry if present
    const filtered = existing.filter(c => c.id !== customer.id);
    
    // Add new entry at the beginning
    const updated = [newCustomer, ...filtered];
    
    // Keep only the most recent entries
    const trimmed = updated.slice(0, MAX_RECENT_CUSTOMERS);
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(trimmed));
  } catch (error) {
    console.error('Error saving recently viewed customer:', error);
  }
};

export const removeRecentlyViewedCustomer = (customerId: number): void => {
  try {
    const existing = getRecentlyViewedCustomers();
    const filtered = existing.filter(c => c.id !== customerId);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filtered));
  } catch (error) {
    console.error('Error removing recently viewed customer:', error);
  }
};

export const clearRecentlyViewedCustomers = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing recently viewed customers:', error);
  }
};
