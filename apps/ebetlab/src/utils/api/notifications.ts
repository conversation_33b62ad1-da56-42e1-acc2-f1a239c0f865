// Notifications API endpoints and types
// This module handles all notifications-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Notifications-related interfaces
// Using flexible typing to handle different notification data structures
export interface NotificationData {
  currency?: string;
  amount?: string;
  [key: string]: any; // Allow for additional properties based on notification type
}

export interface NotificationItem {
  id: number;
  customer_id: number;
  type: string;
  lang_key: string;
  timestamp: string;
  data: NotificationData;
}

export interface NotificationsResponse {
  total: number;
  data: NotificationItem[];
}

export interface NotificationSearchParams {
  customer_id?: string;
  type?: string | null;
  lang_key?: string | null;
  from?: number | null;
  to?: number | null;
}

/**
 * Fetch notifications data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with notifications data
 */
export const fetchNotifications = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<NotificationSearchParams> = {}
): Promise<ApiResponse<NotificationsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      limit: limit,
      page: page,
      customer_id: null,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/notifications/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
