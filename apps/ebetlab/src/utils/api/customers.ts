// Customer API endpoints and types
// This module handles all customer-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Customer-related interfaces
export interface CustomerProfile {
  id?: number;
  name?: string;
  surname?: string;
  customer_id?: number;
  verification_level?: number;
  birthday?: string;
  country_id?: number;
  identity_no?: string;
}

export interface CustomerDeposit {
  id?: number;
  customer_id?: number;
  timestamp?: number;
  amount?: string;
  currency?: string;
  type?: number;
  provider?: string;
  payment_provider?: string | null;
}

export interface CustomerSummary {
  id?: number;
  customer_id?: number;
  total_in_usd?: string;
  total_out_usd?: string;
  percentage?: string;
  total_rakeback_usd?: string;
  total_reload_usd?: string;
  total_bonus_drop_usd?: string;
  total_rain_usd?: string;
}

export interface CustomerStatus {
  id?: number;
  name?: string;
}

export interface CustomerAffiliator {
  id?: number;
  name?: string;
}

export interface CustomerPhone {
  id?: number;
  full?: string;
  code?: string;
  number?: string;
  customer_id?: number;
}

export interface CustomerData {
  id: number;
  username?: string;
  last_online_at?: number;
  last_ip?: string;
  ref_id?: number | null;
  affiliator_id?: number;
  registration_country?: string;
  ref_code?: string;
  registration_ts?: number;
  operator_id?: number | null;
  status_id?: number;
  is_public?: boolean;
  lang?: string;
  rank?: string;
  rank_percentage?: string;
  total_turnover?: string;
  next_wager_limit?: string;
  email?: string;
  masked_username?: string;
  last_action?: number | null;
  profile?: CustomerProfile;
  last_deposit?: CustomerDeposit | null;
  first_deposit?: CustomerDeposit | null;
  last_withdraw?: CustomerDeposit | null;
  last_bonus?: any | null;
  summary?: CustomerSummary | null;
  status?: CustomerStatus;
  operator?: any;
  affiliator?: CustomerAffiliator;
  referrer?: any;
  phone?: CustomerPhone | null;
}

export interface CustomersResponse {
  data?: {
    total?: number;
    data?: CustomerData[];
  };
}

export interface CustomerDetailsData {
  id: number;
  merchant_id: number;
  affiliator_id: number | null;
  phone_verified_at: string | null;
  excluded_till: string | null;
  otp_step: boolean;
  username: string;
  ref_id: number | null;
  is_active: boolean;
  is_suspended: boolean;
  last_country: string;
  registration_country: string;
  registration_ip: string;
  last_online_at: number;
  last_ip: string;
  email_verified_at: string | null;
  registration_ts: number;
  website_id: number;
  operator_id: number | null;
  status_id: number;
  is_public: boolean;
  lang: string;
  rank: string;
  rank_percentage: string;
  ref_code: string;
  total_turnover: string;
  next_wager_limit: string;
  authenticator_enabled: boolean;
  social_auth: number;
  email: string;
  masked_username: string;
  last_action: number;
  operator: any | null;
  referrer: any | null;
  affiliator: any | null;
  profile: {
    id: number;
    name: string;
    surname: string;
    customer_id: number;
    verification_level: number;
    birthday: string;
    country_id: number | null;
    verified_at: string | null;
    verified_by: string | null;
    residential: string | null;
    city: string;
    postal_code: string | null;
    occupation: string | null;
    ghost_mode: boolean;
    hide_statistics: boolean;
    hide_race_statistics: boolean;
    exclude_rain: boolean;
    receive_marketing_mails: boolean;
    identity_no: string;
    country: any | null;
  };
  phone: {
    id: number;
    full: string;
    code: string;
    number: string;
    customer_id: number;
  };
}

export interface CustomerDetailsResponse {
  data: CustomerDetailsData;
  status: number;
  success: boolean;
}

export interface CustomerSummaryRangeData {
  day: {
    deposit: number | string;
    withdraw: number | string;
  };
  week: {
    deposit: number | string;
    withdraw: number | string;
  };
  month: {
    deposit: number | string;
    withdraw: number | string;
  };
  year: {
    deposit: number | string;
    withdraw: number | string;
  };
}

export interface CustomerSummaryRangeResponse {
  data: CustomerSummaryRangeData;
  status: number;
  success: boolean;
}

export interface CustomerInfoTransaction {
  id: number;
  amount: string;
  currency: string;
  timestamp: number;
  provider: string;
  payment_provider: string | null;
}

export interface CustomerInfoDiscount {
  id: number;
  timestamp: number;
  code: string;
  before_balance: string;
  amount: string;
  after_balance: string;
  depositAmount: string;
}

export interface CustomerInfoData {
  last_deposit: CustomerInfoTransaction | null;
  last_withdraw: CustomerInfoTransaction | null;
  last_discount: CustomerInfoDiscount | null;
  corrections: any[];
  last_bonus: any | null;
}

export interface CustomerInfoResponse {
  data: CustomerInfoData;
  status: number;
  success: boolean;
}

// Profile Update interfaces
export interface ProfileUpdateRequest {
  id: number;
  username: string;
  phone: string;
  country_code: string;
  email: string;
  name: string;
  surname: string;
  occupation: string;
  identity_no: string;
  residential: string;
  birthday: string;
  city: string;
  ghost_mode: boolean;
  hide_statistics: boolean;
  hide_race_statistics: boolean;
  exclude_rain: boolean;
  receive_marketing_mails: boolean;
  rt: number;
}

export interface ProfileUpdateResponse {
  data: string;
  status: number;
  success: boolean;
}

// Password Update interfaces
export interface PasswordUpdateRequest {
  password: string;
  id: number;
  rt: number;
}

export interface PasswordUpdateResponse {
  data: boolean;
  status: number;
  success: boolean;
}

// Referral code update interfaces
export interface ReferralCodeUpdateRequest {
  id: number;
  code: string;
  rt: number;
}

export interface ReferralCodeUpdateResponse {
  data: string;
  status: number;
  success: boolean;
}

export interface CustomerSearchParams {
  id?: string;
  affiliator?: string;
  referrer?: string | null;
  username?: string;
  name?: string;
  birthday?: string;
  surname?: string;
  ref_code?: string;
  identity_no?: string;
  verification_level?: string;
  email?: string;
  ip?: string;
  status_id?: number | null;
  operator_id?: number | null;
  registration_country?: string;
  language?: string | null;
  rank?: string;
  phone?: string;
  register_start?: number | null;
  register_end?: number | null;
  first_deposit_start?: number | null;
  first_deposit_end?: number | null;
  last_deposit_start?: number | null;
  last_deposit_end?: number | null;
  last_withdraw_start?: number | null;
  last_withdraw_end?: number | null;
  last_bonus_start?: number | null;
  last_bonus_end?: number | null;
  last_login_start?: number | null;
  last_login_end?: number | null;
  total_reload_min?: string;
  total_reload_max?: string;
  total_rain_min?: string;
  total_rain_max?: string;
  total_deposit_greater?: string;
  total_deposit_lower?: string;
  total_withdraw_greater?: string;
  total_withdraw_lower?: string;
  total_bonus_drop_min?: string;
  total_bonus_drop_max?: string;
  total_turnover_greater?: string;
  total_turnover_lower?: string;
  net_percentage_min?: string;
  net_percentage_max?: string;
  rakebackMin?: string;
  rakebackMax?: string;
  sortBy?: string | null;
  direction?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch customers data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with customers data
 */
export const fetchCustomers = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<CustomerSearchParams> = {}
): Promise<ApiResponse<CustomersResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      // Default search parameters (can be overridden)
      id: "",
      affiliator: "",
      referrer: null,
      username: "",
      name: "",
      birthday: "",
      surname: "",
      ref_code: "",
      identity_no: "",
      verification_level: "",
      email: "",
      ip: "",
      status_id: null,
      operator_id: null,
      registration_country: "",
      language: null,
      rank: "",
      phone: "",
      register_start: null,
      register_end: null,
      first_deposit_start: null,
      first_deposit_end: null,
      last_deposit_start: null,
      last_deposit_end: null,
      last_withdraw_start: null,
      last_withdraw_end: null,
      last_bonus_start: null,
      last_bonus_end: null,
      last_login_start: null,
      last_login_end: null,
      total_reload_min: "",
      total_reload_max: "",
      total_rain_min: "",
      total_rain_max: "",
      total_deposit_greater: "",
      total_deposit_lower: "",
      total_withdraw_greater: "",
      total_withdraw_lower: "",
      total_bonus_drop_min: "",
      total_bonus_drop_max: "",
      total_turnover_greater: "",
      total_turnover_lower: "",
      net_percentage_min: "",
      net_percentage_max: "",
      rakebackMin: "",
      rakebackMax: "",
      sortBy: null,
      direction: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customers/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch individual customer details by ID
 * @param customerId - Customer ID as string
 * @returns Promise with customer details data
 */
export const fetchCustomerDetails = async (customerId: string): Promise<ApiResponse<CustomerDetailsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customers/show/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        id: customerId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch customer summary range data (deposit/withdraw for day/week/month/year)
 * @param customerId - Customer ID as string
 * @returns Promise with customer summary range data
 */
export const fetchCustomerSummaryRange = async (customerId: string): Promise<ApiResponse<CustomerSummaryRangeResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customers/summary-range/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        id: customerId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch customer info data (last deposit, withdraw, discount, bonus)
 * @param customerId - Customer ID as string
 * @returns Promise with customer info data
 */
export const fetchCustomerInfo = async (customerId: string): Promise<ApiResponse<CustomerInfoResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customers/info/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        customer_id: customerId,
        id: customerId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update customer profile information
 * @param profileId - Profile ID as number (for both URL and request body)
 * @param profileData - Profile data to update
 * @returns Promise with update result
 */
export const updateCustomerProfile = async (
  profileId: number,
  profileData: Omit<ProfileUpdateRequest, 'id' | 'rt'>
): Promise<ApiResponse<ProfileUpdateResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const requestBody: ProfileUpdateRequest = {
      id: profileId,
      ...profileData,
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/profile/update/${profileId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error updating customer profile:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update customer profile'
    };
  }
};

/**
 * Update customer password
 * @param customerId - Customer ID as string
 * @param password - New password
 * @returns Promise with update result
 */
export const updateCustomerPassword = async (customerId: string, password: string): Promise<ApiResponse<PasswordUpdateResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const requestBody: PasswordUpdateRequest = {
      password: password,
      id: parseInt(customerId),
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customers/password/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error updating customer password:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update customer password'
    };
  }
};

/**
 * Update customer referral code
 * @param customerId - Customer ID as string
 * @param referralCode - New referral code
 * @returns Promise with update result
 */
export const updateCustomerReferralCode = async (customerId: string, referralCode: string): Promise<ApiResponse<ReferralCodeUpdateResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const requestBody: ReferralCodeUpdateRequest = {
      id: parseInt(customerId),
      code: referralCode,
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/affiliates/set/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error updating referral code:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update referral code'
    };
  }
};
