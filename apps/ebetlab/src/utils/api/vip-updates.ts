// VIP Updates API endpoints and types
// This module handles all VIP state changes related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// VIP Updates related interfaces
export interface VipUpdateCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: number;
}

export interface VipUpdateData {
  id: number;
  customer_id: number;
  timestamp: string;
  total_wager: string;
  from: string;
  to: string;
  customer: VipUpdateCustomer;
}

export interface VipUpdatesResponse {
  data: {
    page: string;
    total: number;
    data: VipUpdateData[];
  };
  status: number;
  success: boolean;
}

// VIP Rank Assignment interfaces
export interface VipRankAssignmentRequest {
  slug: string;
  add_gift: boolean;
  customer_id: string;
  rt: number;
}

export interface VipRankAssignmentResponse {
  data: boolean;
  status: number;
  success: boolean;
}

/**
 * Fetch customer VIP updates
 * @param customerId - Customer ID as string
 * @returns Promise with VIP updates data
 */
export const fetchVipUpdates = async (customerId: string): Promise<ApiResponse<VipUpdatesResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const requestBody = {
      limit: 100,
      page: 1,
      customer_id: customerId,
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/vip-state-changes/index/1/100`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching VIP updates:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch VIP updates'
    };
  }
};

/**
 * Assign VIP rank to a customer
 * @param customerId - Customer ID as string
 * @param slug - VIP rank slug (e.g., 'no-vip', 'iron', 'gold', etc.)
 * @param addGift - Whether to add a gift with the rank assignment
 * @returns Promise with assignment result
 */
export const assignVipRank = async (
  customerId: string,
  slug: string,
  addGift: boolean = false
): Promise<ApiResponse<VipRankAssignmentResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const requestBody: VipRankAssignmentRequest = {
      slug,
      add_gift: addGift,
      customer_id: customerId,
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/vip-state/rank/${slug}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error assigning VIP rank:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to assign VIP rank'
    };
  }
};
