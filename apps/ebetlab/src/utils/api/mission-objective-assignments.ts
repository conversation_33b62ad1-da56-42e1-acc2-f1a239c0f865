// Mission Objective Assignments API endpoints and types
// This module handles all mission-objective-assignments-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, MissionObjectiveAssignmentsQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Mission Objective Assignments interfaces - Now for User Progress Tracking
export interface MissionObjectiveAssignment {
  id: number;
  missionObjectiveId: number;
  userId: number;
  progress: number;
  lastCheckedRecordTimestamp: number | null;
  createdAt: string;
  updatedAt: string;
  missionObjective?: MissionObjective;
}

export interface Mission {
  id: number;
  name: string;
  missionType: string;
  reward: number;
  description?: string;
  startDate?: number;
  endDate?: number;
}

export interface MissionObjective {
  id: number;
  missionId: number;
  objectiveType: string;
  subtype: string;
  operator: string;
  targetValue: string;
  description?: string;
  timeframeStart?: number | null;
  timeframeEnd?: number | null;
  metadata?: Record<string, any> | null;
}

export interface CreateMissionObjectiveAssignmentRequest {
  userId: number;
  missionObjectiveId: number;
  progress?: number;
  lastCheckedRecordTimestamp?: number | null;
}

export interface AssignUserToMultipleObjectivesRequest {
  userId: number;
  missionObjectiveIds: number[];
}

export interface UpdateUserProgressRequest {
  userId: number;
  missionObjectiveId: number;
  progress: number;
  lastCheckedRecordTimestamp?: number | null;
}

export interface MissionObjectiveAssignmentsResponse {
  success: boolean;
  data: MissionObjectiveAssignment[];
  message?: string;
}

export interface MissionObjectiveAssignmentResponse {
  success: boolean;
  data: MissionObjectiveAssignment;
  message?: string;
}

export interface AssignmentStatsResponse {
  success: boolean;
  data: {
    totalAssignments: number;
    missionsWithObjectives: number;
    objectivesInUse: number;
  };
  message?: string;
}

/**
 * Fetch all mission objective assignments with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated mission objective assignments data
 */
export const fetchMissionObjectiveAssignments = async (queryParams?: MissionObjectiveAssignmentsQueryParams): Promise<PaginatedApiResponse<MissionObjectiveAssignment>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const queryString = queryParams ? buildQueryString(queryParams) : '';
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || [],
      meta: result.meta || {
        page: 1,
        limit: 20,
        total: result.data?.length || 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch all mission objective assignments (legacy function for backward compatibility)
 * @returns Promise with mission objective assignments data
 */
export const fetchMissionObjectiveAssignmentsLegacy = async (): Promise<ApiResponse<MissionObjectiveAssignment[]>> => {
  try {
    const response = await fetchMissionObjectiveAssignments();
    return {
      success: response.success,
      data: response.data,
      error: response.error
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Create a new mission objective assignment
 * @param assignmentData - Mission objective assignment data to create
 * @returns Promise with created mission objective assignment data
 */
export const createMissionObjectiveAssignment = async (assignmentData: CreateMissionObjectiveAssignmentRequest): Promise<ApiResponse<MissionObjectiveAssignment>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(assignmentData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission objective assignment by ID
 * @param id - Assignment ID
 * @returns Promise with mission objective assignment data
 */
export const fetchMissionObjectiveAssignmentById = async (id: number): Promise<ApiResponse<MissionObjectiveAssignment>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get assignments by user ID
 * @param userId - User ID
 * @returns Promise with user's objective assignments data
 */
export const fetchObjectiveAssignmentsByUserId = async (userId: number): Promise<ApiResponse<MissionObjectiveAssignment[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments/user/${userId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get user progress on all objectives for a specific mission
 * @param userId - User ID
 * @param missionId - Mission ID
 * @returns Promise with user's progress on mission objectives
 */
export const fetchUserProgressByMissionId = async (userId: number, missionId: number): Promise<ApiResponse<MissionObjectiveAssignment[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments/user/${userId}/mission/${missionId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * DEPRECATED: Get assignments by mission ID - kept for backward compatibility
 * This function is deprecated as objectives are now directly tied to missions
 * Use fetchMissionObjectivesByMissionId from mission-objectives.ts instead
 * @param missionId - Mission ID
 * @returns Promise with empty array (deprecated functionality)
 */
export const fetchObjectiveAssignmentsByMissionId = async (missionId: number): Promise<ApiResponse<any[]>> => {
  console.warn('fetchObjectiveAssignmentsByMissionId is deprecated. Use fetchMissionObjectivesByMissionId from mission-objectives.ts instead.');
  return {
    success: true,
    data: []
  };
};

/**
 * Get assignments by objective ID
 * @param missionObjectiveId - Mission objective ID
 * @returns Promise with objective's assignments data
 */
export const fetchAssignmentsByObjectiveId = async (missionObjectiveId: number): Promise<ApiResponse<MissionObjectiveAssignment[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments/objective/${missionObjectiveId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete mission objective assignment by ID
 * @param id - Assignment ID
 * @returns Promise with deletion result
 */
export const deleteMissionObjectiveAssignment = async (id: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete assignment by user and objective IDs
 * @param assignmentData - User and objective IDs
 * @returns Promise with deletion result
 */
export const deleteMissionObjectiveAssignmentByIds = async (assignmentData: { userId: number; missionObjectiveId: number }): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
      body: JSON.stringify(assignmentData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Assign user to multiple mission objectives
 * @param assignmentData - User ID and objective IDs to assign
 * @returns Promise with created assignments data
 */
export const assignUserToMultipleObjectives = async (assignmentData: AssignUserToMultipleObjectivesRequest): Promise<ApiResponse<MissionObjectiveAssignment[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments/assign-user`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(assignmentData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update user progress on a mission objective
 * @param progressData - User progress update data
 * @returns Promise with updated assignment data
 */
export const updateUserProgress = async (progressData: UpdateUserProgressRequest): Promise<ApiResponse<MissionObjectiveAssignment>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments/update-progress`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(progressData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get assignment statistics
 * @returns Promise with assignment statistics data
 */
export const fetchObjectiveAssignmentStatistics = async (): Promise<ApiResponse<{ totalAssignments: number; missionsWithObjectives: number; objectivesInUse: number }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objective-assignments/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

// Helper constants for UI components
export const OBJECTIVE_ASSIGNMENT_STATUS_OPTIONS = [
  { value: 'active', label: 'Active Assignment' },
  { value: 'inactive', label: 'Inactive Assignment' }
] as const;

// Helper function to format assignment display
export const getObjectiveAssignmentDisplay = (assignment: MissionObjectiveAssignment) => {
  const objectiveDescription = assignment.missionObjective?.description || `Objective #${assignment.missionObjectiveId}`;
  const progressPercentage = assignment.missionObjective?.targetValue
    ? Math.min(100, (assignment.progress / parseFloat(assignment.missionObjective.targetValue)) * 100)
    : 0;

  return {
    userId: assignment.userId,
    objectiveDescription,
    progress: assignment.progress,
    progressPercentage: Math.round(progressPercentage),
    displayText: `User ${assignment.userId}: ${objectiveDescription} (${assignment.progress} progress)`
  };
};

// Helper function to get assignment status based on progress
export const getObjectiveAssignmentStatus = (assignment: MissionObjectiveAssignment): { status: string; color: string } => {
  if (!assignment.missionObjective?.targetValue) {
    return { status: 'Unknown Target', color: 'text-gray-400' };
  }

  const targetValue = parseFloat(assignment.missionObjective.targetValue);
  const progress = assignment.progress;

  if (progress >= targetValue) {
    return { status: 'Completed', color: 'text-green-400' };
  } else if (progress > 0) {
    return { status: 'In Progress', color: 'text-blue-400' };
  } else {
    return { status: 'Not Started', color: 'text-gray-400' };
  }
};

// DEPRECATED: Backward compatibility functions
export const assignMultipleObjectivesToMission = async (missionId: number, objectivesData: any): Promise<ApiResponse<any[]>> => {
  console.warn('assignMultipleObjectivesToMission is deprecated. Objectives are now directly tied to missions.');
  return { success: true, data: [] };
};

export const replaceAllObjectivesForMission = async (missionId: number, objectivesData: any): Promise<ApiResponse<any[]>> => {
  console.warn('replaceAllObjectivesForMission is deprecated. Objectives are now directly tied to missions.');
  return { success: true, data: [] };
};
