import { getAuthToken, getAuthHeaders } from './common';

// Auto Bonus Rule interfaces
export interface AutoBonusRuleOperator {
  id: number;
  name: string;
  email: string;
}

export interface AutoBonusRuleBonus {
  id: number;
  currency: string;
  name: string;
  description: string;
  typeable_id: number;
  typeable_type: string;
}

export interface AutoBonusRule {
  field: string;
  value: string;
  operator: string;
}

export interface AutoBonusRuleData {
  id: number;
  operator_id: number;
  bonus_id: number;
  rules: AutoBonusRule[];
  is_active: boolean;
  timestamp: number;
  bonus_code: string;
  total: number;
  reference_tag: string;
  claims_count: number;
  operator: AutoBonusRuleOperator;
  bonus: AutoBonusRuleBonus;
  // Additional fields from detailed response
  website_id?: number;
  currency?: string;
  is_deleted?: boolean;
  claims_sum_income?: number;
  claims_sum_usd_income?: number;
  claims_sum_wallet_income?: number;
}

export interface AutoBonusRulesResponse {
  success: boolean;
  message: string;
  data: {
    total: number;
    data: AutoBonusRuleData[];
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface AutoBonusRulesSearchParams {
  id?: number | null;
  operator_id?: number | null;
  bonus_id?: number | null;
  is_active?: boolean | null;
  bonus_code?: string | null;
  sortBy?: string | null;
  direction?: 'asc' | 'desc' | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch auto bonus rules with optional filters and pagination
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param filters - Optional search filters
 * @returns Promise with auto bonus rules data
 */
export const fetchAutoBonusRules = async (
  page: number = 1,
  limit: number = 20,
  filters: AutoBonusRulesSearchParams = {}
): Promise<ApiResponse<AutoBonusRulesResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    queryParams.append('rt', Math.floor(Date.now() / 1000).toString());

    // Add filters to query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/auto-bonus-rules?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching auto bonus rules:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch auto bonus rules'
    };
  }
};

/**
 * Get auto bonus rule details by ID
 * @param ruleId - The ID of the auto bonus rule
 * @returns Promise with auto bonus rule details
 */
export const fetchAutoBonusRuleDetails = async (ruleId: string | number): Promise<ApiResponse<{ data: AutoBonusRuleData }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/auto-bonus-rules/${ruleId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching auto bonus rule details:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch auto bonus rule details'
    };
  }
};

/**
 * Delete auto bonus rule by ID
 * @param ruleId - The ID of the auto bonus rule to delete
 * @returns Promise with deletion result
 */
export const deleteAutoBonusRule = async (ruleId: string | number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/auto-bonus-rules/${ruleId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error deleting auto bonus rule:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete auto bonus rule'
    };
  }
};

// Auto Bonus Rule Claims interfaces
export interface AutoBonusRuleClaim {
  id: number;
  auto_bonus_rule_id: number;
  website_id: number;
  customer_id: number;
  timestamp: number;
  details: string | null;
  freespin_claim_id: number;
  customer: {
    id: number;
    username: string;
    masked_username: string;
    last_action: string | null;
  };
  claim: {
    id: number;
    currency: string;
    is_completed: boolean;
    is_active: boolean;
    income: string;
    usd_income: string;
    wallet_income: string;
  };
}

export interface AutoBonusRuleClaimsResponse {
  data: {
    total: number;
    data: AutoBonusRuleClaim[];
  };
}

/**
 * Fetch auto bonus rule claims
 * @param ruleId - The ID of the auto bonus rule
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @returns Promise with claims data
 */
export const fetchAutoBonusRuleClaims = async (
  ruleId: string | number,
  page: number = 1,
  limit: number = 20
): Promise<ApiResponse<AutoBonusRuleClaimsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/auto-bonus-rules/${ruleId}/claims?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching auto bonus rule claims:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch auto bonus rule claims'
    };
  }
};
