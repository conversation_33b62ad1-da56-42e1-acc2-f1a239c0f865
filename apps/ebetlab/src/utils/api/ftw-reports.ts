// FTW (First Time Withdrawal) Reports API endpoints and types
// This module handles all FTW reports-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// FTW-related interfaces
export interface FTWData {
  customer_id: number;
  username: string;
  registration_ts: number;
  currency: string;
  first_withdrawal_date: number;
  first_withdrawal_id: number;
  first_withdrawal_amount: string;
  first_withdrawal_currency: string;
  first_withdrawal_amount_usd: string;
  total_withdrawal_amount: string;
  total_usd_amount: string;
  phone_full: string | null;
}

export interface FTWResponse {
  data: {
    total_count: number;
    total_sum: string;
    data: FTWData[];
  };
  status: number;
  success: boolean;
}

// Search parameters interface
export interface FTWSearchParams {
  id?: number | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  affiliator?: string | null;
  ref_code?: string | null;
  currency?: string | null;
  operator_id?: number | null;
  type?: string | null;
  is_manuel?: boolean | null;
  status_id?: number | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: 'asc' | 'desc' | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch FTW reports data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with FTW reports data
 */
export const fetchFTWReports = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<FTWSearchParams> = {}
): Promise<ApiResponse<FTWResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Process search parameters to handle empty strings and convert to appropriate types
    const processedParams: Partial<FTWSearchParams> = {};
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        processedParams[key as keyof FTWSearchParams] = value;
      }
    });

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      method: null,
      provider: null,
      username: null,
      affiliator: null,
      ref_code: null,
      currency: null,
      operator_id: null,
      type: null,
      is_manuel: null,
      status_id: null,
      tx: null,
      related: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...processedParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/transactions/ftw/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching FTW reports:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
