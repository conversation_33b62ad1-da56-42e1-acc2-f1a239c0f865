// Final Mission Claims API endpoints and types
// This module handles all final-mission-claims-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, FinalMissionClaimsQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Final Mission Claims interfaces
export interface FinalMissionClaim {
  id: number;
  userId: number;
  claimType: ClaimType;
  grantedReward: number;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: number;
    externalId: number;
    points: number;
    createdAt?: string;
    updatedAt?: string;
  };
}

export type ClaimType = 'daily' | 'weekly' | 'monthly';

export interface CreateFinalMissionClaimRequest {
  userId: number;
  claimType: ClaimType;
  grantedReward: number;
}

export interface UpdateFinalMissionClaimRequest {
  userId?: number;
  claimType?: ClaimType;
  grantedReward?: number;
}

export interface FinalMissionClaimStatistics {
  totalClaims: number;
  dailyClaims: number;
  weeklyClaims: number;
  monthlyClaims: number;
  totalRewardsGranted: number;
}

// Claim type options for dropdowns
export const CLAIM_TYPE_OPTIONS = [
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' }
] as const;

/**
 * Get display text for claim type
 * @param claimType - Claim type value
 * @returns Formatted display text
 */
export const getClaimTypeDisplay = (claimType: ClaimType): string => {
  const option = CLAIM_TYPE_OPTIONS.find(opt => opt.value === claimType);
  return option ? option.label : claimType;
};

/**
 * Fetch all final mission claims with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated final mission claims data
 */
export const fetchFinalMissionClaims = async (queryParams?: FinalMissionClaimsQueryParams): Promise<PaginatedApiResponse<FinalMissionClaim>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const queryString = queryParams ? buildQueryString(queryParams) : '';
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/final-mission-claims${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Handle both direct array response and wrapped response
    if (Array.isArray(result)) {
      return {
        success: true,
        data: result,
        meta: {
          page: 1,
          limit: result.length,
          total: result.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      };
    }

    // Handle wrapped response with pagination
    return {
      success: true,
      data: result.data || [],
      meta: result.pagination || {
        page: 1,
        limit: (result.data || []).length,
        total: (result.data || []).length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  } catch (error) {
    console.error('Error fetching final mission claims:', error);
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 0,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch a single final mission claim by ID
 * @param id - Final mission claim ID
 * @returns Promise with final mission claim data
 */
export const fetchFinalMissionClaimById = async (id: number): Promise<ApiResponse<FinalMissionClaim>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/final-mission-claims/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    console.error('Error fetching final mission claim:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Create a new final mission claim
 * @param claimData - Final mission claim data to create
 * @returns Promise with created final mission claim data
 */
export const createFinalMissionClaim = async (claimData: CreateFinalMissionClaimRequest): Promise<ApiResponse<FinalMissionClaim>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/final-mission-claims`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(claimData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    console.error('Error creating final mission claim:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update an existing final mission claim
 * @param id - Final mission claim ID
 * @param claimData - Final mission claim data to update
 * @returns Promise with updated final mission claim data
 */
export const updateFinalMissionClaim = async (id: number, claimData: UpdateFinalMissionClaimRequest): Promise<ApiResponse<FinalMissionClaim>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/final-mission-claims/${id}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(claimData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    console.error('Error updating final mission claim:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete a final mission claim
 * @param id - Final mission claim ID
 * @returns Promise with deletion result
 */
export const deleteFinalMissionClaim = async (id: number): Promise<ApiResponse<void>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/final-mission-claims/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting final mission claim:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch final mission claim statistics
 * @returns Promise with statistics data
 */
export const fetchFinalMissionClaimStatistics = async (): Promise<ApiResponse<FinalMissionClaimStatistics>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/final-mission-claims/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    console.error('Error fetching final mission claim statistics:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
