// Chat blacklist API
// This module handles API calls related to chat blacklisted customers

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Types for chat blacklist data (reusing customer types with additional fields)
export interface ChatBlacklistProfile {
  id: number;
  name: string;
  surname: string;
  customer_id: number;
  verification_level: number;
}

export interface ChatBlacklistDeposit {
  id: number;
  customer_id: number;
  timestamp: number;
  amount: string;
  currency: string;
  type: number;
  provider: string;
  payment_provider: string | null;
}

export interface ChatBlacklistSummary {
  id: number;
  customer_id: number;
  total_in_usd: string;
  total_out_usd: string;
  percentage: string;
  total_rakeback_usd: string;
  total_reload_usd: string;
  total_bonus_drop_usd: string;
  total_rain_usd: string;
}

export interface ChatBlacklistStatus {
  id: number;
  name: string;
}

export interface ChatBlacklistData {
  id: number;
  email: string;
  username: string;
  last_online_at: number;
  last_ip: string;
  registration_country: string;
  registration_ts: number;
  operator_id: number | null;
  status_id: number;
  is_public: boolean;
  lang: string;
  rank: string;
  rank_percentage: string;
  total_turnover: string;
  next_wager_limit: string;
  chat_muted_at: number | null;
  masked_username: string;
  last_action: number | null;
  profile: ChatBlacklistProfile;
  last_deposit: ChatBlacklistDeposit | null;
  first_deposit: ChatBlacklistDeposit | null;
  summary: ChatBlacklistSummary;
  status: ChatBlacklistStatus;
  operator: any | null;
  blacklists: any[];
}

export interface ChatBlacklistResponse {
  total: number;
  data: ChatBlacklistData[];
}

export interface ChatBlacklistSearchParams {
  id?: number | null;
  username?: string | null;
  name?: string | null;
  verification_level?: number | null;
  email?: string | null;
  ip?: string | null;
  surname?: string | null;
  status_id?: number | null;
  operator_id?: number | null;
  registration_country?: string | null;
  language?: string | null;
  rank?: string | null;
  register_start?: string | null;
  register_end?: string | null;
  first_deposit_start?: string | null;
  first_deposit_end?: string | null;
  last_deposit_start?: string | null;
  last_deposit_end?: string | null;
  last_login_start?: string | null;
  last_login_end?: string | null;
  total_reload_min?: string | null;
  total_reload_max?: string | null;
  total_rain_min?: string | null;
  total_rain_max?: string | null;
  total_deposit_greater?: string | null;
  total_deposit_lower?: string | null;
  total_withdraw_greater?: string | null;
  total_withdraw_lower?: string | null;
  total_bonus_drop_min?: string | null;
  total_bonus_drop_max?: string | null;
  total_turnover_greater?: string | null;
  total_turnover_lower?: string | null;
  net_percentage_min?: string | null;
  net_percentage_max?: string | null;
  rakebackMin?: string | null;
  rakebackMax?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch chat blacklist data with pagination
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with chat blacklist data
 */
export const fetchChatBlacklist = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<ChatBlacklistSearchParams> = {}
): Promise<ApiResponse<ChatBlacklistResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      username: null,
      name: null,
      verification_level: null,
      email: null,
      ip: null,
      surname: null,
      status_id: null,
      operator_id: null,
      registration_country: null,
      language: null,
      rank: null,
      register_start: null,
      register_end: null,
      first_deposit_start: null,
      first_deposit_end: null,
      last_deposit_start: null,
      last_deposit_end: null,
      last_login_start: null,
      last_login_end: null,
      total_reload_min: null,
      total_reload_max: null,
      total_rain_min: null,
      total_rain_max: null,
      total_deposit_greater: null,
      total_deposit_lower: null,
      total_withdraw_greater: null,
      total_withdraw_lower: null,
      total_bonus_drop_min: null,
      total_bonus_drop_max: null,
      total_turnover_greater: null,
      total_turnover_lower: null,
      net_percentage_min: null,
      net_percentage_max: null,
      rakebackMin: null,
      rakebackMax: null,
      sortBy: null,
      direction: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customers/chat/blacklist/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
