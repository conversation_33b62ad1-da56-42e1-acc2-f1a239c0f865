// Bonuses API endpoints and types
// This module handles all bonus-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Bonus related interfaces
export interface BonusGame {
  id: number;
  percentage: number;
}

export interface BonusGameMerchant {
  id: number;
  name: string;
}

export interface BonusTypeable {
  id: number;
  quantity?: number;
  bet_level?: number;
  game_merchant_id?: number;
  aggregator_id?: number;
  game_merchant_name?: string;
  max_win?: string;
  wager_multiplier?: string | null;
  disable_bet_amount?: string | null;
  allowed_min_usd?: string | null;
  allowed_max_usd?: string | null;
  game_merchant?: BonusGameMerchant;
}

// Single bonus view interface (detailed view)
export interface BonusData {
  id: number;
  merchant_id?: number;
  website_id?: number;
  operator_id: number;
  model: string;
  currency: string | BonusCurrency; // Union type to handle both cases
  currency_id: number;
  name: string;
  description: string;
  note?: string;
  from: string;
  to: string;
  product: string;
  timestamp: string;
  email_verification: boolean | null;
  phone_verification: boolean | null;
  request_able: boolean;
  typeable_id?: number;
  typeable_type?: string;
  is_active: boolean;
  disable_withdraw: boolean;
  cancel_able: boolean;
  expire_type?: string;
  expire_unit?: string;
  games?: BonusGame[];
  block_balance?: number;
  is_deleted?: boolean;
  typeable?: BonusTypeable;
  // For list view compatibility
  operator?: BonusOperator;
}

// Operator interface for list view
export interface BonusOperator {
  id: number;
  name: string;
  email: string;
  role_id: number;
}

// Currency interface for list view
export interface BonusCurrency {
  id: number;
  currency_id: number | null;
  name: string;
  alpha: string | null;
  precision: number;
  min_transfer: string;
  network_id: number;
  block_count: number | null;
}

// Base bonus interface for list view
export interface BonusListItem {
  id: number;
  operator_id: number;
  is_active: boolean;
  name: string;
  description: string;
  currency_id: number;
  model: string;
  from: string;
  to: string;
  product: string;
  timestamp: string;
  phone_verification: boolean | null;
  email_verification: boolean | null;
  request_able: boolean;
  disable_withdraw: boolean;
  cancel_able: boolean;
  operator: BonusOperator;
  currency: BonusCurrency;
}

export interface BonusesResponse {
  success: boolean;
  data: {
    total: number;
    data: BonusData[];
  };
  timestamp: string;
}

export interface BonusSearchParams {
  id?: number | null;
  name?: string | null;
  description?: string | null;
  operator_id?: number | null;
  currency?: string | null;
  is_active?: boolean | null;
  model?: string | null;
  product?: string | null;
  to_start?: string | null;
  to_end?: string | null;
  from_start?: string | null;
  from_end?: string | null;
  sortBy?: string | null;
  direction?: 'asc' | 'desc' | null;
  page?: number;
  limit?: number;
  rt?: number;
}

// Create bonus request interface
export interface CreateBonusRequest {
  provider_id: number;
  provider: {
    label: string;
    value: number;
  };
  type: string;
  name: string;
  from: number;
  to: number;
  currency: string;
  product: string;
  maximum_payout?: string;
  maximum_payout_percentage?: string;
  expire_type: string;
  expire_unit: string;
  verified_players: boolean;
  request_able: boolean;
  cancel_able: boolean;
  disable_withdraw: boolean;
  note?: string;
  description?: string;
  multiplier?: string;
  minimum_bet?: string;
  maximum_bet?: string;
  minimum_amount?: string;
  maximum_amount?: string;
  bet_level?: string;
  max_win?: string;
  quantity?: string;
  allowed_min_usd?: string;
  allowed_max_usd?: string;
  disable_bet_amount?: string;
  wager_multiplier?: string;
  games?: Array<{
    id: number;
    percentage: number;
  }>;
}

// Update bonus request interface
export interface UpdateBonusRequest {
  id: number;
  provider_id?: string;
  provider?: string;
  type: string;
  name: string;
  from: number;
  to: number;
  currency: string;
  product: string;
  maximum_payout?: string;
  maximum_payout_percentage?: string;
  expire_type: string;
  expire_unit: string;
  verified_players: boolean;
  request_able: boolean;
  cancel_able: boolean;
  disable_withdraw: boolean;
  note?: string;
  description?: string;
  multiplier?: string;
  minimum_bet?: string;
  maximum_bet?: string;
  minimum_amount?: string;
  maximum_amount?: string;
  bet_level?: number;
  max_win?: string;
  quantity?: number;
  allowed_min_usd?: string | null;
  allowed_max_usd?: string | null;
  wager_multiplier?: string | null;
  disable_bet_amount?: string | null;
  from_edit?: string;
  to_edit?: string;
  selected_games?: Array<{
    id: number;
    percentage: number;
  }>;
  games?: Array<{
    id: number;
    percentage: number;
  }>;
}

/**
 * Fetch bonuses data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with bonuses data
 */
export const fetchBonuses = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<BonusSearchParams> = {}
): Promise<ApiResponse<BonusesResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      rt: Math.floor(Date.now() / 1000).toString(),
      ...Object.fromEntries(
        Object.entries(searchParams).filter(([_, value]) => value !== null && value !== undefined)
      )
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonuses?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching bonuses:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch bonuses'
    };
  }
};

/**
 * Fetch individual bonus details by ID
 * @param bonusId - Bonus ID as string or number
 * @returns Promise with bonus details data
 */
export const fetchBonusDetails = async (bonusId: string | number): Promise<ApiResponse<{ data: BonusData }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonuses/${bonusId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching bonus details:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch bonus details'
    };
  }
};

/**
 * Create a new bonus
 * @param bonusData - Bonus data to create
 * @returns Promise with created bonus data
 */
export const createBonus = async (bonusData: CreateBonusRequest): Promise<ApiResponse<{ data: BonusData }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonuses`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(bonusData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error creating bonus:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create bonus'
    };
  }
};

/**
 * Update an existing bonus
 * @param bonusId - Bonus ID to update
 * @param bonusData - Updated bonus data
 * @returns Promise with updated bonus data
 */
export const updateBonus = async (bonusId: string | number, bonusData: UpdateBonusRequest): Promise<ApiResponse<{ data: BonusData }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonuses/${bonusId}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(bonusData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error updating bonus:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update bonus'
    };
  }
};

/**
 * Delete a bonus
 * @param bonusId - Bonus ID to delete
 * @returns Promise with deletion result
 */
export const deleteBonus = async (bonusId: string | number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonuses/${bonusId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error deleting bonus:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete bonus'
    };
  }
};

// Game interface for fetching games
export interface Game {
  id: number;
  name: string;
  merchant_id: number; // This is the provider ID
  provider_id?: number; // Keep for backward compatibility
  provider_name?: string;
  provider?: string; // Alternative provider name field
  image?: string;
  type?: string;
}

export interface GamesResponse {
  success: boolean;
  data: Game[];
  message?: string;
}

/**
 * Fetch games by providers and type
 * @param providers - Array of provider IDs
 * @param type - Game type (e.g., 'freespin')
 * @returns Promise with games data
 */
export const fetchGamesByProviders = async (providers: number[], type: string = 'freespin'): Promise<ApiResponse<GamesResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    providers.forEach(providerId => {
      queryParams.append('providers', providerId.toString());
    });
    queryParams.append('type', type);

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/games?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching games:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch games'
    };
  }
};

/**
 * Deactivate a bonus by calling the cancellations endpoint
 * @param bonusId - The ID of the bonus to deactivate
 * @returns Promise with API response
 */
export const deactivateBonus = async (bonusId: string | number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonuses/${bonusId}/cancellations`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error deactivating bonus:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to deactivate bonus'
    };
  }
};


