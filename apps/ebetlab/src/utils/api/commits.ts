// Commits API endpoints and types
// This module handles all commits related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Commits related interfaces
export interface CommitCustomer {
  id: number;
  username: string;
  email: string;
  masked_username: string;
  last_action: number;
}

export interface CommitData {
  id: number;
  customer_id: number;
  timestamp: number;
  wallet_currency: string;
  way: string;
  model: string;
  amount: string;
  before_balance: string;
  after_balance: string;
  commitable_id: number;
  commitable_type: string;
  customer: CommitCustomer;
  commitable: any | null;
}

export interface CommitsResponse {
  data: {
    total: number;
    data: CommitData[];
  };
  status: number;
  success: boolean;
}

export interface CommitSearchParams {
  id?: string | null;
  model?: string[] | null;
  way?: string | null;
  currency?: string | null;
  from?: number | null;
  to?: number | null;
  sortBy?: string | null;
  direction?: string | null;
  amount_max?: string | null;
  amount_min?: string | null;
  customer_id?: string;
  page?: number;
  limit?: number;
  rt?: number;
}

// Available commit models
export const COMMIT_MODELS = [
  'debit',
  'debit-trade',
  'bet-cancel',
  'bonus-drop',
  'boost',
  'campaign',
  'correction-up',
  'correction-up-cancel',
  'correction-down',
  'correction-down-cancel',
  'deposit',
  'discount',
  'freespin',
  'initiative-bonus',
  'rain',
  'rakeback',
  'reload',
  'rank-gift',
  'tip',
  'transaction',
  'transaction-cancel',
  'withdraw-cancel',
  'vault-withdraw',
  'vault-deposit',
  'welcome-bonus',
  'tournament-result',
  'race-result',
  'raffle-result',
  'wheel-ticket-prize',
  'unified-balance',
  'unify-reset'
];

/**
 * Fetch commits data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with commits data
 */
export const fetchCommits = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<CommitSearchParams> = {}
): Promise<ApiResponse<CommitsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      model: COMMIT_MODELS, // Default to all models
      way: null,
      currency: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      amount_max: null,
      amount_min: null,
      customer_id: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/commits/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching commits:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch commits'
    };
  }
};
