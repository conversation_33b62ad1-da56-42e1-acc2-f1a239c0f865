// Tips API endpoints and types
// This module handles all tips-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Tips-related interfaces
export interface TipsSearchParams {
  id?: string | null;
  sender?: string | null;
  taker?: string | null;
  currency?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string | null;
}

export interface TipCustomer {
  id: number;
  username: string;
  rank: string;
  masked_username: string;
  last_action: number | null;
}

export interface TippedCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: number | null;
}

export interface TipData {
  id: number;
  customer_id: number;
  tipped_customer_id: number;
  tipped_wallet_id: number;
  amount: string;
  usd_amount: string;
  timestamp: string;
  code: string;
  customer: TipCustomer;
  tipped: TippedCustomer;
}

export interface TipsResponse {
  data: {
    total: number;
    data: TipData[];
  };
  status: number;
  success: boolean;
}

/**
 * Fetch tips data from the API
 * @param page - Page number (default: 1)
 * @param limit - Items per page (default: 20)
 * @param searchParams - Optional search parameters
 * @returns Promise with tips data
 */
export const fetchTips = async (
  page: number = 1,
  limit: number = 20,
  searchParams: TipsSearchParams = {}
): Promise<ApiResponse<TipsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      sender: null,
      taker: null,
      currency: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    console.log('Tips API Request:', {
      url: `${import.meta.env.VITE_API_URL || ''}/api/operator/tips/index/${page}/${limit}`,
      method: 'POST',
      body: requestBody
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/tips/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    console.log('Tips API Response:', result);
    
    return {
      success: true,
      data: result
    }
  } catch (error) {
    console.error('Tips API Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
