// Extended Users API endpoints and types
// This module handles all extended-users-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, ExtendedUsersQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Extended Users interfaces
export interface ExtendedUser {
  id: number;
  externalId: number;
  points: number;
  createdAt: string;
  updatedAt: string;
  externalUsername?: string; // Optional field for username from external system
}

export interface CreateExtendedUserRequest {
  externalId: number;
  points?: number;
}

export interface UpdatePointsRequest {
  points: number;
}

export interface UpdateExtendedUserRequest {
  externalId?: number;
  points?: number;
  pointsIncrement?: number;
}

export interface UpsertExtendedUserRequest {
  externalId: number;
  points: number;
}

export interface ExtendedUsersResponse {
  success: boolean;
  data: ExtendedUser[];
  message?: string;
}

export interface ExtendedUserResponse {
  success: boolean;
  data: ExtendedUser;
  message?: string;
}

export interface DeleteExtendedUserResponse {
  success: boolean;
  message: string;
}

export interface ExtendedUserStatistics {
  totalUsers: number;
  totalPoints: number;
  averagePoints: number;
}

// API Response interface for extended users with pagination
interface ExtendedUsersApiResponse {
  success: boolean;
  message: string;
  data: ExtendedUser[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Create a new extended user
 * @param userData - User data to create
 * @returns Promise with created user data
 */
export const createExtendedUser = async (userData: CreateExtendedUserRequest): Promise<ApiResponse<ExtendedUser>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/extended-users`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Create extended user failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create extended user'
    };
  }
};

/**
 * Get all extended users with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated extended users data
 */
export const fetchExtendedUsers = async (queryParams?: ExtendedUsersQueryParams): Promise<PaginatedApiResponse<ExtendedUser>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const queryString = queryParams ? buildQueryString(queryParams) : '';
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/extended-users${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Transform the API response to match our PaginatedApiResponse interface
    // The API returns pagination data in 'pagination' field, not 'meta'
    return {
      success: result.success || true,
      data: result.data || [],
      meta: result.pagination ? {
        page: result.pagination.page,
        limit: result.pagination.limit,
        total: result.pagination.total,
        totalPages: result.pagination.totalPages,
        hasNext: result.pagination.page < result.pagination.totalPages,
        hasPrev: result.pagination.page > 1
      } : {
        page: 1,
        limit: 20,
        total: result.data?.length || 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  } catch (error) {
    console.error('Fetch extended users failed:', error);
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Failed to fetch extended users'
    };
  }
};

/**
 * Get all extended users (legacy function for backward compatibility)
 * @returns Promise with all extended users
 */
export const fetchExtendedUsersLegacy = async (): Promise<ApiResponse<ExtendedUser[]>> => {
  try {
    const response = await fetchExtendedUsers();
    return {
      success: response.success,
      data: response.data,
      error: response.error
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch extended users'
    };
  }
};

/**
 * Get extended user by ID
 * @param id - User ID
 * @returns Promise with user data
 */
export const fetchExtendedUserById = async (id: number): Promise<ApiResponse<ExtendedUser>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/extended-users/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    console.error('Fetch extended user failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch extended user'
    };
  }
};

/**
 * Get extended user by external ID (for lookup purposes)
 * @param externalId - External user ID
 * @returns Promise with user data
 */
export const fetchExtendedUserByExternalId = async (externalId: number): Promise<ApiResponse<ExtendedUser>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/extended-users/external/${externalId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    console.error('Error fetching extended user by external ID:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update extended user (partial update)
 * @param id - User ID
 * @param userData - Updated user data (partial)
 * @returns Promise with updated user data
 */
export const updateExtendedUser = async (id: number, userData: UpdateExtendedUserRequest): Promise<ApiResponse<ExtendedUser>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/extended-users/${id}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update user points (DEPRECATED - use updateExtendedUser with points field)
 * @param id - User ID
 * @param pointsData - New points value
 * @returns Promise with updated user data
 * @deprecated Use updateExtendedUser(id, { points }) instead
 */
export const updateUserPoints = async (id: number, pointsData: UpdatePointsRequest): Promise<ApiResponse<ExtendedUser>> => {
  console.warn('updateUserPoints is deprecated. Use updateExtendedUser(id, { points }) instead.');

  try {
    // Use the new PATCH approach with unified update endpoint
    const response = await updateExtendedUser(id, { points: pointsData.points });

    if (response.success) {
      return response;
    } else {
      return {
        success: false,
        error: response.error || 'Failed to update user points'
      };
    }
  } catch (error) {
    console.error('Update user points failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update user points'
    };
  }
};

/**
 * Add points to user (DEPRECATED - use frontend calculation with updateExtendedUser)
 * @param id - User ID
 * @param pointsData - Points to add (can be negative)
 * @returns Promise with updated user data
 * @deprecated Calculate new points on frontend and use updateExtendedUser(id, { points: newTotal }) instead
 */
export const addUserPoints = async (id: number, pointsData: UpdatePointsRequest): Promise<ApiResponse<ExtendedUser>> => {
  console.warn('addUserPoints is deprecated. Calculate new points on frontend and use updateExtendedUser(id, { points: newTotal }) instead.');

  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Use the new PATCH approach with increment logic
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/extended-users/${id}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        pointsIncrement: pointsData.points // Use increment field instead of absolute points
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    console.error('Add user points failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to add user points'
    };
  }
};

/**
 * Create or update user (DEPRECATED - use createExtendedUser or updateExtendedUser)
 * @param userData - User data to upsert
 * @returns Promise with user data
 * @deprecated Use createExtendedUser for new users or updateExtendedUser for existing users instead
 */
export const upsertExtendedUser = async (userData: UpsertExtendedUserRequest): Promise<ApiResponse<ExtendedUser>> => {
  console.warn('upsertExtendedUser is deprecated. Use createExtendedUser for new users or updateExtendedUser for existing users instead.');

  try {
    // First try to find the user by external ID
    const existingUserResponse = await fetchExtendedUserByExternalId(userData.externalId);

    if (existingUserResponse.success && existingUserResponse.data) {
      // User exists, update it
      const updateResponse = await updateExtendedUser(existingUserResponse.data.id, {
        points: userData.points
      });
      return updateResponse;
    } else {
      // User doesn't exist, create it
      const createResponse = await createExtendedUser({
        externalId: userData.externalId,
        points: userData.points
      });
      return createResponse;
    }
  } catch (error) {
    console.error('Upsert extended user failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upsert extended user'
    };
  }
};

/**
 * Delete extended user
 * @param id - User ID
 * @returns Promise with deletion result
 */
export const deleteExtendedUser = async (id: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/extended-users/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: { message: result.message || 'Extended user deleted successfully' }
    };
  } catch (error) {
    console.error('Delete extended user failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete extended user'
    };
  }
};

/**
 * Fetch extended user points statistics
 * @returns Promise with statistics data
 */
export const fetchExtendedUserStatistics = async (): Promise<ApiResponse<ExtendedUserStatistics>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/extended-users/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Fetch extended user statistics failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch extended user statistics'
    };
  }
};


