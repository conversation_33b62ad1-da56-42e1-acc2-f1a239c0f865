// Mission Objectives API endpoints and types
// This module handles all mission-objectives-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, MissionObjectivesQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Mission Objectives interfaces
export interface MissionObjective {
  id: number;
  missionId: number;
  objectiveType: ObjectiveType;
  subtype: string;
  operator: ObjectiveOperator;
  targetValue: string;
  description: string;
  timeframeStart: number | null;
  timeframeEnd: number | null;
  metadata?: Record<string, any> | null;
  createdAt: string;
  updatedAt: string;
}

export type ObjectiveType =
  | 'slot'
  // | 'liveCasino'
  | 'deposit'
  // | 'withdraw'
  | 'turnover';

export type ObjectiveOperator = 
  | 'eq'  // Equal to
  | 'ne'  // Not equal to
  | 'gt'  // Greater than
  | 'lt'  // Less than
  | 'ge'  // Greater than or equal to
  | 'le'; // Less than or equal to

export interface CreateMissionObjectiveRequest {
  missionId: number;
  objectiveType: ObjectiveType;
  subtype: string;
  operator: ObjectiveOperator;
  targetValue: string;
  description: string;
  timeframeStart?: number | null;
  timeframeEnd?: number | null;
  metadata?: Record<string, any> | null;
}

export interface UpdateMissionObjectiveRequest {
  missionId?: number;
  objectiveType?: ObjectiveType;
  subtype?: string;
  operator?: ObjectiveOperator;
  targetValue?: string;
  description?: string;
  timeframeStart?: number | null;
  timeframeEnd?: number | null;
  metadata?: Record<string, any> | null;
}

export interface MissionObjectiveResponse {
  success: boolean;
  data: MissionObjective;
  message?: string;
}

export interface MissionObjectivesResponse {
  success: boolean;
  data: MissionObjective[];
  message?: string;
}

/**
 * Fetch all mission objectives with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated mission objectives data
 */
export const fetchMissionObjectives = async (queryParams?: MissionObjectivesQueryParams): Promise<PaginatedApiResponse<MissionObjective>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Validate and sanitize query parameters
    const validatedParams = queryParams ? validateMissionObjectivesQueryParams(queryParams) : {};
    const queryString = Object.keys(validatedParams).length > 0 ? buildQueryString(validatedParams) : '';

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objectives${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || [],
      meta: result.meta || {
        page: 1,
        limit: 20,
        total: result.data?.length || 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch all mission objectives (legacy function for backward compatibility)
 * @returns Promise with mission objectives data
 */
export const fetchMissionObjectivesLegacy = async (): Promise<ApiResponse<MissionObjective[]>> => {
  try {
    const response = await fetchMissionObjectives();
    return {
      success: response.success,
      data: response.data,
      error: response.error
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Create a new mission objective
 * @param objectiveData - Mission objective data to create
 * @returns Promise with created mission objective data
 */
export const createMissionObjective = async (objectiveData: CreateMissionObjectiveRequest): Promise<ApiResponse<MissionObjective>> => {
  try {
    console.log('createMissionObjective called with:', objectiveData);
    const token = getAuthToken();
    console.log('Auth token:', token ? 'present' : 'missing');

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const url = `${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objectives`;
    console.log('Making request to:', url);
    console.log('Request headers:', getAuthHeaders());
    console.log('Request body:', JSON.stringify(objectiveData));

    const response = await fetch(url, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(objectiveData)
    });

    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Response error text:', errorText);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('Response result:', result);
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    console.log('createMissionObjective error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission objective by ID
 * @param id - Mission objective ID
 * @returns Promise with mission objective data
 */
export const fetchMissionObjectiveById = async (id: number): Promise<ApiResponse<MissionObjective>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objectives/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update mission objective (partial update)
 * @param id - Mission objective ID
 * @param objectiveData - Mission objective data to update (partial)
 * @returns Promise with updated mission objective data
 */
export const updateMissionObjective = async (id: number, objectiveData: UpdateMissionObjectiveRequest): Promise<ApiResponse<MissionObjective>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objectives/${id}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(objectiveData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete mission objective
 * @param id - Mission objective ID
 * @returns Promise with deletion result
 */
export const deleteMissionObjective = async (id: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objectives/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission objectives by type
 * @param objectiveType - Objective type to filter by
 * @returns Promise with filtered mission objectives data
 */
export const fetchMissionObjectivesByType = async (objectiveType: ObjectiveType): Promise<ApiResponse<MissionObjective[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objectives/type/${objectiveType}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission objectives by mission ID
 * @param missionId - Mission ID to get objectives for
 * @returns Promise with mission's objectives data
 */
export const fetchMissionObjectivesByMissionId = async (missionId: number): Promise<ApiResponse<{ objectives: MissionObjective[] }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-objectives/mission/${missionId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

// Helper constants for UI components
export const OBJECTIVE_TYPE_OPTIONS = [
  { value: 'slot', label: 'Slot Games' },
  // { value: 'liveCasino', label: 'Live Casino' },
  { value: 'deposit', label: 'Deposit' },
  // { value: 'withdraw', label: 'Withdraw' },
  { value: 'turnover', label: 'Turnover' }
] as const;

export const OBJECTIVE_OPERATOR_OPTIONS = [
  { value: 'eq', label: 'Equal to (=)' },
  { value: 'ne', label: 'Not equal to (≠)' },
  { value: 'gt', label: 'Greater than (>)' },
  { value: 'lt', label: 'Less than (<)' },
  { value: 'ge', label: 'Greater than or equal to (≥)' },
  { value: 'le', label: 'Less than or equal to (≤)' }
] as const;

// Subtype options for each objective type
export const OBJECTIVE_SUBTYPE_OPTIONS = {
  slot: [
    { value: 'total_wins', label: 'Slot Wins' },
    { value: 'win_multiplier', label: 'Big Wins (5x+ bet)' },
    { value: 'spins', label: 'Slot Spins' },
    { value: 'total_turnover', label: 'Turnover' },
  ],
  // liveCasino: [
  //   { value: 'winningHands', label: 'Winning Hands' },
  //   { value: 'gamesPlayed', label: 'Games Played' },
  //   { value: 'consecutiveWins', label: 'Consecutive Wins' },
  //   { value: 'highStakes', label: 'High Stakes Games' }
  // ],
  deposit: [
    { value: 'amount', label: 'Deposit Amount' },
    // { value: 'frequency', label: 'Deposit Frequency' },
    // { value: 'firstDeposit', label: 'First Deposit' },
    // { value: 'largeDeposit', label: 'Large Deposit (>$100)' }
  ],
  // withdraw: [
  //   { value: 'amount', label: 'Withdrawal Amount' },
  //   { value: 'frequency', label: 'Withdrawal Frequency' },
  //   { value: 'fastWithdraw', label: 'Fast Withdrawal' },
  //   { value: 'successfulWithdraw', label: 'Successful Withdrawal' }
  // ],
  turnover: [
    { value: 'total_turnover', label: 'Total Turnover' },
    // { value: 'dailyTurnover', label: 'Daily Turnover' },
    // { value: 'weeklyTurnover', label: 'Weekly Turnover' },
    // { value: 'gameSpecific', label: 'Game-Specific Turnover' }
  ]
} as const;

// Helper function to get subtype options for a specific objective type
export const getSubtypeOptions = (objectiveType: ObjectiveType) => {
  return OBJECTIVE_SUBTYPE_OPTIONS[objectiveType] || [];
};

// Valid sortBy fields for mission objectives
export const MISSION_OBJECTIVES_SORT_FIELDS = [
  'id',
  'missionId',
  'objectiveType',
  'subtype',
  'operator',
  'targetValue',
  'createdAt',
  'updatedAt'
] as const;

// Validation constraints
export const MISSION_OBJECTIVES_CONSTRAINTS = {
  MAX_LIMIT: 100,
  DEFAULT_LIMIT: 20,
  DEFAULT_PAGE: 1,
  DEFAULT_SORT_BY: 'createdAt',
  DEFAULT_SORT_ORDER: 'DESC'
} as const;

/**
 * Validate and sanitize mission objectives query parameters
 * @param params - Raw query parameters
 * @returns Validated and sanitized parameters
 */
export const validateMissionObjectivesQueryParams = (params: any): MissionObjectivesQueryParams => {
  const validated: MissionObjectivesQueryParams = {};

  // Pagination validation
  if (params.page !== undefined) {
    const page = parseInt(params.page);
    validated.page = page >= 1 ? page : MISSION_OBJECTIVES_CONSTRAINTS.DEFAULT_PAGE;
  }

  if (params.limit !== undefined) {
    const limit = parseInt(params.limit);
    validated.limit = limit >= 1 && limit <= MISSION_OBJECTIVES_CONSTRAINTS.MAX_LIMIT
      ? limit
      : MISSION_OBJECTIVES_CONSTRAINTS.DEFAULT_LIMIT;
  }

  // Sorting validation
  if (params.sortBy && MISSION_OBJECTIVES_SORT_FIELDS.includes(params.sortBy)) {
    validated.sortBy = params.sortBy;
  }

  if (params.sortOrder && ['ASC', 'DESC', 'asc', 'desc'].includes(params.sortOrder)) {
    validated.sortOrder = params.sortOrder.toUpperCase() as 'ASC' | 'DESC';
  }

  // Filter validation
  if (params.missionId !== undefined) {
    const missionId = parseInt(params.missionId);
    if (!isNaN(missionId) && missionId > 0) {
      validated.missionId = missionId;
    }
  }

  // Enum filters validation
  if (params.objectiveType && OBJECTIVE_TYPE_OPTIONS.some(opt => opt.value === params.objectiveType)) {
    validated.objectiveType = params.objectiveType;
  }

  if (params.operator && OBJECTIVE_OPERATOR_OPTIONS.some(opt => opt.value === params.operator)) {
    validated.operator = params.operator;
  }

  // Text filters (no validation needed, just pass through)
  if (params.subtype) {
    validated.subtype = String(params.subtype);
  }

  if (params.targetValue) {
    validated.targetValue = String(params.targetValue);
  }

  if (params.description) {
    validated.description = String(params.description);
  }

  if (params.search) {
    validated.search = String(params.search);
  }

  // Timeframe filters validation (must be non-negative integers)
  ['timeframeStartFrom', 'timeframeStartTo', 'timeframeEndFrom', 'timeframeEndTo'].forEach(field => {
    if (params[field] !== undefined) {
      const timestamp = parseInt(params[field]);
      if (!isNaN(timestamp) && timestamp >= 0) {
        (validated as any)[field] = timestamp;
      }
    }
  });

  // Legacy parameters (for backward compatibility)
  if (params.name) {
    validated.name = String(params.name);
  }

  if (params.hasTimeframe !== undefined) {
    validated.hasTimeframe = Boolean(params.hasTimeframe);
  }

  return validated;
};

// Helper function to format objective display
export const getObjectiveDisplay = (objective: MissionObjective) => {
  const operatorSymbol = OBJECTIVE_OPERATOR_OPTIONS.find(op => op.value === objective.operator)?.label || objective.operator;
  const typeLabel = OBJECTIVE_TYPE_OPTIONS.find(type => type.value === objective.objectiveType)?.label || objective.objectiveType;

  return {
    missionId: objective.missionId,
    typeLabel,
    operatorSymbol,
    targetValue: objective.targetValue,
    displayText: `${typeLabel}: ${operatorSymbol} ${objective.targetValue}`,
    description: objective.description
  };
};

// Helper function to get objective status display
export const getObjectiveStatusDisplay = (objective: MissionObjective): { status: string; color: string } => {
  const now = Date.now();
  
  if (objective.timeframeStart && now < objective.timeframeStart) {
    return { status: 'Upcoming', color: 'text-blue-400' };
  }
  
  if (objective.timeframeEnd && now > objective.timeframeEnd) {
    return { status: 'Expired', color: 'text-red-400' };
  }
  
  if (objective.timeframeStart && objective.timeframeEnd) {
    return { status: 'Active', color: 'text-green-400' };
  }
  
  return { status: 'Always Active', color: 'text-gray-400' };
};
