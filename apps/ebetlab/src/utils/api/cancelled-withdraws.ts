// Cancelled Withdraws API endpoints and types
// This module handles all cancelled withdraws-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Cancelled withdraws-related interfaces
export interface CancelledWithdrawSearchParams {
  currency?: string | null;
  from?: number | null;
  to?: number | null;
  is_manuel?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export interface CancelledWithdrawStatus {
  id: number;
  name: string;
  key: string;
}

export interface CancelledWithdrawOperator {
  id: number;
  name: string;
  email: string;
  role_id: number;
}

export interface CancelledWithdrawLastAction {
  id: number;
  operator_id: number;
  transaction_id: number;
  action: string;
  note: string;
  operator: {
    id: number;
    name: string;
  };
}

export interface CancelledWithdrawCustomer {
  id: number;
  username: string;
  affiliator_id: number;
  ref_id: number | null;
  ref_code: string;
  masked_username: string;
  last_action: number;
  profile: {
    id: number;
    name: string;
    surname: string;
    customer_id: number;
  };
  affiliator: {
    id: number;
    name: string;
  };
}

export interface CancelledWithdrawPaymentProvider {
  id: number;
  name: string;
}

export interface CancelledWithdrawApproveDetails {
  id: number;
  rt: number;
  note: string;
}

export interface CancelledWithdrawData {
  id: number;
  method: string;
  payment_provider_id: number;
  discount_able: boolean;
  approve_details: CancelledWithdrawApproveDetails;
  network: string | null;
  description: string | null;
  customer_id: number;
  is_manuel: number;
  completed_at: string | null;
  target_address: string;
  unique_id: string;
  operator_id: number;
  currency: string;
  wallet_id: number;
  amount: string;
  usd_amount: string;
  status_id: number;
  timestamp: number;
  type: number;
  tx_id: string;
  aml_met: boolean;
  provider: string;
  status: CancelledWithdrawStatus;
  discount: any | null;
  operator: CancelledWithdrawOperator;
  last_action: CancelledWithdrawLastAction;
  customer: CancelledWithdrawCustomer;
  payment_provider: CancelledWithdrawPaymentProvider;
}

export interface CancelledWithdrawsResponse {
  success: boolean;
  message: string;
  data: {
    total: number;
    data: CancelledWithdrawData[];
  };
  timestamp: string;
}

/**
 * Fetch cancelled withdraws data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with cancelled withdraws data
 */
export const fetchCancelledWithdraws = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<CancelledWithdrawSearchParams> = {}
): Promise<ApiResponse<CancelledWithdrawsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      currency: "",
      status_id: 3, // Important: status_id 3 for cancelled
      from: 1749699520, // Default from timestamp
      to: "",
      is_manuel: "all",
      type: "withdrawal", // Important: type withdrawal
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    console.log('Cancelled Withdraws API Request:', {
      url: `${import.meta.env.VITE_API_URL || ''}/api/operator/transactions/index/${page}/${limit}`,
      method: 'POST',
      body: requestBody
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/transactions/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Failed to fetch cancelled withdraws:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
