// Mission Rules API endpoints and types
// This module handles all mission-rules-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, MissionRulesQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Mission Rules interfaces
export interface MissionRule {
  id: number;
  ruleType: RuleType;
  compare: CompareOperator;
  compareValue: string;
  minDate: number | null;
  maxDate: number | null;
  createdAt: string;
  updatedAt: string;
}

export type RuleType = 
  | 'joinedAt'
  | 'totalDeposit'
  | 'totalWithdraw'
  | 'netProfit'
  | 'vipRank'
  | 'country'
  | 'phoneNumber'
  | 'kycLevel'
  | 'referrer';

export type CompareOperator = 
  | 'eq'  // Equal to
  | 'ne'  // Not equal to
  | 'gt'  // Greater than
  | 'lt'  // Less than
  | 'ge'  // Greater than or equal to
  | 'le'; // Less than or equal to

export interface CreateMissionRuleRequest {
  ruleType: RuleType;
  compare: CompareOperator;
  compareValue: string;
  minDate?: number | null;
  maxDate?: number | null;
}

export interface UpdateMissionRuleRequest {
  ruleType?: RuleType;
  compare?: CompareOperator;
  compareValue?: string;
  minDate?: number | null;
  maxDate?: number | null;
}

export interface MissionRulesResponse {
  success: boolean;
  data: MissionRule[];
  message?: string;
}

export interface MissionRuleResponse {
  success: boolean;
  data: MissionRule;
  message?: string;
}

/**
 * Fetch all mission rules with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated mission rules data
 */
export const fetchMissionRules = async (queryParams?: MissionRulesQueryParams): Promise<PaginatedApiResponse<MissionRule>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Validate and sanitize query parameters
    const validatedParams = queryParams ? validateMissionRulesQueryParams(queryParams) : {};
    const queryString = Object.keys(validatedParams).length > 0 ? buildQueryString(validatedParams) : '';

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rules${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || [],
      meta: result.meta || {
        page: 1,
        limit: 20,
        total: result.data?.length || 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch all mission rules (legacy function for backward compatibility)
 * @returns Promise with mission rules data
 */
export const fetchMissionRulesLegacy = async (): Promise<ApiResponse<MissionRule[]>> => {
  try {
    const response = await fetchMissionRules();
    return {
      success: response.success,
      data: response.data,
      error: response.error
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Create a new mission rule
 * @param ruleData - Mission rule data to create
 * @returns Promise with created mission rule data
 */
export const createMissionRule = async (ruleData: CreateMissionRuleRequest): Promise<ApiResponse<MissionRule>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rules`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(ruleData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission rule by ID
 * @param id - Mission rule ID
 * @returns Promise with mission rule data
 */
export const fetchMissionRuleById = async (id: number): Promise<ApiResponse<MissionRule>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rules/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update mission rule by ID (partial update)
 * @param id - Mission rule ID
 * @param ruleData - Updated mission rule data (partial)
 * @returns Promise with updated mission rule data
 */
export const updateMissionRule = async (id: number, ruleData: UpdateMissionRuleRequest): Promise<ApiResponse<MissionRule>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rules/${id}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(ruleData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete mission rule by ID
 * @param id - Mission rule ID
 * @returns Promise with deletion result
 */
export const deleteMissionRule = async (id: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rules/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission rules by type (DEPRECATED - use fetchMissionRules with ruleType query parameter)
 * @param ruleType - Rule type to filter by
 * @returns Promise with filtered mission rules data
 * @deprecated Use fetchMissionRules({ ruleType }) instead
 */
export const fetchMissionRulesByType = async (ruleType: RuleType): Promise<ApiResponse<MissionRule[]>> => {
  console.warn('fetchMissionRulesByType is deprecated. Use fetchMissionRules({ ruleType }) instead.');

  try {
    // Use the new query parameter approach
    const response = await fetchMissionRules({ ruleType });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.error || 'Failed to fetch mission rules by type'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

// Helper constants for UI components
export const RULE_TYPE_OPTIONS = [
  { value: 'joinedAt', label: 'Joined At' },
  { value: 'totalDeposit', label: 'Total Deposit' },
  { value: 'totalWithdraw', label: 'Total Withdraw' },
  { value: 'netProfit', label: 'Net Profit' },
  { value: 'vipRank', label: 'VIP Rank' },
  { value: 'country', label: 'Country' },
  { value: 'phoneNumber', label: 'Phone Number' },
  { value: 'kycLevel', label: 'KYC Level' },
  { value: 'referrer', label: 'Referrer' }
] as const;

export const COMPARE_OPERATOR_OPTIONS = [
  { value: 'eq', label: 'Equal to (=)' },
  { value: 'ne', label: 'Not equal to (≠)' },
  { value: 'gt', label: 'Greater than (>)' },
  { value: 'lt', label: 'Less than (<)' },
  { value: 'ge', label: 'Greater than or equal to (≥)' },
  { value: 'le', label: 'Less than or equal to (≤)' }
] as const;

// Valid sortBy fields for mission rules
export const MISSION_RULES_SORT_FIELDS = [
  'id',
  'ruleType',
  'compare',
  'compareValue',
  'minDate',
  'maxDate',
  'createdAt',
  'updatedAt'
] as const;

// Validation constraints
export const MISSION_RULES_CONSTRAINTS = {
  MAX_LIMIT: 100,
  DEFAULT_LIMIT: 20,
  DEFAULT_PAGE: 1
} as const;

/**
 * Validate and sanitize mission rules query parameters
 * @param params - Raw query parameters
 * @returns Validated and sanitized parameters
 */
export const validateMissionRulesQueryParams = (params: any): MissionRulesQueryParams => {
  const validated: MissionRulesQueryParams = {};

  // Pagination validation
  if (params.page !== undefined) {
    const page = parseInt(params.page);
    validated.page = page >= 1 ? page : MISSION_RULES_CONSTRAINTS.DEFAULT_PAGE;
  }

  if (params.limit !== undefined) {
    const limit = parseInt(params.limit);
    validated.limit = limit >= 1 && limit <= MISSION_RULES_CONSTRAINTS.MAX_LIMIT
      ? limit
      : MISSION_RULES_CONSTRAINTS.DEFAULT_LIMIT;
  }

  // Sorting validation
  if (params.sortBy && MISSION_RULES_SORT_FIELDS.includes(params.sortBy)) {
    validated.sortBy = params.sortBy;
  }

  if (params.sortOrder && ['ASC', 'DESC', 'asc', 'desc'].includes(params.sortOrder)) {
    validated.sortOrder = params.sortOrder.toUpperCase() as 'ASC' | 'DESC';
  }

  // Enum filters validation
  if (params.ruleType && RULE_TYPE_OPTIONS.some(opt => opt.value === params.ruleType)) {
    validated.ruleType = params.ruleType;
  }

  if (params.compare && COMPARE_OPERATOR_OPTIONS.some(opt => opt.value === params.compare)) {
    validated.compare = params.compare;
  }

  // Text filters (no validation needed, just pass through)
  if (params.compareValue) {
    validated.compareValue = String(params.compareValue);
  }

  if (params.search) {
    validated.search = String(params.search);
  }

  // Date range filters validation (must be non-negative integers)
  ['minDateFrom', 'minDateTo', 'maxDateFrom', 'maxDateTo'].forEach(field => {
    if (params[field] !== undefined) {
      const timestamp = parseInt(params[field]);
      if (!isNaN(timestamp) && timestamp >= 0) {
        (validated as any)[field] = timestamp;
      }
    }
  });

  // Legacy parameters (for backward compatibility)
  if (params.compareOperator) {
    validated.compareOperator = String(params.compareOperator);
  }

  if (params.compareValueMin !== undefined) {
    const val = parseFloat(params.compareValueMin);
    if (!isNaN(val)) {
      validated.compareValueMin = val;
    }
  }

  if (params.compareValueMax !== undefined) {
    const val = parseFloat(params.compareValueMax);
    if (!isNaN(val)) {
      validated.compareValueMax = val;
    }
  }

  if (params.hasDateRange !== undefined) {
    validated.hasDateRange = Boolean(params.hasDateRange);
  }

  return validated;
};
