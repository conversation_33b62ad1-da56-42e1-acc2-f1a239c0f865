// Market Product Requests API endpoints and types
// This module handles all market-product-requests-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, BaseQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Import the new types from market-products
import { type MarketSelectedProvider } from './market-products';

// Market Product Requests interfaces
export interface MarketProductRequest {
  id: number;
  status: 'pending' | 'completed' | 'rejected' | 'refunded';
  currency: string;
  providers: MarketSelectedProvider[] | Record<string, number[]> | null; // Support both new and legacy formats
  userId: number;
  productId: number;
  history: MarketProductRequestHistory[];
  rejectReason: string | null;
  createdAt: string;
  updatedAt: string;
  user: {
    id: number;
    externalId: number;
    points: number;
  };
  product: {
    id: number;
    name: string;
    slug: string;
    type: 'general' | 'slots';
    category: 'free_spins' | 'cash' | 'reload' | 'scatter';
    price: number;
    photoUrl: string | null;
  };
}

export interface MarketProductRequestHistory {
  status: string;
  timestamp: number;
  message: string;
}

// Request/Response interfaces
export interface RejectMarketProductRequestRequest {
  reason: string;
}

export interface RefundMarketProductRequestRequest {
  reason: string;
}

// Query parameters interface
export interface MarketProductRequestsQueryParams extends BaseQueryParams {
  status?: 'pending' | 'completed' | 'rejected' | 'refunded';
  statuses?: string; // comma-separated statuses
  userId?: number;
  productId?: number;
  currency?: string;
  currencies?: string; // comma-separated currencies
  createdFrom?: string; // ISO date string
  createdTo?: string; // ISO date string
  updatedFrom?: string; // ISO date string
  updatedTo?: string; // ISO date string
  search?: string;
  productType?: 'general' | 'slots';
  productCategory?: 'free_spins' | 'cash' | 'reload' | 'scatter';
  minPrice?: number;
  maxPrice?: number;
  hasRejectReason?: boolean;
  sortBy?: 'createdAt' | 'updatedAt' | 'status' | 'userId' | 'productId' | 'currency' | 'product.name' | 'product.price' | 'product.type' | 'product.category';
  sortOrder?: 'ASC' | 'DESC';
}

// Market Product Requests API specific response interface
interface MarketProductRequestsApiResponse {
  success: boolean;
  data: MarketProductRequest[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
}

// Constants for UI components
export const MARKET_PRODUCT_REQUEST_STATUS_OPTIONS = [
  { value: 'pending', label: 'Pending' },
  { value: 'completed', label: 'Completed' },
  { value: 'rejected', label: 'Rejected' },
  { value: 'refunded', label: 'Refunded' }
] as const;

export const PRODUCT_TYPE_OPTIONS = [
  { value: 'general', label: 'General' },
  { value: 'slots', label: 'Slots' }
] as const;

export const PRODUCT_CATEGORY_OPTIONS = [
  { value: 'free_spins', label: 'Free Spins' },
  { value: 'cash', label: 'Cash' },
  { value: 'reload', label: 'Reload' },
  { value: 'scatter', label: 'Scatter' }
] as const;

export const MARKET_PRODUCT_REQUESTS_SORT_FIELDS = [
  { value: 'createdAt', label: 'Created Date' },
  { value: 'updatedAt', label: 'Updated Date' },
  { value: 'status', label: 'Status' },
  { value: 'userId', label: 'User ID' },
  { value: 'productId', label: 'Product ID' },
  { value: 'currency', label: 'Currency' },
  { value: 'product.name', label: 'Product Name' },
  { value: 'product.price', label: 'Product Price' },
  { value: 'product.type', label: 'Product Type' },
  { value: 'product.category', label: 'Product Category' }
] as const;

// Utility functions
export const getStatusDisplay = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'Pending',
    completed: 'Completed',
    rejected: 'Rejected',
    refunded: 'Refunded'
  };
  return statusMap[status] || status;
};

export const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    pending: 'text-yellow-400 bg-yellow-400/10',
    completed: 'text-green-400 bg-green-400/10',
    rejected: 'text-red-400 bg-red-400/10',
    refunded: 'text-blue-400 bg-blue-400/10'
  };
  return colorMap[status] || 'text-gray-400 bg-gray-400/10';
};

export const getProductTypeDisplay = (type: string): string => {
  const typeMap: Record<string, string> = {
    general: 'General',
    slots: 'Slots'
  };
  return typeMap[type] || type;
};

export const getProductCategoryDisplay = (category: string): string => {
  const categoryMap: Record<string, string> = {
    free_spins: 'Free Spins',
    cash: 'Cash',
    reload: 'Reload',
    scatter: 'Scatter'
  };
  return categoryMap[category] || category;
};

export const formatPrice = (price: number): string => {
  return Math.round(price).toLocaleString() + ' points';
};

export const formatTimestamp = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

export const canReject = (status: string): boolean => {
  return status === 'pending';
};

export const canComplete = (status: string): boolean => {
  return status === 'pending';
};

export const canRefund = (status: string): boolean => {
  return ['pending', 'completed', 'rejected'].includes(status);
};

/**
 * Get all market product requests with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated market product requests data
 */
export const fetchMarketProductRequests = async (queryParams?: MarketProductRequestsQueryParams): Promise<PaginatedApiResponse<MarketProductRequest>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const queryString = queryParams ? buildQueryString(queryParams) : '';
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-product-requests${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: MarketProductRequestsApiResponse = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch market product requests');
    }

    // Transform the API response to match our expected format
    return {
      success: true,
      data: result.data,
      meta: {
        page: result.pagination.page,
        limit: result.pagination.limit,
        total: result.pagination.total,
        totalPages: result.pagination.totalPages,
        hasNext: result.pagination.page < result.pagination.totalPages,
        hasPrev: result.pagination.page > 1
      }
    };
  } catch (error) {
    console.error('Error fetching market product requests:', error);
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Reject a market product request
 * @param id - The ID of the market product request to reject
 * @param request - The rejection request with reason
 * @returns Promise with the updated market product request
 */
export const rejectMarketProductRequest = async (id: number, request: RejectMarketProductRequestRequest): Promise<ApiResponse<MarketProductRequest>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-product-requests/${id}/rejections`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to reject market product request');
    }

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Error rejecting market product request:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Complete a market product request
 * @param id - The ID of the market product request to complete
 * @returns Promise with the updated market product request
 */
export const completeMarketProductRequest = async (id: number): Promise<ApiResponse<MarketProductRequest>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-product-requests/${id}/completions`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to complete market product request');
    }

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Error completing market product request:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Refund a market product request
 * @param id - The ID of the market product request to refund
 * @param request - The refund request with reason
 * @returns Promise with the updated market product request
 */
export const refundMarketProductRequest = async (id: number, request: RefundMarketProductRequestRequest): Promise<ApiResponse<MarketProductRequest>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-product-requests/${id}/refunds`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to refund market product request');
    }

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Error refunding market product request:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete a market product request
 * @param id - The ID of the market product request to delete
 * @returns Promise with success status
 */
export const deleteMarketProductRequest = async (id: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-product-requests/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to delete market product request');
    }

    return {
      success: true,
      data: { message: result.message || 'Market product request deleted successfully' }
    };
  } catch (error) {
    console.error('Error deleting market product request:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
