import { getAuthToken, getAuthHeaders, type ApiResponse } from './common';

// Bonus Drop interfaces
export interface BonusDropRule {
  field: string;
  value: string | number;
  operator: string;
  base?: number;
}

export interface BonusDropOperator {
  id: number;
  name: string;
}

export interface BonusDropData {
  id: number;
  operator_id: number;
  rules: BonusDropRule[];
  start: string;
  end: string;
  currency_code: string;
  count: number;
  code: string;
  amount: string;
  operator: BonusDropOperator;
}

export interface BonusDropCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: string | null;
}

export interface BonusDropRedeem {
  id: number;
  currency: string;
  customer_id: number;
  timestamp: number;
  usd_amount: string;
  bonus_drop_id: number;
  before_balance: string;
  amount: string;
  after_balance: string;
  bonus_drop: BonusDropData;
  customer: BonusDropCustomer;
}

export interface BonusDropRedeemsResponse {
  data: {
    total: number;
    data: BonusDropRedeem[];
  };
}

// Bonus Drop List interfaces
export interface BonusDropListItem {
  id: number;
  merchant_id: number;
  website_id: number;
  operator_id: number;
  code: string;
  timestamp: number;
  start: string;
  end: string;
  is_active: boolean;
  currency_id: number;
  currency_code: string;
  amount: string;
  deactivated_at: number | null;
  deactivated_by: number | null;
  count: number;
  ref_code: string | null;
  required_wager: string;
  is_deleted: boolean;
  rules: BonusDropRule[];
  reference_tag: string | null;
  required_wager_currency: string | null;
  redeems_count: number;
  corrections_count: number;
  operator: BonusDropOperator;
}

export interface BonusDropListResponse {
  success: boolean;
  message: string;
  data: {
    total: number;
    data: BonusDropListItem[];
  };
  timestamp: string;
}

export interface BonusDropDetailsResponse {
  data: BonusDropListItem;
  status: number;
  success: boolean;
}



export interface BonusDropRedeemsSearchParams {
  id?: number | null;
  customer_id?: number | null;
  bonus_drop_id?: number | null;
  currency?: string | null;
  sortBy?: string | null;
  direction?: 'asc' | 'desc' | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch bonus drop redeems with optional filters and pagination
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param filters - Optional search filters
 * @returns Promise with bonus drop redeems data
 */
export const fetchBonusDropRedeems = async (
  page: number = 1,
  limit: number = 20,
  filters: BonusDropRedeemsSearchParams = {}
): Promise<ApiResponse<BonusDropRedeemsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    queryParams.append('rt', Math.floor(Date.now() / 1000).toString());

    // Add filters to query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonus-drops/redeems?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching bonus drop redeems:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch bonus drop redeems'
    };
  }
};

export interface BonusDropListSearchParams {
  id?: number | null;
  operator_id?: number | null;
  is_active?: boolean | null;
  code?: string | null;
  currency_code?: string | null;
  sortBy?: string | null;
  direction?: 'asc' | 'desc' | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch bonus drops with optional filters and pagination
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param filters - Optional search filters
 * @returns Promise with bonus drops data
 */
export const fetchBonusDrops = async (
  page: number = 1,
  limit: number = 20,
  filters: BonusDropListSearchParams = {}
): Promise<ApiResponse<BonusDropListResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    queryParams.append('rt', Math.floor(Date.now() / 1000).toString());

    // Add filters to query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonus-drops?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching bonus drops:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch bonus drops'
    };
  }
};

/**
 * Get bonus drop details by ID
 * @param bonusDropId - The ID of the bonus drop
 * @returns Promise with bonus drop details
 */
export const fetchBonusDropDetails = async (bonusDropId: string | number): Promise<ApiResponse<BonusDropDetailsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonus-drops/${bonusDropId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching bonus drop details:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch bonus drop details'
    };
  }
};

/**
 * Deactivate bonus drop by ID
 * @param bonusDropId - The ID of the bonus drop to deactivate
 * @returns Promise with deactivation result
 */
export const deactivateBonusDrop = async (bonusDropId: string | number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonus-drops/${bonusDropId}/cancellations`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error deactivating bonus drop:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to deactivate bonus drop'
    };
  }
};

/**
 * Delete bonus drop by ID
 * @param bonusDropId - The ID of the bonus drop to delete
 * @returns Promise with deletion result
 */
export const deleteBonusDrop = async (bonusDropId: string | number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonus-drops/${bonusDropId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error deleting bonus drop:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete bonus drop'
    };
  }
};

// Bonus Drop Create/Update interfaces
export interface BonusDropCreateData {
  code: string;
  currency: string;
  amount: string;
  start: number;
  start_date?: string;
  count: string;
  end: number;
  required_wager: string;
  required_wager_currency: string;
  ref_code: string;
  reference_tag: string;
  rules: Array<{
    field: string;
    operator: string;
    value: string;
  }>;
}

export interface BonusDropUpdateData {
  id: number;
  code: string;
  currency: string;
  amount: string;
  count: number;
  ref_code: string;
  reference_tag: string;
  required_wager: string;
  required_wager_currency: string;
  start: number;
  end: number;
  rules: Array<{
    field: string;
    value: string;
    operator: string;
  }>;
}

/**
 * Create bonus drop
 * @param bonusDropData - The bonus drop data to create
 * @returns Promise with creation result
 */
export const createBonusDrop = async (bonusDropData: BonusDropCreateData): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonus-drops`, {
      method: 'POST',
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(bonusDropData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error creating bonus drop:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create bonus drop'
    };
  }
};

/**
 * Update bonus drop by ID
 * @param bonusDropId - The ID of the bonus drop to update
 * @param bonusDropData - The bonus drop data to update
 * @returns Promise with update result
 */
export const updateBonusDrop = async (bonusDropId: string | number, bonusDropData: BonusDropCreateData): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the update request body
    const updateRequestBody: BonusDropUpdateData = {
      id: parseInt(bonusDropId.toString()),
      code: bonusDropData.code,
      currency: bonusDropData.currency,
      amount: bonusDropData.amount,
      count: parseInt(bonusDropData.count),
      ref_code: bonusDropData.ref_code,
      reference_tag: bonusDropData.reference_tag,
      required_wager: bonusDropData.required_wager,
      required_wager_currency: bonusDropData.required_wager_currency,
      start: bonusDropData.start,
      end: bonusDropData.end,
      rules: bonusDropData.rules
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/bonus-drops/${bonusDropId}`, {
      method: 'PATCH',
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateRequestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error updating bonus drop:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update bonus drop'
    };
  }
};
