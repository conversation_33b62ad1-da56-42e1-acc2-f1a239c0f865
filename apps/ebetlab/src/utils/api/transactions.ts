// Points Transactions API endpoints and types
// This module handles all points transactions-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, BaseQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Transaction-related interfaces
export interface TransactionStatus {
  id: number;
  name: string;
  key: string;
}

export interface TransactionDiscount {
  id: number;
  merchant_id: number;
  website_id: number;
  customer_id: number;
  deposit_id: number;
  timestamp: number;
  before_balance: string;
  amount: string;
  after_balance: string;
  details: {
    t: number;
    rt: number;
    code: string;
    pre_t: number;
    deposit: string;
    real_ip: string;
    withdraw: number;
    difference: number;
    return_url: string;
    website_id: number;
    deposit_url: string;
    merchant_id: number;
    website_domain: string;
    payment_provider_id: number;
  };
  code: string;
  wallet_id: number;
  percentage: string;
  note: string | null;
  depositAmount: string;
}

export interface TransactionCustomer {
  id: number;
  username: string;
  affiliator_id: number;
  ref_id: string | null;
  ref_code: string;
  masked_username: string;
  last_action: number;
  profile: {
    id: number;
    name: string;
    surname: string;
    customer_id: number;
  };
  affiliator: {
    id: number;
    name: string;
  };
}

export interface TransactionPaymentProvider {
  id: number;
  name: string;
}

export interface TransactionData {
  id: number;
  type: number; // 1 = Deposit, 2 = Withdraw, etc.
  method: string;
  payment_provider_id: number;
  discount_able?: boolean;
  approve_details?: any;
  completed_at: number | null;
  target_address: string | null;
  unique_id: string;
  currency: string;
  amount: string;
  usd_amount: string;
  status: TransactionStatus;
  operator?: any;
  last_action?: any;
  customer: TransactionCustomer;
  payment_provider: TransactionPaymentProvider;
  discount?: TransactionDiscount | null;
  aml_met?: boolean;
}

export interface TransactionsResponse {
  data: {
    total: number;
    data: TransactionData[];
  };
  status: number;
  success: boolean;
}

export interface TransactionSearchParams {
  id?: string | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: string | null;
  affiliator?: string | null;
  ref_code?: string | null;
  is_manuel?: boolean | null;
  status_id?: string | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  amount_min?: string | null;
  amount_max?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string;
}

// Transaction summary interfaces
export interface TransactionSummaryData {
  deposit: {
    deposit: string;
    total: number;
    provider: string;
    payment_provider: any;
  };
  withdraw: {
    withdraw: string;
    total: number;
    provider: string;
    payment_provider: any;
  };
  currency: string;
}

export interface TransactionSummaryResponse {
  data: TransactionSummaryData;
  status: number;
  success: boolean;
}

/**
 * Fetch transaction summary data for deposits/withdrawals page
 * @param searchParams - Optional search parameters
 * @returns Promise with transaction summary data
 */
export const fetchTransactionsSummary = async (
  searchParams: Partial<TransactionSearchParams> = {}
): Promise<ApiResponse<TransactionSummaryResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Process search parameters to handle empty strings and convert to appropriate types
    const processedParams: Partial<TransactionSearchParams> = {};
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        processedParams[key as keyof TransactionSearchParams] = value;
      }
    });

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      method: null,
      provider: null,
      username: null,
      currency: null,
      operator_id: null,
      type: null,
      affiliator: null,
      ref_code: null,
      is_manuel: null,
      status_id: null,
      tx: null,
      related: null,
      usd_min: null,
      usd_max: null,
      amount_min: null,
      amount_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page: 1,
      limit: 20,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...processedParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/transactions/summary`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching transaction summary:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch transactions data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with transactions data
 */
export const fetchTransactions = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<TransactionSearchParams> = {}
): Promise<ApiResponse<TransactionsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Process search parameters to handle empty strings and convert to appropriate types
    const processedParams: Partial<TransactionSearchParams> = {};
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        processedParams[key as keyof TransactionSearchParams] = value;
      }
    });

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      method: null,
      provider: null,
      username: null,
      currency: null,
      operator_id: null,
      type: null,
      affiliator: null,
      ref_code: null,
      is_manuel: null,
      status_id: null,
      tx: null,
      related: null,
      usd_min: null,
      usd_max: null,
      amount_min: null,
      amount_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...processedParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/transactions/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
