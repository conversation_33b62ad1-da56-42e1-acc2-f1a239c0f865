// Balance corrections API endpoints and types
// This module handles all balance corrections-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Corrections-related interfaces
export interface CorrectionsSummaryData {
  deposit: {
    deposit: string;
    total: number;
  };
  withdraw: {
    withdraw: string;
    total: number;
  };
}

export interface CorrectionsSummaryResponse {
  data: CorrectionsSummaryData;
  status: number;
  success: boolean;
}

export interface CorrectionCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: number;
}

export interface CorrectionData {
  id: number;
  model: string;
  customer_id: number;
  operator_id: number | null;
  reason: string | null;
  is_active: boolean;
  currency: string;
  wallet_id: number;
  merchant_id: number;
  website_id: number;
  amount: string;
  usd_amount: string;
  timestamp: number;
  before_balance: string;
  way: string;
  after_balance: string;
  completed_at: number | null;
  required_wager_currency: string;
  note: string;
  wagered: string;
  wagered_currency: string;
  required_wager: string;
  completed: boolean;
  customer: CorrectionCustomer;
  operator: any | null;
}

export interface CorrectionsResponse {
  data: {
    total: number;
    data: CorrectionData[];
  };
  status: number;
  success: boolean;
}

export interface CorrectionsSearchParams {
  id?: string | null;
  username?: string | null;
  model?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  way?: string | null;
  note?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

/**
 * Fetch corrections summary data
 * @param username - Optional username to filter by specific user
 * @returns Promise with corrections summary data
 */
export const fetchCorrectionsSummary = async (
  username?: string | null
): Promise<ApiResponse<CorrectionsSummaryResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body
    const requestBody = {
      username: username || null
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/balance-corrections/summary`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching corrections summary:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch corrections data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with corrections data
 */
export const fetchCorrections = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<CorrectionsSearchParams> = {}
): Promise<ApiResponse<CorrectionsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      username: null,
      model: null,
      currency: null,
      operator_id: null,
      way: null,
      note: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/balance-corrections/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching corrections:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
