// Wallets API endpoints and types
// This module handles all wallet-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Wallet-related interfaces
export interface WalletNetwork {
  id: number;
  name: string;
  key: string;
  pname: string;
}

export interface WalletCurrency {
  id: number;
  payment_provider_id: number;
  network_id: number;
  currency_id: number | null;
  name: string;
  alias: string;
  precision: number;
  min_transfer: string;
  is_active: boolean;
  network: WalletNetwork;
}

export interface WalletData {
  id: number;
  code: string;
  balance: string;
  address: string | null;
  network_id: number | null;
  currency_id: number;
  currency: WalletCurrency;
}

export interface WalletsResponse {
  total: number;
  data: WalletData[];
}

/**
 * Fetch customer wallets
 * @param customerId - Customer ID as string
 * @returns Promise with wallets data
 */
export const fetchWallets = async (customerId: string): Promise<ApiResponse<WalletsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const requestBody = {
      limit: 30,
      page: 1,
      customer_id: customerId,
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/wallets/index/1/30`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Error fetching wallets:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch wallets'
    };
  }
};
