// Discounts API endpoints and types
// This module handles all discounts-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Discounts-related interfaces
export interface DiscountTransaction {
  id: number;
  amount: string;
  usd_amount: string;
  timestamp: number;
  provider: string;
  payment_provider: any;
}

export interface DiscountCustomer {
  id: number;
  username: string;
  masked_username: string;
  last_action: number;
}

export interface DiscountData {
  id: number;
  timestamp: number;
  customer_id: number;
  before_balance: string;
  amount: string;
  after_balance: string;
  code: string;
  deposit_id: number;
  depositAmount: string;
  transaction: DiscountTransaction;
  customer: DiscountCustomer;
}

export interface DiscountsResponse {
  total: number;
  data: DiscountData[];
}

export interface DiscountSearchParams {
  username?: string | null;
  currency?: string | null;
  from?: number | null;
  to?: number | null;
  customer_id?: string;
}

/**
 * Fetch discounts data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with discounts data
 */
export const fetchDiscounts = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<DiscountSearchParams> = {}
): Promise<ApiResponse<DiscountsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      username: null,
      currency: null,
      from: null,
      to: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/discounts/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
