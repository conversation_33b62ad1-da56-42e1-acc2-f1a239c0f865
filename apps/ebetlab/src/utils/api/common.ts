// Common API utilities and types
// This module contains shared types and utility functions used across all API modules

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// New paginated response interface for native API filtering
export interface PaginatedApiResponse<T = any> {
  success: boolean;
  data: T[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

// Query parameter interfaces for filtering and sorting
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

// Extended Users query parameters
export interface ExtendedUsersQueryParams extends BaseQueryParams {
  externalId?: number;
  points?: number;
  minPoints?: number;
  maxPoints?: number;
  externalUsername?: string;
  search?: string;
}

// Mission Rules query parameters
export interface MissionRulesQueryParams extends BaseQueryParams {
  // Enum Filters
  ruleType?: 'joinedAt' | 'totalDeposit' | 'totalWithdraw' | 'netProfit' | 'vipRank' | 'country' | 'phoneNumber' | 'kycLevel' | 'referrer';
  compare?: 'eq' | 'ne' | 'gt' | 'lt' | 'ge' | 'le';

  // Text Filters
  compareValue?: string; // Partial match, case-insensitive
  search?: string; // General search term (searches compareValue, partial match)

  // Date Range Filters
  minDateFrom?: number; // Min date range filtering (rules with minDate >= this timestamp)
  minDateTo?: number; // Min date range filtering (rules with minDate <= this timestamp)
  maxDateFrom?: number; // Max date range filtering (rules with maxDate >= this timestamp)
  maxDateTo?: number; // Max date range filtering (rules with maxDate <= this timestamp)

  // Legacy parameters (for backward compatibility)
  compareOperator?: string;
  compareValueMin?: number;
  compareValueMax?: number;
  hasDateRange?: boolean;
}

// Missions query parameters
export interface MissionsQueryParams extends BaseQueryParams {
  name?: string;
  missionType?: string;
  reward?: number;
  rewardMin?: number;
  rewardMax?: number;
  status?: string;
  startDateFrom?: number;
  startDateTo?: number;
  endDateFrom?: number;
  endDateTo?: number;
  search?: string;
}

// Mission Participations query parameters
export interface MissionParticipationsQueryParams extends BaseQueryParams {
  // Filter Parameters
  userId?: number; // Must be > 0
  missionId?: number; // Must be > 0
  isCompleted?: boolean; // true | false

  // Date Range Filter Parameters (Unix timestamps in seconds)
  createdAtFrom?: number; // Unix timestamp (seconds)
  createdAtTo?: number; // Unix timestamp (seconds)
  updatedAtFrom?: number; // Unix timestamp (seconds)
  updatedAtTo?: number; // Unix timestamp (seconds)

  // Search Parameter
  search?: string; // General search term that searches in userId and missionId fields
}

// Mission Rule Assignments query parameters
export interface MissionRuleAssignmentsQueryParams extends BaseQueryParams {
  missionId?: number;
  missionRuleId?: number;
  createdFrom?: string;
  createdTo?: string;
}

// Mission Objectives query parameters
export interface MissionObjectivesQueryParams extends BaseQueryParams {
  // Filter Parameters
  missionId?: number; // Must be > 0
  objectiveType?: 'slot' | 'liveCasino' | 'deposit' | 'withdraw' | 'turnover';
  subtype?: string; // Partial match filter on subtype field (case-insensitive)
  operator?: 'eq' | 'ne' | 'gt' | 'lt' | 'ge' | 'le';
  targetValue?: string; // Partial match filter on target value (case-insensitive)
  description?: string; // Partial match filter on description field (case-insensitive)

  // Timeframe Filter Parameters
  timeframeStartFrom?: number; // Must be ≥ 0
  timeframeStartTo?: number; // Must be ≥ 0
  timeframeEndFrom?: number; // Must be ≥ 0
  timeframeEndTo?: number; // Must be ≥ 0

  // Search Parameter
  search?: string; // General search term that searches across both description and targetValue fields

  // Legacy parameters (for backward compatibility)
  name?: string;
  hasTimeframe?: boolean;
}

// Mission Objective Assignments query parameters
export interface MissionObjectiveAssignmentsQueryParams extends BaseQueryParams {
  missionId?: number;
  missionObjectiveId?: number;
  createdFrom?: string;
  createdTo?: string;
}

// Final Mission Claims query parameters
export interface FinalMissionClaimsQueryParams extends BaseQueryParams {
  // Filter Parameters
  userId?: number; // Filter by specific user ID (integer)
  claimType?: 'daily' | 'weekly' | 'monthly'; // Filter by claim type
  minReward?: number; // Minimum reward amount (integer)
  maxReward?: number; // Maximum reward amount (integer)

  // Date Range Filters
  createdAtFrom?: string; // Claims created on/after this date (date string)
  createdAtTo?: string; // Claims created on/before this date (date string)
}

/**
 * Get authentication token from localStorage
 * @returns Authentication token or null if not found
 */
export const getAuthToken = () => {
  // First check betroz_auth (current key)
  let storedAuthData = localStorage.getItem('betroz_auth');

  // If not found, check betroz_login (alternative key)
  if (!storedAuthData) {
    storedAuthData = localStorage.getItem('betroz_login');
  }

  if (storedAuthData) {
    try {
      const authData = JSON.parse(storedAuthData);
      // Return the token from auth data (adjust property name as needed)
      return authData.token || authData.accessToken || authData.authToken || null;
    } catch (error) {
      console.error('Failed to parse stored auth data:', error);
      return null;
    }
  }

  return null;
};

/**
 * Get authentication headers with Bearer token
 * @returns Headers object with Authorization header
 */
export const getAuthHeaders = () => {
  const token = getAuthToken();
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

/**
 * Utility function to build query string from parameters
 * @param params - Object with query parameters
 * @returns Query string with leading ? or empty string
 */
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    // Include the value if it's not undefined, not null, and not an empty string
    // But allow 0 and false values to be included
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
};
