// Session API endpoints and types
// This module handles all session-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Session-related interfaces
export interface SessionCheckData {
  ip: string;
  usernames: string[];
}

export interface SessionCheckResponse {
  success: boolean;
  message: string;
  data: {
    data: SessionCheckData[];
    status: number;
    success: boolean;
  };
  timestamp: string;
}

// Session data interfaces for the sessions index endpoint
export interface SessionCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: number;
}

export interface SessionData {
  id: number;
  customer_id: number;
  ip: string;
  is_active: boolean;
  timestamp: number;
  country: string;
  browser: string;
  customer: SessionCustomer;
}

export interface SessionsResponse {
  total: number;
  data: SessionData[];
}

// Search parameters interface for sessions
export interface SessionSearchParams {
  from?: number | null;
  to?: number | null;
  customer_id?: string;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Check for conflicting IPs for a customer
 * @param customerId - Customer ID as string
 * @returns Promise with session check data showing conflicting IPs and usernames
 */
export const fetchCustomerSessionCheck = async (customerId: string): Promise<ApiResponse<SessionCheckResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/sessions/check/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        id: customerId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch sessions data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with sessions data
 */
export const fetchSessions = async (
  page: number = 1,
  limit: number = 100,
  searchParams: Partial<SessionSearchParams> = {}
): Promise<ApiResponse<SessionsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      from: null,
      to: null,
      limit: limit,
      page: page,
      customer_id: null,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    console.log('Sessions API Request:', {
      url: `${import.meta.env.VITE_API_URL || ''}/api/operator/sessions/index/${page}/${limit}`,
      method: 'POST',
      body: requestBody
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/sessions/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
