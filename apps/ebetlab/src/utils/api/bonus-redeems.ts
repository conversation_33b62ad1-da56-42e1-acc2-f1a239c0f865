// Bonus Redeems API endpoints and types
// This module handles all bonus redeems-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Bonus redeems-related interfaces
export interface BonusRedeemOperator {
  id: number;
  name: string;
  email: string;
  role_id: number;
}

export interface BonusRedeemGame {
  id: number;
  percentage: number;
}

export interface BonusRedeemTypeable {
  id: number;
  quantity: number;
  bet_level: number;
  game_merchant_id: number;
  aggregator_id: number;
  game_merchant_name: string;
  max_win: string;
}

export interface BonusRedeemBonusData {
  id: number;
  merchant_id: number;
  website_id: number;
  operator_id: number;
  model: string;
  currency: string;
  currency_id: number;
  name: string;
  description: string;
  note: string;
  from: string;
  to: string;
  product: string;
  timestamp: string;
  request_able: boolean;
  typeable_id: number;
  typeable_type: string;
  is_active: boolean;
  expire_type: string;
  expire_unit: string;
  games: BonusRedeemGame[];
  block_balance: number;
  is_deleted: boolean;
  typeable: BonusRedeemTypeable;
}

export interface BonusRedeemCustomer {
  id: number;
  username: string;
  masked_username: string;
  last_action: number | null;
}

export interface BonusRedeemData {
  id: number;
  merchant_id: number;
  website_id: number;
  bonus_id: number;
  customer_id: number;
  wallet_id: number;
  timestamp: string;
  amount: string;
  multiplier: string;
  required: string;
  current: string;
  is_completed: number;
  operator_id: number;
  note: string;
  deactivated_at: string | null;
  deactivated_by: string | null;
  is_active: number;
  earn_amount: string | null;
  operator: BonusRedeemOperator;
  bonus: BonusRedeemBonusData;
  customer: BonusRedeemCustomer;
}

export interface BonusRedeemsResponse {
  total: number;
  data: BonusRedeemData[];
}

// Search parameters interface for bonus redeems
export interface BonusRedeemSearchParams {
  id?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  is_active?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  amount_min?: string | null;
  amount_max?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string;
  type?: string;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch bonus redeems data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with bonus redeems data
 */
export const fetchBonusRedeems = async (
  page: number = 1,
  limit: number = 50,
  searchParams: Partial<BonusRedeemSearchParams> = {}
): Promise<ApiResponse<BonusRedeemsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      username: null,
      currency: null,
      operator_id: null,
      is_active: null,
      usd_min: null,
      usd_max: null,
      amount_min: null,
      amount_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      type: "manual",
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    console.log('Bonus Redeems API Request:', {
      url: `${import.meta.env.VITE_API_URL || ''}/api/operator/bonus-redeems/index/${page}/${limit}`,
      method: 'POST',
      body: requestBody
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/bonus-redeems/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
