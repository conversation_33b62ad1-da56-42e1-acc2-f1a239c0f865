// Player List API endpoints and types
// This module handles all player list-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Player list-related interfaces
export interface PlayerCustomer {
  id: number;
  username: string;
  ranki: string;
  rankc: string;
  masked_username: string;
  last_action: number;
}

export interface PlayerData {
  id: number;
  currency: string;
  wallet_id: number;
  customer_id: number;
  sportsbook_id: string;
  customer: PlayerCustomer;
}

export interface PlayerListResponse {
  data: {
    total: number;
    data: PlayerData[];
  };
  status: number;
  success: boolean;
}

export interface PlayerListSearchParams {
  customer_id?: string;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch player list data with pagination
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with player list data
 */
export const fetchPlayerList = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<PlayerListSearchParams> = {}
): Promise<ApiResponse<PlayerListResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      customer_id: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/players/sportsbook/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
