// General Limits API endpoints and types
// This module handles all general limits-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// General limits-related interfaces
export interface GeneralLimitsData {
  login_disabled: boolean;
  bets_disabled: boolean;
  bonus_disabled: boolean;
  aml_disabled: boolean;
  local_aml_disabled: boolean;
  withdraw_disabled: boolean;
  chat_disabled: boolean;
  tip_disabled: boolean;
  rakeback_disabled: boolean;
  raffle_disabled: boolean;
  race_disabled: boolean;
  casino_bets_disabled: boolean;
  live_casino_bets_disabled: boolean;
  sportsbook_disabled: boolean;
  withdrawal_bypass_otp: boolean;
  instant_discount_disabled: number;
  weekly_discount_disabled: number;
  monthly_discount_disabled: number;
  pokerklas_enabled: number;
}

export interface GeneralLimitsResponse {
  data: GeneralLimitsData | null;
  status: number;
  success: boolean;
}

/**
 * Fetch general limits data for a customer
 * @param customerId - Customer ID as string
 * @returns Promise with general limits data
 */
export const fetchGeneralLimits = async (customerId: string): Promise<ApiResponse<GeneralLimitsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/general-limits/show/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        id: customerId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Apply general limits changes for a customer
 * @param customerId - Customer ID as string
 * @param limitsData - General limits data to apply
 * @returns Promise with apply result
 */
export const applyGeneralLimits = async (customerId: string, limitsData: GeneralLimitsData): Promise<ApiResponse<{ data: boolean; status: number; success: boolean }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Check if user has full access (all restrictions are false/disabled)
    const hasFullAccess = !limitsData.login_disabled &&
                         !limitsData.bets_disabled &&
                         !limitsData.bonus_disabled &&
                         !limitsData.aml_disabled &&
                         !limitsData.local_aml_disabled &&
                         !limitsData.withdraw_disabled &&
                         !limitsData.chat_disabled &&
                         !limitsData.tip_disabled &&
                         !limitsData.rakeback_disabled &&
                         !limitsData.raffle_disabled &&
                         !limitsData.race_disabled &&
                         !limitsData.casino_bets_disabled &&
                         !limitsData.live_casino_bets_disabled &&
                         !limitsData.sportsbook_disabled &&
                         !limitsData.withdrawal_bypass_otp &&
                         limitsData.instant_discount_disabled === 0 &&
                         limitsData.weekly_discount_disabled === 0 &&
                         limitsData.monthly_discount_disabled === 0 &&
                         limitsData.pokerklas_enabled === 1;

    // Build request body according to API specification
    const requestBody = {
      full_access: hasFullAccess,
      bonus_disabled: limitsData.bonus_disabled,
      login_disabled: limitsData.login_disabled,
      withdraw_disabled: limitsData.withdraw_disabled,
      chat_disabled: limitsData.chat_disabled,
      tip_disabled: limitsData.tip_disabled,
      rakeback_disabled: limitsData.rakeback_disabled,
      raffle_disabled: limitsData.raffle_disabled,
      bets_disabled: limitsData.bets_disabled,
      casino_bets_disabled: limitsData.casino_bets_disabled,
      live_casino_bets_disabled: limitsData.live_casino_bets_disabled,
      sportsbook_disabled: limitsData.sportsbook_disabled,
      race_disabled: limitsData.race_disabled,
      aml_disabled: limitsData.aml_disabled,
      local_aml_disabled: limitsData.local_aml_disabled,
      withdrawal_bypass_otp: limitsData.withdrawal_bypass_otp,
      instant_discount_disabled: Boolean(limitsData.instant_discount_disabled),
      weekly_discount_disabled: Boolean(limitsData.weekly_discount_disabled),
      monthly_discount_disabled: Boolean(limitsData.monthly_discount_disabled),
      pokerklas_enabled: Boolean(limitsData.pokerklas_enabled),
      id: customerId,
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/general-limits/apply/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
