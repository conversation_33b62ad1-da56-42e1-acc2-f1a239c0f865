// Points Transactions API endpoints and types
// This module handles all points transactions-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, BaseQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Points Transactions interfaces
export interface PointsTransaction {
  id: number;
  fromUserId: number | null;
  toUserId: number | null;
  type: TransactionType;
  category: TransactionCategory;
  amount: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  fromUser?: ExtendedUser | null;
  toUser?: ExtendedUser | null;
}

export interface ExtendedUser {
  id: number;
  externalId: number;
  points: number;
  createdAt: string;
  updatedAt: string;
  externalUsername?: string; // Optional field for username from external system
}

export type TransactionType = 'deposit' | 'withdrawal' | 'charge' | 'refund';
export type TransactionCategory = 'mission_reward' | 'market_purchase' | 'admin_adjustment';

// Query parameters interface for points transactions
export interface PointsTransactionsQueryParams extends BaseQueryParams {
  // Filter Parameters
  id?: number;
  fromUserId?: number;
  toUserId?: number;
  type?: TransactionType;
  category?: TransactionCategory;
  minAmount?: number;
  maxAmount?: number;
  
  // Date Range Filter Parameters (ISO format)
  createdAfter?: string;
  createdBefore?: string;
  
  // Search Parameter
  search?: string;
}

// API Response interface for points transactions
interface PointsTransactionsApiResponse {
  success: boolean;
  message: string;
  data: PointsTransaction[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Delete response interface
interface DeleteTransactionResponse {
  success: boolean;
  message: string;
  data: {
    id: number;
    deletedAt: string;
  };
}

// Transaction statistics interface
export interface TransactionStatistics {
  totalCount: number;
  totalVolume: number;
  depositCount: number;
  depositVolume: number;
  chargeCount: number;
  chargeVolume: number;
  refundCount: number;
  refundVolume: number;
  withdrawCount: number;
  withdrawVolume: number;
}

// Statistics API response interface
interface TransactionStatisticsResponse {
  success: boolean;
  message: string;
  data: TransactionStatistics;
}

// Sort fields for points transactions
export const POINTS_TRANSACTIONS_SORT_FIELDS = [
  { value: 'id', label: 'ID' },
  { value: 'fromUserId', label: 'From User ID' },
  { value: 'toUserId', label: 'To User ID' },
  { value: 'type', label: 'Type' },
  { value: 'category', label: 'Category' },
  { value: 'amount', label: 'Amount' },
  { value: 'createdAt', label: 'Created At' },
  { value: 'updatedAt', label: 'Updated At' }
];

// Transaction type options for UI
export const TRANSACTION_TYPE_OPTIONS = [
  { value: 'deposit', label: 'Deposit' },
  { value: 'withdrawal', label: 'Withdrawal' },
  { value: 'charge', label: 'Charge' },
  { value: 'refund', label: 'Refund' }
];

// Transaction category options for UI
export const TRANSACTION_CATEGORY_OPTIONS = [
  { value: 'mission_reward', label: 'Mission Reward' },
  { value: 'market_purchase', label: 'Market Purchase' },
  { value: 'admin_adjustment', label: 'Admin Adjustment' }
];

/**
 * Fetch all points transactions with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated points transactions data
 */
export const fetchPointsTransactions = async (queryParams?: PointsTransactionsQueryParams): Promise<PaginatedApiResponse<PointsTransaction>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const queryString = queryParams ? buildQueryString(queryParams) : '';
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/transactions${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: PointsTransactionsApiResponse = await response.json();
    
    // Transform the API response to match our PaginatedApiResponse interface
    return {
      success: result.success,
      data: result.data,
      meta: {
        page: result.pagination.page,
        limit: result.pagination.limit,
        total: result.pagination.total,
        totalPages: result.pagination.totalPages,
        hasNext: result.pagination.page < result.pagination.totalPages,
        hasPrev: result.pagination.page > 1
      }
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch transaction statistics
 * @returns Promise with transaction statistics data
 */
export const fetchTransactionStatistics = async (): Promise<ApiResponse<TransactionStatistics>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/transactions/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: TransactionStatisticsResponse = await response.json();
    return {
      success: result.success,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete a points transaction by ID
 * @param id - Transaction ID to delete
 * @returns Promise with delete operation result
 */
export const deletePointsTransaction = async (id: number): Promise<ApiResponse<DeleteTransactionResponse['data']>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/transactions/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('Transaction not found or already deleted');
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: DeleteTransactionResponse = await response.json();
    return {
      success: result.success,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get transaction type display label
 * @param type - Transaction type
 * @returns Display label for the transaction type
 */
export const getTransactionTypeDisplay = (type: TransactionType): string => {
  const option = TRANSACTION_TYPE_OPTIONS.find(opt => opt.value === type);
  return option ? option.label : type;
};

/**
 * Get transaction category display label
 * @param category - Transaction category
 * @returns Display label for the transaction category
 */
export const getTransactionCategoryDisplay = (category: TransactionCategory): string => {
  const option = TRANSACTION_CATEGORY_OPTIONS.find(opt => opt.value === category);
  return option ? option.label : category;
};

/**
 * Format transaction amount for display
 * @param amount - Amount as string
 * @returns Formatted amount string
 */
export const formatTransactionAmount = (amount: string): string => {
  const numAmount = parseFloat(amount);
  return numAmount.toFixed(0) + ' points';
};

/**
 * Get transaction type color class for UI styling
 * @param type - Transaction type
 * @returns CSS class name for styling
 */
export const getTransactionTypeColor = (type: TransactionType): string => {
  switch (type) {
    case 'deposit':
      return 'text-green-400';
    case 'charge':
      return 'text-red-400';
    case 'refund':
      return 'text-blue-400';
    case 'withdrawal':
      return 'text-orange-400';
    default:
      return 'text-gray-400';
  }
};

/**
 * Get transaction category color class for UI styling
 * @param category - Transaction category
 * @returns CSS class name for styling
 */
export const getTransactionCategoryColor = (category: TransactionCategory): string => {
  switch (category) {
    case 'mission_reward':
      return 'text-purple-400';
    case 'market_purchase':
      return 'text-blue-400';
    case 'admin_adjustment':
      return 'text-yellow-400';
    default:
      return 'text-gray-400';
  }
};
