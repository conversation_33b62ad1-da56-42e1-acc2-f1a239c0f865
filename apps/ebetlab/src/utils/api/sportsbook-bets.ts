// Sportsbook Bets API endpoints and types
// This module handles all sportsbook bets-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Sportsbook bets-related interfaces
export interface SportsbookBetEvent {
  id: number;
  market_name: string;
  outcome_name: string;
  match_name: string;
  match_start_date: string;
}

export interface SportsbookBetData {
  id: number;
  customer_id: number;
  timestamp: string;
  wallet_currency: string;
  currency: string;
  finished: boolean;
  amount: string;
  possible_win: string;
  wallet_amount: string;
  is_active: boolean;
  income: string;
  type: string;
  state: string;
  odds: string;
  events: SportsbookBetEvent[];
}

export interface SportsbookBetStats {
  bet: number;
  win: number;
  bet_total: number;
  win_total: number;
}

export interface SportsbookBetsResponse {
  total: number;
  data: SportsbookBetData[];
  stats: SportsbookBetStats;
}

export interface SportsbookBetSearchParams {
  id?: string | null;
  username?: string | null;
  game?: string | null;
  provider_id?: string | null;
  type?: string | null;
  status?: string | null;
  currency?: string | null;
  wallet_currency?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: number | null;
  to?: number | null;
  amount_min?: string | null;
  amount_max?: string | null;
  multiplier_min?: string | null;
  multiplier_max?: string | null;
  income_min?: string | null;
  income_max?: string | null;
  net_min?: string | null;
  net_max?: string | null;
  income_usd_min?: string | null;
  income_usd_max?: string | null;
  wallet_amount_min?: string | null;
  wallet_amount_max?: string | null;
  win_wallet_amount_min?: string | null;
  win_wallet_amount_max?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  odd_min?: string | null;
  odd_max?: string | null;
  customer_id?: string;
}

/**
 * Fetch sportsbook bets data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with sportsbook bets data
 */
export const fetchSportsbookBets = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<SportsbookBetSearchParams> = {}
): Promise<ApiResponse<SportsbookBetsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      username: null,
      game: null,
      provider_id: null,
      type: null,
      status: null,
      currency: null,
      wallet_currency: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      amount_min: null,
      amount_max: null,
      multiplier_min: null,
      multiplier_max: null,
      income_min: null,
      income_max: null,
      net_min: null,
      net_max: null,
      income_usd_min: null,
      income_usd_max: null,
      wallet_amount_min: null,
      wallet_amount_max: null,
      win_wallet_amount_min: null,
      win_wallet_amount_max: null,
      sortBy: null,
      direction: null,
      odd_min: null,
      odd_max: null,
      page: page,
      limit: limit,
      customer_id: null,
      timezone: -3,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/sportsbook-debits/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
