// Admin discounts API endpoints and types
// This module handles all admin-level discounts-related API calls (not per-user discounts)

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Discounts summary-related interfaces
export interface DiscountSummaryItem {
  sum: string;
  count: number;
  code: string;
  depositAmount: string;
}

export interface DiscountsSummaryResponse {
  data: {
    data: DiscountSummaryItem[];
    status: number;
    success: boolean;
  };
  status: number;
  headers: {
    'content-length': string;
    'content-type': string;
  };
  timestamp: string;
}

// Discount transaction and customer interfaces
export interface DiscountTransaction {
  id: number;
  amount: string;
  usd_amount: string;
  timestamp: number;
  provider: string;
  payment_provider: any | null;
}

export interface DiscountCustomer {
  id: number;
  username: string;
  masked_username: string;
  last_action: number;
}

export interface DiscountData {
  id: number;
  timestamp: number;
  customer_id: number;
  before_balance: string;
  amount: string;
  after_balance: string;
  code: string;
  deposit_id: number;
  depositAmount: string;
  transaction: DiscountTransaction;
  customer: DiscountCustomer;
}

export interface DiscountsResponse {
  data: {
    total: number;
    data: DiscountData[];
  };
  status: number;
  success: boolean;
}

export interface DiscountsSearchParams {
  username?: string | null;
  currency?: string | null;
  from?: number | string | null;
  to?: number | string | null;
}

/**
 * Fetch discounts summary data
 * @param searchParams - Optional search parameters
 * @returns Promise with discounts summary data
 */
export const fetchDiscountsSummary = async (
  searchParams: Partial<DiscountsSearchParams> = {}
): Promise<ApiResponse<DiscountsSummaryResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Convert date strings to timestamps if needed
    const processedParams = { ...searchParams };
    if (processedParams.from && typeof processedParams.from === 'string') {
      processedParams.from = Math.floor(new Date(processedParams.from).getTime() / 1000);
    }
    if (processedParams.to && typeof processedParams.to === 'string') {
      processedParams.to = Math.floor(new Date(processedParams.to).getTime() / 1000);
    }

    // Build the request body
    const requestBody = {
      username: null,
      currency: null,
      from: null,
      to: null,
      page: 1,
      limit: 20,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...processedParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/discounts/summary`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching discounts summary:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch discounts data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with discounts data
 */
export const fetchDiscountsAdmin = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<DiscountsSearchParams> = {}
): Promise<ApiResponse<DiscountsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Convert date strings to timestamps if needed
    const processedParams = { ...searchParams };
    if (processedParams.from && typeof processedParams.from === 'string') {
      processedParams.from = Math.floor(new Date(processedParams.from).getTime() / 1000);
    }
    if (processedParams.to && typeof processedParams.to === 'string') {
      processedParams.to = Math.floor(new Date(processedParams.to).getTime() / 1000);
    }

    // Build the request body
    const requestBody = {
      username: null,
      currency: null,
      from: null,
      to: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...processedParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/discounts/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching discounts:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
