// Mission Participations API endpoints and types
// This module handles all mission-participations-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, MissionParticipationsQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Mission Participations interfaces
export interface MissionParticipation {
  id: number;
  userId: number;
  missionId: number;
  isCompleted: boolean;
  createdAt: string;
  updatedAt: string;
  mission?: Mission;
}

export interface Mission {
  id: number;
  name: string;
  missionType: string;
  reward: number;
  description?: string;
  startDate?: number;
  endDate?: number;
}

export interface CreateMissionParticipationRequest {
  userId: number;
  missionId: number;
  isCompleted?: boolean;
}

export interface UpdateMissionParticipationRequest {
  isCompleted?: boolean;
}

export interface MissionParticipationsResponse {
  success: boolean;
  data: MissionParticipation[];
  message?: string;
}

export interface MissionParticipationResponse {
  success: boolean;
  data: MissionParticipation;
  message?: string;
}

export interface MissionStatsResponse {
  success: boolean;
  data: {
    missionId: number;
    statistics: {
      totalParticipations: number;
      completedParticipations: number;
      pendingParticipations: number;
      uniqueParticipants: number;
      totalExtendedUsers: number;
      completionRate: number;
      participationRate: number;
    };
    breakdown: {
      completed: {
        count: number;
        percentage: number;
      };
      pending: {
        count: number;
        percentage: number;
      };
    };
  };
  message?: string;
}

// Legacy interface for backward compatibility
export interface MissionStats {
  totalParticipants: number;
  completedParticipants: number;
  completionRate: number;
}

// Enhanced interface with full statistics
export interface EnhancedMissionStats {
  missionId: number;
  statistics: {
    totalParticipations: number;
    completedParticipations: number;
    pendingParticipations: number;
    uniqueParticipants: number;
    totalExtendedUsers: number;
    completionRate: number;
    participationRate: number;
  };
  breakdown: {
    completed: {
      count: number;
      percentage: number;
    };
    pending: {
      count: number;
      percentage: number;
    };
  };
}

export interface UserStatsResponse {
  success: boolean;
  data: {
    totalParticipations: number;
    completedMissions: number;
    completionRate: number;
  };
  message?: string;
}

/**
 * Create a new mission participation
 * @param participationData - Mission participation data to create
 * @returns Promise with created mission participation data
 */
export const createMissionParticipation = async (participationData: CreateMissionParticipationRequest): Promise<ApiResponse<MissionParticipation>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-participations`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(participationData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch all mission participations with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated mission participations data
 */
export const fetchMissionParticipations = async (queryParams?: MissionParticipationsQueryParams): Promise<PaginatedApiResponse<MissionParticipation>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const queryString = queryParams ? buildQueryString(queryParams) : '';
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-participations${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Handle the nested response structure: data.missionParticipations
    const participationsData = result.data?.missionParticipations || result.data || [];
    const total = result.data?.total || participationsData.length;
    const page = result.data?.page || 1;
    const limit = result.data?.limit || 20;
    const totalPages = result.data?.totalPages || Math.ceil(total / limit);

    return {
      success: true,
      data: participationsData,
      meta: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch all mission participations (legacy function for backward compatibility)
 * @returns Promise with mission participations data
 */
export const fetchMissionParticipationsLegacy = async (): Promise<ApiResponse<MissionParticipation[]>> => {
  try {
    const response = await fetchMissionParticipations();
    return {
      success: response.success,
      data: response.data,
      error: response.error
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission participation by ID
 * @param id - Mission participation ID
 * @returns Promise with mission participation data
 */
export const fetchMissionParticipationById = async (id: number): Promise<ApiResponse<MissionParticipation>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-participations/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get participations by user ID (DEPRECATED - use fetchMissionParticipations with userId query parameter)
 * @param userId - User ID
 * @returns Promise with user's mission participations data
 * @deprecated Use fetchMissionParticipations({ userId }) instead
 */
export const fetchParticipationsByUserId = async (userId: number): Promise<ApiResponse<MissionParticipation[]>> => {
  console.warn('fetchParticipationsByUserId is deprecated. Use fetchMissionParticipations({ userId }) instead.');

  try {
    // Use the new query parameter approach
    const response = await fetchMissionParticipations({ userId });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.error || 'Failed to fetch participations by user ID'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get participations by mission ID
 * @param missionId - Mission ID
 * @returns Promise with mission's participations data
 */
export const fetchParticipationsByMissionId = async (missionId: number): Promise<ApiResponse<MissionParticipation[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-participations/mission/${missionId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    console.log('fetchParticipationsByMissionId raw response:', result);
    console.log('Response structure analysis:', {
      hasData: !!result.data,
      dataType: typeof result.data,
      isDataArray: Array.isArray(result.data),
      hasMissionParticipations: !!result.data?.missionParticipations,
      missionParticipationsType: typeof result.data?.missionParticipations,
      isMissionParticipationsArray: Array.isArray(result.data?.missionParticipations),
      dataKeys: result.data ? Object.keys(result.data) : [],
      resultKeys: Object.keys(result)
    });

    // Handle multiple possible response structures
    let participationsData = [];

    if (result.data?.missionParticipations && Array.isArray(result.data.missionParticipations)) {
      console.log('Using data.missionParticipations path');
      participationsData = result.data.missionParticipations;
    } else if (result.data && Array.isArray(result.data)) {
      console.log('Using data array path');
      participationsData = result.data;
    } else if (Array.isArray(result)) {
      console.log('Using root array path');
      participationsData = result;
    } else if (result.data) {
      console.log('Using fallback data path');
      // If data exists but is not an array, wrap it
      participationsData = Array.isArray(result.data) ? result.data : [result.data];
    } else {
      console.log('No valid data found, using empty array');
    }

    console.log('fetchParticipationsByMissionId processed data:', participationsData);
    console.log('Sample participation (first item):', participationsData[0]);

    return {
      success: true,
      data: participationsData
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get completed participations by user ID (DEPRECATED - use fetchMissionParticipations with userId and isCompleted query parameters)
 * @param userId - User ID
 * @returns Promise with user's completed mission participations data
 * @deprecated Use fetchMissionParticipations({ userId, isCompleted: true }) instead
 */
export const fetchCompletedParticipationsByUserId = async (userId: number): Promise<ApiResponse<MissionParticipation[]>> => {
  console.warn('fetchCompletedParticipationsByUserId is deprecated. Use fetchMissionParticipations({ userId, isCompleted: true }) instead.');

  try {
    // Use the new query parameter approach
    const response = await fetchMissionParticipations({ userId, isCompleted: true });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.error || 'Failed to fetch completed participations by user ID'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get pending participations by user ID (DEPRECATED - use fetchMissionParticipations with userId and isCompleted query parameters)
 * @param userId - User ID
 * @returns Promise with user's pending mission participations data
 * @deprecated Use fetchMissionParticipations({ userId, isCompleted: false }) instead
 */
export const fetchPendingParticipationsByUserId = async (userId: number): Promise<ApiResponse<MissionParticipation[]>> => {
  console.warn('fetchPendingParticipationsByUserId is deprecated. Use fetchMissionParticipations({ userId, isCompleted: false }) instead.');

  try {
    // Use the new query parameter approach
    const response = await fetchMissionParticipations({ userId, isCompleted: false });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.error || 'Failed to fetch pending participations by user ID'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get specific user-mission participation (DEPRECATED - use fetchMissionParticipations with userId and missionId query parameters)
 * @param userId - User ID
 * @param missionId - Mission ID
 * @returns Promise with specific user-mission participation data
 * @deprecated Use fetchMissionParticipations({ userId, missionId }) instead
 */
export const fetchUserMissionParticipation = async (userId: number, missionId: number): Promise<ApiResponse<MissionParticipation>> => {
  console.warn('fetchUserMissionParticipation is deprecated. Use fetchMissionParticipations({ userId, missionId }) instead.');

  try {
    // Use the new query parameter approach
    const response = await fetchMissionParticipations({ userId, missionId });

    if (response.success && response.data && response.data.length > 0) {
      return {
        success: true,
        data: response.data[0] // Return the first (and should be only) result
      };
    } else {
      return {
        success: false,
        error: response.error || 'No participation found for user and mission'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update mission participation by ID (partial update)
 * @param id - Mission participation ID
 * @param participationData - Updated mission participation data (partial)
 * @returns Promise with updated mission participation data
 */
export const updateMissionParticipation = async (id: number, participationData: UpdateMissionParticipationRequest): Promise<ApiResponse<MissionParticipation>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-participations/${id}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(participationData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Mark mission as completed (DEPRECATED - use updateMissionParticipation with isCompleted: true)
 * @param id - Mission participation ID
 * @returns Promise with updated mission participation data
 * @deprecated Use updateMissionParticipation(id, { isCompleted: true }) instead
 */
export const markMissionAsCompleted = async (id: number): Promise<ApiResponse<MissionParticipation>> => {
  console.warn('markMissionAsCompleted is deprecated. Use updateMissionParticipation(id, { isCompleted: true }) instead.');

  try {
    // Use the new PATCH approach with body data
    const response = await updateMissionParticipation(id, { isCompleted: true });

    if (response.success) {
      return response;
    } else {
      return {
        success: false,
        error: response.error || 'Failed to mark mission as completed'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete mission participation by ID
 * @param id - Mission participation ID
 * @returns Promise with deletion result
 */
export const deleteMissionParticipation = async (id: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-participations/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get enhanced mission statistics with full data
 * @param missionId - Mission ID
 * @returns Promise with enhanced mission statistics data
 */
export const fetchEnhancedMissionStatistics = async (missionId: number): Promise<ApiResponse<EnhancedMissionStats>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-participations/mission/${missionId}/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    console.log('fetchEnhancedMissionStatistics raw response:', result);

    // Return the full enhanced data structure
    if (result.data) {
      return {
        success: true,
        data: result.data
      };
    }

    return {
      success: false,
      error: 'No statistics data received'
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission statistics (legacy function for backward compatibility)
 * @param missionId - Mission ID
 * @returns Promise with mission statistics data
 */
export const fetchMissionStatistics = async (missionId: number): Promise<ApiResponse<MissionStats>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-participations/mission/${missionId}/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Handle the nested response structure and map to legacy format
    const stats = result.data?.statistics;
    if (stats) {
      return {
        success: true,
        data: {
          totalParticipants: stats.uniqueParticipants || stats.totalParticipations || 0,
          completedParticipants: stats.completedParticipations || 0,
          completionRate: stats.completionRate || 0
        }
      };
    }

    // Fallback for direct data format
    return {
      success: true,
      data: {
        totalParticipants: result.data?.totalParticipants || 0,
        completedParticipants: result.data?.completedParticipants || 0,
        completionRate: result.data?.completionRate || 0
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get user statistics
 * @param userId - User ID
 * @returns Promise with user statistics data
 */
export const fetchUserStatistics = async (userId: number): Promise<ApiResponse<{ totalParticipations: number; completedMissions: number; completionRate: number }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-participations/user/${userId}/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

// Valid sortBy fields for mission participations
export const MISSION_PARTICIPATIONS_SORT_FIELDS = [
  'id',
  'userId',
  'missionId',
  'isCompleted',
  'createdAt',
  'updatedAt'
] as const;

// Validation constraints
export const MISSION_PARTICIPATIONS_CONSTRAINTS = {
  MAX_LIMIT: 100,
  DEFAULT_LIMIT: 20,
  DEFAULT_PAGE: 1,
  DEFAULT_SORT_BY: 'createdAt',
  DEFAULT_SORT_ORDER: 'DESC'
} as const;

// Helper constants for UI components
export const PARTICIPATION_STATUS_OPTIONS = [
  { value: true, label: 'Completed' },
  { value: false, label: 'Pending' }
] as const;

// Helper function to get participation status display
export const getParticipationStatusDisplay = (isCompleted: boolean) => {
  return isCompleted
    ? { label: 'Completed', color: 'text-green-400' }
    : { label: 'Pending', color: 'text-yellow-400' };
};

/**
 * Validate and sanitize mission participations query parameters
 * @param params - Raw query parameters
 * @returns Validated and sanitized parameters
 */
export const validateMissionParticipationsQueryParams = (params: any): MissionParticipationsQueryParams => {
  const validated: MissionParticipationsQueryParams = {};

  // Pagination validation
  if (params.page !== undefined) {
    const page = parseInt(params.page);
    validated.page = page >= 1 ? page : MISSION_PARTICIPATIONS_CONSTRAINTS.DEFAULT_PAGE;
  }

  if (params.limit !== undefined) {
    const limit = parseInt(params.limit);
    validated.limit = limit >= 1 && limit <= MISSION_PARTICIPATIONS_CONSTRAINTS.MAX_LIMIT
      ? limit
      : MISSION_PARTICIPATIONS_CONSTRAINTS.DEFAULT_LIMIT;
  }

  // Sorting validation
  if (params.sortBy && MISSION_PARTICIPATIONS_SORT_FIELDS.includes(params.sortBy)) {
    validated.sortBy = params.sortBy;
  }

  if (params.sortOrder && ['ASC', 'DESC', 'asc', 'desc'].includes(params.sortOrder)) {
    validated.sortOrder = params.sortOrder.toUpperCase() as 'ASC' | 'DESC';
  }

  // Filter validation
  if (params.userId !== undefined) {
    const userId = parseInt(params.userId);
    if (!isNaN(userId) && userId > 0) {
      validated.userId = userId;
    }
  }

  if (params.missionId !== undefined) {
    const missionId = parseInt(params.missionId);
    if (!isNaN(missionId) && missionId > 0) {
      validated.missionId = missionId;
    }
  }

  if (params.isCompleted !== undefined) {
    validated.isCompleted = Boolean(params.isCompleted);
  }

  // Date range filters validation (Unix timestamps in seconds)
  ['createdAtFrom', 'createdAtTo', 'updatedAtFrom', 'updatedAtTo'].forEach(field => {
    if (params[field] !== undefined) {
      const timestamp = parseInt(params[field]);
      if (!isNaN(timestamp) && timestamp >= 0) {
        (validated as any)[field] = timestamp;
      }
    }
  });

  // Search parameter
  if (params.search) {
    validated.search = String(params.search);
  }

  return validated;
};
