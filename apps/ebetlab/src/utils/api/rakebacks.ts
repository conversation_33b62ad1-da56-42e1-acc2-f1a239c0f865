// Rakebacks API endpoints and types
// This module handles all rakebacks-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Rakebacks-related interfaces
export interface RakebackCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: number | null;
}

export interface RakebackUsage {
  id: number;
  before_balance: string;
  after_balance: string;
  amount: string;
  amount_usd: string;
  customer_id: number;
  wallet_id: number;
  timestamp: number;
  customer: RakebackCustomer;
}

export interface RakebackAvailable {
  id: number;
  customer_id: number;
  lasttime: number;
  usd_amount: string;
  wallet_amount: string;
  wallet_currency: string;
  customer: RakebackCustomer;
}

export interface RakebackUsagesResponse {
  total: number;
  data: RakebackUsage[];
}

export interface RakebackAvailablesResponse {
  page: string;
  total: number;
  data: RakebackAvailable[];
}

export interface RakebackSearchParams {
  from?: number | null;
  to?: number | null;
  customer_id?: string;
}

/**
 * Fetch rakeback usages data with pagination and date range filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters including date range
 * @returns Promise with rakeback usages data
 */
export const fetchRakebackUsages = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<RakebackSearchParams> = {}
): Promise<ApiResponse<RakebackUsagesResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      from: null,
      to: null,
      limit: limit,
      page: page,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/rakeback-usages/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch rakeback availables data with pagination and date range filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters including date range
 * @returns Promise with rakeback availables data
 */
export const fetchRakebackAvailables = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<RakebackSearchParams> = {}
): Promise<ApiResponse<RakebackAvailablesResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      from: null,
      to: null,
      limit: limit,
      page: page,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/rakeback-availables/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
