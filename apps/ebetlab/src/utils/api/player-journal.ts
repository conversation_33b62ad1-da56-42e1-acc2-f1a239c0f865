// Player Journal API endpoints and types
// This module handles all player actions/journal related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Player Journal related interfaces
export interface PlayerActionDetails {
  [key: string]: any; // Dynamic object structure
}

export interface PlayerActionResult {
  status: string;
  message: string;
}

export interface PlayerActionData {
  id: number;
  merchant_id: number;
  website_id: number;
  wallet_currency: string | null;
  customer_id: number;
  wallet_id: number | null;
  timestamp: number;
  requested: string;
  details: PlayerActionDetails;
  result: PlayerActionResult;
}

export interface PlayerJournalResponse {
  data: {
    total: number;
    data: PlayerActionData[];
  };
  status: number;
  success: boolean;
}

export interface PlayerJournalSearchParams {
  timestamp_start?: string | null;
  timestamp_end?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch player journal/actions data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with player journal data
 */
export const fetchPlayerJournal = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<PlayerJournalSearchParams> = {}
): Promise<ApiResponse<PlayerJournalResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      timestamp_start: null,
      timestamp_end: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/player-actions/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching player journal:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch player journal'
    };
  }
};
