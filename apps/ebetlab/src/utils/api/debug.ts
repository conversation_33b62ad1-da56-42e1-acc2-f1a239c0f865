// Debug API endpoints and types
// This module handles debug API calls to the wildcard endpoint

import { ApiResponse, getAuthHeaders } from './common';

export interface DebugApiRequest {
  url: string;
  method: string;
  body?: any;
}

export interface DebugApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  status?: number;
  headers?: Record<string, string>;
  timestamp?: string;
}

/**
 * Send a debug API request to the wildcard endpoint
 * @param url - Full URL with /debug prefix (e.g., https://service.ebetlab.com/debug/api/operator/customers/index/1/20)
 * @param method - HTTP method (GET, POST, PUT, DELETE, etc.)
 * @param requestBody - Request body as object (will be J<PERSON><PERSON> stringified)
 * @returns Promise with debug API response
 */
export const sendDebugApiRequest = async (
  url: string,
  method: string = 'POST',
  requestBody?: any
): Promise<ApiResponse<DebugApiResponse>> => {
  try {

    // Prepare the request options
    const requestOptions: RequestInit = {
      method: method.toUpperCase(),
      headers: getAuthHeaders(),
    };

    // Add body for methods that support it
    if (requestBody && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      requestOptions.body = JSON.stringify(requestBody);
    }

    console.log('Debug API Request:', {
      url,
      method: method.toUpperCase(),
      headers: requestOptions.headers,
      body: requestOptions.body
    });

    const startTime = Date.now();
    const response = await fetch(url, requestOptions);
    const endTime = Date.now();

    // Get response headers
    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });

    let responseData;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }

    const debugResponse: DebugApiResponse = {
      success: response.ok,
      data: responseData,
      status: response.status,
      headers: responseHeaders,
      timestamp: new Date().toISOString(),
    };

    if (!response.ok) {
      debugResponse.error = `HTTP ${response.status}: ${response.statusText}`;
    }

    console.log('Debug API Response:', {
      status: response.status,
      statusText: response.statusText,
      duration: `${endTime - startTime}ms`,
      data: responseData
    });

    return {
      success: true,
      data: debugResponse
    };

  } catch (error) {
    console.error('Debug API Error:', error);
    
    const errorResponse: DebugApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date().toISOString(),
    };

    return {
      success: false,
      data: errorResponse,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get example request body for common endpoints
 * @param endpoint - The endpoint path (e.g., '/api/operator/customers/index/1/20')
 * @returns Example request body object
 */
export const getExampleRequestBody = (endpoint: string): any => {
  if (endpoint.includes('/customers/index')) {
    return {
      username: null,
      email: null,
      first_name: null,
      last_name: null,
      phone: null,
      country_id: null,
      currency: null,
      status: null,
      is_verified: null,
      is_active: null,
      created_from: null,
      created_to: null,
      last_login_from: null,
      last_login_to: null,
      sortBy: null,
      direction: null,
      page: 1,
      limit: 20,
      rt: Math.floor(Date.now() / 1000)
    };
  }

  if (endpoint.includes('/transactions/index')) {
    return {
      id: null,
      currency: null,
      operator_id: null,
      type: null,
      status_id: null,
      tx: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      customer_id: null,
      page: 1,
      limit: 20,
      rt: Math.floor(Date.now() / 1000)
    };
  }

  // Default request body
  return {
    page: 1,
    limit: 20,
    rt: Math.floor(Date.now() / 1000)
  };
};
