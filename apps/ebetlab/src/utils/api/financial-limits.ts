// Financial limits API endpoints and types
// This module handles all financial limits-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Financial limits-related interfaces
export interface FinancialLimitsData {
  id: number;
  merchant_id: number;
  website_id: number;
  customer_id: number;
  withdraw_able: boolean;
  auto_withdraw_limit: string;
  min_withdraw: string;
  max_withdraw: string;
  rakeback_able: boolean;
  reload_able: boolean;
  tips_able: boolean;
  affiliate_funds_able: boolean;
  boost_able: boolean;
  race_able: boolean;
  raffle_able: boolean;
}

export interface FinancialLimitsResponse {
  data: FinancialLimitsData;
  status: number;
  success: boolean;
}

/**
 * Fetch financial limits data for a customer
 * @param customerId - Customer ID as string
 * @returns Promise with financial limits data
 */
export const fetchFinancialLimits = async (customerId: string): Promise<ApiResponse<FinancialLimitsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customer-limits/show/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        id: parseInt(customerId),
        rt: Math.floor(Date.now() / 1000)
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update financial limits for a customer
 * @param customerId - Customer ID as string
 * @param limitsData - Financial limits data to update (must include all required fields)
 * @returns Promise with update result
 */
export const updateFinancialLimits = async (customerId: string, limitsData: FinancialLimitsData): Promise<ApiResponse<FinancialLimitsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Ensure all required fields are included in the request body
    const requestBody = {
      id: limitsData.id,
      merchant_id: limitsData.merchant_id,
      website_id: limitsData.website_id,
      customer_id: limitsData.customer_id,
      withdraw_able: limitsData.withdraw_able,
      auto_withdraw_limit: limitsData.auto_withdraw_limit,
      min_withdraw: limitsData.min_withdraw,
      max_withdraw: limitsData.max_withdraw,
      rakeback_able: limitsData.rakeback_able,
      reload_able: limitsData.reload_able,
      tips_able: limitsData.tips_able,
      affiliate_funds_able: limitsData.affiliate_funds_able,
      boost_able: limitsData.boost_able,
      race_able: limitsData.race_able,
      raffle_able: limitsData.raffle_able,
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customer-limits/update/${customerId}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
