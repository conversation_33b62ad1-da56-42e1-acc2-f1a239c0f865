// Verification codes API
// This module handles API calls related to verification codes

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Types for verification code data
export interface VerificationCodeCustomer {
  id: number;
  username: string;
  email: string;
  rank: string;
  masked_username: string;
  last_action: number;
}

export interface VerificationCodeData {
  id: number;
  code: string;
  customer_id: number;
  type: string;
  timestamp: string;
  used_at: string | null;
  customer: VerificationCodeCustomer;
}

export interface VerificationCodesResponse {
  total: number;
  data: VerificationCodeData[];
}

export interface VerificationCodeSearchParams {
  username?: string | null;
  from?: number | null;
  to?: number | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch verification codes data with pagination
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with verification codes data
 */
export const fetchVerificationCodes = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<VerificationCodeSearchParams> = {}
): Promise<ApiResponse<VerificationCodesResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      username: null,
      from: null,
      to: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/verification-codes/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
