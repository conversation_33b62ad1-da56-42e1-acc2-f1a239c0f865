// Market Products API endpoints and types
// This module handles all market-products-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, BaseQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Market Products API specific response interface
interface MarketProductsApiResponse {
  success: boolean;
  data: MarketProduct[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
}

// Market Product interfaces
export interface MarketSelectedGame {
  id: number;
  name: string;
  image: string;
}

export interface MarketSelectedProvider {
  provider: {
    id: number;
    identifier: string;
    name: string;
    image: string;
    total: number;
  };
  games: MarketSelectedGame[];
}

export interface MarketProduct {
  id: number;
  name: string;
  slug: string;
  name_i18n: Record<string, string>;
  description_i18n: Record<string, string>;
  type: ProductType;
  category: ProductCategory;
  availableAmount: number | null;
  isMultiPerBuyer: boolean;
  photoUrl: string;
  price: number;
  currencies: string[];
  providers: MarketSelectedProvider[] | Record<string, number[]>; // Support both new and legacy formats
  createdAt: string;
  updatedAt: string;
}

export interface CreateMarketProductRequest {
  name: string;
  slug?: string;
  name_i18n?: Record<string, string>;
  description_i18n?: Record<string, string>;
  type: ProductType;
  category: ProductCategory;
  availableAmount?: number | null;
  isMultiPerBuyer?: boolean;
  photoUrl: string;
  price: number;
  currencies?: string[];
  providers: MarketSelectedProvider[];
}

export interface UpdateMarketProductRequest {
  name?: string;
  slug?: string;
  name_i18n?: Record<string, string>;
  description_i18n?: Record<string, string>;
  type?: ProductType;
  category?: ProductCategory;
  availableAmount?: number | null;
  isMultiPerBuyer?: boolean;
  photoUrl?: string;
  price?: number;
  currencies?: string[];
  providers?: MarketSelectedProvider[];
}

// Enums and constants
export type ProductType = 'general' | 'slots';
export type ProductCategory = 'free_spins' | 'cash' | 'reload' | 'scatter';

export const PRODUCT_TYPE_OPTIONS: { value: ProductType; label: string }[] = [
  { value: 'general', label: 'General' },
  { value: 'slots', label: 'Slots' }
];

export const PRODUCT_CATEGORY_OPTIONS: { value: ProductCategory; label: string }[] = [
  { value: 'free_spins', label: 'Free Spins' },
  { value: 'cash', label: 'Cash' },
  { value: 'reload', label: 'Reload' },
  { value: 'scatter', label: 'Scatter' }
];

// Query parameters interface
export interface MarketProductsQueryParams extends BaseQueryParams {
  type?: ProductType;
  category?: ProductCategory;
  minPrice?: number;
  maxPrice?: number;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
}

// Sort fields for market products
export const MARKET_PRODUCTS_SORT_FIELDS = [
  { value: 'id', label: 'ID' },
  { value: 'name', label: 'Name' },
  { value: 'type', label: 'Type' },
  { value: 'category', label: 'Category' },
  { value: 'price', label: 'Price' },
  { value: 'availableAmount', label: 'Available Amount' },
  { value: 'createdAt', label: 'Created At' },
  { value: 'updatedAt', label: 'Updated At' }
];

/**
 * Get all market products with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated market products data
 */
export const fetchMarketProducts = async (queryParams?: MarketProductsQueryParams): Promise<PaginatedApiResponse<MarketProduct>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const queryString = queryParams ? buildQueryString(queryParams) : '';
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-products${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: MarketProductsApiResponse = await response.json();

    // Transform the response to match our expected format
    if (result.success && result.data) {
      // Handle pagination data from the 'pagination' field in the response
      const paginationData = result.pagination;

      return {
        success: true,
        data: Array.isArray(result.data) ? result.data : [],
        meta: paginationData ? {
          page: paginationData.page,
          limit: paginationData.limit,
          total: paginationData.total,
          totalPages: paginationData.totalPages,
          hasNext: paginationData.page < paginationData.totalPages,
          hasPrev: paginationData.page > 1
        } : {
          page: 1,
          limit: 20,
          total: Array.isArray(result.data) ? result.data.length : 0,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      };
    }

    return {
      success: false,
      data: [],
      meta: { page: 1, limit: 20, total: 0, totalPages: 0, hasNext: false, hasPrev: false },
      error: result.error || 'Failed to fetch market products'
    };
  } catch (error) {
    console.error('Error fetching market products:', error);
    return {
      success: false,
      data: [],
      meta: { page: 1, limit: 20, total: 0, totalPages: 0, hasNext: false, hasPrev: false },
      error: error instanceof Error ? error.message : 'Failed to fetch market products'
    };
  }
};

/**
 * Get a single market product by ID
 * @param id - Market product ID
 * @returns Promise with market product data
 */
export const fetchMarketProduct = async (id: number): Promise<ApiResponse<MarketProduct>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-products/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('Market Product API Response:', result);

    return result;
  } catch (error) {
    console.error('Error fetching market product:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch market product'
    };
  }
};

/**
 * Create a new market product
 * @param productData - Market product data to create
 * @returns Promise with created market product data
 */
export const createMarketProduct = async (productData: CreateMarketProductRequest): Promise<ApiResponse<MarketProduct>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    console.log('Creating market product:', productData);

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-products`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(productData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('Create Market Product API Response:', result);

    return result;
  } catch (error) {
    console.error('Error creating market product:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create market product'
    };
  }
};

/**
 * Update an existing market product
 * @param id - Market product ID to update
 * @param productData - Partial market product data to update
 * @returns Promise with updated market product data
 */
export const updateMarketProduct = async (id: number, productData: UpdateMarketProductRequest): Promise<ApiResponse<MarketProduct>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    console.log('Updating market product:', { id, productData });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-products/${id}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(productData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('Update Market Product API Response:', result);

    return result;
  } catch (error) {
    console.error('Error updating market product:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update market product'
    };
  }
};

/**
 * Delete a market product
 * @param id - Market product ID to delete
 * @returns Promise with deletion result
 */
export const deleteMarketProduct = async (id: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    console.log('Deleting market product:', id);

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/market-products/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('Delete Market Product API Response:', result);

    return result;
  } catch (error) {
    console.error('Error deleting market product:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete market product'
    };
  }
};

// Utility functions
export const getProductTypeDisplay = (type: ProductType): string => {
  const option = PRODUCT_TYPE_OPTIONS.find(opt => opt.value === type);
  return option ? option.label : type;
};

export const getProductCategoryDisplay = (category: ProductCategory): string => {
  const option = PRODUCT_CATEGORY_OPTIONS.find(opt => opt.value === category);
  return option ? option.label : category;
};

export const formatPrice = (price: number, currency?: string): string => {
  return Math.round(price).toLocaleString() + ' points';
};

export const getAvailabilityDisplay = (availableAmount: number | null): string => {
  if (availableAmount === null) return 'Unlimited';
  if (availableAmount === 0) return 'Out of Stock';
  return `${availableAmount} available`;
};

export const getAvailabilityStatus = (availableAmount: number | null): 'unlimited' | 'available' | 'low' | 'out' => {
  if (availableAmount === null) return 'unlimited';
  if (availableAmount === 0) return 'out';
  if (availableAmount <= 5) return 'low';
  return 'available';
};

/**
 * Fetch all available currencies from the ebetlab public API
 * @returns Promise with currencies data
 */
export const fetchAvailableCurrencies = async (): Promise<ApiResponse<string[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/ebetlab/public/currencies`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch currencies');
    }

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Error fetching available currencies:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch available currencies'
    };
  }
};
