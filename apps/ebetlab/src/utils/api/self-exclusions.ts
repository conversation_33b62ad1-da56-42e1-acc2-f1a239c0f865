// Self-exclusions API
// This module handles API calls related to self-exclusions

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Types for self-exclusion data
export interface SelfExclusionCustomer {
  id: number;
  username: string;
  masked_username: string;
  last_action: number;
}

export interface SelfExclusionData {
  id: number;
  customer_id: number;
  timestamp: number;
  excluded_till: number;
  step: number;
  is_active: number;
  customer: SelfExclusionCustomer;
}

export interface SelfExclusionsResponse {
  total: number;
  data: SelfExclusionData[];
}

export interface SelfExclusionSearchParams {
  id?: number | null;
  username?: string | null;
  step?: number | null;
  is_active?: number | null;
  from?: string | null;
  to?: string | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch self-exclusions data with pagination
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with self-exclusions data
 */
export const fetchSelfExclusions = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<SelfExclusionSearchParams> = {}
): Promise<ApiResponse<SelfExclusionsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      username: null,
      step: null,
      is_active: null,
      from: null,
      to: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/self-exclusions/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
