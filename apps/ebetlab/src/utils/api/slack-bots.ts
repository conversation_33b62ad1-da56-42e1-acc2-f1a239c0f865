// Slack Bots API endpoints and types
// This module handles all slack bots-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Slack Bot interfaces
export interface SlackBot {
  id: string;
  route: string;
  channel: string;
  registeredAt: string;
  status: 'active' | 'inactive';
  description: string;
  type: string;
}

export interface SlackBotStats {
  totalBots: number;
  activeChannels: number;
  isSlackConfigured: boolean;
  registrationTimeRange: {
    earliest: number;
    latest: number;
  };
}

export interface SlackBotsResponse {
  bots: SlackBot[];
  stats: SlackBotStats;
}

/**
 * Fetch all slack bots information
 * @returns Promise with slack bots data and statistics
 */
export const fetchSlackBots = async (): Promise<ApiResponse<SlackBotsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/slack-bots`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    console.error('Error fetching slack bots:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch slack bots'
    };
  }
};
