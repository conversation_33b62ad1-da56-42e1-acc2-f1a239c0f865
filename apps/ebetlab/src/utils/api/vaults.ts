// Vaults API endpoints and types
// This module handles all vault-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Vault-related interfaces
export interface VaultData {
  id: number;
  currency: string;
  balance: string;
}

export interface VaultsResponse {
  data: VaultData[];
}

export interface VaultSearchParams {
  id?: string | null;
  sender?: string | null;
  taker?: string | null;
  currency?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id: string;
  page: number;
  limit: number;
  rt: number;
}

/**
 * Fetch customer vaults
 * @param customerId - Customer ID as string
 * @returns Promise with vaults data
 */
export const fetchVaults = async (customerId: string): Promise<ApiResponse<VaultsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const requestBody: VaultSearchParams = {
      id: null,
      sender: null,
      taker: null,
      currency: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      customer_id: customerId,
      page: 1,
      limit: 100,
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/vaults/index/1/100`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data?.data || []
    };
  } catch (error) {
    console.error('Error fetching vaults:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch vaults'
    };
  }
};
