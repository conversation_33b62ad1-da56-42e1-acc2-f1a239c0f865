import { getAuthToken, getAuthHeaders } from './common';

// Welcome Bonus interfaces
export interface WelcomeBonusOperator {
  id: number;
  name: string;
}

export interface WelcomeBonusDetails {
  rt: number;
  to: number;
  city: string;
  code: string;
  from: number;
  total: string;
  amount: string;
  country: string;
  real_ip: string;
  ref_code: string;
  usd_amount: string;
  currency_code: string;
  required_wager: string;
  required_wager_currency: string;
}

export interface WelcomeBonusData {
  id: number;
  merchant_id: number;
  website_id: number;
  operator_id: number;
  code: string;
  timestamp: string;
  currency_id: number | null;
  currency_code: string;
  amount: string;
  from: number;
  to: number;
  details: WelcomeBonusDetails;
  is_active: boolean;
  usd_amount: string;
  ref_code: string;
  total: number;
  required_wager: string;
  is_deleted: boolean;
  required_wager_currency: string;
  redeems_count: number;
  operator?: WelcomeBonusOperator; // Optional since it's not in details response
}

export interface WelcomeBonusesResponse {
  data: {
    total: number;
    data: WelcomeBonusData[];
  };
  status: number;
  success: boolean;
}

export interface WelcomeBonusDetailsResponse {
  success: boolean;
  message: string;
  data: WelcomeBonusData;
  timestamp: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface WelcomeBonusesSearchParams {
  id?: number | null;
  operator_id?: number | null;
  is_active?: boolean | null;
  code?: string | null;
  currency_code?: string | null;
  sortBy?: string | null;
  direction?: 'asc' | 'desc' | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch welcome bonuses with optional filters and pagination
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param filters - Optional search filters
 * @returns Promise with welcome bonuses data
 */
export const fetchWelcomeBonuses = async (
  page: number = 1,
  limit: number = 20,
  filters: WelcomeBonusesSearchParams = {}
): Promise<ApiResponse<WelcomeBonusesResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    queryParams.append('rt', Math.floor(Date.now() / 1000).toString());

    // Add filters to query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/welcome-bonuses?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching welcome bonuses:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch welcome bonuses'
    };
  }
};

/**
 * Get welcome bonus details by ID
 * @param bonusId - The ID of the welcome bonus
 * @returns Promise with welcome bonus details
 */
export const fetchWelcomeBonusDetails = async (bonusId: string | number): Promise<ApiResponse<WelcomeBonusDetailsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/welcome-bonuses/${bonusId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching welcome bonus details:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch welcome bonus details'
    };
  }
};

/**
 * Deactivate welcome bonus by ID
 * @param bonusId - The ID of the welcome bonus to deactivate
 * @returns Promise with deactivation result
 */
export const deactivateWelcomeBonus = async (bonusId: string | number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/welcome-bonuses/${bonusId}/cancellations`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error deactivating welcome bonus:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to deactivate welcome bonus'
    };
  }
};

/**
 * Delete welcome bonus by ID
 * @param bonusId - The ID of the welcome bonus to delete
 * @returns Promise with deletion result
 */
export const deleteWelcomeBonus = async (bonusId: string | number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/welcome-bonuses/${bonusId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error deleting welcome bonus:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete welcome bonus'
    };
  }
};

// Welcome Bonus Create/Update interfaces
export interface WelcomeBonusCreateData {
  code: string;
  currency_code: string;
  usd_amount: string;
  from: number;
  to: number;
  required_wager: string;
  amount: string;
  required_wager_currency: string;
  total: string;
  ref_code: string;
}

export interface WelcomeBonusUpdateData {
  code: string;
  currency_code: string;
  currency: string;
  total: number;
  amount: string;
  usd_amount: string;
  ref_code: string;
  required_wager: string;
  required_wager_currency: string;
  from: number;
  to: number;
  id: number;
}

/**
 * Create welcome bonus
 * @param bonusData - The welcome bonus data to create
 * @returns Promise with creation result
 */
export const createWelcomeBonus = async (bonusData: WelcomeBonusCreateData): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/welcome-bonuses`, {
      method: 'POST',
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(bonusData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error creating welcome bonus:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create welcome bonus'
    };
  }
};

/**
 * Update welcome bonus by ID
 * @param bonusId - The ID of the welcome bonus to update
 * @param bonusData - The welcome bonus data to update
 * @returns Promise with update result
 */
export const updateWelcomeBonus = async (bonusId: string | number, bonusData: WelcomeBonusCreateData): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the update request body with additional fields
    const updateRequestBody: WelcomeBonusUpdateData = {
      code: bonusData.code,
      currency_code: bonusData.currency_code,
      currency: bonusData.currency_code, // Same as currency_code
      total: parseInt(bonusData.total), // Convert to number
      amount: bonusData.amount,
      usd_amount: bonusData.usd_amount,
      ref_code: bonusData.ref_code,
      required_wager: bonusData.required_wager,
      required_wager_currency: bonusData.required_wager_currency,
      from: bonusData.from,
      to: bonusData.to,
      id: parseInt(bonusId.toString()) // Include the ID
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/welcome-bonuses/${bonusId}`, {
      method: 'PATCH',
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateRequestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error updating welcome bonus:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update welcome bonus'
    };
  }
};
