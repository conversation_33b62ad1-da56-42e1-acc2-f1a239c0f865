// Trade Bets API endpoints and types
// This module handles all trade bets-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Trade bets-related interfaces
export interface TradeBetCustomer {
  id: number;
  username: string;
  nickname: string | null;
  email: string;
  is_public: boolean;
  rank: string;
  masked_username: string;
  last_action: number;
}

export interface TradeBetData {
  id: number;
  customer_id: number;
  timestamp: number;
  amount: string;
  income: string;
  multiplier: string;
  wallet_currency: string;
  wallet_amount: string;
  wallet_income: string;
  game_id: string;
  amount_usd: string;
  income_usd: string;
  currency: string;
  finished: boolean;
  customer: TradeBetCustomer;
}

export interface TradeBetStats {
  bet: number;
  win: number;
  bet_total: number;
  win_total: number;
}

export interface TradeBetsResponse {
  total: number;
  data: TradeBetData[];
  stats: TradeBetStats;
}

// Search parameters interface for trade bets
export interface TradeBetSearchParams {
  id?: string | null;
  game?: string | null;
  provider_id?: string | null;
  type?: string | null;
  status?: string | null;
  currency?: string | null;
  wallet_currency?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: string | null;
  to?: string | null;
  amount_min?: string | null;
  amount_max?: string | null;
  multiplier_min?: string | null;
  multiplier_max?: string | null;
  income_min?: string | null;
  income_max?: string | null;
  net_min?: string | null;
  net_max?: string | null;
  income_usd_min?: string | null;
  income_usd_max?: string | null;
  wallet_amount_min?: string | null;
  wallet_amount_max?: string | null;
  win_wallet_amount_min?: string | null;
  win_wallet_amount_max?: string | null;
  sortBy?: string | null;
  direction?: string | null;
  customer_id?: string;
}

/**
 * Fetch trade bets data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with trade bets data
 */
export const fetchTradeBets = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<TradeBetSearchParams> = {}
): Promise<ApiResponse<TradeBetsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      game: null,
      provider_id: null,
      type: null,
      status: null,
      currency: null,
      wallet_currency: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      amount_min: null,
      amount_max: null,
      multiplier_min: null,
      multiplier_max: null,
      income_min: null,
      income_max: null,
      net_min: null,
      net_max: null,
      income_usd_min: null,
      income_usd_max: null,
      wallet_amount_min: null,
      wallet_amount_max: null,
      win_wallet_amount_min: null,
      win_wallet_amount_max: null,
      sortBy: null,
      direction: null,
      page: page,
      limit: limit,
      customer_id: null,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    console.log('Trade Bets API Request:', {
      url: `${import.meta.env.VITE_API_URL || ''}/api/operator/trade-debits/index/${page}/${limit}`,
      method: 'POST',
      body: requestBody
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/trade-debits/index/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
