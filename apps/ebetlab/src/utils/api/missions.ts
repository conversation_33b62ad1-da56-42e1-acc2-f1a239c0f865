// Missions API endpoints and types
// This module handles all missions-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, MissionsQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Missions interfaces
export interface Mission {
  id: number;
  name: string;
  missionType: MissionType;
  reward: number;
  description: string;
  startDate: number;
  endDate: number;
  name_i18n: Record<string, string>;
  description_i18n: Record<string, string>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export type MissionType =
  | 'daily'
  | 'weekly'
  | 'monthly'
  | 'custom';

export interface CreateMissionRequest {
  name: string;
  missionType: MissionType;
  reward: number;
  description: string;
  startDate: number;
  endDate: number;
  name_i18n: Record<string, string>;
  description_i18n: Record<string, string>;
  isActive: boolean;
}

export interface UpdateMissionRequest {
  name?: string;
  missionType?: MissionType;
  reward?: number;
  description?: string;
  startDate?: number;
  endDate?: number;
  name_i18n?: Record<string, string>;
  description_i18n?: Record<string, string>;
  isActive?: boolean;
}

export interface MissionsResponse {
  success: boolean;
  data: Mission[];
  message?: string;
}

export interface MissionResponse {
  success: boolean;
  data: Mission;
  message?: string;
}

/**
 * Fetch all missions with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated missions data
 */
export const fetchMissions = async (queryParams?: MissionsQueryParams): Promise<PaginatedApiResponse<Mission>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const queryString = queryParams ? buildQueryString(queryParams) : '';
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/missions${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || [],
      meta: result.meta || {
        page: 1,
        limit: 20,
        total: result.data?.length || 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch all missions (legacy function for backward compatibility)
 * @returns Promise with missions data
 */
export const fetchMissionsLegacy = async (): Promise<ApiResponse<Mission[]>> => {
  try {
    const response = await fetchMissions();
    return {
      success: response.success,
      data: response.data,
      error: response.error
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Create a new mission
 * @param missionData - Mission data to create
 * @returns Promise with created mission data
 */
export const createMission = async (missionData: CreateMissionRequest): Promise<ApiResponse<Mission>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/missions`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(missionData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission by ID
 * @param id - Mission ID
 * @returns Promise with mission data
 */
export const fetchMissionById = async (id: number): Promise<ApiResponse<Mission>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/missions/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Update mission by ID (partial update)
 * @param id - Mission ID
 * @param missionData - Updated mission data (partial)
 * @returns Promise with updated mission data
 */
export const updateMission = async (id: number, missionData: UpdateMissionRequest): Promise<ApiResponse<Mission>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/missions/${id}`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(missionData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete mission by ID
 * @param id - Mission ID
 * @returns Promise with deletion result
 */
export const deleteMission = async (id: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/missions/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get missions by type (DEPRECATED - use fetchMissions with missionType query parameter)
 * @param missionType - Mission type to filter by
 * @returns Promise with filtered missions data
 * @deprecated Use fetchMissions({ missionType }) instead
 */
export const fetchMissionsByType = async (missionType: MissionType): Promise<ApiResponse<Mission[]>> => {
  console.warn('fetchMissionsByType is deprecated. Use fetchMissions({ missionType }) instead.');

  try {
    // Use the new query parameter approach
    const response = await fetchMissions({ missionType });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.error || 'Failed to fetch missions by type'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get active missions (DEPRECATED - use fetchMissions with date range filters)
 * @returns Promise with active missions data
 * @deprecated Use fetchMissions with appropriate date range filters instead
 */
export const fetchActiveMissions = async (): Promise<ApiResponse<Mission[]>> => {
  console.warn('fetchActiveMissions is deprecated. Use fetchMissions with date range filters instead.');

  try {
    const now = Math.floor(Date.now() / 1000);

    // Use the new query parameter approach with date filters for active missions
    const response = await fetchMissions({
      startDateTo: now,  // startDate <= now
      endDateFrom: now   // endDate >= now
    });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.error || 'Failed to fetch active missions'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission details including all its objectives
 * @param id - Mission ID
 * @returns Promise with mission data including objectives
 */
export const fetchMissionWithObjectives = async (id: number): Promise<ApiResponse<Mission & { objectives?: any[] }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/missions/${id}/with-objectives`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get upcoming missions (DEPRECATED - use fetchMissions with date range filters)
 * @returns Promise with upcoming missions data
 * @deprecated Use fetchMissions with startDateFrom filter instead
 */
export const fetchUpcomingMissions = async (): Promise<ApiResponse<Mission[]>> => {
  console.warn('fetchUpcomingMissions is deprecated. Use fetchMissions with startDateFrom filter instead.');

  try {
    const now = Math.floor(Date.now() / 1000);

    // Use the new query parameter approach with date filters for upcoming missions
    const response = await fetchMissions({
      startDateFrom: now  // startDate > now
    });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.error || 'Failed to fetch upcoming missions'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get expired missions (DEPRECATED - use fetchMissions with date range filters)
 * @returns Promise with expired missions data
 * @deprecated Use fetchMissions with endDateTo filter instead
 */
export const fetchExpiredMissions = async (): Promise<ApiResponse<Mission[]>> => {
  console.warn('fetchExpiredMissions is deprecated. Use fetchMissions with endDateTo filter instead.');

  try {
    const now = Math.floor(Date.now() / 1000);

    // Use the new query parameter approach with date filters for expired missions
    const response = await fetchMissions({
      endDateTo: now  // endDate < now
    });

    if (response.success && response.data) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.error || 'Failed to fetch expired missions'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

// Helper constants for UI components
export const MISSION_TYPE_OPTIONS = [
  { value: 'daily', label: 'Daily Mission' },
  { value: 'weekly', label: 'Weekly Mission' },
  { value: 'monthly', label: 'Monthly Mission' },
  { value: 'custom', label: 'Custom Period' }
] as const;

// Helper function to get mission status based on dates
export const getMissionStatus = (mission: Mission): 'upcoming' | 'active' | 'expired' => {
  // Daily/weekly/monthly missions are always active
  if (mission.missionType === 'daily' || mission.missionType === 'weekly' || mission.missionType === 'monthly') {
    return 'active';
  }

  // Only custom missions use date-based status calculation
  const now = Math.floor(Date.now() / 1000);
  if (now < mission.startDate) return 'upcoming';
  if (now > mission.endDate) return 'expired';
  return 'active';
};

// Helper function to format mission status with colors
export const getMissionStatusDisplay = (mission: Mission) => {
  const status = getMissionStatus(mission);
  const statusConfig = {
    upcoming: { label: 'Upcoming', color: 'text-blue-400' },
    active: { label: 'Active', color: 'text-green-400' },
    expired: { label: 'Expired', color: 'text-red-400' }
  };
  return statusConfig[status];
};
