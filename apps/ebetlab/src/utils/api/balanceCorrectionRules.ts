import { getAuthToken, getAuthHeaders } from './common';

// Balance Correction Rule interfaces
export interface BalanceCorrectionRuleOperator {
  id: number;
  name: string;
}

export interface BalanceCorrectionRuleData {
  id: number;
  min_usd: string;
  range: string;
  is_auto: boolean;
  limits: string | null;
  currencies: string | null;
  redeem_code: string;
  deposit_order: number | null;
  max_usd_amount: string;
  max_usd: string;
  from: number;
  to: number;
  operator_id: number;
  is_active: number;
  amount_percentage: string;
  wager_percentage: string;
  timestamp: number;
  disable_discount: boolean;
  bonus_id: number;
  reference_tag: string;
  ref_id: number | null;
  affiliator_id: number | null;
  ref_code: string | null;
  name: string;
  operator: BalanceCorrectionRuleOperator;
  referrer: any | null;
  affiliator: any | null;
}

export interface BalanceCorrectionRulesResponse {
  success: boolean;
  message: string;
  data: {
    total: number;
    data: BalanceCorrectionRuleData[];
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface BalanceCorrectionRulesSearchParams {
  id?: number | null;
  operator_id?: number | null;
  is_active?: boolean | null;
  redeem_code?: string | null;
  sortBy?: string | null;
  direction?: 'asc' | 'desc' | null;
  page?: number;
  limit?: number;
  rt?: number;
}

/**
 * Fetch balance correction rules with optional filters and pagination
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param filters - Optional search filters
 * @returns Promise with balance correction rules data
 */
export const fetchBalanceCorrectionRules = async (
  page: number = 1,
  limit: number = 20,
  filters: BalanceCorrectionRulesSearchParams = {}
): Promise<ApiResponse<BalanceCorrectionRulesResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    queryParams.append('rt', Math.floor(Date.now() / 1000).toString());

    // Add filters to query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/balance-correction-rules?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching balance correction rules:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch balance correction rules'
    };
  }
};

/**
 * Deactivate balance correction rule by ID
 * @param ruleId - The ID of the balance correction rule to deactivate
 * @returns Promise with deactivation result
 */
export const deactivateBalanceCorrectionRule = async (ruleId: string | number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/balance-correction-rules/${ruleId}/cancellations`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error deactivating balance correction rule:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to deactivate balance correction rule'
    };
  }
};

/**
 * Delete balance correction rule by ID
 * @param ruleId - The ID of the balance correction rule to delete
 * @returns Promise with deletion result
 */
export const deleteBalanceCorrectionRule = async (ruleId: string | number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/balance-correction-rules/${ruleId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error deleting balance correction rule:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete balance correction rule'
    };
  }
};

// Balance Correction Rule Claims interfaces
export interface BalanceCorrectionRuleClaim {
  id: number;
  merchant_id: number;
  website_id: number;
  operator_id: number | null;
  customer_id: number;
  wallet_id: number;
  way: string;
  before_balance: string;
  amount: string;
  after_balance: string;
  timestamp: number;
  details: any;
  currency: string;
  usd_amount: string;
  required_wager: string;
  required_wager_currency: string | null;
  completed: boolean;
  reason: string | null;
  morph_id: number;
  morph_key: string;
  note: string;
  wagered: string;
  wagered_currency: string | null;
  is_active: boolean;
  model: string;
  completed_at: number | null;
  customer: {
    id: number;
    username: string;
    email: string;
    rank: string;
    masked_username: string;
    last_action: string | null;
  };
}

export interface BalanceCorrectionRuleClaimsResponse {
  data: {
    bonus: any;
    claims: {
      total: number;
      data: BalanceCorrectionRuleClaim[];
    };
  };
}

/**
 * Fetch balance correction rule claims
 * @param ruleId - The ID of the balance correction rule
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @returns Promise with claims data
 */
export const fetchBalanceCorrectionRuleClaims = async (
  ruleId: string | number,
  page: number = 1,
  limit: number = 20
): Promise<ApiResponse<BalanceCorrectionRuleClaimsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/balance-correction-rules/${ruleId}/claims?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching balance correction rule claims:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch balance correction rule claims'
    };
  }
};
