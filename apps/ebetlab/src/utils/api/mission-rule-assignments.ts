// Mission Rule Assignments API endpoints and types
// This module handles all mission-rule-assignments-related API calls for Makroz entities

import { ApiResponse, PaginatedApiResponse, MissionRuleAssignmentsQueryParams, getAuthToken, getAuthHeaders, buildQueryString } from './common';

// Mission Rule Assignments interfaces
export interface MissionRuleAssignment {
  id: number;
  missionId: number;
  missionRuleId: number;
  createdAt: string;
  updatedAt: string;
  mission?: Mission;
  missionRule?: MissionRule;
}

export interface Mission {
  id: number;
  name: string;
  missionType: string;
  reward: number;
  description?: string;
  startDate?: number;
  endDate?: number;
}

export interface MissionRule {
  id: number;
  ruleType: string;
  compare: string;
  compareValue: string;
  minDate?: number | null;
  maxDate?: number | null;
}

export interface CreateMissionRuleAssignmentRequest {
  missionId: number;
  missionRuleId: number;
}

export interface AssignMultipleRulesRequest {
  missionRuleIds: number[];
}

export interface ReplaceAllRulesRequest {
  missionRuleIds: number[];
}

export interface MissionRuleAssignmentsResponse {
  success: boolean;
  data: MissionRuleAssignment[];
  message?: string;
}

export interface MissionRuleAssignmentResponse {
  success: boolean;
  data: MissionRuleAssignment;
  message?: string;
}

export interface AssignmentStatsResponse {
  success: boolean;
  data: {
    totalAssignments: number;
    missionsWithRules: number;
    rulesInUse: number;
  };
  message?: string;
}

/**
 * Create a new mission rule assignment
 * @param assignmentData - Mission rule assignment data to create
 * @returns Promise with created mission rule assignment data
 */
export const createMissionRuleAssignment = async (assignmentData: CreateMissionRuleAssignmentRequest): Promise<ApiResponse<MissionRuleAssignment>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(assignmentData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch all mission rule assignments with optional filtering, pagination, and sorting
 * @param queryParams - Optional query parameters for filtering, pagination, and sorting
 * @returns Promise with paginated mission rule assignments data
 */
export const fetchMissionRuleAssignments = async (queryParams?: MissionRuleAssignmentsQueryParams): Promise<PaginatedApiResponse<MissionRuleAssignment>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const queryString = queryParams ? buildQueryString(queryParams) : '';
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments${queryString}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || [],
      meta: result.meta || {
        page: 1,
        limit: 20,
        total: result.data?.length || 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      meta: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch all mission rule assignments (legacy function for backward compatibility)
 * @returns Promise with mission rule assignments data
 */
export const fetchMissionRuleAssignmentsLegacy = async (): Promise<ApiResponse<MissionRuleAssignment[]>> => {
  try {
    const response = await fetchMissionRuleAssignments();
    return {
      success: response.success,
      data: response.data,
      error: response.error
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get mission rule assignment by ID
 * @param id - Mission rule assignment ID
 * @returns Promise with mission rule assignment data
 */
export const fetchMissionRuleAssignmentById = async (id: number): Promise<ApiResponse<MissionRuleAssignment>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments/${id}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get assignments by mission ID
 * @param missionId - Mission ID
 * @returns Promise with mission's rule assignments data
 */
export const fetchAssignmentsByMissionId = async (missionId: number): Promise<ApiResponse<MissionRuleAssignment[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments/mission/${missionId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Handle new API response structure: {success, message, data: {assignments: [...], mission: {...}}}
    // or proxy server response structure: {success, message, data: {total, data: [...]}, timestamp}
    // or direct API response: {data: [...]} or [...]
    let assignmentsData = [];

    if (result.data) {
      // Check for new API structure with assignments array
      if (result.data.assignments && Array.isArray(result.data.assignments)) {
        assignmentsData = result.data.assignments;
      }
      // Check for proxy structure with nested data array
      else if (result.data.data && Array.isArray(result.data.data)) {
        assignmentsData = result.data.data;
      }
      // Check for direct data array
      else if (Array.isArray(result.data)) {
        assignmentsData = result.data;
      }
    } else if (Array.isArray(result)) {
      // Direct array response
      assignmentsData = result;
    }

    return {
      success: true,
      data: assignmentsData
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get assignments by mission rule ID
 * @param missionRuleId - Mission rule ID
 * @returns Promise with rule's assignments data
 */
export const fetchAssignmentsByMissionRuleId = async (missionRuleId: number): Promise<ApiResponse<MissionRuleAssignment[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments/rule/${missionRuleId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Handle proxy server response structure: {success, message, data: {total, data: [...]}, timestamp}
    // or direct API response: {data: [...]} or [...]
    let assignmentsData = [];

    if (result.data) {
      // If result.data exists, check if it has a nested data array (proxy structure)
      if (result.data.data && Array.isArray(result.data.data)) {
        assignmentsData = result.data.data;
      } else if (Array.isArray(result.data)) {
        assignmentsData = result.data;
      }
    } else if (Array.isArray(result)) {
      // Direct array response
      assignmentsData = result;
    }

    return {
      success: true,
      data: assignmentsData
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get specific assignment by mission and rule
 * @param missionId - Mission ID
 * @param missionRuleId - Mission rule ID
 * @returns Promise with specific assignment data
 */
export const fetchAssignmentByMissionAndRule = async (missionId: number, missionRuleId: number): Promise<ApiResponse<MissionRuleAssignment>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments/mission/${missionId}/rule/${missionRuleId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Assign multiple rules to mission (DEPRECATED - use replaceAllRulesForMission with PUT method)
 * @param missionId - Mission ID
 * @param rulesData - Multiple rule IDs to assign
 * @returns Promise with created assignments data
 * @deprecated Use replaceAllRulesForMission(missionId, rulesData) instead
 */
export const assignMultipleRulesToMission = async (missionId: number, rulesData: AssignMultipleRulesRequest): Promise<ApiResponse<MissionRuleAssignment[]>> => {
  console.warn('assignMultipleRulesToMission is deprecated. Use replaceAllRulesForMission(missionId, rulesData) instead.');

  try {
    // Use the new PUT bulk operation approach
    const response = await replaceAllRulesForMission(missionId, rulesData);

    if (response.success) {
      return response;
    } else {
      return {
        success: false,
        error: response.error || 'Failed to assign multiple rules to mission'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Replace all rules for mission
 * @param missionId - Mission ID
 * @param rulesData - Rule IDs to replace with
 * @returns Promise with new assignments data
 */
export const replaceAllRulesForMission = async (missionId: number, rulesData: ReplaceAllRulesRequest): Promise<ApiResponse<MissionRuleAssignment[]>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments/mission/${missionId}/rules`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(rulesData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete mission rule assignment by ID
 * @param id - Mission rule assignment ID
 * @returns Promise with deletion result
 */
export const deleteMissionRuleAssignment = async (id: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete assignment by mission and rule
 * @param missionId - Mission ID
 * @param missionRuleId - Mission rule ID
 * @returns Promise with deletion result
 */
export const deleteAssignmentByMissionAndRule = async (missionId: number, missionRuleId: number): Promise<ApiResponse<{ message: string }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments/mission/${missionId}/rule/${missionRuleId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get assignment statistics
 * @returns Promise with assignment statistics data
 */
export const fetchAssignmentStatistics = async (): Promise<ApiResponse<{ totalAssignments: number; missionsWithRules: number; rulesInUse: number }>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/makroz/admin/mission-rule-assignments/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data || result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

// Helper constants for UI components
export const ASSIGNMENT_STATUS_OPTIONS = [
  { value: 'active', label: 'Active Assignment' },
  { value: 'inactive', label: 'Inactive Assignment' }
] as const;

// Helper function to format assignment display
export const getAssignmentDisplay = (assignment: MissionRuleAssignment) => {
  const missionName = assignment.mission?.name || `Mission #${assignment.missionId}`;
  const ruleType = assignment.missionRule?.ruleType || 'Unknown Rule';
  return {
    missionName,
    ruleType,
    displayText: `${missionName} → ${ruleType}`
  };
};

// Helper function to get assignment status
export const getAssignmentStatus = (assignment: MissionRuleAssignment) => {
  // For now, all assignments are considered active
  // This could be extended with business logic in the future
  return {
    status: 'active' as const,
    label: 'Active',
    color: 'text-green-400'
  };
};
