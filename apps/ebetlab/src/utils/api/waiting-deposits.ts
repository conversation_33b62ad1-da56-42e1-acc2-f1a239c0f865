// Waiting Deposits API endpoints and types
// This module handles all waiting deposits-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Waiting deposits-related interfaces
export interface WaitingDepositSearchParams {
  id?: string | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  is_manuel?: string | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: number | null;
  usd_max?: number | null;
  from?: string | null;
  to?: string | null;
  sortBy?: string | null;
  direction?: string | null;
}

export interface WaitingDepositStatus {
  id: number;
  name: string;
  key: string;
}

export interface WaitingDepositCustomer {
  id: number;
  username: string;
  masked_username: string;
  last_action: number;
  profile: {
    id: number;
    name: string;
    surname: string;
    customer_id: number;
    identity_no: string;
  };
}

export interface WaitingDepositPaymentProvider {
  id: number;
  name: string;
}

export interface WaitingDepositData {
  id: number;
  method: string;
  customer_id: number;
  rate: string;
  amount: string;
  usd_amount: string;
  payment_provider_id: number;
  currency: string;
  timestamp: string;
  status_id: number;
  type: number;
  unique_id: string;
  provider: string;
  status: WaitingDepositStatus;
  customer: WaitingDepositCustomer;
  payment_provider: WaitingDepositPaymentProvider;
}

export interface WaitingDepositsResponse {
  success: boolean;
  message: string;
  data: {
    total: number;
    data: WaitingDepositData[];
  };
  timestamp: string;
}

/**
 * Fetch waiting deposits data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with waiting deposits data
 */
export const fetchWaitingDeposits = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<WaitingDepositSearchParams> = {}
): Promise<ApiResponse<WaitingDepositsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      method: null,
      provider: null,
      username: null,
      currency: null,
      operator_id: null,
      type: 1, // Important: type 1 for deposits
      is_manuel: null,
      status_id: 1, // Important: status_id 1 for waiting
      tx: null,
      related: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...searchParams
    };

    console.log('Waiting Deposits API Request:', {
      url: `${import.meta.env.VITE_API_URL || ''}/api/operator/transactions/fiats/${page}/${limit}`,
      method: 'POST',
      body: requestBody
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/transactions/fiats/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Failed to fetch waiting deposits:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
