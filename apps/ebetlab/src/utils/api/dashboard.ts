// Dashboard API endpoints and types
// This module handles all dashboard-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// Dashboard-related interfaces
export interface TransactionSummaryItem {
  date: string;
  deposit: number;
  withdraw: number;
  deposit_count: number;
  withdraw_count: number;
  net: number;
}

export interface TransactionSummaryResponse {
  data: TransactionSummaryItem[];
}

export interface DashboardStatsData {
  total_deposit: string;
  total_deposit_count: number;
  total_withdraw: string;
  total_withdraw_count: number;
  players_logged_in: number;
  registered_players: number;
  deposit_players: number;
  withdraw_players: number;
}

export interface DashboardStatsResponse {
  data: DashboardStatsData;
}

export interface Customer {
  id: number;
  username: string;
  is_public: boolean;
  rank: string;
  ranki: string;
  masked_username: string;
  last_action: number;
}

export interface BigWinLoseItem {
  id: number;
  customer_id: number;
  game_id: string;
  game_name: string;
  timestamp: number;
  amount: string;
  income: string;
  net: string;
  customer: Customer;
}

export interface LastActivityItem {
  id: number;
  game_id: number;
  timestamp: number;
  customer: Customer | null;
  game: {
    id: number;
    name: string;
    slug: string;
    merchant_id: number;
    provider: string;
    blacklist: string[];
    game_website: {
      id: number;
      website_id: number;
      game_id: number;
      image: string;
      name: string;
      is_active: boolean;
      sort: number;
      color: string;
    };
    game_provider: {
      id: number;
      identifier: string;
      name: string;
      currencies: string[];
      restrictions: string[];
      system_id: number;
    };
  };
}

export interface BigWinLoseResponse {
  data: {
    win: BigWinLoseItem[];
    lose: BigWinLoseItem[];
    last: LastActivityItem[];
  };
}

export interface GameWinItem {
  game_id: string;
  game_name: string;
  total_win: string;
}

export interface GameLoseItem {
  game_id: string;
  game_name: string;
  total_loss: string;
}

export interface GameWinLoseResponse {
  data: {
    win: GameWinItem[];
    lose: GameLoseItem[];
  };
}

/**
 * Fetch transaction summary data for dashboard
 * @param range - Time range (day, week, month)
 * @param timezone - Timezone offset in hours
 * @returns Promise with transaction summary data
 */
export const fetchTransactionSummary = async (range: string = 'day', timezone: number = 0): Promise<ApiResponse<TransactionSummaryResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Build query parameters
    const params = new URLSearchParams({
      range: range,
      timezone: timezone.toString(),
    });

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/dashboard/transactions/summary?${params}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch game win/lose data for dashboard pie charts
 * @returns Promise with game win/lose data
 */
export const fetchGameWinLose = async (): Promise<ApiResponse<GameWinLoseResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/dashboard/debits/game-win-lose`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch big win/lose data for dashboard tables
 * @returns Promise with big win/lose data
 */
export const fetchBigWinLose = async (): Promise<ApiResponse<BigWinLoseResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/dashboard/debits/big-win-lose`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch customer-specific game win/lose data for dashboard pie charts
 * @param customerId - Customer ID as string
 * @returns Promise with customer game win/lose data
 */
export const fetchCustomerGameWinLose = async (customerId: string): Promise<ApiResponse<GameWinLoseResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customers/dashboard/debits/game-win-lose`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        customer_id: customerId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch customer-specific big win/lose data for dashboard tables
 * @param customerId - Customer ID as string
 * @returns Promise with customer big win/lose data
 */
export const fetchCustomerBigWinLose = async (customerId: string): Promise<ApiResponse<BigWinLoseResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/customers/dashboard/debits/big-win-lose`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        customer_id: customerId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch dashboard stats data for infographics
 * @param from - Start timestamp (Unix timestamp)
 * @param to - End timestamp (Unix timestamp)
 * @returns Promise with dashboard stats data
 */
export const fetchDashboardStats = async (from: number, to: number): Promise<ApiResponse<DashboardStatsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/dashboard/widgets/stats`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        from: from,
        to: to
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
