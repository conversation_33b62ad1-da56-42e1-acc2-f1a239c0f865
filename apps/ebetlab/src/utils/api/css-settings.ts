// CSS Settings API endpoints and types
// This module handles all CSS settings-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// CSS Settings interfaces
export interface CssSettingOperator {
  id: number;
  name: string;
}

export interface CssSettingData {
  id: number;
  operator_id: number;
  last_update: number;
  theme: string;
  is_active: boolean;
  name: string;
  operator: CssSettingOperator;
  // Additional fields for detailed view
  merchant_id?: number;
  website_id?: number;
  path?: string;
}

export interface CssSettingsResponse {
  total: number;
  data: CssSettingData[];
}

export interface PaginationParams {
  page: number;
  limit: number;
}

/**
 * Fetch CSS settings list
 * @param params - Pagination parameters
 * @returns Promise with CSS settings data
 */
export const fetchCssSettings = async (params: PaginationParams): Promise<ApiResponse<CssSettingsResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const { page, limit } = params;

    const requestBody = {
      id: null,
      username: null,
      currency: null,
      operator_id: null,
      type: null,
      status_id: null,
      tx: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/css-settings/index2/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Fetch single CSS setting details
 * @param id - CSS setting ID
 * @returns Promise with CSS setting data
 */
export const fetchCssSetting = async (id: number): Promise<ApiResponse<CssSettingData>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const requestBody = {
      id: id.toString(),
      rt: Math.floor(Date.now() / 1000)
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/css-settings/show/${id}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
