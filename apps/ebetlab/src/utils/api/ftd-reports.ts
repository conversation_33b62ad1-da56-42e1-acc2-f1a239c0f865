// FTD (First Time Deposit) Reports API endpoints and types
// This module handles all FTD reports-related API calls

import { ApiResponse, getAuthToken, getAuthHeaders } from './common';

// FTD-related interfaces
export interface FTDData {
  customer_id: number;
  username: string;
  registration_ts: number;
  currency: string;
  unique_id: string;
  first_deposit_date: number;
  first_deposit_id: number;
  first_deposit_amount: string;
  first_deposit_currency: string;
  first_deposit_amount_usd: string;
  total_deposit_amount: string;
  total_usd_amount: string;
  phone_full: string | null;
}

export interface FTDResponse {
  data: {
    total_count: number;
    total_sum: string;
    data: FTDData[];
  };
  status: number;
  success: boolean;
}

export interface FTDSearchParams {
  id?: string | null;
  method?: string | null;
  provider?: string | null;
  username?: string | null;
  affiliator?: string | null;
  ref_code?: string | null;
  currency?: string | null;
  operator_id?: string | null;
  type?: string | null;
  is_manuel?: string | null;
  status_id?: string | null;
  tx?: string | null;
  related?: string | null;
  usd_min?: string | null;
  usd_max?: string | null;
  from?: number | string | null;
  to?: number | string | null;
  sortBy?: string | null;
  direction?: string | null;
}

/**
 * Fetch FTD reports data with pagination and search filters
 * @param page - Page number (1-based)
 * @param limit - Number of items per page
 * @param searchParams - Optional search parameters
 * @returns Promise with FTD reports data
 */
export const fetchFTDReports = async (
  page: number = 1,
  limit: number = 20,
  searchParams: Partial<FTDSearchParams> = {}
): Promise<ApiResponse<FTDResponse>> => {
  try {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Convert date strings to timestamps if needed
    const processedParams = { ...searchParams };
    if (processedParams.from && typeof processedParams.from === 'string') {
      processedParams.from = Math.floor(new Date(processedParams.from).getTime() / 1000);
    }
    if (processedParams.to && typeof processedParams.to === 'string') {
      processedParams.to = Math.floor(new Date(processedParams.to).getTime() / 1000);
    }

    // Build the request body with default values and search parameters
    const requestBody = {
      id: null,
      method: null,
      provider: null,
      username: null,
      affiliator: null,
      ref_code: null,
      currency: null,
      operator_id: null,
      type: null,
      is_manuel: null,
      status_id: null,
      tx: null,
      related: null,
      usd_min: null,
      usd_max: null,
      from: null,
      to: null,
      sortBy: null,
      direction: null,
      page: page,
      limit: limit,
      rt: Math.floor(Date.now() / 1000),
      // Override with provided search parameters
      ...processedParams
    };

    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/operator/transactions/ftd/${page}/${limit}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error fetching FTD reports:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
