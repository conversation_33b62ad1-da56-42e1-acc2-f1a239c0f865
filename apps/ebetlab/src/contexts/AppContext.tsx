import React, { create<PERSON>ontext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { getAuthHeaders } from '../utils/api/common';
import { useAuth } from './AuthContext';

// Type definitions for API responses
export interface WebsiteConfig {
  domain: string;
  logo_dark: string;
  logo_white: string;
  logo_gray: string;
  color: string;
  support: string;
  affiliate: string;
  info: string;
  cover_png: string;
  bg_dark: string;
  bg2_dark: string;
  bg_light: string;
  bg2_light: string;
  mini_logo: string;
  secret: string;
  mini_logo_dark: string;
  stream_enabled: boolean;
  favicon: string;
  slider_type: number;
  facebook: string | null;
  twitter: string | null;
  telegram: string | null;
  instagram: string | null;
  trade_able: boolean;
  theme: string;
  has_themes: boolean;
  call_me_able: boolean;
  casino_bg: string;
  sportsbook_bg: string;
  forbidden_image: string;
  intercom: string | null;
  icon: string;
  slider_bg: string | null;
  fiat_enabled: number;
  cdn: string;
  id: number;
  pokerklas_able: number;
}

export interface GameCategory {
  id: number;
  name: string;
}

export interface Rank {
  id: number;
  name: string;
  slug: string;
  icon: string;
  wager_limit: string;
  gift_amount: string;
  reload_days: number;
  reload_amount: string;
  rake_percentage: string;
}

export interface Provider {
  id: number;
  identifier: string;
  name: string;
  image: string;
  total: number;
}

export interface Language {
  id: number;
  name: string;
  key: string;
  locale: string;
  flag: string;
}

export interface Role {
  id: number;
  name: string;
  key: string;
  permissionCount: number;
  permissions: string[];
}

export interface Configurations {
  website: WebsiteConfig;
  currencies: string[];
  game_categories: GameCategory[];
  statuses: unknown[];
  ranks: Rank[];
  providers: Provider[];
  languages: Language[];
  prefixes: unknown[];
  roles: Role[];
  permissions: string[];
}

export interface CurrencyRates {
  [currency: string]: number;
}

export interface User {
  id: number;
  merchant_id: number;
  website_id: number;
  role_id: number;
  name: string;
  email: string;
  is_active: boolean;
  last_ip: string;
  username: string;
  icon: string;
  permissions: string[] | null;
  staffs: unknown | null;
  roles: unknown | null;
  is_dev: number;
  is_deleted: number;
  authenticator_enabled: boolean;
  last_online_at: number;
  last_country: string;
  sip: string | null;
  is_full_manager: number;
  websites: unknown | null;
}

export interface UserData {
  token: string;
  user: User;
  ws: string;
}

// Context state interface
export interface AppContextState {
  configurations: Configurations | null;
  currencyRates: CurrencyRates | null;
  userData: UserData | null;
  isLoading: boolean;
  error: string | null;
}

// Context interface
export interface AppContextType extends AppContextState {
  refetchData: () => Promise<void>;
}

// Create context
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider component props
interface AppProviderProps {
  children: ReactNode;
}

// Provider component
export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const { logout } = useAuth();
  const [state, setState] = useState<AppContextState>({
    configurations: null,
    currencyRates: null,
    userData: null,
    isLoading: true,
    error: null
  });

  // Helper function to check for unauthorized responses
  const checkUnauthorized = useCallback((response: Response, result?: { message?: string; error?: string }) => {
    if (response.status === 401 ||
        (result && (result.message?.toLowerCase().includes('unauthorized') || result.error?.toLowerCase().includes('unauthorized')))) {
      console.warn('Unauthorized response detected, logging out user');
      logout();
      throw new Error('Session expired. Please log in again.');
    }
  }, [logout]);

  // Fetch configurations
  const fetchConfigurations = useCallback(async (): Promise<Configurations> => {
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/v1/configurations`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      checkUnauthorized(response);
    }

    const result = await response.json();
    checkUnauthorized(response, result);

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch configurations');
    }

    return result.data;
  }, [checkUnauthorized]);

  // Fetch currency rates
  const fetchCurrencyRates = useCallback(async (): Promise<CurrencyRates> => {
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/currencies/rates/conversions/USD`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      checkUnauthorized(response);
      throw new Error(`Failed to fetch currency rates: ${response.status}`);
    }

    const result = await response.json();
    checkUnauthorized(response, result);

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch currency rates');
    }

    return result.data;
  }, [checkUnauthorized]);

  // Fetch user data
  const fetchUserData = useCallback(async (): Promise<UserData> => {
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/users/me`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      checkUnauthorized(response);
      throw new Error(`Failed to fetch user data: ${response.status}`);
    }

    const result = await response.json();
    checkUnauthorized(response, result);

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch user data');
    }

    return result.data;
  }, [checkUnauthorized]);

  // Load all data
  const loadAppData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Execute all three API calls in parallel
      const [configurations, currencyRates, userData] = await Promise.all([
        fetchConfigurations(),
        fetchCurrencyRates(),
        fetchUserData()
      ]);

      setState({
        configurations,
        currencyRates,
        userData,
        isLoading: false,
        error: null
      });
    } catch (error) {
      console.error('Failed to load app data:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load application data'
      }));
    }
  }, [fetchConfigurations, fetchCurrencyRates, fetchUserData]);

  // Refetch data function
  const refetchData = async () => {
    await loadAppData();
  };

  // Load data on mount
  useEffect(() => {
    loadAppData();
  }, [loadAppData]);

  const contextValue: AppContextType = {
    ...state,
    refetchData
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Hook to use the context
export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
