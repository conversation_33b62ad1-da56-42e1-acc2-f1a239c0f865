import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: number;
  merchant_id: number;
  website_id: number;
  role_id: number;
  name: string;
  email: string;
  is_active: boolean;
  last_ip: string;
  username: string;
  icon: string;
  permissions: any;
  staffs: any;
  roles: any;
  is_dev: number;
  is_deleted: number;
  authenticator_enabled: boolean;
  last_online_at: number;
  last_country: string;
  sip: any;
  is_full_manager: number;
  websites: any;
}

interface AuthData {
  token: string;
  cf_clearance: string;
  signed_challange: string;
  x_fingerprint: string;
  user?: User;
}

interface AuthContextType {
  isAuthenticated: boolean;
  authData: AuthData | null;
  login: (authData: AuthData) => void;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authData, setAuthData] = useState<AuthData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored authentication data on app load
    // First check betroz_auth (current key)
    let storedAuthData = localStorage.getItem('betroz_auth');

    // If not found, check betroz_login (alternative key)
    if (!storedAuthData) {
      storedAuthData = localStorage.getItem('betroz_login');
    }

    if (storedAuthData) {
      try {
        const parsedData = JSON.parse(storedAuthData);
        setAuthData(parsedData);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Failed to parse stored auth data:', error);
        localStorage.removeItem('betroz_auth');
        localStorage.removeItem('betroz_login');
      }
    }
    setIsLoading(false);
  }, []);

  const login = (newAuthData: AuthData) => {
    setAuthData(newAuthData);
    setIsAuthenticated(true);
    // Store in both keys for compatibility
    localStorage.setItem('betroz_auth', JSON.stringify(newAuthData));
    localStorage.setItem('betroz_login', JSON.stringify(newAuthData));
  };

  const logout = () => {
    setAuthData(null);
    setIsAuthenticated(false);
    // Remove both keys
    localStorage.removeItem('betroz_auth');
    localStorage.removeItem('betroz_login');
  };

  const value: AuthContextType = {
    isAuthenticated,
    authData,
    login,
    logout,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export type { User, AuthData };
