import React from 'react';
import { Search, X } from 'lucide-react';

interface TextFilterProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const TextFilter: React.FC<TextFilterProps> = ({
  label,
  value,
  onChange,
  placeholder = 'Enter text...'
}) => (
  <div>
    <label className="block text-sm font-medium text-gray-300 mb-2">{label}</label>
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full pl-10 pr-10 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
        placeholder={placeholder}
      />
      {value && (
        <button
          onClick={() => onChange('')}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-200"
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  </div>
);

interface NumberFilterProps {
  label: string;
  value: number | '';
  onChange: (value: number | '') => void;
  placeholder?: string;
  min?: number;
  max?: number;
}

export const NumberFilter: React.FC<NumberFilterProps> = ({
  label,
  value,
  onChange,
  placeholder = 'Enter number...',
  min,
  max
}) => (
  <div>
    <label className="block text-sm font-medium text-gray-300 mb-2">{label}</label>
    <input
      type="number"
      value={value}
      onChange={(e) => onChange(e.target.value === '' ? '' : Number(e.target.value))}
      className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
      placeholder={placeholder}
      min={min}
      max={max}
    />
  </div>
);

interface NumberRangeFilterProps {
  label: string;
  minValue: number | '';
  maxValue: number | '';
  onMinChange: (value: number | '') => void;
  onMaxChange: (value: number | '') => void;
  minPlaceholder?: string;
  maxPlaceholder?: string;
}

export const NumberRangeFilter: React.FC<NumberRangeFilterProps> = ({
  label,
  minValue,
  maxValue,
  onMinChange,
  onMaxChange,
  minPlaceholder = 'Min',
  maxPlaceholder = 'Max'
}) => (
  <div>
    <label className="block text-sm font-medium text-gray-300 mb-2">{label}</label>
    <div className="grid grid-cols-2 gap-2">
      <input
        type="number"
        value={minValue}
        onChange={(e) => onMinChange(e.target.value === '' ? '' : Number(e.target.value))}
        className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
        placeholder={minPlaceholder}
      />
      <input
        type="number"
        value={maxValue}
        onChange={(e) => onMaxChange(e.target.value === '' ? '' : Number(e.target.value))}
        className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
        placeholder={maxPlaceholder}
      />
    </div>
  </div>
);

interface SelectFilterProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: Array<{ value: string; label: string }>;
  placeholder?: string;
}

export const SelectFilter: React.FC<SelectFilterProps> = ({
  label,
  value,
  onChange,
  options,
  placeholder = 'Select option...'
}) => (
  <div>
    <label className="block text-sm font-medium text-gray-300 mb-2">{label}</label>
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
    >
      <option value="">{placeholder}</option>
      {options.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  </div>
);

interface DateRangeFilterProps {
  label: string;
  startDate: string;
  endDate: string;
  onStartDateChange: (value: string) => void;
  onEndDateChange: (value: string) => void;
  startPlaceholder?: string;
  endPlaceholder?: string;
}

export const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  label,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  startPlaceholder = 'Start date',
  endPlaceholder = 'End date'
}) => (
  <div>
    <label className="block text-sm font-medium text-gray-300 mb-2">{label}</label>
    <div className="grid grid-cols-2 gap-2">
      <input
        type="date"
        value={startDate}
        onChange={(e) => onStartDateChange(e.target.value)}
        className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
        placeholder={startPlaceholder}
      />
      <input
        type="date"
        value={endDate}
        onChange={(e) => onEndDateChange(e.target.value)}
        className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
        placeholder={endPlaceholder}
      />
    </div>
  </div>
);

interface BooleanFilterProps {
  label: string;
  value: boolean | undefined;
  onChange: (value: boolean | undefined) => void;
  trueLabel?: string;
  falseLabel?: string;
}

export const BooleanFilter: React.FC<BooleanFilterProps> = ({
  label,
  value,
  onChange,
  trueLabel = 'Yes',
  falseLabel = 'No'
}) => (
  <div>
    <label className="block text-sm font-medium text-gray-300 mb-2">{label}</label>
    <select
      value={value === undefined ? '' : value.toString()}
      onChange={(e) => {
        const val = e.target.value;
        onChange(val === '' ? undefined : val === 'true');
      }}
      className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
    >
      <option value="">All</option>
      <option value="true">{trueLabel}</option>
      <option value="false">{falseLabel}</option>
    </select>
  </div>
);

interface FilterContainerProps {
  title?: string;
  children: React.ReactNode;
  onClearFilters?: () => void;
}

export const FilterContainer: React.FC<FilterContainerProps> = ({
  title = 'Filters',
  children,
  onClearFilters
}) => (
  <div className="bg-dark-800 rounded-lg p-4 mb-6">
    <div className="flex items-center justify-between mb-4">
      <h3 className="text-lg font-semibold text-gray-100">{title}</h3>
      {onClearFilters && (
        <button
          onClick={onClearFilters}
          className="px-3 py-1 bg-dark-600 hover:bg-dark-500 text-gray-100 rounded-md text-sm"
        >
          Clear Filters
        </button>
      )}
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {children}
    </div>
  </div>
);
