/**
 * UnifiedTable - A flexible table component that unifies all table patterns
 * 
 * This component provides:
 * - Consistent styling across all tables
 * - Backward compatibility with existing implementations
 * - Support for different table variants (simple, advanced, legacy)
 * - Flexible column configuration
 * - Built-in loading, empty, and error states
 */

import React from 'react';
import { RefreshCw, ChevronUp, ChevronDown, ChevronsUpDown } from 'lucide-react';

// Base interfaces
export interface TableColumn<T = any> {
  key: string;
  label: string;
  width?: string;
  sortable?: boolean;
  sticky?: boolean; // For action columns
  className?: string;
  render: (item: T, index?: number) => React.ReactNode;
}

export interface SortState {
  field: string | null;
  direction: 'asc' | 'desc';
}

export interface UnifiedTableProps<T = any> {
  // Data
  data: T[];
  columns: TableColumn<T>[];
  
  // Table configuration
  variant?: 'simple' | 'advanced' | 'legacy';
  minWidth?: string;
  className?: string;
  
  // Header
  title?: string;
  subtitle?: string;
  headerActions?: React.ReactNode;
  
  // Sorting
  sortable?: boolean;
  sortState?: SortState;
  onSort?: (field: string) => void;
  
  // Row interaction
  onRowClick?: (item: T, index: number) => void;
  rowClassName?: (item: T, index: number) => string;
  
  // States
  isLoading?: boolean;
  error?: string | null;
  emptyState?: {
    icon?: React.ReactNode;
    title: string;
    description: string;
    action?: React.ReactNode;
  };
  
  // Actions
  onRetry?: () => void;
}

const UnifiedTable = <T extends Record<string, any>>({
  data,
  columns,
  variant = 'advanced',
  minWidth = '1000px',
  className = '',
  title,
  subtitle,
  headerActions,
  sortable = false,
  sortState = { field: null, direction: 'asc' },
  onSort,
  onRowClick,
  rowClassName,
  isLoading = false,
  error = null,
  emptyState,
  onRetry
}: UnifiedTableProps<T>) => {

  // Handle column sorting
  const handleColumnSort = (field: string, isSortable?: boolean) => {
    if (!isSortable || !onSort) return;
    onSort(field);
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (!sortState.field || sortState.field !== field) {
      return <ChevronsUpDown size={14} className="text-gray-500" />;
    }
    
    return sortState.direction === 'asc' 
      ? <ChevronUp size={14} className="text-primary-500" />
      : <ChevronDown size={14} className="text-primary-500" />;
  };

  // Get table classes based on variant
  const getTableClasses = () => {
    const baseClasses = 'data-table';
    switch (variant) {
      case 'legacy':
        return `${baseClasses} data-table-legacy`;
      case 'simple':
        return `${baseClasses}`;
      default:
        return baseClasses;
    }
  };

  // Error state
  if (error) {
    return (
      <div className={`table-container ${className}`}>
        <div className="text-center py-12">
          <div className="text-red-500 mb-2 font-medium">Error loading data</div>
          <div className="text-gray-400 text-sm mb-4">{error}</div>
          {onRetry && (
            <button 
              className="btn btn-primary"
              onClick={onRetry}
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`table-container ${className}`}>
      {/* Table Header */}
      {(title || subtitle || headerActions) && (
        <div className="table-header">
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h3 className="text-lg font-semibold text-gray-100">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-400 mt-1">{subtitle}</p>
              )}
            </div>
            {headerActions && (
              <div className="flex items-center gap-2">
                {headerActions}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Table Content */}
      <div className="table-content">
        <table className={getTableClasses()} style={{ minWidth }}>
          <thead>
            <tr>
              {columns.map((column, index) => {
                const isFirstColumn = index === 0;
                const isLastColumn = index === columns.length - 1;
                const isSortableColumn = sortable && column.sortable;
                
                return (
                  <th
                    key={column.key}
                    style={{ minWidth: column.width }}
                    className={`
                      ${isFirstColumn ? 'rounded-tl-lg' : ''}
                      ${isLastColumn ? 'rounded-tr-lg' : ''}
                      ${isSortableColumn ? 'sortable-column' : ''}
                      ${column.sticky ? 'actions-column' : ''}
                      ${column.className || ''}
                    `.trim()}
                    onClick={() => handleColumnSort(column.key, isSortableColumn)}
                  >
                    <div className="flex items-center justify-between">
                      <span>{column.label}</span>
                      {isSortableColumn && renderSortIndicator(column.key)}
                    </div>
                  </th>
                );
              })}
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={columns.length} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <RefreshCw size={20} className="animate-spin mr-2" />
                    Loading...
                  </div>
                </td>
              </tr>
            ) : data.length > 0 ? (
              data.map((item, index) => {
                const customRowClass = rowClassName ? rowClassName(item, index) : '';
                const clickable = onRowClick ? 'cursor-pointer' : '';
                
                return (
                  <tr
                    key={item.id || index}
                    onClick={() => onRowClick?.(item, index)}
                    className={`hover:bg-dark-700/50 transition-colors ${clickable} ${customRowClass}`.trim()}
                  >
                    {columns.map((column) => (
                      <td
                        key={`${item.id || index}-${column.key}`}
                        className={`${column.sticky ? 'sticky right-0 bg-dark-700 z-10' : ''} ${column.className || ''}`.trim()}
                      >
                        {column.render(item, index)}
                      </td>
                    ))}
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={columns.length} className="text-center py-12">
                  {emptyState ? (
                    <div className="flex flex-col items-center">
                      {emptyState.icon}
                      <h3 className="text-lg font-medium text-gray-300 mb-2">
                        {emptyState.title}
                      </h3>
                      <p className="text-gray-400 mb-4">{emptyState.description}</p>
                      {emptyState.action}
                    </div>
                  ) : (
                    <p className="text-gray-400">No data available</p>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default UnifiedTable;
