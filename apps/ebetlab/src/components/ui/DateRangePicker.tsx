import React, { useState } from 'react';
import { Calendar } from 'lucide-react';

interface DateRange {
  from: Date | null;
  to: Date | null;
}

interface DateRangePickerProps {
  value: DateRange;
  onChange: (range: DateRange) => void;
  className?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({ value, onChange, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);

  // Format date for input[type="date"]
  const formatDateForInput = (date: Date | null): string => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  // Handle from date change
  const handleFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFromDate = e.target.value ? new Date(e.target.value) : null;
    onChange({
      from: newFromDate,
      to: value.to
    });
  };

  // Handle to date change
  const handleToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newToDate = e.target.value ? new Date(e.target.value) : null;
    // Set to end of day if date exists
    if (newToDate) {
      newToDate.setHours(23, 59, 59, 999);
    }
    onChange({
      from: value.from,
      to: newToDate
    });
  };

  // Quick preset options
  const presets = [
    {
      label: 'Last 7 days',
      getValue: () => {
        const to = new Date();
        to.setHours(23, 59, 59, 999);
        const from = new Date();
        from.setDate(from.getDate() - 6);
        from.setHours(0, 0, 0, 0);
        return { from, to };
      }
    },
    {
      label: 'Last 30 days',
      getValue: () => {
        const to = new Date();
        to.setHours(23, 59, 59, 999);
        const from = new Date();
        from.setDate(from.getDate() - 29);
        from.setHours(0, 0, 0, 0);
        return { from, to };
      }
    },
    {
      label: 'This month',
      getValue: () => {
        const now = new Date();
        const from = new Date(now.getFullYear(), now.getMonth(), 1);
        from.setHours(0, 0, 0, 0);
        const to = new Date();
        to.setHours(23, 59, 59, 999);
        return { from, to };
      }
    },
    {
      label: 'Last month',
      getValue: () => {
        const now = new Date();
        const from = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        from.setHours(0, 0, 0, 0);
        const to = new Date(now.getFullYear(), now.getMonth(), 0);
        to.setHours(23, 59, 59, 999);
        return { from, to };
      }
    }
  ];

  // Format display text
  const formatDisplayText = (range: DateRange): string => {
    if (!range.from || !range.to) {
      return 'Select date range';
    }

    const fromStr = range.from.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: range.from.getFullYear() !== range.to.getFullYear() ? 'numeric' : undefined
    });
    const toStr = range.to.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
    return `${fromStr} - ${toStr}`;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 bg-dark-700 border border-dark-600 rounded-md px-3 py-2 text-sm hover:bg-dark-600 transition-colors"
      >
        <Calendar size={16} />
        <span>{formatDisplayText(value)}</span>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Content */}
          <div className="absolute top-full left-0 mt-1 bg-dark-700 border border-dark-600 rounded-md shadow-lg z-20 min-w-[320px]">
            <div className="p-4">
              {/* Quick Presets */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Quick Select
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {presets.map((preset) => (
                    <button
                      key={preset.label}
                      type="button"
                      onClick={() => {
                        onChange(preset.getValue());
                        setIsOpen(false);
                      }}
                      className="text-left px-3 py-2 text-sm bg-dark-600 hover:bg-dark-500 rounded border border-dark-500 transition-colors"
                    >
                      {preset.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Custom Date Inputs */}
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    From Date
                  </label>
                  <input
                    type="date"
                    value={formatDateForInput(value.from)}
                    onChange={handleFromChange}
                    className="w-full bg-dark-600 border border-dark-500 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    To Date
                  </label>
                  <input
                    type="date"
                    value={formatDateForInput(value.to)}
                    onChange={handleToChange}
                    className="w-full bg-dark-600 border border-dark-500 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Apply Button */}
              <div className="mt-4 pt-3 border-t border-dark-600">
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors"
                >
                  Apply
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default DateRangePicker;
