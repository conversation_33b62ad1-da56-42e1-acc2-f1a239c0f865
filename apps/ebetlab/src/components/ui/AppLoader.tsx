import React from 'react';
import { RefreshCw, AlertCircle, RotateCcw } from 'lucide-react';
import Button from './Button';

interface AppLoaderProps {
  isLoading: boolean;
  error: string | null;
  onRetry?: () => void;
}

const AppLoader: React.FC<AppLoaderProps> = ({ isLoading, error, onRetry }) => {
  if (error) {
    return (
      <div className="min-h-screen bg-dark-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-8">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-100 mb-2">
              Failed to Load Application
            </h1>
            <p className="text-gray-400 mb-6">
              {error}
            </p>
            {onRetry && (
              <Button
                onClick={onRetry}
                variant="primary"
                className="flex items-center gap-2 mx-auto"
              >
                <RotateCcw className="w-4 h-4" />
                Retry
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-dark-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-8">
            {/* Logo placeholder - you can replace with actual logo */}
            <div className="w-16 h-16 bg-primary-500 rounded-lg mx-auto mb-6 flex items-center justify-center">
              <span className="text-white font-bold text-xl">BO</span>
            </div>
            
            <h1 className="text-2xl font-bold text-gray-100 mb-2">
              Loading Application
            </h1>
            <p className="text-gray-400 mb-8">
              Please wait while we load your data...
            </p>
            
            {/* Loading animation */}
            <div className="flex items-center justify-center mb-6">
              <RefreshCw className="w-8 h-8 text-primary-500 animate-spin" />
            </div>
            
            {/* Loading steps */}
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Loading configurations</span>
                <div className="w-4 h-4 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Loading currency rates</span>
                <div className="w-4 h-4 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Loading user data</span>
                <div className="w-4 h-4 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default AppLoader;
