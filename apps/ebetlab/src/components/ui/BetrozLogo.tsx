import React from 'react';
import { Flame } from 'lucide-react';

interface BetrozLogoProps {
  className?: string;
}

export const BetrozLogo: React.FC<BetrozLogoProps> = ({ className = '' }) => {
  return (
    <div className={`relative w-8 h-8 flex items-center justify-center bg-gradient-to-br from-primary-500 to-primary-600 rounded-full ${className}`}>
      <Flame size={18} className="text-dark-800" />
    </div>
  );
};