/**
 * TableWrapper - Migration helper component
 * 
 * This component provides a smooth migration path from existing table implementations
 * to the new unified table system. It allows gradual migration without breaking changes.
 */

import React from 'react';
import UnifiedTable, { TableColumn, UnifiedTableProps } from './UnifiedTable';

// Legacy table column interface (for backward compatibility)
export interface LegacyTableColumn<T = any> {
  key: string;
  label: string;
  width?: string;
  sortable?: boolean;
  render: (item: T) => React.ReactNode;
}

// Migration wrapper props
export interface TableWrapperProps<T = any> {
  // Choose implementation
  useUnified?: boolean; // Set to true to use new unified table
  
  // Legacy props (for existing implementations)
  legacyColumns?: LegacyTableColumn<T>[];
  legacyClassName?: string;
  legacyMinWidth?: string;
  
  // Unified table props
  data: T[];
  columns?: TableColumn<T>[];
  
  // Common props
  title?: string;
  subtitle?: string;
  isLoading?: boolean;
  error?: string | null;
  onRowClick?: (item: T, index?: number) => void;
  onRetry?: () => void;
  
  // Custom render function for legacy tables
  customRender?: () => React.ReactNode;
}

const TableWrapper = <T extends Record<string, any>>({
  useUnified = false,
  legacyColumns = [],
  legacyClassName = '',
  legacyMinWidth = '1000px',
  data,
  columns = [],
  title,
  subtitle,
  isLoading = false,
  error = null,
  onRowClick,
  onRetry,
  customRender
}: TableWrapperProps<T>) => {

  // If custom render is provided, use it (for complex legacy tables)
  if (customRender) {
    return <>{customRender()}</>;
  }

  // Use unified table if requested
  if (useUnified && columns.length > 0) {
    return (
      <UnifiedTable
        data={data}
        columns={columns}
        title={title}
        subtitle={subtitle}
        isLoading={isLoading}
        error={error}
        onRowClick={onRowClick}
        onRetry={onRetry}
        variant="advanced"
      />
    );
  }

  // Convert legacy columns to unified format
  const unifiedColumns: TableColumn<T>[] = legacyColumns.map(col => ({
    key: col.key,
    label: col.label,
    width: col.width,
    sortable: col.sortable,
    render: (item: T, index?: number) => col.render(item)
  }));

  // Fallback to legacy-style table with unified styling
  return (
    <div className="table-container">
      {title && (
        <div className="table-header">
          <h3 className="text-lg font-semibold text-gray-100">{title}</h3>
          {subtitle && <p className="text-sm text-gray-400 mt-1">{subtitle}</p>}
        </div>
      )}
      
      <div className="table-content">
        <table 
          className={`data-table ${legacyClassName}`} 
          style={{ minWidth: legacyMinWidth }}
        >
          <thead>
            <tr>
              {unifiedColumns.map((column, index) => (
                <th
                  key={column.key}
                  style={{ minWidth: column.width }}
                  className={`${index === 0 ? 'rounded-tl-lg' : ''} ${
                    index === unifiedColumns.length - 1 ? 'rounded-tr-lg' : ''
                  }`}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={unifiedColumns.length} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500 mr-2"></div>
                    Loading...
                  </div>
                </td>
              </tr>
            ) : error ? (
              <tr>
                <td colSpan={unifiedColumns.length} className="text-center py-8">
                  <div className="text-red-500 mb-2">Error loading data</div>
                  <div className="text-gray-400 text-sm mb-4">{error}</div>
                  {onRetry && (
                    <button className="btn btn-primary" onClick={onRetry}>
                      Try Again
                    </button>
                  )}
                </td>
              </tr>
            ) : data.length > 0 ? (
              data.map((item, index) => (
                <tr
                  key={item.id || index}
                  onClick={() => onRowClick?.(item, index)}
                  className={`hover:bg-dark-700/50 transition-colors ${
                    onRowClick ? 'cursor-pointer' : ''
                  }`}
                >
                  {unifiedColumns.map((column) => (
                    <td key={`${item.id || index}-${column.key}`}>
                      {column.render(item, index)}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={unifiedColumns.length} className="text-center py-8">
                  <p className="text-gray-400">No data available</p>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TableWrapper;
