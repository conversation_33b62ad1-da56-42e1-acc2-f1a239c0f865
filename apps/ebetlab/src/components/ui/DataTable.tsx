/**
 * DataTable - Reusable table component with filtering, sorting, and pagination
 *
 * Features:
 * - Configurable columns with custom render functions
 * - Collapsible filters section with grouping support
 * - Column sorting with visual indicators
 * - Pagination with customizable items per page
 * - Loading and empty states
 * - Row click handling
 * - Responsive design with horizontal scrolling
 *
 * Usage Example:
 * ```tsx
 * const columns: TableColumn<MyDataType>[] = [
 *   {
 *     key: 'id',
 *     label: 'ID',
 *     width: '80px',
 *     sortable: true,
 *     render: (item) => <span>#{item.id}</span>
 *   }
 * ];
 *
 * const filterGroups: FilterGroup[] = [
 *   {
 *     name: 'basic',
 *     displayName: 'Basic Filters',
 *     fields: [
 *       {
 *         key: 'name',
 *         label: 'Name',
 *         type: 'text',
 *         placeholder: 'Search by name...'
 *       }
 *     ]
 *   }
 * ];
 *
 * <DataTable
 *   data={myData}
 *   columns={columns}
 *   filterGroups={filterGroups}
 *   total={totalItems}
 *   currentPage={page}
 *   itemsPerPage={limit}
 *   onPageChange={handlePageChange}
 *   onItemsPerPageChange={handleLimitChange}
 *   onSort={handleSort}
 *   onFilterChange={handleFilterChange}
 *   onRowClick={handleRowClick}
 * />
 * ```
 */

import React, { useState } from 'react';
import { ChevronUp, ChevronDown, RefreshCw, Filter, Search, X } from 'lucide-react';
import Button from './Button';
import Input from './Input';
import Pagination from './Pagination';

// Column configuration interface
export interface TableColumn<T = any> {
  key: string;
  label: string;
  width: string;
  sortable: boolean;
  render: (item: T) => React.ReactNode;
}

// Filter field interface
export interface FilterField {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'datetime-local' | 'number';
  placeholder?: string;
  options?: { value: string; label: string }[];
  group?: string; // Optional grouping
  min?: number; // Minimum value for number inputs
  max?: number; // Maximum value for number inputs
}

// Filter group interface
export interface FilterGroup {
  name: string;
  displayName: string;
  fields: FilterField[];
}

// Sort state interface
export interface SortState {
  field: string | null;
  direction: 'asc' | 'desc';
}

// Main component props
export interface DataTableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  filterFields?: FilterField[];
  filterGroups?: FilterGroup[];
  total: number;
  currentPage: number;
  itemsPerPage: number;
  isLoading?: boolean;
  sortState?: SortState;
  filters?: Record<string, unknown>;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  onSort?: (field: string) => void;
  onFilterChange?: (filters: Record<string, unknown>) => void;
  onRowClick?: (item: T) => void;
  emptyState?: {
    icon?: React.ReactNode;
    title: string;
    description: string;
    action?: React.ReactNode;
  };
  title?: string;
}

const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  filterFields = [],
  filterGroups = [],
  total,
  currentPage,
  itemsPerPage,
  isLoading = false,
  sortState = { field: null, direction: 'asc' },
  filters = {},
  onPageChange,
  onItemsPerPageChange,
  onSort,
  onFilterChange,
  onRowClick,
  emptyState,
  title
}: DataTableProps<T>) => {
  const [showFilters, setShowFilters] = useState(false);
  const [localFilters, setLocalFilters] = useState<Record<string, unknown>>(filters);
  const [activeTab, setActiveTab] = useState<string>('');

  // Organize filter fields into groups
  const organizedFilters = React.useMemo(() => {
    if (filterGroups.length > 0) {
      return filterGroups;
    }

    // If no groups provided, create a default group
    if (filterFields.length > 0) {
      return [{
        name: 'default',
        displayName: 'Filters',
        fields: filterFields
      }];
    }

    return [];
  }, [filterFields, filterGroups]);

  // Set initial active tab
  React.useEffect(() => {
    if (organizedFilters.length > 0 && !activeTab) {
      setActiveTab(organizedFilters[0].name);
    }
  }, [organizedFilters, activeTab]);

  // Sorting functions
  const handleColumnSort = (columnKey: string, sortable: boolean) => {
    if (!sortable || !onSort) return;
    onSort(columnKey);
  };

  const renderSortIndicator = (columnKey: string) => {
    if (sortState.field !== columnKey) {
      return <ChevronUp className="w-4 h-4 text-gray-500" />;
    }
    return sortState.direction === 'asc' 
      ? <ChevronUp className="w-4 h-4 text-primary-500" />
      : <ChevronDown className="w-4 h-4 text-primary-500" />;
  };

  // Filter functions
  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
  };

  const applyFilters = () => {
    if (onFilterChange) {
      // Remove empty values
      const cleanFilters = Object.fromEntries(
        Object.entries(localFilters).filter(([_, value]) =>
          value !== '' && value !== null && value !== undefined
        )
      );
      onFilterChange(cleanFilters);
    }
  };

  const clearFilters = () => {
    setLocalFilters({});
    if (onFilterChange) {
      onFilterChange({});
    }
  };

  const renderFilterField = (field: FilterField) => {
    const value = localFilters[field.key] !== undefined ? String(localFilters[field.key]) : '';

    switch (field.type) {
      case 'select':
        return (
          <div key={field.key} className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">{field.label}</label>
            <select
              value={value}
              onChange={(e) => handleFilterChange(field.key, e.target.value)}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">All {field.label}</option>
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        );

      case 'date':
        return (
          <Input
            key={field.key}
            label={field.label}
            type="date"
            value={value}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
          />
        );

      case 'datetime-local':
        return (
          <Input
            key={field.key}
            label={field.label}
            type="datetime-local"
            value={value}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
          />
        );

      case 'number':
        return (
          <Input
            key={field.key}
            label={field.label}
            type="number"
            placeholder={field.placeholder}
            value={value}
            min={field.min}
            max={field.max}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
          />
        );

      default:
        return (
          <Input
            key={field.key}
            label={field.label}
            placeholder={field.placeholder}
            value={value}
            onChange={(e) => handleFilterChange(field.key, e.target.value)}
          />
        );
    }
  };

  const totalPages = Math.ceil(total / itemsPerPage);

  return (
    <div className="space-y-6">
      {/* Filters Section */}
      {organizedFilters.length > 0 && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-200">Search & Filters</h3>
            <Button
              onClick={() => setShowFilters(!showFilters)}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <Filter className="w-4 h-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </Button>
          </div>

          {showFilters && (
            <div className="space-y-4">
              {/* Tabs Navigation */}
              {organizedFilters.length > 1 && (
                <div className="border-b border-dark-600">
                  <nav className="flex space-x-8">
                    {organizedFilters.map((group) => (
                      <button
                        key={group.name}
                        onClick={() => setActiveTab(group.name)}
                        className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                          activeTab === group.name
                            ? 'border-primary-500 text-primary-500'
                            : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                        }`}
                      >
                        {group.displayName}
                      </button>
                    ))}
                  </nav>
                </div>
              )}

              {/* Active Tab Content */}
              {organizedFilters.map((group) => (
                <div
                  key={group.name}
                  className={`${activeTab === group.name ? 'block' : 'hidden'}`}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {group.fields.map(renderFilterField)}
                  </div>
                </div>
              ))}

              <div className="flex items-center gap-3 pt-4 border-t border-dark-600">
                <Button
                  onClick={applyFilters}
                  variant="primary"
                  className="flex items-center gap-2"
                >
                  <Search className="w-4 h-4" />
                  Apply Filters
                </Button>

                <Button
                  onClick={clearFilters}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <X className="w-4 h-4" />
                  Clear
                </Button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Table */}
      <div className="table-container">
        {/* Table Header */}
        {title && (
          <div className="table-header">
            <h3 className="text-lg font-semibold text-gray-100">
              {title} ({total.toLocaleString()})
            </h3>
          </div>
        )}

        {/* Table Content */}
        <div className="relative">
          <div className="table-content">
            <table className="data-table" style={{ minWidth: '1000px' }}>
              <thead>
                <tr>
                  {columns.map((column, index) => {
                    const isActionsColumn = column.key === 'actions';
                    return (
                      <th
                        key={column.key}
                        style={{ minWidth: column.width }}
                        className={`${index === 0 ? 'rounded-tl-lg' : index === columns.length - 1 ? 'rounded-tr-lg' : ''} ${
                          column.sortable ? 'sortable-column' : ''
                        } ${isActionsColumn ? 'actions-column' : ''}`}
                        onClick={() => handleColumnSort(column.key, column.sortable)}
                      >
                        <div className="flex items-center justify-between">
                          <span>{column.label}</span>
                          {column.sortable && renderSortIndicator(column.key)}
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={columns.length} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <RefreshCw size={20} className="animate-spin mr-2" />
                        Loading...
                      </div>
                    </td>
                  </tr>
                ) : data.length > 0 ? (
                  data.map((item, index) => (
                    <tr
                      key={item.id || index}
                      onClick={() => onRowClick?.(item)}
                      className={`${onRowClick ? 'cursor-pointer' : ''} hover:bg-dark-700/50 transition-colors`}
                    >
                      {columns.map((column) => {
                        const isActionsColumn = column.key === 'actions';
                        return (
                          <td
                            key={`${item.id || index}-${column.key}`}
                            className={isActionsColumn ? 'sticky right-0 bg-dark-700 z-10' : ''}
                          >
                            {column.render(item)}
                          </td>
                        );
                      })}
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={columns.length} className="text-center py-8">
                      {emptyState ? (
                        <div className="flex flex-col items-center">
                          {emptyState.icon}
                          <h3 className="text-lg font-medium text-gray-300 mb-2">{emptyState.title}</h3>
                          <p className="text-gray-400 mb-4">{emptyState.description}</p>
                          {emptyState.action}
                        </div>
                      ) : (
                        <p className="text-gray-400">No data found.</p>
                      )}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={total}
          itemsPerPage={itemsPerPage}
          onPageChange={onPageChange}
          onItemsPerPageChange={onItemsPerPageChange}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

export default DataTable;
