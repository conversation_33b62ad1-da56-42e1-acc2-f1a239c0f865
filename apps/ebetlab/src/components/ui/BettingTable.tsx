/**
 * BettingTable - Unified component for Casino and Sportsbook betting tables
 * 
 * This component consolidates the nearly identical casino and sportsbook betting
 * table implementations into a single, reusable component.
 */

import React, { useState, useCallback } from 'react';
import { DollarSign, Download, Filter, RefreshCw, Eye, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';
import { useRefreshableData } from '../customer-details/hooks/useRefreshableData';
import UnifiedTable, { TableColumn } from './UnifiedTable';
import Pagination from './Pagination';
import Modal from './Modal';

// Common betting interfaces
export interface BetData {
  id: number;
  game_name: string;
  amount: string;
  income: string;
  multiplier: string;
  result: string;
  status: string;
  created_at: number;
  currency: string;
  [key: string]: any; // Allow for additional fields
}

export interface BetSearchParams {
  customer_id: string;
  game_name?: string;
  amount_min?: string;
  amount_max?: string;
  income_min?: string;
  income_max?: string;
  from?: string;
  to?: string;
  status?: string;
  [key: string]: any;
}

export interface BettingTableProps {
  type: 'casino' | 'sportsbook' | 'trade';
  customer: {
    id: number;
    username: string;
  };
  fetchFunction: (page: number, limit: number, params: Partial<BetSearchParams>) => Promise<any>;
  title?: string;
}

const BettingTable: React.FC<BettingTableProps> = ({
  type,
  customer,
  fetchFunction,
  title
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedBet, setSelectedBet] = useState<BetData | null>(null);
  const [showBetModal, setShowBetModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<BetSearchParams>>({
    customer_id: customer.id.toString()
  });

  // Fetch betting data
  const fetchBettingData = useCallback(async () => {
    const result = await fetchFunction(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || `Failed to load ${type} bets`);
    }
  }, [currentPage, itemsPerPage, searchParams, fetchFunction, type]);

  const { data: bettingData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchBettingData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  // Format currency
  const formatCurrency = (amount: string | number | null | undefined, currency: string = 'USD') => {
    if (amount === null || amount === undefined || amount === '') {
      return '$0.00';
    }

    const value = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(value)) {
      return '$0.00';
    }

    return `$${value.toFixed(2)}`;
  };

  // Format date
  const formatDate = (timestamp: number | string | null | undefined) => {
    if (!timestamp) {
      return 'Unknown';
    }

    try {
      const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
      if (isNaN(ts)) {
        return 'Invalid Date';
      }

      return new Date(ts * 1000).toLocaleString();
    } catch (error) {
      return 'Invalid Date';
    }
  };

  // Get status badge - handles both casino (finished boolean) and sportsbook (status string) formats
  const getStatusBadge = (bet: any) => {
    const statusConfig = {
      'completed': { icon: CheckCircle, color: 'text-green-500', bg: 'bg-green-500/10' },
      'finished': { icon: CheckCircle, color: 'text-green-500', bg: 'bg-green-500/10' },
      'pending': { icon: Clock, color: 'text-yellow-500', bg: 'bg-yellow-500/10' },
      'in_progress': { icon: Clock, color: 'text-yellow-500', bg: 'bg-yellow-500/10' },
      'failed': { icon: XCircle, color: 'text-red-500', bg: 'bg-red-500/10' },
      'cancelled': { icon: AlertCircle, color: 'text-gray-500', bg: 'bg-gray-500/10' },
      'unknown': { icon: AlertCircle, color: 'text-gray-500', bg: 'bg-gray-500/10' }
    };

    let statusKey = 'unknown';
    let displayStatus = 'Unknown';

    // For casino bets: use finished boolean
    if (typeof bet.finished === 'boolean') {
      statusKey = bet.finished ? 'finished' : 'in_progress';
      displayStatus = bet.finished ? 'Finished' : 'In Progress';
    }
    // For sportsbook bets: use status string or state
    else if (bet.status) {
      statusKey = bet.status.toLowerCase();
      displayStatus = bet.status;
    }
    else if (bet.state) {
      statusKey = bet.state.toLowerCase();
      displayStatus = bet.state;
    }

    const config = statusConfig[statusKey] || statusConfig.unknown;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color} ${config.bg}`}>
        <Icon size={12} />
        {displayStatus}
      </span>
    );
  };

  // Get result badge - handles both casino (is_win boolean) and sportsbook (result string) formats
  const getResultBadge = (bet: any) => {
    // For casino bets: use is_win boolean
    if (typeof bet.is_win === 'boolean') {
      return (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          bet.is_win ? 'text-green-500 bg-green-500/10' : 'text-red-500 bg-red-500/10'
        }`}>
          {bet.is_win ? 'WIN' : 'LOSS'}
        </span>
      );
    }

    // For sportsbook bets: use result string
    const result = bet.result;
    if (!result) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-500 bg-gray-500/10">
          Unknown
        </span>
      );
    }

    const isWin = result.toLowerCase() === 'win';
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        isWin ? 'text-green-500 bg-green-500/10' : 'text-red-500 bg-red-500/10'
      }`}>
        {result}
      </span>
    );
  };

  // Handle bet click
  const handleBetClick = (bet: BetData) => {
    setSelectedBet(bet);
    setShowBetModal(true);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (newFilters: Partial<BetSearchParams>) => {
    setSearchParams(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  };

  // Table columns configuration
  const columns: TableColumn<BetData>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '100px',
      render: (bet) => (
        <span className="font-mono text-sm font-medium text-gray-100">
          #{bet.id}
        </span>
      )
    },
    {
      key: 'game_name',
      label: 'Game',
      width: '200px',
      render: (bet) => (
        <div className="font-medium text-gray-100">
          {bet.game_name || bet.game || 'Unknown Game'}
        </div>
      )
    },
    {
      key: 'amount',
      label: 'Bet Amount',
      width: '120px',
      render: (bet) => (
        <span className="font-medium text-blue-400">
          {formatCurrency(bet.amount, bet.currency || bet.wallet_currency)}
        </span>
      )
    },
    {
      key: 'income',
      label: 'Income',
      width: '120px',
      render: (bet) => (
        <span className="font-medium text-green-400">
          {formatCurrency(bet.income, bet.currency || bet.wallet_currency)}
        </span>
      )
    },
    {
      key: 'multiplier',
      label: 'Multiplier',
      width: '100px',
      render: (bet) => (
        <span className="font-medium text-purple-400">
          {bet.multiplier || '0'}x
        </span>
      )
    },
    {
      key: 'result',
      label: 'Result',
      width: '100px',
      render: (bet) => getResultBadge(bet)
    },
    {
      key: 'status',
      label: 'Status',
      width: '120px',
      render: (bet) => getStatusBadge(bet)
    },
    {
      key: 'created_at',
      label: 'Date',
      width: '180px',
      render: (bet) => (
        <span className="text-sm text-gray-300">
          {formatDate(bet.created_at || bet.timestamp)}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      render: (bet) => (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleBetClick(bet);
          }}
          className="btn btn-outline text-xs py-1 px-2 flex items-center"
        >
          <Eye size={14} className="mr-1" />
          View
        </button>
      )
    }
  ];

  const bets = bettingData?.data || [];
  const total = bettingData?.total || 0;

  return (
    <div className="space-y-6">
      {/* Header with filters */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-100">
            {title || `${type === 'casino' ? 'Casino' : type === 'sportsbook' ? 'Sportsbook' : 'Trade'} Bets`}
          </h3>
          <p className="text-sm text-gray-400">
            Total: {total.toLocaleString()} bets
          </p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn ${showFilters ? 'btn-primary' : 'btn-outline'}`}
          >
            <Filter size={16} className="mr-2" />
            Filters
          </button>
          <button
            onClick={refetch}
            className="btn btn-outline"
            disabled={loading}
          >
            <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Filters (if shown) */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Game Name
              </label>
              <input
                type="text"
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 placeholder-gray-400 focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                placeholder="Search by game name..."
                value={searchParams.game_name || ''}
                onChange={(e) => handleFilterChange({ game_name: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Min Amount
              </label>
              <input
                type="number"
                step="0.01"
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 placeholder-gray-400 focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                placeholder="0.00"
                value={searchParams.amount_min || ''}
                onChange={(e) => handleFilterChange({ amount_min: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Max Amount
              </label>
              <input
                type="number"
                step="0.01"
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 placeholder-gray-400 focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                placeholder="0.00"
                value={searchParams.amount_max || ''}
                onChange={(e) => handleFilterChange({ amount_max: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Status
              </label>
              <select
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                value={searchParams.status || ''}
                onChange={(e) => handleFilterChange({ status: e.target.value })}
              >
                <option value="">All Statuses</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>

          {/* Clear Filters Button */}
          <div className="flex justify-end mt-4 pt-4 border-t border-dark-600">
            <button
              onClick={() => {
                setSearchParams({ customer_id: customer.id.toString() });
                setCurrentPage(1);
              }}
              className="btn btn-outline"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Betting Table */}
      <UnifiedTable
        data={bets}
        columns={columns}
        isLoading={loading}
        error={error}
        onRowClick={handleBetClick}
        onRetry={refetch}
        minWidth="1200px"
        emptyState={{
          icon: <DollarSign className="w-12 h-12 text-gray-400 mb-4" />,
          title: `No ${type} bets found`,
          description: `This customer has no ${type} betting activity.`
        }}
      />

      {/* Pagination */}
      {total > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(total / itemsPerPage)}
          itemsPerPage={itemsPerPage}
          totalItems={total}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
        />
      )}

      {/* Bet Details Modal */}
      {selectedBet && (
        <Modal
          isOpen={showBetModal}
          onClose={() => setShowBetModal(false)}
          title={`${type === 'casino' ? 'Casino' : type === 'sportsbook' ? 'Sportsbook' : 'Trade'} Bet Details`}
        >
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400">Bet ID</label>
                <p className="text-gray-100 font-mono">#{selectedBet.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400">Game</label>
                <p className="text-gray-100">{selectedBet.game_name || selectedBet.game || 'Unknown Game'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400">Amount</label>
                <p className="text-blue-400 font-medium">
                  {formatCurrency(selectedBet.amount, selectedBet.currency || selectedBet.wallet_currency)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400">Income</label>
                <p className="text-green-400 font-medium">
                  {formatCurrency(selectedBet.income, selectedBet.currency || selectedBet.wallet_currency)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400">Multiplier</label>
                <p className="text-purple-400 font-medium">{selectedBet.multiplier || '0'}x</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400">Result</label>
                <div>{getResultBadge(selectedBet)}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400">Status</label>
                <div>{getStatusBadge(selectedBet)}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400">Date</label>
                <p className="text-gray-100">{formatDate(selectedBet.created_at || selectedBet.timestamp)}</p>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default BettingTable;
