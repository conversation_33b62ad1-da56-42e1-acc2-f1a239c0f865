import React from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { cn } from '../../utils/cn';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  isLoading?: boolean;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange,
  isLoading = false
}) => {
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && !isLoading) {
      onPageChange(page);
    }
  };

  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (!isLoading) {
      onItemsPerPageChange(parseInt(e.target.value));
    }
  };

  // Always show pagination controls if there are items, even for single page
  // This allows users to change items per page
  if (totalItems === 0) {
    return null;
  }

  return (
    <div className="flex items-center justify-between px-6 py-4 bg-dark-700 border-t border-dark-600">
      {/* Items per page and info */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-400">Show:</span>
          <select
            value={itemsPerPage}
            onChange={handleItemsPerPageChange}
            disabled={isLoading}
            className="bg-dark-600 border border-dark-500 rounded px-2 py-1 text-sm text-gray-200 focus:outline-none focus:border-primary-500 disabled:opacity-50"
          >
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
          <span className="text-sm text-gray-400">per page</span>
        </div>
        
        <div className="text-sm text-gray-400">
          Showing {startItem} to {endItem} of {totalItems} results
        </div>
      </div>

      {/* Pagination controls */}
      <div className="flex items-center gap-1">
        {/* First page */}
        <button
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1 || isLoading}
          className={cn(
            "p-2 rounded-md transition-colors",
            currentPage === 1 || isLoading
              ? "text-gray-500 cursor-not-allowed"
              : "text-gray-300 hover:bg-dark-600 hover:text-primary-500"
          )}
        >
          <ChevronsLeft className="w-4 h-4" />
        </button>

        {/* Previous page */}
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1 || isLoading}
          className={cn(
            "p-2 rounded-md transition-colors",
            currentPage === 1 || isLoading
              ? "text-gray-500 cursor-not-allowed"
              : "text-gray-300 hover:bg-dark-600 hover:text-primary-500"
          )}
        >
          <ChevronLeft className="w-4 h-4" />
        </button>

        {/* Page numbers */}
        <div className="flex items-center gap-1 mx-2">
          {totalPages > 1 ? (
            getVisiblePages().map((page, index) => (
              <React.Fragment key={index}>
                {page === '...' ? (
                  <span className="px-3 py-1 text-gray-500">...</span>
                ) : (
                  <button
                    onClick={() => handlePageChange(page as number)}
                    disabled={isLoading}
                    className={cn(
                      "px-3 py-1 rounded-md text-sm transition-colors",
                      currentPage === page
                        ? "bg-primary-500 text-dark-800 font-medium"
                        : isLoading
                        ? "text-gray-500 cursor-not-allowed"
                        : "text-gray-300 hover:bg-dark-600 hover:text-primary-500"
                    )}
                  >
                    {page}
                  </button>
                )}
              </React.Fragment>
            ))
          ) : (
            <span className="px-3 py-1 text-sm text-gray-400">Page 1 of 1</span>
          )}
        </div>

        {/* Next page */}
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages || isLoading}
          className={cn(
            "p-2 rounded-md transition-colors",
            currentPage === totalPages || isLoading
              ? "text-gray-500 cursor-not-allowed"
              : "text-gray-300 hover:bg-dark-600 hover:text-primary-500"
          )}
        >
          <ChevronRight className="w-4 h-4" />
        </button>

        {/* Last page */}
        <button
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages || isLoading}
          className={cn(
            "p-2 rounded-md transition-colors",
            currentPage === totalPages || isLoading
              ? "text-gray-500 cursor-not-allowed"
              : "text-gray-300 hover:bg-dark-600 hover:text-primary-500"
          )}
        >
          <ChevronsRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default Pagination;
