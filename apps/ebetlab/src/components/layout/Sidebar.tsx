import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { cn } from '../../utils/cn';
import {
  LayoutDashboard, Paintbrush, Users, ChevronDown, Dices,
  Settings, List, DollarSign, Star, Zap, Shield, Gift,
  Target, Award, ShoppingCart, FileText, Receipt, Database, Terminal, Server, MessageSquare
} from 'lucide-react';
import { BetrozLogo } from '../ui/BetrozLogo';

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  badge?: string | number;
  end?: boolean;
}

const NavItem: React.FC<NavItemProps> = ({ to, icon, label, badge, end = false }) => {
  return (
    <NavLink
      to={to}
      end={end}
      className={({ isActive }) => cn('sidebar-item', isActive && 'active')}
    >
      {icon}
      <span className="flex-1">{label}</span>
      {badge && (
        <span className="px-2 py-0.5 text-xs bg-primary-500 text-dark-800 rounded-full">
          {badge}
        </span>
      )}
    </NavLink>
  );
};

interface DropdownNavItemProps {
  icon: React.ReactNode;
  label: string;
  children: React.ReactNode;
}

const DropdownNavItem: React.FC<DropdownNavItemProps> = ({ icon, label, children }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="sidebar-item w-full text-left"
      >
        {icon}
        <span className="flex-1">{label}</span>
        <ChevronDown
          size={16}
          className={cn('transition-transform duration-200', isOpen && 'rotate-180')}
        />
      </button>
      {isOpen && (
        <div className="dropdown-content">
          {children}
        </div>
      )}
    </div>
  );
};

interface SidebarProps {
  open: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ open }) => {
  return (
    <aside 
      className={cn(
        'bg-dark-800 border-r border-dark-700 h-screen transition-all duration-300 overflow-y-auto',
        open ? 'w-64' : 'w-0'
      )}
    >
      <div className="p-4 border-b border-dark-700">
        <div className="flex items-center gap-3">
          <BetrozLogo />
          <span className="font-bold text-xl text-white">Betroz</span>
          <span className="text-xs bg-dark-700 px-2 py-0.5 rounded text-primary-500">Admin</span>
        </div>
      </div>
      
      <div className="p-3">
        <NavItem to="/" icon={<LayoutDashboard size={20} />} label="Dashboard" end={true} />

        <div className="mt-6 mb-2 px-4 text-xs font-semibold text-gray-400 uppercase">
          People
        </div>

        <DropdownNavItem
          icon={<Users size={20} />}
          label="Customers"
        >
          <NavItem
            to="/customers/list"
            icon={<Users size={16} />}
            label="Customer List"
          />
          <NavItem
            to="/customers/self-exclusions"
            icon={<Users size={16} />}
            label="Self Exclusion List"
          />
          <NavItem
            to="/customers/chat-blacklist"
            icon={<Users size={16} />}
            label="Chat Blacklist"
          />
          <NavItem
            to="/verification-codes"
            icon={<Shield size={16} />}
            label="Verification Codes"
          />
        </DropdownNavItem>

        <div className="mt-6 mb-2 px-4 text-xs font-semibold text-gray-400 uppercase">
          Casino
        </div>

        <DropdownNavItem
          icon={<Dices size={20} />}
          label="Bonus Management"
        >
          <NavItem
            to="/bonuses"
            icon={<Gift size={16} />}
            label="Bonuses"
          />

          <DropdownNavItem
            icon={<Settings size={16} />}
            label="Auto Bonus Rules"
          >
            <NavItem
              to="/auto-bonus-rules/list"
              icon={<List size={14} />}
              label="List"
            />
            {/* <NavItem
              to="/auto-bonus-rules/claims"
              icon={<FileCheck size={14} />}
              label="Claims"
            /> */}
          </DropdownNavItem>

          <NavItem
            to="/balance-correction-rules"
            icon={<DollarSign size={16} />}
            label="Balance Correction Rules"
          />

          {/* <NavItem
            to="/bonus-request-content"
            icon={<FileText size={16} />}
            label="Bonus Request Content"
          /> */}

          <NavItem
            to="/welcome-bonus"
            icon={<Star size={16} />}
            label="Welcome Bonus"
          />

          <DropdownNavItem
            icon={<Zap size={16} />}
            label="Bonus Drops"
          >
            <NavItem
              to="/bonus-drops/list"
              icon={<List size={14} />}
              label="List"
            />
            <NavItem
              to="/bonus-drops/usage"
              icon={<List size={14} />}
              label="Usage"
            />
          </DropdownNavItem>
        </DropdownNavItem>

        <div className="mt-6 mb-2 px-4 text-xs font-semibold text-gray-400 uppercase">
          Missions
        </div>

        <DropdownNavItem
          icon={<Target size={20} />}
          label="Missions Management"
        >
          <NavItem
            to="/missions"
            icon={<Target size={16} />}
            label="Missions"
            end={true}
          />
          <NavItem
            to="/missions/points"
            icon={<Award size={16} />}
            label="Points"
          />
          <NavItem
            to="/missions/market"
            icon={<ShoppingCart size={16} />}
            label="Market"
          />
          <NavItem
            to="/missions/market-requests"
            icon={<FileText size={16} />}
            label="Market Requests"
          />
          <NavItem
            to="/missions/transactions"
            icon={<Receipt size={16} />}
            label="Points Transactions"
          />
        </DropdownNavItem>

        <div className="mt-6 mb-2 px-4 text-xs font-semibold text-gray-400 uppercase">
          Finance
        </div>

        <DropdownNavItem
          icon={<DollarSign size={20} />}
          label="Financial Management"
        >
          <NavItem
            to="/finance/corrections"
            icon={<DollarSign size={16} />}
            label="Balance Corrections"
          />
          <NavItem
            to="/finance/discounts"
            icon={<DollarSign size={16} />}
            label="Discounts"
          />
          <NavItem
            to="/finance/ftd-reports"
            icon={<DollarSign size={16} />}
            label="FTD Reports"
          />
          <NavItem
            to="/finance/ftw-reports"
            icon={<DollarSign size={16} />}
            label="FTW Reports"
          />
          <NavItem
            to="/finance/deposits-withdraws"
            icon={<DollarSign size={16} />}
            label="Deposits & Withdrawals"
          />
          <NavItem
            to="/finance/waiting-deposits"
            icon={<DollarSign size={16} />}
            label="Waiting Deposits"
          />
          <NavItem
            to="/finance/cancelled-withdraws"
            icon={<DollarSign size={16} />}
            label="Cancelled Withdrawals"
          />
        </DropdownNavItem>

        <div className="mt-6 mb-2 px-4 text-xs font-semibold text-gray-400 uppercase">
          Settings
        </div>

        <NavItem
          to="/settings/css"
          icon={<Paintbrush size={20} />}
          label="CSS Settings"
        />

        <NavItem
          to="/crud-manager"
          icon={<Database size={20} />}
          label="CRUD Manager"
        />

        <div className="mt-6 mb-2 px-4 text-xs font-semibold text-gray-400 uppercase">
          Debug Tools
        </div>

        <NavItem
          to="/debug/ebetlab-api"
          icon={<Terminal size={20} />}
          label="Debug Ebetlab API"
        />

        <NavItem
          to="/debug/backend-api"
          icon={<Server size={20} />}
          label="Debug Backend API"
        />

        <NavItem
          to="/debug/slack-bots"
          icon={<MessageSquare size={20} />}
          label="Slack Bot Status"
        />
      </div>
    </aside>
  );
};

export default Sidebar;
