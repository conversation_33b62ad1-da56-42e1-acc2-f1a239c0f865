import React, { useState, useEffect, useRef } from 'react';
import { Bell, Settings, Menu, Search, User, LogOut, ChevronDown, UserCircle, Terminal, Server } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils/cn';
import { useAuth } from '../../contexts/AuthContext';

interface HeaderProps {
  toggleSidebar: () => void;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar }) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { logout, authData } = useAuth();
  const navigate = useNavigate();
  const userMenuRef = useRef<HTMLDivElement>(null);

  const handleLogout = () => {
    logout();
    setShowUserMenu(false);
  };

  const handleProfileClick = () => {
    navigate('/profile');
    setShowUserMenu(false);
  };

  const handleDebugEbetlabClick = () => {
    navigate('/debug/ebetlab-api');
    setShowUserMenu(false);
  };

  const handleDebugBackendClick = () => {
    navigate('/debug/backend-api');
    setShowUserMenu(false);
  };

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="flex items-center justify-between px-6 py-4 bg-dark-800 border-b border-dark-700">
      <div className="flex items-center gap-4">
        <button 
          onClick={toggleSidebar}
          className="p-2 rounded-md hover:bg-dark-700 text-gray-300"
        >
          <Menu size={20} />
        </button>
        
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search size={16} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search..."
            className="py-2 pl-10 pr-4 bg-dark-700 rounded-md border border-dark-600 focus:outline-none focus:border-primary-500 text-sm w-64"
          />
        </div>
      </div>
      
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-1 px-3 py-1.5 bg-dark-700 rounded-md">
          <div className="w-2 h-2 rounded-full bg-green-500"></div>
          <span className="text-sm font-medium">Online</span>
        </div>
        
        <button className={cn(
          "relative p-2 rounded-md text-gray-500 cursor-not-allowed opacity-50"
        )} disabled>
          <Bell size={20} />
          <span className="absolute top-1 right-1 w-2 h-2 bg-gray-500 rounded-full"></span>
        </button>

        <button className="p-2 rounded-md text-gray-500 cursor-not-allowed opacity-50" disabled>
          <Settings size={20} />
        </button>
        
        <div ref={userMenuRef} className="relative ml-4 pl-4 border-l border-dark-600">
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center gap-3 hover:bg-dark-700 rounded-md p-2 transition-colors"
          >
            <div className="flex flex-col items-end">
              <span className="text-sm font-medium">{authData?.user?.name || 'Admin User'}</span>
              <span className="text-xs text-gray-400">{authData?.user?.email || '<EMAIL>'}</span>
            </div>
            <div className="w-9 h-9 bg-primary-500 rounded-full flex items-center justify-center text-dark-800">
              <User size={18} />
            </div>
            <ChevronDown size={16} className="text-gray-400" />
          </button>

          {showUserMenu && (
            <div className="absolute right-0 top-full mt-2 w-48 bg-dark-700 border border-dark-600 rounded-md shadow-lg z-50">
              <div className="py-1">
                <button
                  onClick={handleProfileClick}
                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-300 hover:bg-dark-600 hover:text-primary-500 transition-colors"
                >
                  <UserCircle size={16} />
                  View Profile
                </button>
                <button
                  onClick={handleDebugEbetlabClick}
                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-300 hover:bg-dark-600 hover:text-primary-500 transition-colors"
                >
                  <Terminal size={16} />
                  Debug Ebetlab API
                </button>
                <button
                  onClick={handleDebugBackendClick}
                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-300 hover:bg-dark-600 hover:text-primary-500 transition-colors"
                >
                  <Server size={16} />
                  Debug Backend API
                </button>
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-300 hover:bg-dark-600 hover:text-error-500 transition-colors"
                >
                  <LogOut size={16} />
                  Sign Out
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;