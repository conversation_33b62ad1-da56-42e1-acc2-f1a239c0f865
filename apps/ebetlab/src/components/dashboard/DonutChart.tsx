import React from 'react';
import {
  Chart as ChartJ<PERSON>,
  ArcElement,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'chart.js';
import { Doughn<PERSON> } from 'react-chartjs-2';

ChartJS.register(ArcElement, Tooltip, Legend);

interface DonutChartData {
  label: string;
  value: number;
  percentage?: number;
}

interface DonutChartProps {
  title?: string;
  data: DonutChartData[];
  centerText?: string;
  centerSubtext?: string;
  showLegend?: boolean;
}

const DonutChart: React.FC<DonutChartProps> = ({ title, data, centerText, centerSubtext, showLegend = false }) => {
  // Generate blue/aqua colors for donut slices
  const generateBlueAquaColors = (count: number) => {
    const colors = [];
    const backgroundColors = [];
    
    // Base blue/aqua colors from theme
    const baseColors = [
      '#18cffb', // primary-500
      '#0ac3c6', // secondary-500
      '#33dffa', // primary-400
      '#33f9fa', // secondary-400
      '#66e7fc', // primary-300
      '#66fbfc', // secondary-300
      '#99effd', // primary-200
      '#99fcfd', // secondary-200
      '#0ca5c8', // primary-600
      '#089c9e', // secondary-600
      '#097c96', // primary-700
      '#067577', // secondary-700
    ];
    
    for (let i = 0; i < count; i++) {
      const baseColor = baseColors[i % baseColors.length];
      
      // Add slight variation for more colors if needed
      if (i >= baseColors.length) {
        const variation = Math.floor(i / baseColors.length);
        const hue = 190 + (variation * 20); // Stay in blue/aqua range
        const saturation = 70 - (variation * 5); // Slightly reduce saturation
        const lightness = 55 + (variation * 5);
        
        colors.push(`hsl(${hue}, ${saturation}%, ${lightness}%)`);
        backgroundColors.push(`hsla(${hue}, ${saturation}%, ${lightness}%, 0.8)`);
      } else {
        colors.push(baseColor);
        // Convert hex to rgba for background
        const rgb = hexToRgb(baseColor);
        if (rgb) {
          backgroundColors.push(`rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.8)`);
        } else {
          backgroundColors.push(baseColor + 'CC'); // fallback with hex opacity
        }
      }
    }
    
    return { colors, backgroundColors };
  };

  // Helper function to convert hex to RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  const { colors, backgroundColors } = generateBlueAquaColors(data.length);

  const chartData = {
    labels: data.map(item => item.label),
    datasets: [
      {
        data: data.map(item => item.value),
        backgroundColor: backgroundColors,
        borderColor: colors,
        borderWidth: 2,
        hoverBackgroundColor: colors,
        hoverBorderWidth: 3,
        cutout: '60%', // Makes it a donut chart
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: showLegend,
        position: 'bottom' as const,
        labels: {
          color: '#ffffff',
          font: {
            size: 11,
          },
          padding: 12,
          usePointStyle: true,
          pointStyle: 'circle',
          boxWidth: 12,
          boxHeight: 12,
          generateLabels: function(chart: any) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              const total = data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
              return data.labels.map((label: string, i: number) => {
                const value = data.datasets[0].data[i];
                const percentage = ((value / total) * 100).toFixed(1);
                return {
                  text: `${label}: ${percentage}%`,
                  fillStyle: data.datasets[0].backgroundColor[i],
                  strokeStyle: data.datasets[0].borderColor[i],
                  lineWidth: 2,
                  hidden: false,
                  index: i,
                  fontColor: '#ffffff'
                };
              });
            }
            return [];
          }
        },
      },
      tooltip: {
        backgroundColor: '#061d2b',
        titleColor: '#f3f4f6',
        bodyColor: '#d1d5db',
        borderColor: '#1f2937',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = Math.round((context.parsed + Number.EPSILON) * 100) / 100;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} (${percentage}%)`;
          }
        }
      },
    },
    elements: {
      arc: {
        borderWidth: 2,
      }
    },
  };

  return (
    <div className="h-full w-full">
      {title && (
        <h3 className="text-lg font-semibold mb-4">{title}</h3>
      )}
      <div className="h-full w-full relative">
        {data.length > 0 ? (
          <>
            <Doughnut data={chartData} options={options} />
            {/* Center text overlay */}
            {(centerText || centerSubtext) && (
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="text-center">
                  {centerText && (
                    <div className="text-2xl font-bold text-gray-100">{centerText}</div>
                  )}
                  {centerSubtext && (
                    <div className="text-sm text-gray-400 mt-1">{centerSubtext}</div>
                  )}
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400">
            No data available
          </div>
        )}
      </div>
    </div>
  );
};

export default DonutChart;
