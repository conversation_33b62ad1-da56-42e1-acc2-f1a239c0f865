import React, { useState } from 'react';
import { BigWinLoseItem } from '../../utils/api';

interface BigWinLoseTableProps {
  winData: BigWinLoseItem[];
  loseData: BigWinLoseItem[];
  isLoading: boolean;
  error: string | null;
  onRetry: () => void;
}

const BigWinLoseTable: React.FC<BigWinLoseTableProps> = ({ 
  winData, 
  loseData, 
  isLoading, 
  error, 
  onRetry 
}) => {
  const [activeTab, setActiveTab] = useState<'wins' | 'losses'>('wins');

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string) => {
    return `₺${parseFloat(amount).toFixed(2)}`;
  };

  if (error) {
    return (
      <div className="admin-card">
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">Error loading big win/lose data</div>
          <div className="text-gray-400 text-sm">{error}</div>
          <button 
            className="btn btn-primary mt-4"
            onClick={onRetry}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="admin-card">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <div className="text-gray-400">Loading big win/lose data...</div>
        </div>
      </div>
    );
  }

  const currentData = activeTab === 'wins' ? winData : loseData;

  return (
    <div className="admin-card">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Big Wins & Losses</h3>
        
        {/* Tab Navigation */}
        <div className="flex bg-dark-700 rounded-lg p-1">
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'wins'
                ? 'bg-green-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('wins')}
          >
            Big Wins ({winData.length})
          </button>
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'losses'
                ? 'bg-red-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('losses')}
          >
            Big Losses ({loseData.length})
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-dark-600">
              <th className="text-left py-3 px-4 font-medium text-gray-400">Game Name</th>
              <th className="text-left py-3 px-4 font-medium text-gray-400">Player</th>
              <th className="text-left py-3 px-4 font-medium text-gray-400">Bet Amount</th>
              <th className="text-left py-3 px-4 font-medium text-gray-400">Win Amount</th>
              <th className="text-left py-3 px-4 font-medium text-gray-400">Net</th>
              <th className="text-left py-3 px-4 font-medium text-gray-400">Time</th>
            </tr>
          </thead>
          <tbody>
            {currentData.length > 0 ? (
              currentData.map((item) => (
                <tr key={item.id} className="border-b border-dark-700 hover:bg-dark-700/50">
                  <td className="py-3 px-4">
                    <div className="font-medium">{item.game_name}</div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <div className="text-sm">
                        <div className="font-medium">{item.customer.username || item.customer.masked_username}</div>
                        <div className="text-gray-400 text-xs">{item.customer.rank}</div>
                      </div>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-blue-400 font-medium">
                      {formatCurrency(item.amount)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`font-medium ${
                      activeTab === 'wins' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {formatCurrency(item.income)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`font-medium ${
                      parseFloat(item.net) >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {formatCurrency(item.net)}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-gray-400 text-sm">
                    {formatTimestamp(item.timestamp)}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="py-8 text-center text-gray-400">
                  No {activeTab === 'wins' ? 'big wins' : 'big losses'} data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default BigWinLoseTable;
