import React from 'react';
import { cn } from '../../utils/cn';

interface ActivityItemProps {
  title: string;
  time: string;
  description: string;
  icon: React.ReactNode;
  status?: 'success' | 'warning' | 'error' | 'default';
}

const ActivityItem: React.FC<ActivityItemProps> = ({
  title,
  time,
  description,
  icon,
  status = 'default',
}) => {
  const statusColors = {
    success: 'bg-success-500',
    warning: 'bg-warning-500',
    error: 'bg-error-500',
    default: 'bg-primary-500',
  };
  
  return (
    <div className="flex gap-4 py-3 border-b border-dark-700 last:border-0">
      <div className={cn(
        'p-2 rounded-full w-10 h-10 flex items-center justify-center',
        statusColors[status]
      )}>
        {icon}
      </div>
      
      <div className="flex-1">
        <div className="flex justify-between">
          <h4 className="font-medium">{title}</h4>
          <span className="text-sm text-gray-400">{time}</span>
        </div>
        <p className="text-sm text-gray-400 mt-1">{description}</p>
      </div>
    </div>
  );
};

export default ActivityItem;