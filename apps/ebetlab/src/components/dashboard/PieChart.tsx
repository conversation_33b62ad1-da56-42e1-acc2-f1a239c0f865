import React from 'react';
import {
  Chart as ChartJ<PERSON>,
  ArcElement,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'chart.js';
import { Pie } from 'react-chartjs-2';

ChartJS.register(ArcElement, Toolt<PERSON>, Legend);

interface PieChartData {
  game_name: string;
  amount_usd: string;
}

interface PieChartProps {
  title?: string;
  data: PieChartData[];
  type?: 'win' | 'lose';
  showLegend?: boolean;
}

const PieChart: React.FC<PieChartProps> = ({ title, data, type = 'win', showLegend = false }) => {
  // Generate blue/aqua colors for pie slices
  const generateBlueAquaColors = (count: number, type: 'win' | 'lose') => {
    const colors = [];
    const backgroundColors = [];

    // Base blue/aqua colors from theme
    const primaryShades = [
      '#18cffb', // primary-500
      '#33dffa', // primary-400
      '#66e7fc', // primary-300
      '#99effd', // primary-200
      '#ccf7fe', // primary-100
      '#0ca5c8', // primary-600
      '#097c96', // primary-700
      '#065264', // primary-800
    ];

    const secondaryShades = [
      '#18cffb', // primary-500 (same as primary now)
      '#33dffa', // primary-400
      '#66e7fc', // primary-300
      '#99effd', // primary-200
      '#ccf7fe', // primary-100
      '#0ca5c8', // primary-600
      '#097c96', // primary-700
      '#065264', // primary-800
    ];

    // For wins, use more vibrant blues/aquas, for losses use deeper blues
    const baseColors = type === 'win' ? primaryShades : secondaryShades;

    for (let i = 0; i < count; i++) {
      const baseColor = baseColors[i % baseColors.length];

      // Add slight variation for more colors if needed
      if (i >= baseColors.length) {
        const variation = Math.floor(i / baseColors.length);
        const hue = type === 'win' ? 190 + (variation * 15) : 180 + (variation * 15); // Stay in blue/aqua range
        const saturation = 70 - (variation * 5); // Slightly reduce saturation
        const lightness = type === 'win' ? 55 + (variation * 5) : 45 + (variation * 5);

        colors.push(`hsl(${hue}, ${saturation}%, ${lightness}%)`);
        backgroundColors.push(`hsla(${hue}, ${saturation}%, ${lightness}%, 0.8)`);
      } else {
        colors.push(baseColor);
        // Convert hex to rgba for background
        const rgb = hexToRgb(baseColor);
        if (rgb) {
          backgroundColors.push(`rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.8)`);
        } else {
          backgroundColors.push(baseColor + 'CC'); // fallback with hex opacity
        }
      }
    }

    return { colors, backgroundColors };
  };

  // Helper function to convert hex to RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  const { colors, backgroundColors } = generateBlueAquaColors(data.length, type);

  const chartData = {
    labels: data.map(item => item.game_name),
    datasets: [
      {
        data: data.map(item => parseFloat(item.amount_usd)),
        backgroundColor: backgroundColors,
        borderColor: colors,
        borderWidth: 2,
        hoverBackgroundColor: colors,
        hoverBorderWidth: 3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: showLegend,
        position: 'bottom' as const,
        labels: {
          color: '#ffffff',
          font: {
            size: 11,
          },
          padding: 12,
          usePointStyle: true,
          pointStyle: 'circle',
          boxWidth: 12,
          boxHeight: 12,
          generateLabels: function(chart: any) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              const total = data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
              return data.labels.map((label: string, i: number) => {
                const value = data.datasets[0].data[i];
                const percentage = ((value / total) * 100).toFixed(1);
                return {
                  text: `${label}: ${percentage}%`,
                  fillStyle: data.datasets[0].backgroundColor[i],
                  strokeStyle: data.datasets[0].borderColor[i],
                  lineWidth: 2,
                  hidden: false,
                  index: i,
                  fontColor: '#ffffff'
                };
              });
            }
            return [];
          }
        },
      },
      tooltip: {
        backgroundColor: '#061d2b',
        titleColor: '#f3f4f6',
        bodyColor: '#d1d5db',
        borderColor: '#1f2937',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: $${value} (${percentage}%)`;
          }
        }
      },
    },
    elements: {
      arc: {
        borderWidth: 2,
      }
    },
  };

  return (
    <div className="h-full w-full">
      {title && (
        <h4 className="text-md font-semibold mb-3 text-center">{title}</h4>
      )}
      <div className="h-full w-full">
        {data.length > 0 ? (
          <Pie data={chartData} options={options} />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400">
            No {type} data available
          </div>
        )}
      </div>
    </div>
  );
};

export default PieChart;
