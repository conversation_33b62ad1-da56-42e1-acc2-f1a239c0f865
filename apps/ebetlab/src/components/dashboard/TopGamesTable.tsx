import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface GameData {
  name: string;
  provider: string;
  plays: number;
  revenue: string;
  trend: 'up' | 'down' | 'neutral';
  change: number;
}

const TopGamesTable: React.FC = () => {
  const games: GameData[] = [
    { 
      name: 'Crypto Fortune', 
      provider: 'PlayNGo', 
      plays: 8742, 
      revenue: '₿ 34.56', 
      trend: 'up', 
      change: 12 
    },
    { 
      name: 'Roulette Pro', 
      provider: 'Evolution', 
      plays: 5621, 
      revenue: '₿ 28.92', 
      trend: 'up', 
      change: 8 
    },
    { 
      name: 'Bitcoin Blast', 
      provider: 'Pragmatic', 
      plays: 4389, 
      revenue: '₿ 21.43', 
      trend: 'down', 
      change: 3 
    },
    { 
      name: 'Blackjack VIP', 
      provider: 'Evolution', 
      plays: 3245, 
      revenue: '₿ 18.75', 
      trend: 'neutral', 
      change: 0 
    },
    { 
      name: 'Ethereum Spins', 
      provider: 'NetEnt', 
      plays: 2978, 
      revenue: '₿ 15.21', 
      trend: 'up', 
      change: 5 
    },
  ];

  const renderTrend = (trend: string, change: number) => {
    if (trend === 'up') {
      return (
        <div className="flex items-center text-green-500">
          <TrendingUp size={16} className="mr-1" />
          <span>+{change}%</span>
        </div>
      );
    } else if (trend === 'down') {
      return (
        <div className="flex items-center text-red-500">
          <TrendingDown size={16} className="mr-1" />
          <span>-{change}%</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center text-gray-400">
          <Minus size={16} className="mr-1" />
          <span>{change}%</span>
        </div>
      );
    }
  };

  return (
    <div className="admin-card">
      <div className="flex justify-between items-center mb-5">
        <h3 className="text-lg font-semibold">Top Performing Games</h3>
        <button className="text-sm text-primary-500 hover:text-primary-400">
          View All
        </button>
      </div>
      
      <div className="overflow-x-auto">
        <table className="data-table">
          <thead>
            <tr>
              <th className="rounded-tl-lg">Game</th>
              <th>Provider</th>
              <th>Plays</th>
              <th>Revenue</th>
              <th className="rounded-tr-lg">Trend</th>
            </tr>
          </thead>
          <tbody>
            {games.map((game, index) => (
              <tr key={index}>
                <td className="font-medium">{game.name}</td>
                <td>{game.provider}</td>
                <td>{game.plays.toLocaleString()}</td>
                <td>{game.revenue}</td>
                <td>{renderTrend(game.trend, game.change)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TopGamesTable;