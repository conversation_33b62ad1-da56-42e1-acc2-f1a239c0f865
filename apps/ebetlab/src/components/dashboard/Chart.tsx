import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line, Bar, Chart as ChartComponent } from 'react-chartjs-2';

// Blue/Aqua color palette generator
const generateBlueAquaPalette = (count: number, type: 'primary' | 'secondary' | 'mixed' = 'mixed') => {
  const colors = [];
  const backgroundColors = [];

  // Base colors from theme
  const primaryColors = [
    '#18cffb', // primary-500
    '#33dffa', // primary-400
    '#66e7fc', // primary-300
    '#99effd', // primary-200
    '#0ca5c8', // primary-600
    '#097c96', // primary-700
  ];

  const secondaryColors = [
    '#18cffb', // primary-500 (same as primary now)
    '#33dffa', // primary-400
    '#66e7fc', // primary-300
    '#99effd', // primary-200
    '#0ca5c8', // primary-600
    '#097c96', // primary-700
  ];

  const mixedColors = [...primaryColors, ...secondaryColors];

  for (let i = 0; i < count; i++) {
    let baseColor;

    if (type === 'primary') {
      baseColor = primaryColors[i % primaryColors.length];
    } else if (type === 'secondary') {
      baseColor = secondaryColors[i % secondaryColors.length];
    } else {
      baseColor = mixedColors[i % mixedColors.length];
    }

    colors.push(baseColor);

    // Create background color with opacity
    const rgb = hexToRgb(baseColor);
    if (rgb) {
      backgroundColors.push(`rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.6)`);
    } else {
      backgroundColors.push(baseColor + '99'); // fallback with hex opacity
    }
  }

  return { colors, backgroundColors };
};

// Helper function to convert hex to RGB
const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface ChartDataset {
  label: string;
  data: number[];
  borderColor?: string;
  backgroundColor?: string;
  type?: 'line' | 'bar';
  yAxisID?: string;
  tension?: number;
  borderWidth?: number;
  pointBackgroundColor?: string;
  pointBorderColor?: string;
  pointBorderWidth?: number;
  pointRadius?: number;
  pointHoverRadius?: number;
}

interface ChartProps {
  type: 'line' | 'bar' | 'mixed';
  title: string;
  labels: string[];
  datasets: ChartDataset[];
  height?: string;
  className?: string;
}

const Chart: React.FC<ChartProps> = ({ type, title, labels, datasets, height = "300px", className }) => {
  const isMixed = type === 'mixed';

  // Calculate the range for centering Y-axis at 0 for mixed charts
  let yAxisRange: number | undefined;
  if (isMixed) {
    const allValues = datasets.flatMap(dataset => dataset.data);
    const maxValue = Math.max(...allValues);
    const minValue = Math.min(...allValues);
    const maxAbsValue = Math.max(Math.abs(maxValue), Math.abs(minValue));
    yAxisRange = maxAbsValue * 1.1; // Add 10% padding
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: '#9ca3af',
          font: {
            size: 12,
          },
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#061d2b',
        titleColor: '#f3f4f6',
        bodyColor: '#d1d5db',
        borderColor: '#1f2937',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              // Format numbers with proper currency or count formatting
              const roundedValue = Math.round((context.parsed.y + Number.EPSILON) * 100) / 100;
              if (context.dataset.label === 'Net') {
                label += new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'USD',
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }).format(roundedValue);
              } else if (context.dataset.label?.includes('Count')) {
                label += Math.round(context.parsed.y).toLocaleString();
              } else {
                label += new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'USD',
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }).format(roundedValue);
              }
            }
            return label;
          }
        }
      },
    },
    scales: {
      x: {
        grid: {
          color: '#1f2937',
          drawBorder: false,
        },
        ticks: {
          color: '#9ca3af',
        },
      },
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        ...(yAxisRange && {
          min: -yAxisRange,
          max: yAxisRange,
        }),
        grid: {
          color: '#1f2937',
          drawBorder: false,
          lineWidth: function(context: any) {
            // Make the zero line more prominent
            return context.tick.value === 0 ? 2 : 1;
          },
        },
        ticks: {
          color: '#9ca3af',
          callback: function(value: any) {
            const roundedValue = Math.round((value + Number.EPSILON) * 100) / 100;
            return new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            }).format(roundedValue);
          }
        },
      },
      ...(isMixed && {
        y1: {
          type: 'linear' as const,
          display: true,
          position: 'right' as const,
          ...(yAxisRange && {
            min: -yAxisRange,
            max: yAxisRange,
          }),
          grid: {
            drawOnChartArea: false,
          },
          ticks: {
            color: '#9ca3af',
            callback: function(value: any) {
              const roundedValue = Math.round((value + Number.EPSILON) * 100) / 100;
              return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              }).format(roundedValue);
            }
          },
        }
      })
    },
  };

  // Generate blue/aqua color palette for datasets that don't have colors specified
  const palette = generateBlueAquaPalette(datasets.length, 'mixed');

  const chartData = {
    labels,
    datasets: datasets.map((dataset, index) => {
      // Use provided colors or fall back to generated palette
      const borderColor = dataset.borderColor || palette.colors[index];
      const backgroundColor = dataset.backgroundColor || palette.backgroundColors[index];

      const baseDataset = {
        ...dataset,
        borderColor,
        backgroundColor,
        borderWidth: dataset.borderWidth || 2,
        tension: dataset.tension || 0.3,
      };

      // Apply different styling based on dataset type
      if (dataset.type === 'line' || (!dataset.type && type === 'line')) {
        return {
          ...baseDataset,
          type: 'line' as const,
          pointBackgroundColor: dataset.pointBackgroundColor || borderColor,
          pointBorderColor: dataset.pointBorderColor || '#ffffff',
          pointBorderWidth: dataset.pointBorderWidth || 3,
          pointRadius: dataset.pointRadius || 6,
          pointHoverRadius: dataset.pointHoverRadius || 8,
          yAxisID: dataset.yAxisID || (isMixed ? 'y1' : 'y'),
          borderWidth: dataset.borderWidth || 4, // Thicker line for prominence
          shadowOffsetX: 2,
          shadowOffsetY: 2,
          shadowBlur: 4,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
        };
      } else {
        return {
          ...baseDataset,
          type: 'bar' as const,
          yAxisID: dataset.yAxisID || 'y',
          borderWidth: dataset.borderWidth || 2,
          borderSkipped: false, // Show borders on all sides
          borderRadius: 4, // Rounded corners for better appearance
          barThickness: dataset.barThickness || 'flex',
          maxBarThickness: 60,
        };
      }
    }),
  };

  return (
    <div className={className || "admin-card"}>
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <div style={{ height }}>
        {type === 'mixed' ? (
          <ChartComponent type="bar" options={options} data={chartData} />
        ) : type === 'line' ? (
          <Line options={options} data={chartData} />
        ) : (
          <Bar options={options} data={chartData} />
        )}
      </div>
    </div>
  );
};

export default Chart;
