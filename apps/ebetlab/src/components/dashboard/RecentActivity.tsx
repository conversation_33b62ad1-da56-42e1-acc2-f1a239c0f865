import React from 'react';
import ActivityItem from './ActivityItem';
import { User, CreditCard, Flag, Gamepad2 } from 'lucide-react';

const RecentActivity = () => {
  const activities = [
    {
      title: 'New User Registration',
      time: '10 min ago',
      description: 'User #28491 has registered with email cr****@gmail.com',
      icon: <User size={18} className="text-dark-800" />,
      status: 'success' as const,
    },
    {
      title: 'Withdrawal Request',
      time: '25 min ago',
      description: 'User #12385 requested withdrawal of 0.5 BTC',
      icon: <CreditCard size={18} className="text-dark-800" />,
      status: 'warning' as const,
    },
    {
      title: 'Suspicious Activity',
      time: '1 hour ago',
      description: 'Multiple failed login attempts for User #94832',
      icon: <Flag size={18} className="text-dark-800" />,
      status: 'error' as const,
    },
    {
      title: 'New Game Added',
      time: '3 hours ago',
      description: 'Provider BoostGaming added "Crypto Fortune" slot',
      icon: <Gamepad2 size={18} className="text-dark-800" />,
      status: 'default' as const,
    },
  ];
  
  return (
    <div className="admin-card h-full">
      <div className="flex justify-between items-center mb-5">
        <h3 className="text-lg font-semibold">Recent Activity</h3>
        <button className="text-sm text-primary-500 hover:text-primary-400">
          View All
        </button>
      </div>
      
      <div className="divide-y divide-dark-700">
        {activities.map((activity, index) => (
          <ActivityItem key={index} {...activity} />
        ))}
      </div>
    </div>
  );
};

export default RecentActivity;