import React, { ReactNode } from 'react';
import { cn } from '../../utils/cn';
import { ArrowUpIcon, ArrowDownIcon } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: ReactNode;
  change?: number;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  change,
  className,
}) => {
  const isPositive = change && change > 0;
  
  return (
    <div className={cn('stats-card', className)}>
      <div className="flex justify-between mb-4">
        <div className="text-gray-400 text-sm">{title}</div>
        <div className="p-2 bg-dark-600 rounded-md">{icon}</div>
      </div>
      
      <div className="text-2xl font-semibold mb-2">{value}</div>
      
      {typeof change !== 'undefined' && (
        <div className="flex items-center text-sm">
          <span className={cn(
            'flex items-center',
            isPositive ? 'text-green-500' : 'text-red-500'
          )}>
            {isPositive ? (
              <ArrowUpIcon size={14} className="mr-1" />
            ) : (
              <ArrowDownIcon size={14} className="mr-1" />
            )}
            {Math.abs(change)}%
          </span>
          <span className="ml-2 text-gray-400">from last period</span>
        </div>
      )}
    </div>
  );
};

export default StatCard;