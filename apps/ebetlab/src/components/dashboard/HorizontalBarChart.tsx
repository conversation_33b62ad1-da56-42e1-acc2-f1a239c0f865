import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface HorizontalBarChartData {
  label: string;
  value: number;
  additionalInfo?: string;
}

interface HorizontalBarChartProps {
  title: string;
  data: HorizontalBarChartData[];
  valueLabel?: string;
  maxItems?: number;
}

const HorizontalBarChart: React.FC<HorizontalBarChartProps> = ({ 
  title, 
  data, 
  valueLabel = 'Value',
  maxItems = 10 
}) => {
  // Generate blue/aqua gradient colors
  const generateGradientColors = (count: number) => {
    const colors = [];
    const backgroundColors = [];
    
    // Base blue/aqua colors from theme
    const baseColors = [
      '#18cffb', // primary-500
      '#0ac3c6', // secondary-500
      '#33dffa', // primary-400
      '#33f9fa', // secondary-400
      '#66e7fc', // primary-300
      '#66fbfc', // secondary-300
    ];
    
    for (let i = 0; i < count; i++) {
      const baseColor = baseColors[i % baseColors.length];
      
      // Create a gradient effect by adjusting opacity based on position
      const opacity = 0.9 - (i * 0.1);
      const minOpacity = 0.3;
      const finalOpacity = Math.max(opacity, minOpacity);
      
      colors.push(baseColor);
      
      // Convert hex to rgba for background
      const rgb = hexToRgb(baseColor);
      if (rgb) {
        backgroundColors.push(`rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${finalOpacity})`);
      } else {
        backgroundColors.push(baseColor + Math.floor(finalOpacity * 255).toString(16).padStart(2, '0'));
      }
    }
    
    return { colors, backgroundColors };
  };

  // Helper function to convert hex to RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  // Limit and sort data
  const sortedData = [...data]
    .sort((a, b) => b.value - a.value)
    .slice(0, maxItems);

  const { colors, backgroundColors } = generateGradientColors(sortedData.length);

  const chartData = {
    labels: sortedData.map(item => item.label),
    datasets: [
      {
        label: valueLabel,
        data: sortedData.map(item => item.value),
        backgroundColor: backgroundColors,
        borderColor: colors,
        borderWidth: 2,
        borderRadius: 4,
        borderSkipped: false,
      },
    ],
  };

  const options = {
    indexAxis: 'y' as const,
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#061d2b',
        titleColor: '#f3f4f6',
        bodyColor: '#d1d5db',
        borderColor: '#1f2937',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const dataPoint = sortedData[context.dataIndex];
            const roundedValue = Math.round((context.parsed.x + Number.EPSILON) * 100) / 100;
            let label = `${valueLabel}: ${roundedValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
            if (dataPoint.additionalInfo) {
              label += ` (${dataPoint.additionalInfo})`;
            }
            return label;
          }
        }
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        grid: {
          color: '#1f2937',
          drawBorder: false,
        },
        ticks: {
          color: '#9ca3af',
          callback: function(value: any) {
            const roundedValue = Math.round((value + Number.EPSILON) * 100) / 100;
            return roundedValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
          }
        },
      },
      y: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#9ca3af',
          font: {
            size: 11,
          },
          callback: function(_value: any, index: number) {
            const label = sortedData[index]?.label || '';
            // Truncate long labels
            return label.length > 15 ? label.substring(0, 15) + '...' : label;
          }
        },
      },
    },
    elements: {
      bar: {
        borderWidth: 2,
      }
    },
  };

  return (
    <div className="admin-card h-full">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <div className="h-[400px]">
        {sortedData.length > 0 ? (
          <Bar data={chartData} options={options} />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400">
            No data available
          </div>
        )}
      </div>
      
      {/* Data summary below chart */}
      {sortedData.length > 0 && (
        <div className="mt-4 pt-4 border-t border-dark-600">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Total Items:</span>
              <span className="ml-2 font-medium text-gray-200">{sortedData.length}</span>
            </div>
            <div>
              <span className="text-gray-400">Total {valueLabel}:</span>
              <span className="ml-2 font-medium text-primary-400">
                {(Math.round((sortedData.reduce((sum, item) => sum + item.value, 0) + Number.EPSILON) * 100) / 100).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HorizontalBarChart;
