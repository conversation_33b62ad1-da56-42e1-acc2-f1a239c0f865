import React, { useState } from 'react';
import { Lock, User, Shield, AlertCircle } from 'lucide-react';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Button from '../ui/Button';
import { <PERSON>rozLogo } from '../ui/BetrozLogo';
import { useAuth } from '../../contexts/AuthContext';

interface LoginModalProps {
  isOpen: boolean;
}

const LoginModal: React.FC<LoginModalProps> = ({ isOpen }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    otp: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [generalError, setGeneralError] = useState('');

  const { login } = useAuth();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
    
    // Clear general error
    if (generalError) {
      setGeneralError('');
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
    }

    if (!formData.otp.trim()) {
      newErrors.otp = 'OTP is required';
    } else if (!/^\d{6}$/.test(formData.otp.trim())) {
      newErrors.otp = 'OTP must be 6 digits';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setGeneralError('');

    try {
      // Use the server-side authentication endpoint
      const response = await fetch((import.meta.env.VITE_API_URL || '') + '/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: formData.username.trim(),
          password: formData.password,
          otp: formData.otp.trim()
        })
      });
      
      const result = await response.json();

      if (result.success) {
        login(result.data);
      } else {
        setGeneralError(result.error || 'Login failed. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setGeneralError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} showCloseButton={false}>
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <BetrozLogo className="h-12 w-auto" />
        </div>
        <h1 className="text-2xl font-bold text-gray-100 mb-2">
          Welcome to Betroz Admin
        </h1>
        <p className="text-gray-400">
          Please sign in to access the admin panel
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {generalError && (
          <div className="flex items-center gap-2 p-3 bg-error-500/10 border border-error-500/20 rounded-md">
            <AlertCircle className="h-5 w-5 text-error-500 flex-shrink-0" />
            <p className="text-sm text-error-500">{generalError}</p>
          </div>
        )}

        <div className="space-y-4">
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              name="username"
              type="email"
              placeholder="Enter your email"
              value={formData.username}
              onChange={handleInputChange}
              error={errors.username}
              className="pl-10"
              disabled={isLoading}
            />
          </div>

          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              name="password"
              type="password"
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleInputChange}
              error={errors.password}
              className="pl-10"
              disabled={isLoading}
            />
          </div>

          <div className="relative">
            <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              name="otp"
              type="text"
              placeholder="Enter 6-digit OTP"
              value={formData.otp}
              onChange={handleInputChange}
              error={errors.otp}
              className="pl-10"
              maxLength={6}
              disabled={isLoading}
            />
          </div>
        </div>

        <Button
          type="submit"
          variant="primary"
          size="lg"
          loading={isLoading}
          className="w-full"
        >
          {isLoading ? 'Signing in...' : 'Sign In'}
        </Button>
      </form>

      <div className="mt-6 text-center">
        <p className="text-xs text-gray-500">
          Secure login powered by EbetLab authentication
        </p>
      </div>
    </Modal>
  );
};

export default LoginModal;


