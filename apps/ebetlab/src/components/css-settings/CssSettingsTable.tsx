import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useNavigate } from 'react-router-dom';
import { Info, User, Calendar, Tag, Activity } from 'lucide-react';
import Pagination from '../ui/Pagination';
import UnifiedTable, { TableColumn } from '../ui/UnifiedTable';

interface CssSettingOperator {
  id: number;
  name: string;
}

interface CssSetting {
  id: number;
  operator_id: number;
  last_update: number;
  theme: string;
  is_active: boolean;
  name: string;
  operator: CssSettingOperator;
}

interface CssSettingsTableProps {
  settings: CssSetting[];
  total: number;
  currentPage: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  isLoading?: boolean;
}

interface TooltipProps {
  setting: CssSetting;
  children: React.ReactNode;
}

const Tooltip: React.FC<TooltipProps> = ({ setting, children }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);

  const updatePosition = () => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setPosition({
        x: rect.left + rect.width / 2,
        y: rect.top - 10
      });
    }
  };

  useEffect(() => {
    if (isVisible) {
      updatePosition();
      window.addEventListener('scroll', updatePosition);
      window.addEventListener('resize', updatePosition);

      return () => {
        window.removeEventListener('scroll', updatePosition);
        window.removeEventListener('resize', updatePosition);
      };
    }
  }, [isVisible]);

  const tooltipContent = isVisible ? (
    <div
      className="fixed z-[9999] w-80 p-3 bg-dark-600 border border-dark-500 rounded-lg shadow-xl pointer-events-none"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translate(-50%, -100%)'
      }}
    >
      <div className="text-xs">
        <h4 className="text-gray-200 font-medium mb-2">Raw CSS Setting Data</h4>
        <pre className="text-gray-400 whitespace-pre-wrap overflow-x-auto max-h-60">
          {JSON.stringify(setting, null, 2)}
        </pre>
      </div>
      {/* Arrow */}
      <div
        className="absolute w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-dark-600"
        style={{
          left: '50%',
          top: '100%',
          transform: 'translateX(-50%)'
        }}
      ></div>
    </div>
  ) : null;

  return (
    <>
      <div
        ref={triggerRef}
        className="relative inline-block"
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
      >
        {children}
      </div>
      {tooltipContent && createPortal(tooltipContent, document.body)}
    </>
  );
};

const CssSettingsTable: React.FC<CssSettingsTableProps> = ({
  settings,
  total,
  currentPage,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange,
  isLoading = false
}) => {
  const navigate = useNavigate();

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const totalPages = Math.ceil(total / itemsPerPage);

  const handleRowClick = (setting: CssSetting) => {
    navigate(`/settings/css/edit/${setting.id}`);
  };

  const getThemeColor = (theme: string) => {
    switch (theme.toLowerCase()) {
      case 'both':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      case 'light':
        return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20';
      case 'dark':
        return 'bg-purple-500/10 text-purple-500 border-purple-500/20';
      default:
        return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  // Table columns configuration
  const columns: TableColumn<CssSetting>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      render: (setting) => (
        <span className="text-sm font-medium text-gray-200">#{setting.id}</span>
      )
    },
    {
      key: 'name',
      label: 'Name',
      width: '200px',
      render: (setting) => (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-200 font-medium">{setting.name}</span>
        </div>
      )
    },
    {
      key: 'theme',
      label: 'Theme',
      width: '120px',
      render: (setting) => (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getThemeColor(setting.theme)}`}>
          {setting.theme}
        </span>
      )
    },
    {
      key: 'is_active',
      label: 'Status',
      width: '100px',
      render: (setting) => (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          setting.is_active
            ? 'bg-green-500/10 text-green-500 border border-green-500/20'
            : 'bg-red-500/10 text-red-500 border border-red-500/20'
        }`}>
          <Activity className="w-3 h-3 mr-1" />
          {setting.is_active ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'operator',
      label: 'Operator',
      width: '150px',
      render: (setting) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-300">{setting.operator?.name || 'Unknown'}</span>
        </div>
      )
    },
    {
      key: 'last_update',
      label: 'Last Update',
      width: '180px',
      render: (setting) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-300">{formatDate(setting.last_update)}</span>
        </div>
      )
    },
    {
      key: 'info',
      label: 'Info',
      width: '80px',
      render: (setting) => (
        <Tooltip setting={setting}>
          <Info className="w-4 h-4 text-gray-400 hover:text-primary-500 cursor-help" />
        </Tooltip>
      )
    }
  ];

  return (
    <div className="space-y-4">
      {/* Unified Table */}
      <UnifiedTable
        data={settings}
        columns={columns}
        title="CSS Settings"
        subtitle={`Total: ${total} settings`}
        isLoading={isLoading}
        onRowClick={handleRowClick}
        minWidth="1000px"
        emptyState={{
          icon: <Tag className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No CSS settings found',
          description: 'No CSS settings are available at the moment.'
        }}
      />

      {/* Pagination */}
      {total > 0 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={total}
            onPageChange={onPageChange}
            onItemsPerPageChange={onItemsPerPageChange}
            isLoading={isLoading}
          />
        </div>
      )}
    </div>
  );
};

export default CssSettingsTable;
