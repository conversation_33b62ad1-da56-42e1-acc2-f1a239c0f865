import React, { useState, useEffect, useMemo } from 'react';
import { Check, ChevronRight, ChevronLeft, AlertCircle, ChevronDown, ChevronUp, Search } from 'lucide-react';
import { useProviders } from '../../hooks/useAppData';
import { fetchGamesByProviders, type Game } from '../../utils/api/bonuses';

/**
 * MarketProviderGameSelector - Provider and game selection component for market products
 *
 * This component uses a different structure than the mission objectives selector:
 * - Providers are stored as objects with full metadata (id, identifier, name, image, total)
 * - Games are stored with id, name, and image
 * - Structure: [{ provider: {...}, games: [...] }]
 *
 * This allows for richer display of provider information and game details in the market context.
 */

export interface MarketSelectedGame {
  id: number;
  name: string;
  image: string;
}

export interface MarketSelectedProvider {
  provider: {
    id: number;
    identifier: string;
    name: string;
    image: string;
    total: number;
  };
  games: MarketSelectedGame[];
}

interface MarketProviderGameSelectorProps {
  selectedProviders: MarketSelectedProvider[];
  onProvidersChange: (providers: MarketSelectedProvider[]) => void;
  disabled?: boolean;
}

const MarketProviderGameSelector: React.FC<MarketProviderGameSelectorProps> = ({
  selectedProviders,
  onProvidersChange,
  disabled = false
}) => {
  const providers = useProviders();
  const [availableGames, setAvailableGames] = useState<Game[]>([]);
  const [loadingGames, setLoadingGames] = useState(false);
  const [error, setError] = useState<string>('');

  // Collapsible states
  const [isProviderSectionExpanded, setIsProviderSectionExpanded] = useState(false);
  const [isGameSectionExpanded, setIsGameSectionExpanded] = useState(false);

  // Search states
  const [providerSearchTerm, setProviderSearchTerm] = useState('');
  const [gameSearchTerm, setGameSearchTerm] = useState('');

  // Get currently selected provider IDs
  const selectedProviderIds = useMemo(() => 
    selectedProviders.map(sp => sp.provider.id), 
    [selectedProviders]
  );

  // Load games when providers are selected
  useEffect(() => {
    if (selectedProviderIds.length > 0) {
      loadGames();
    } else {
      setAvailableGames([]);
    }
  }, [selectedProviderIds]);

  const loadGames = async () => {
    setLoadingGames(true);
    setError('');

    try {
      const response = await fetchGamesByProviders(selectedProviderIds, 'freespin');
      if (response.success && response.data) {
        setAvailableGames(response.data.data);
      } else {
        setError(response.error || 'Failed to load games');
        setAvailableGames([]);
      }
    } catch (err) {
      setError('Failed to load games');
      setAvailableGames([]);
    } finally {
      setLoadingGames(false);
    }
  };

  const toggleProvider = (providerId: number) => {
    if (disabled) return;
    
    const provider = providers.find(p => p.id === providerId);
    if (!provider) return;

    const isSelected = selectedProviderIds.includes(providerId);
    
    if (isSelected) {
      // Remove provider and all its games
      const newProviders = selectedProviders.filter(sp => sp.provider.id !== providerId);
      onProvidersChange(newProviders);
    } else {
      // Add provider with empty games array
      const newProvider: MarketSelectedProvider = {
        provider: {
          id: provider.id,
          identifier: provider.identifier || `provider_${provider.id}`,
          name: provider.name || `Provider ${provider.id}`,
          image: provider.image || '',
          total: provider.total || 0
        },
        games: []
      };
      onProvidersChange([...selectedProviders, newProvider]);
    }
  };

  const addGameToProvider = (game: Game) => {
    if (disabled) return;

    const providerId = game.merchant_id;
    const providerIndex = selectedProviders.findIndex(sp => sp.provider.id === providerId);
    
    if (providerIndex === -1) return;

    const isAlreadySelected = selectedProviders[providerIndex].games.some(g => g.id === game.id);
    if (isAlreadySelected) return;

    const newGame: MarketSelectedGame = {
      id: game.id,
      name: game.name,
      image: game.image || ''
    };

    const updatedProviders = [...selectedProviders];
    updatedProviders[providerIndex] = {
      ...updatedProviders[providerIndex],
      games: [...updatedProviders[providerIndex].games, newGame]
    };

    onProvidersChange(updatedProviders);
  };

  const removeGameFromProvider = (providerId: number, gameId: number) => {
    if (disabled) return;
    
    const providerIndex = selectedProviders.findIndex(sp => sp.provider.id === providerId);
    if (providerIndex === -1) return;

    const updatedProviders = [...selectedProviders];
    updatedProviders[providerIndex] = {
      ...updatedProviders[providerIndex],
      games: updatedProviders[providerIndex].games.filter(g => g.id !== gameId)
    };

    onProvidersChange(updatedProviders);
  };

  const getAvailableGames = () => {
    const allSelectedGameIds = selectedProviders.flatMap(sp => sp.games.map(g => g.id));
    return availableGames.filter(game => !allSelectedGameIds.includes(game.id));
  };

  // Filtered providers based on search term
  const filteredProviders = useMemo(() => {
    if (!providerSearchTerm.trim()) return providers;
    return providers.filter(provider =>
      provider.name && provider.name.toLowerCase().includes(providerSearchTerm.toLowerCase())
    );
  }, [providers, providerSearchTerm]);

  // Filtered available games based on search term
  const filteredAvailableGames = useMemo(() => {
    const availableGames = getAvailableGames();
    if (!gameSearchTerm.trim()) return availableGames;
    return availableGames.filter(game =>
      game.name && game.name.toLowerCase().includes(gameSearchTerm.toLowerCase())
    );
  }, [availableGames, gameSearchTerm, selectedProviders]);

  return (
    <div className="space-y-6">
      {/* Provider Selection */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="text-md font-medium text-gray-200">Select Providers</h4>
          <button
            type="button"
            onClick={() => setIsProviderSectionExpanded(!isProviderSectionExpanded)}
            className="flex items-center gap-2 text-sm text-gray-400 hover:text-gray-300 transition-colors"
          >
            {isProviderSectionExpanded ? (
              <>
                <ChevronUp className="w-4 h-4" />
                Collapse
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4" />
                Expand ({selectedProviderIds.length} selected)
              </>
            )}
          </button>
        </div>

        {isProviderSectionExpanded && (
          <div className="space-y-3">
            {/* Provider Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search providers..."
                value={providerSearchTerm}
                onChange={(e) => setProviderSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-sm text-gray-200 placeholder-gray-400 focus:outline-none focus:border-primary-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
              {filteredProviders.map((provider) => (
                <div
                  key={provider.id}
                  onClick={() => toggleProvider(provider.id)}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    disabled
                      ? 'opacity-50 cursor-not-allowed'
                      : selectedProviderIds.includes(provider.id)
                        ? 'border-primary-500 bg-primary-500/10'
                        : 'border-dark-600 hover:border-dark-500'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-medium text-gray-200 text-sm">{provider.name}</h5>
                      <p className="text-xs text-gray-400">{provider.total} games</p>
                    </div>
                    <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                      selectedProviderIds.includes(provider.id)
                        ? 'border-primary-500 bg-primary-500'
                        : 'border-gray-400'
                    }`}>
                      {selectedProviderIds.includes(provider.id) && (
                        <Check className="w-2.5 h-2.5 text-white" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Game Selection */}
      {selectedProviderIds.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-md font-medium text-gray-200">Select Games</h4>
            <button
              type="button"
              onClick={() => setIsGameSectionExpanded(!isGameSectionExpanded)}
              className="flex items-center gap-2 text-sm text-gray-400 hover:text-gray-300 transition-colors"
            >
              {isGameSectionExpanded ? (
                <>
                  <ChevronUp className="w-4 h-4" />
                  Collapse
                </>
              ) : (
                <>
                  <ChevronDown className="w-4 h-4" />
                  Expand ({selectedProviders.reduce((total, sp) => total + sp.games.length, 0)} selected)
                </>
              )}
            </button>
          </div>

          {isGameSectionExpanded && (
            <div className="space-y-4">
              {/* Game Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search games..."
                  value={gameSearchTerm}
                  onChange={(e) => setGameSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-sm text-gray-200 placeholder-gray-400 focus:outline-none focus:border-primary-500"
                />
              </div>

              {/* Available Games */}
              {loadingGames ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
                  <p className="text-sm text-gray-400 mt-2">Loading games...</p>
                </div>
              ) : error ? (
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <AlertCircle className="w-5 h-5 text-red-400" />
                    <p className="text-sm text-red-400">{error}</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-medium text-gray-300 mb-2">Available Games</h5>
                    <div className="border border-dark-600 rounded-lg p-3 bg-dark-800/50 max-h-64 overflow-y-auto">
                      {filteredAvailableGames.length > 0 ? (
                        <div className="space-y-2">
                          {filteredAvailableGames.map((game) => (
                            <div
                              key={game.id}
                              className={`p-2 rounded-lg border border-dark-600 transition-all cursor-pointer ${
                                disabled
                                  ? 'opacity-50 cursor-not-allowed'
                                  : 'hover:border-dark-500'
                              }`}
                              onClick={() => addGameToProvider(game)}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <h6 className="font-medium text-gray-200 text-xs">{game.name}</h6>
                                  <p className="text-xs text-gray-400">{game.provider_name}</p>
                                </div>
                                <div className="text-primary-400 hover:text-primary-300">
                                  <ChevronRight className="w-3 h-3" />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-400 text-center py-4">
                          {gameSearchTerm ? 'No games match your search' : 'No games available'}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Selected Games by Provider */}
              {selectedProviders.length > 0 && (
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-gray-300">Selected Games by Provider</h5>
                  {selectedProviders.map((selectedProvider) => (
                    <div key={selectedProvider.provider.id} className="border border-dark-600 rounded-lg p-3 bg-dark-800/50">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <h6 className="font-medium text-gray-200 text-sm">{selectedProvider.provider.name}</h6>
                          <span className="text-xs text-gray-400">
                            ({selectedProvider.games.length} games)
                          </span>
                        </div>
                      </div>

                      {selectedProvider.games.length > 0 ? (
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {selectedProvider.games.map((game) => (
                            <div
                              key={game.id}
                              className="flex items-center justify-between p-2 bg-dark-700 rounded border border-dark-600"
                            >
                              <div>
                                <h6 className="font-medium text-gray-200 text-xs">{game.name}</h6>
                              </div>
                              <button
                                type="button"
                                onClick={() => removeGameFromProvider(selectedProvider.provider.id, game.id)}
                                className={`text-red-400 hover:text-red-300 transition-colors ${
                                  disabled ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                                disabled={disabled}
                              >
                                <ChevronLeft className="w-3 h-3" />
                              </button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-xs text-gray-400 text-center py-2">
                          No games selected for this provider
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MarketProviderGameSelector;
