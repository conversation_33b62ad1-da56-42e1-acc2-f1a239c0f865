# Market Products Provider/Game Structure

## New Structure for Market Products

The market products now use a different structure for providers and games compared to mission objectives. This allows for richer metadata and better display in the market context.

### Structure Format

```typescript
interface MarketSelectedProvider {
  provider: {
    id: number;
    identifier: string;
    name: string;
    image: string;
    total: number;
  };
  games: MarketSelectedGame[];
}

interface MarketSelectedGame {
  id: number;
  name: string;
  image: string;
}
```

### Example Data

```json
[
  {
    "provider": {
      "id": 146,
      "identifier": "pragmaticplay",
      "name": "Pragmatic Play",
      "image": "https://vendor-provider.fra1.cdn.digitaloceanspaces.com/ebetlab/game-providers/light/pragmaticplay.svg",
      "total": 481
    },
    "games": [
      {
        "id": 54590,
        "name": "Gold Party 2 - After Hours",
        "image": "https://agstatic.com/games/pragmaticplay/gold_party_2_after_hours.jpg"
      },
      {
        "id": 52699,
        "name": "Wealthy Frog",
        "image": "https://agstatic.com/games/pragmaticplay/wealthy_frog.jpg"
      },
      {
        "id": 52500,
        "name": "Sleeping Dragon",
        "image": "https://agstatic.com/games/pragmaticplay/sleeping_dragon.jpg"
      }
    ]
  }
]
```

## Key Differences from Mission Objectives

1. **Provider Metadata**: Full provider information including identifier, name, image, and total games count
2. **Game Images**: Games include image URLs for better visual representation
3. **Structured Format**: Array of provider objects rather than simple ID mappings
4. **Flexibility**: Can easily extend with additional metadata as needed

## Components

- `MarketProviderGameSelector`: Main selector component for market products
- `MarketProductForm`: Form component that uses the new selector
- `MarketProductDetailsModal`: Displays provider/game information with rich metadata

## Backward Compatibility

The system maintains backward compatibility with the legacy `Record<string, number[]>` format for existing data, automatically detecting and handling both formats in display components.
