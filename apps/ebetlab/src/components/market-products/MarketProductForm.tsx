import React, { useState, useEffect } from 'react';
import {
  Save, X, Globe, DollarSign, Package, Tag, Image,
  AlertCircle, Plus, Trash2, Info, Settings
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import MarketProviderGameSelector, { type MarketSelectedProvider } from './MarketProviderGameSelector';
import {
  PRODUCT_TYPE_OPTIONS,
  PRODUCT_CATEGORY_OPTIONS,
  type CreateMarketProductRequest,
  type UpdateMarketProductRequest,
  type ProductType,
  type ProductCategory,
  type MarketProduct,
  fetchAvailableCurrencies
} from '../../utils/api/market-products';

interface MarketProductFormProps {
  isEdit?: boolean;
  initialData?: MarketProduct;
  onSubmit: (data: CreateMarketProductRequest | UpdateMarketProductRequest) => void;
  onCancel: () => void;
  loading?: boolean;
}

const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'tr', name: 'Turkish' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' }
];

// Currency support removed - using points-only system

const MarketProductForm: React.FC<MarketProductFormProps> = React.memo(({
  isEdit = false,
  initialData,
  onSubmit,
  onCancel,
  loading = false
}) => {
  // Basic form fields
  const [name, setName] = useState(initialData?.name || '');
  const [slug, setSlug] = useState(initialData?.slug || '');
  const [type, setType] = useState<ProductType>(initialData?.type || 'general');
  const [category, setCategory] = useState<ProductCategory>(initialData?.category || 'cash');
  const [photoUrl, setPhotoUrl] = useState(initialData?.photoUrl || '');
  const [price, setPrice] = useState(initialData?.price?.toString() || '0');
  const [availableAmount, setAvailableAmount] = useState(
    initialData?.availableAmount?.toString() || ''
  );
  const [isMultiPerBuyer, setIsMultiPerBuyer] = useState(initialData?.isMultiPerBuyer || false);
  
  // Internationalization
  const [nameI18n, setNameI18n] = useState<Record<string, string>>(
    initialData?.name_i18n || { en: '' }
  );
  const [descriptionI18n, setDescriptionI18n] = useState<Record<string, string>>(
    initialData?.description_i18n || { en: '' }
  );
  
  // Currencies
  const [availableCurrencies, setAvailableCurrencies] = useState<string[]>([]);
  const [selectedCurrencies, setSelectedCurrencies] = useState<string[]>(
    initialData?.currencies || []
  );
  const [currenciesLoading, setCurrenciesLoading] = useState(false);
  
  // Provider/Game selection
  const [selectedProviders, setSelectedProviders] = useState<MarketSelectedProvider[]>([]);
  
  // UI state
  const [showAdvanced, setShowAdvanced] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Auto-generate slug from name
  useEffect(() => {
    if (!isEdit && name) {
      const generatedSlug = name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-+|-+$/g, '') // Remove leading/trailing dashes
        .trim();
      setSlug(generatedSlug);
    }
  }, [name, isEdit]);

  // Fetch available currencies on component mount
  useEffect(() => {
    const loadCurrencies = async () => {
      setCurrenciesLoading(true);
      try {
        const response = await fetchAvailableCurrencies();
        if (response.success && response.data) {
          setAvailableCurrencies(response.data);
        } else {
          console.error('Failed to fetch currencies:', response.error);
        }
      } catch (error) {
        console.error('Error loading currencies:', error);
      } finally {
        setCurrenciesLoading(false);
      }
    };

    loadCurrencies();
  }, []);

  // Initialize provider/game data from initialData
  useEffect(() => {
    if (initialData?.providers && Array.isArray(initialData.providers)) {
      // Handle new structure where providers is an array of MarketSelectedProvider objects
      setSelectedProviders(initialData.providers);
    } else if (initialData?.providers && typeof initialData.providers === 'object') {
      // Handle legacy structure where providers is Record<string, number[]>
      // Convert to new structure - this is a fallback for existing data
      const convertedProviders: MarketSelectedProvider[] = [];
      Object.entries(initialData.providers).forEach(([providerId, gameIds]) => {
        convertedProviders.push({
          provider: {
            id: Number(providerId),
            identifier: `provider_${providerId}`,
            name: `Provider ${providerId}`,
            image: '',
            total: gameIds.length
          },
          games: gameIds.map(gameId => ({
            id: gameId,
            name: `Game ${gameId}`,
            image: ''
          }))
        });
      });
      setSelectedProviders(convertedProviders);
    }
  }, [initialData]);

  // Validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!photoUrl.trim()) {
      newErrors.photoUrl = 'Product image URL is required';
    }

    const priceNum = parseFloat(price);
    if (isNaN(priceNum) || priceNum < 0) {
      newErrors.price = 'Price must be a valid positive number';
    }

    if (availableAmount && (isNaN(Number(availableAmount)) || Number(availableAmount) < 0)) {
      newErrors.availableAmount = 'Available amount must be a valid positive number';
    }

    // Currency validation
    if (selectedCurrencies.length === 0) {
      newErrors.currencies = 'At least one currency must be selected';
    }

    // Validate i18n fields
    if (!nameI18n.en?.trim()) {
      newErrors.nameI18nEn = 'English name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle currency selection
  const toggleCurrency = (currency: string) => {
    setSelectedCurrencies(prev => {
      if (prev.includes(currency)) {
        return prev.filter(c => c !== currency);
      } else {
        return [...prev, currency];
      }
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // For slots products, use the new structure directly
    // For general products, use empty array
    const providers = type === 'slots' ? selectedProviders : [];

    const formData = {
      name: name.trim(),
      slug: slug.trim() || undefined,
      type,
      category,
      photoUrl: photoUrl.trim(),
      price: parseFloat(price),
      availableAmount: availableAmount ? Number(availableAmount) : null,
      isMultiPerBuyer,
      currencies: selectedCurrencies,
      providers, // New structure: array of MarketSelectedProvider objects
      name_i18n: nameI18n,
      description_i18n: descriptionI18n
    };

    onSubmit(formData);
  };

  // Currency toggle removed - using points-only system

  // Handle i18n field changes
  const updateNameI18n = (lang: string, value: string) => {
    setNameI18n(prev => ({ ...prev, [lang]: value }));
  };

  const updateDescriptionI18n = (lang: string, value: string) => {
    setDescriptionI18n(prev => ({ ...prev, [lang]: value }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-200 flex items-center gap-2">
          <Package className="w-5 h-5" />
          Basic Information
        </h3>

        {/* Product Name */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Product Name *
          </label>
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter product name"
            error={errors.name}
            required
          />
        </div>

        {/* Slug */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Slug
          </label>
          <Input
            value={slug}
            onChange={(e) => setSlug(e.target.value)}
            placeholder="Auto-generated from name"
            error={errors.slug}
          />
          <p className="text-xs text-gray-400 mt-1">
            URL-friendly identifier. Leave empty to auto-generate from name.
          </p>
        </div>

        {/* Type and Category */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Product Type *
            </label>
            <select
              value={type}
              onChange={(e) => setType(e.target.value as ProductType)}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              required
            >
              {PRODUCT_TYPE_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Category *
            </label>
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value as ProductCategory)}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              required
            >
              {PRODUCT_CATEGORY_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Photo URL */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Product Image URL *
          </label>
          <Input
            value={photoUrl}
            onChange={(e) => setPhotoUrl(e.target.value)}
            placeholder="https://example.com/product-image.jpg"
            error={errors.photoUrl}
            required
          />
          {photoUrl && (
            <div className="mt-2">
              <img 
                src={photoUrl} 
                alt="Product preview"
                className="w-20 h-20 object-cover rounded-lg border border-dark-600"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />
            </div>
          )}
        </div>

        {/* Price and Availability */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Price *
            </label>
            <Input
              type="number"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              placeholder="0.00"
              error={errors.price}
              min="0"
              step="0.01"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Available Amount
            </label>
            <Input
              type="number"
              value={availableAmount}
              onChange={(e) => setAvailableAmount(e.target.value)}
              placeholder="Leave empty for unlimited"
              error={errors.availableAmount}
              min="0"
            />
            <p className="text-xs text-gray-400 mt-1">
              Leave empty for unlimited availability
            </p>
          </div>
        </div>

        {/* Multi Purchase */}
        <div className="flex items-center gap-3">
          <input
            type="checkbox"
            id="isMultiPerBuyer"
            checked={isMultiPerBuyer}
            onChange={(e) => setIsMultiPerBuyer(e.target.checked)}
            className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500"
          />
          <label htmlFor="isMultiPerBuyer" className="text-sm text-gray-300">
            Allow multiple purchases per user
          </label>
        </div>
      </div>

      {/* Provider/Game Selection - Show for slots type */}
      {type === 'slots' && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-200 flex items-center gap-2">
            <Tag className="w-5 h-5" />
            Provider & Game Selection
          </h3>
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Info className="w-5 h-5 text-blue-400 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-400 mb-1">Slot Product Configuration</h4>
                <p className="text-sm text-gray-300">
                  For slot products, you can restrict availability to specific providers and games.
                  Leave empty to allow all providers and games.
                </p>
              </div>
            </div>
          </div>
          <MarketProviderGameSelector
            selectedProviders={selectedProviders}
            onProvidersChange={setSelectedProviders}
            disabled={loading}
          />
        </div>
      )}

      {/* Advanced Settings Toggle */}
      <div className="border-t border-dark-600 pt-4">
        <button
          type="button"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center gap-2 text-primary-400 hover:text-primary-300 transition-colors"
        >
          <Settings className="w-4 h-4" />
          Advanced Settings
          <span className="text-xs bg-primary-500/20 px-2 py-1 rounded">
            {showAdvanced ? 'Hide' : 'Show'}
          </span>
        </button>
      </div>

      {/* Advanced Settings */}
      {showAdvanced && (
        <div className="space-y-6 border-t border-dark-600 pt-6">
          {/* Internationalization */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-200 flex items-center gap-2">
              <Globe className="w-4 h-4" />
              Internationalization
            </h4>

            {/* Product Names */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">
                Product Names by Language
              </label>
              <div className="space-y-3">
                {SUPPORTED_LANGUAGES.map(lang => (
                  <div key={lang.code}>
                    <label className="block text-xs text-gray-400 mb-1">
                      {lang.name} ({lang.code.toUpperCase()})
                      {lang.code === 'en' && ' *'}
                    </label>
                    <Input
                      value={nameI18n[lang.code] || ''}
                      onChange={(e) => updateNameI18n(lang.code, e.target.value)}
                      placeholder={`Product name in ${lang.name}`}
                      error={lang.code === 'en' ? errors.nameI18nEn : undefined}
                      required={lang.code === 'en'}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Product Descriptions */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">
                Product Descriptions by Language
              </label>
              <div className="space-y-3">
                {SUPPORTED_LANGUAGES.map(lang => (
                  <div key={lang.code}>
                    <label className="block text-xs text-gray-400 mb-1">
                      {lang.name} ({lang.code.toUpperCase()})
                    </label>
                    <textarea
                      value={descriptionI18n[lang.code] || ''}
                      onChange={(e) => updateDescriptionI18n(lang.code, e.target.value)}
                      placeholder={`Product description in ${lang.name}`}
                      rows={3}
                      className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 placeholder-gray-400 focus:outline-none focus:border-primary-500 resize-vertical"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Currencies Selection */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-200 flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Supported Currencies *
            </h4>

            {currenciesLoading ? (
              <div className="flex items-center gap-2 text-gray-400">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
                Loading currencies...
              </div>
            ) : (
              <div className="space-y-3">
                <p className="text-sm text-gray-400">
                  Select which currencies this product can be purchased with. At least one currency must be selected.
                </p>

                {availableCurrencies.length > 0 ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {availableCurrencies.map(currency => (
                      <label
                        key={currency}
                        className={`
                          flex items-center gap-2 p-3 rounded-lg border cursor-pointer transition-all
                          ${selectedCurrencies.includes(currency)
                            ? 'bg-primary-500/20 border-primary-500/50 text-primary-300'
                            : 'bg-dark-700 border-dark-600 text-gray-300 hover:border-dark-500'
                          }
                        `}
                      >
                        <input
                          type="checkbox"
                          checked={selectedCurrencies.includes(currency)}
                          onChange={() => toggleCurrency(currency)}
                          className="sr-only"
                        />
                        <div className={`
                          w-4 h-4 rounded border-2 flex items-center justify-center
                          ${selectedCurrencies.includes(currency)
                            ? 'bg-primary-500 border-primary-500'
                            : 'border-gray-500'
                          }
                        `}>
                          {selectedCurrencies.includes(currency) && (
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <span className="text-sm font-medium">{currency}</span>
                      </label>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-400">
                    <DollarSign className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>No currencies available</p>
                  </div>
                )}

                {errors.currencies && (
                  <p className="text-red-400 text-sm">{errors.currencies}</p>
                )}

                {selectedCurrencies.length > 0 && (
                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                    <p className="text-sm text-blue-400">
                      <strong>Selected currencies:</strong> {selectedCurrencies.join(', ')}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>

        </div>
      )}

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 pt-6 border-t border-dark-600">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={loading}
          className="flex items-center gap-2"
        >
          <X size={16} />
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={loading}
          className="flex items-center gap-2"
        >
          <Save size={16} />
          {loading ? 'Saving...' : (isEdit ? 'Update Product' : 'Create Product')}
        </Button>
      </div>

      {/* Validation Errors Summary */}
      {Object.keys(errors).length > 0 && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-red-400 mt-0.5" />
            <div>
              <h4 className="font-medium text-red-400 mb-2">Please fix the following errors:</h4>
              <ul className="text-sm text-gray-300 space-y-1">
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </form>
  );
});

export default MarketProductForm;
