import React from 'react';
import {
  X, Package, Coins, Globe, Tag, Image, Clock, Users,
  CheckCircle, XCircle, AlertTriangle, Info, Calendar, Hash
} from 'lucide-react';
import Modal from '../ui/Modal';
import {
  getProductTypeDisplay,
  getProductCategoryDisplay,
  formatPrice,
  getAvailabilityDisplay,
  getAvailabilityStatus,
  type MarketProduct
} from '../../utils/api/market-products';

interface MarketProductDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: MarketProduct | null;
}

const MarketProductDetailsModal: React.FC<MarketProductDetailsModalProps> = React.memo(({
  isOpen,
  onClose,
  product
}) => {
  if (!product) return null;

  const availabilityStatus = getAvailabilityStatus(product.availableAmount);

  const getAvailabilityBadgeColor = (status: string) => {
    switch (status) {
      case 'unlimited': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'available': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'low': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'out': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'unlimited': return <Info className="w-4 h-4" />;
      case 'available': return <CheckCircle className="w-4 h-4" />;
      case 'low': return <AlertTriangle className="w-4 h-4" />;
      case 'out': return <XCircle className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Product Details"
      size="lg"
    >
      <div className="space-y-6">
        {/* Header with Image and Basic Info */}
        <div className="flex items-start gap-4">
          <div className="w-24 h-24 rounded-lg overflow-hidden bg-dark-700 flex items-center justify-center flex-shrink-0">
            {product.photoUrl ? (
              <img 
                src={product.photoUrl} 
                alt={product.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
            ) : null}
            <Image className="w-8 h-8 text-gray-500" />
          </div>
          
          <div className="flex-1">
            <h2 className="text-xl font-bold text-white mb-2">{product.name}</h2>
            <div className="flex items-center gap-3 mb-3">
              <span className={`px-3 py-1 rounded-full text-sm font-medium border ${
                product.type === 'slots' 
                  ? 'bg-purple-500/20 text-purple-400 border-purple-500/30'
                  : 'bg-blue-500/20 text-blue-400 border-blue-500/30'
              }`}>
                {getProductTypeDisplay(product.type)}
              </span>
              <span className="text-gray-400">•</span>
              <span className="text-gray-300">{getProductCategoryDisplay(product.category)}</span>
            </div>
            <div className="text-2xl font-bold text-green-400">
              {formatPrice(product.price)}
            </div>
          </div>
        </div>

        {/* Availability Status */}
        <div className="bg-dark-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${getAvailabilityBadgeColor(availabilityStatus)}`}>
              {getStatusIcon(availabilityStatus)}
            </div>
            <div>
              <h3 className="font-medium text-gray-200">Availability</h3>
              <p className="text-sm text-gray-400">
                {getAvailabilityDisplay(product.availableAmount)}
              </p>
            </div>
          </div>
        </div>

        {/* Product Information Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Basic Details */}
          <div className="space-y-4">
            <h3 className="font-medium text-gray-200 flex items-center gap-2">
              <Package className="w-4 h-4" />
              Product Details
            </h3>
            
            <div className="space-y-3">
              <div>
                <label className="text-xs text-gray-400 uppercase tracking-wide">Product ID</label>
                <div className="flex items-center gap-2 mt-1">
                  <Hash className="w-4 h-4 text-gray-500" />
                  <span className="text-gray-300 font-mono">{product.id}</span>
                </div>
              </div>
              
              <div>
                <label className="text-xs text-gray-400 uppercase tracking-wide">Slug</label>
                <div className="text-gray-300 mt-1 font-mono text-sm bg-dark-800 px-2 py-1 rounded">
                  {product.slug}
                </div>
              </div>
              
              <div>
                <label className="text-xs text-gray-400 uppercase tracking-wide">Multi Purchase</label>
                <div className="flex items-center gap-2 mt-1">
                  {product.isMultiPerBuyer ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">Allowed</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="w-4 h-4 text-red-400" />
                      <span className="text-red-400">Not Allowed</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div className="space-y-4">
            <h3 className="font-medium text-gray-200 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Timestamps
            </h3>
            
            <div className="space-y-3">
              <div>
                <label className="text-xs text-gray-400 uppercase tracking-wide">Created</label>
                <div className="flex items-center gap-2 mt-1">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <span className="text-gray-300">
                    {new Date(product.createdAt).toLocaleString()}
                  </span>
                </div>
              </div>
              
              <div>
                <label className="text-xs text-gray-400 uppercase tracking-wide">Last Updated</label>
                <div className="flex items-center gap-2 mt-1">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <span className="text-gray-300">
                    {new Date(product.updatedAt).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Supported Currencies */}
        <div className="space-y-3">
          <h3 className="font-medium text-gray-200 flex items-center gap-2">
            <Globe className="w-4 h-4" />
            Supported Currencies
          </h3>
          <div className="flex flex-wrap gap-2">
            {product.currencies.map(currency => (
              <span
                key={currency}
                className="px-2 py-1 bg-green-500/20 text-green-400 border border-green-500/30 rounded text-sm font-medium"
              >
                {currency}
              </span>
            ))}
          </div>
        </div>

        {/* Internationalization */}
        {Object.keys(product.name_i18n).length > 0 && (
          <div className="space-y-3">
            <h3 className="font-medium text-gray-200 flex items-center gap-2">
              <Globe className="w-4 h-4" />
              Internationalization
            </h3>
            
            <div className="space-y-4">
              {/* Names */}
              <div>
                <label className="text-sm text-gray-400 mb-2 block">Product Names</label>
                <div className="space-y-2">
                  {Object.entries(product.name_i18n).map(([lang, name]) => (
                    <div key={lang} className="flex items-center gap-3">
                      <span className="text-xs bg-dark-700 px-2 py-1 rounded font-mono uppercase">
                        {lang}
                      </span>
                      <span className="text-gray-300">{name}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Descriptions */}
              {Object.keys(product.description_i18n).length > 0 && (
                <div>
                  <label className="text-sm text-gray-400 mb-2 block">Product Descriptions</label>
                  <div className="space-y-2">
                    {Object.entries(product.description_i18n).map(([lang, description]) => (
                      <div key={lang} className="space-y-1">
                        <span className="text-xs bg-dark-700 px-2 py-1 rounded font-mono uppercase">
                          {lang}
                        </span>
                        <p className="text-gray-300 text-sm bg-dark-800 p-2 rounded">
                          {description || <em className="text-gray-500">No description</em>}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Provider/Game Configuration */}
        {product.type === 'slots' && product.providers && (
          <div className="space-y-3">
            <h3 className="font-medium text-gray-200 flex items-center gap-2">
              <Tag className="w-4 h-4" />
              Provider & Game Configuration
            </h3>

            <div className="space-y-3">
              {Array.isArray(product.providers) ? (
                // New structure: array of MarketSelectedProvider objects
                product.providers.map((selectedProvider) => (
                  <div key={selectedProvider.provider.id} className="bg-dark-800 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {selectedProvider.provider.image && (
                          <img
                            src={selectedProvider.provider.image}
                            alt={selectedProvider.provider.name}
                            className="w-6 h-6 rounded"
                          />
                        )}
                        <span className="font-medium text-gray-200">{selectedProvider.provider.name}</span>
                      </div>
                      <span className="text-xs bg-primary-500/20 text-primary-400 px-2 py-1 rounded">
                        {selectedProvider.games.length} games
                      </span>
                    </div>
                    {selectedProvider.games.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        {selectedProvider.games.map(game => (
                          <span
                            key={game.id}
                            className="text-xs bg-dark-700 text-gray-400 px-2 py-1 rounded"
                            title={game.name}
                          >
                            {game.name}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <p className="text-xs text-gray-400">All games from this provider</p>
                    )}
                  </div>
                ))
              ) : (
                // Legacy structure: Record<string, number[]>
                Object.entries(product.providers).map(([providerId, gameIds]) => (
                  <div key={providerId} className="bg-dark-800 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-200">Provider {providerId}</span>
                      <span className="text-xs bg-primary-500/20 text-primary-400 px-2 py-1 rounded">
                        {gameIds.length} games
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {gameIds.map(gameId => (
                        <span
                          key={gameId}
                          className="text-xs bg-dark-700 text-gray-400 px-2 py-1 rounded"
                        >
                          Game {gameId}
                        </span>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}

        {/* Close Button */}
        <div className="flex justify-end pt-4 border-t border-dark-600">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-dark-700 hover:bg-dark-600 text-gray-300 rounded-lg transition-colors flex items-center gap-2"
          >
            <X size={16} />
            Close
          </button>
        </div>
      </div>
    </Modal>
  );
});

export default MarketProductDetailsModal;
