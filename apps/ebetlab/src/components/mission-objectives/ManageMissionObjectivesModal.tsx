import React, { useState, useEffect, useCallback } from 'react';
import { Target, Plus, Edit, Trash2, RefreshCw, AlertCircle } from 'lucide-react';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import {
  fetchMissionObjectivesByMissionId,
  deleteMissionObjective,
  OBJECTIVE_TYPE_OPTIONS,
  OBJECTIVE_OPERATOR_OPTIONS,
  getSubtypeOptions,
  getObjectiveDisplay,
  getObjectiveStatusDisplay,
  type MissionObjective
} from '../../utils/api/mission-objectives';
import { type Mission } from '../../utils/api/missions';
import CreateMissionObjectiveModal from './CreateMissionObjectiveModal';
import EditMissionObjectiveModal from './EditMissionObjectiveModal';

interface ManageMissionObjectivesModalProps {
  isOpen: boolean;
  onClose: () => void;
  mission: Mission | null;
}

interface ObjectivesFilter {
  objectiveType?: string;
  operator?: string;
  subtype?: string;
  search?: string;
}

const ManageMissionObjectivesModal: React.FC<ManageMissionObjectivesModalProps> = ({
  isOpen,
  onClose,
  mission
}) => {
  const [objectives, setObjectives] = useState<MissionObjective[]>([]);
  const [filteredObjectives, setFilteredObjectives] = useState<MissionObjective[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [filter, setFilter] = useState<ObjectivesFilter>({});

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedObjective, setSelectedObjective] = useState<MissionObjective | null>(null);

  // Load objectives for the mission
  const loadObjectives = useCallback(async () => {
    if (!mission) return;

    setIsLoading(true);
    setError('');

    try {
      const response = await fetchMissionObjectivesByMissionId(mission.id);
      console.log(response)
      if (response.success) {
        // Ensure we always set an array, even if the API returns something unexpected
        const objectivesData = Array.isArray(response.data?.objectives) ? response.data.objectives : [];
        setObjectives(objectivesData);
      } else {
        setError(response.error || 'Failed to load mission objectives');
        setObjectives([]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load mission objectives');
      setObjectives([]);
    } finally {
      setIsLoading(false);
    }
  }, [mission]);

  // Load objectives when modal opens or mission changes
  useEffect(() => {
    if (isOpen && mission) {
      loadObjectives();
    }
  }, [isOpen, mission, loadObjectives]);

  // Apply filters to objectives
  useEffect(() => {
    // Ensure objectives is always an array
    const objectivesArray = Array.isArray(objectives) ? objectives : [];
    let filtered = [...objectivesArray];

    if (filter.objectiveType) {
      filtered = filtered.filter(obj => obj.objectiveType === filter.objectiveType);
    }

    if (filter.operator) {
      filtered = filtered.filter(obj => obj.operator === filter.operator);
    }

    if (filter.subtype) {
      filtered = filtered.filter(obj => obj.subtype === filter.subtype);
    }

    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      filtered = filtered.filter(obj =>
        obj.description.toLowerCase().includes(searchLower) ||
        obj.targetValue.toLowerCase().includes(searchLower) ||
        (obj.metadata && JSON.stringify(obj.metadata).toLowerCase().includes(searchLower))
      );
    }

    setFilteredObjectives(filtered);
  }, [objectives, filter]);

  const handleFilterChange = (field: keyof ObjectivesFilter, value: string) => {
    setFilter(prev => ({
      ...prev,
      [field]: value || undefined
    }));
  };

  const clearFilters = () => {
    setFilter({});
  };

  const handleCreateObjective = () => {
    setShowCreateModal(true);
  };

  const handleEditObjective = (objective: MissionObjective) => {
    setSelectedObjective(objective);
    setShowEditModal(true);
  };

  const handleDeleteObjective = async (objective: MissionObjective) => {
    if (!confirm(`Are you sure you want to delete the objective "${objective.description}"?`)) {
      return;
    }

    try {
      const response = await deleteMissionObjective(objective.id);
      
      if (response.success) {
        // Reload objectives after deletion
        await loadObjectives();
      } else {
        setError(response.error || 'Failed to delete mission objective');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete mission objective');
    }
  };

  const handleObjectiveCreated = (newObjective: MissionObjective) => {
    // Add the new objective to the list
    setObjectives(prev => {
      const prevArray = Array.isArray(prev) ? prev : [];
      return [...prevArray, newObjective];
    });
    setShowCreateModal(false);
  };

  const handleObjectiveUpdated = (updatedObjective: MissionObjective) => {
    // Update the objective in the list
    setObjectives(prev => {
      const prevArray = Array.isArray(prev) ? prev : [];
      return prevArray.map(obj => obj.id === updatedObjective.id ? updatedObjective : obj);
    });
    setShowEditModal(false);
    setSelectedObjective(null);
  };

  const handleClose = () => {
    onClose();
    setFilter({});
    setError('');
    setObjectives([]);
    setFilteredObjectives([]);
  };

  if (!mission) return null;

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        title={`Manage Objectives: ${mission.name}`}
        size="lg"
      >
        <div className="space-y-6">
          {/* Header with Create Button */}
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-300">Mission Objectives</h4>
              <p className="text-xs text-gray-400">
                Create and manage objectives for this specific mission.
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={loadObjectives}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={isLoading}
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                onClick={handleCreateObjective}
                variant="primary"
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Objective
              </Button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-md p-4 flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
              <p className="text-sm text-red-400">{error}</p>
            </div>
          )}

          {/* Filters */}
          <div className="bg-dark-700 rounded-md p-3 border border-dark-600">
            <h5 className="text-xs font-medium text-gray-400 mb-2">Filter Objectives</h5>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
              <div>
                <select
                  value={filter.objectiveType || ''}
                  onChange={(e) => handleFilterChange('objectiveType', e.target.value)}
                  className="w-full px-2 py-1 text-xs bg-dark-600 border border-dark-500 rounded text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Types</option>
                  {OBJECTIVE_TYPE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <select
                  value={filter.operator || ''}
                  onChange={(e) => handleFilterChange('operator', e.target.value)}
                  className="w-full px-2 py-1 text-xs bg-dark-600 border border-dark-500 rounded text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Operators</option>
                  {OBJECTIVE_OPERATOR_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <select
                  value={filter.subtype || ''}
                  onChange={(e) => handleFilterChange('subtype', e.target.value)}
                  className="w-full px-2 py-1 text-xs bg-dark-600 border border-dark-500 rounded text-gray-100 focus:outline-none focus:border-primary-500"
                >
                  <option value="">All Subtypes</option>
                  {filter.objectiveType && getSubtypeOptions(filter.objectiveType).map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <input
                  type="text"
                  value={filter.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full px-2 py-1 text-xs bg-dark-600 border border-dark-500 rounded text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search objectives..."
                />
              </div>
            </div>
            <div className="flex justify-end mt-2">
              <button
                onClick={clearFilters}
                className="px-2 py-1 text-xs bg-dark-500 hover:bg-dark-400 text-gray-300 rounded"
              >
                Clear Filters
              </button>
            </div>
          </div>

          {/* Objectives List */}
          <div className="bg-dark-800 rounded-md p-4 border border-dark-600">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-2 border-primary-500/20 border-t-primary-500 rounded-full animate-spin mx-auto mb-3" />
                <p className="text-gray-400 text-sm">Loading objectives...</p>
              </div>
            ) : filteredObjectives.length > 0 ? (
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {filteredObjectives.map((objective) => {
                  const statusDisplay = getObjectiveStatusDisplay(objective);
                  return (
                    <div
                      key={objective.id}
                      className="flex items-start gap-3 p-3 bg-dark-700 rounded-md border border-dark-600"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-xs px-2 py-1 rounded-full bg-purple-500/20 text-purple-400 border border-purple-500/30">
                            {OBJECTIVE_TYPE_OPTIONS.find(opt => opt.value === objective.objectiveType)?.label}
                          </span>
                          <span className={`text-xs ${statusDisplay.color}`}>
                            {statusDisplay.status}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-xs text-gray-400">
                            Target: {OBJECTIVE_OPERATOR_OPTIONS.find(op => op.value === objective.operator)?.label} {objective.targetValue}
                          </span>
                        </div>
                        <div className="text-xs text-gray-300 mb-1">
                          {objective.description}
                        </div>
                        {objective.metadata && (
                          <div className="text-xs text-gray-500 font-mono">
                            Metadata: {JSON.stringify(objective.metadata)}
                          </div>
                        )}
                      </div>
                      <div className="flex gap-1">
                        <button
                          onClick={() => handleEditObjective(objective)}
                          className="p-1.5 rounded-md text-gray-300 hover:bg-dark-600 hover:text-primary-500 transition-colors"
                          title="Edit Objective"
                        >
                          <Edit size={14} />
                        </button>
                        <button
                          onClick={() => handleDeleteObjective(objective)}
                          className="p-1.5 rounded-md text-gray-300 hover:bg-dark-600 hover:text-red-400 transition-colors"
                          title="Delete Objective"
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <Target className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400 text-sm">
                  {objectives.length === 0 ? 'No objectives for this mission' : 'No objectives match the current filters'}
                </p>
                <p className="text-gray-500 text-xs mt-1">
                  {objectives.length === 0 ? 'Click "Add Objective" to create objectives for this mission' : 'Try adjusting your filters'}
                </p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
            <Button
              onClick={handleClose}
              variant="outline"
            >
              Close
            </Button>
          </div>
        </div>
      </Modal>

      {/* Create Objective Modal */}
      {mission && (
        <CreateMissionObjectiveModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          mission={mission}
          onObjectiveCreated={handleObjectiveCreated}
        />
      )}

      {/* Edit Objective Modal */}
      {mission && selectedObjective && (
        <EditMissionObjectiveModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedObjective(null);
          }}
          mission={mission}
          objective={selectedObjective}
          onObjectiveUpdated={handleObjectiveUpdated}
        />
      )}
    </>
  );
};

export default ManageMissionObjectivesModal;
