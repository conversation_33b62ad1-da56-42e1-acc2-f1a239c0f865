import React, { useState, useEffect, useMemo } from 'react';
import { Check, ChevronRight, ChevronLeft, AlertCircle, ChevronDown, ChevronUp, Search } from 'lucide-react';
import { useProviders } from '../../hooks/useAppData';
import { fetchGamesByProviders, type Game } from '../../utils/api/bonuses';

export interface SelectedGame {
  id: number;
  name: string;
  provider_name: string;
  provider_id: number;
}

interface ProviderGameSelectorProps {
  selectedProviders: number[];
  selectedGames: SelectedGame[];
  onProvidersChange: (providers: number[]) => void;
  onGamesChange: (games: SelectedGame[]) => void;
  disabled?: boolean;
}

const ProviderGameSelector: React.FC<ProviderGameSelectorProps> = ({
  selectedProviders,
  selectedGames,
  onProvidersChange,
  onGamesChange,
  disabled = false
}) => {
  const providers = useProviders();
  const [availableGames, setAvailableGames] = useState<Game[]>([]);
  const [loadingGames, setLoadingGames] = useState(false);
  const [error, setError] = useState<string>('');

  // Collapsible states
  const [isProviderSectionExpanded, setIsProviderSectionExpanded] = useState(false);
  const [isGameSectionExpanded, setIsGameSectionExpanded] = useState(false);

  // Search states
  const [providerSearchTerm, setProviderSearchTerm] = useState('');
  const [gameSearchTerm, setGameSearchTerm] = useState('');

  // Load games when providers are selected
  useEffect(() => {
    if (selectedProviders.length > 0) {
      loadGames();
    } else {
      setAvailableGames([]);
      onGamesChange([]);
    }
  }, [selectedProviders]);

  const loadGames = async () => {
    setLoadingGames(true);
    setError('');

    try {
      const response = await fetchGamesByProviders(selectedProviders, 'freespin');
      if (response.success && response.data) {
        setAvailableGames(response.data.data);
      } else {
        setError(response.error || 'Failed to load games');
        setAvailableGames([]);
      }
    } catch (err) {
      setError('Failed to load games');
      setAvailableGames([]);
    } finally {
      setLoadingGames(false);
    }
  };

  const toggleProvider = (providerId: number) => {
    if (disabled) return;
    
    const newProviders = selectedProviders.includes(providerId)
      ? selectedProviders.filter(id => id !== providerId)
      : [...selectedProviders, providerId];
    
    onProvidersChange(newProviders);
  };

  const addGameToSelected = (game: Game) => {
    if (disabled) return;

    const isAlreadySelected = selectedGames.some(g => g.id === game.id);
    if (!isAlreadySelected) {
      const newGames = [...selectedGames, {
        id: game.id,
        name: game.name,
        provider_name: game.provider_name || game.provider || '',
        provider_id: game.merchant_id // Use merchant_id as the provider ID
      }];
      onGamesChange(newGames);
    }
  };

  const removeGameFromSelected = (gameId: number) => {
    if (disabled) return;
    
    const newGames = selectedGames.filter(g => g.id !== gameId);
    onGamesChange(newGames);
  };

  const getAvailableGames = () => {
    return availableGames.filter(game =>
      !selectedGames.some(selected => selected.id === game.id)
    );
  };

  // Filtered providers based on search term
  const filteredProviders = useMemo(() => {
    if (!providerSearchTerm.trim()) return providers;
    return providers.filter(provider =>
      provider.name && provider.name.toLowerCase().includes(providerSearchTerm.toLowerCase())
    );
  }, [providers, providerSearchTerm]);

  // Filtered games based on search term
  const filteredAvailableGames = useMemo(() => {
    const availableGames = getAvailableGames();
    if (!gameSearchTerm.trim()) return availableGames;
    return availableGames.filter(game =>
      (game.name && game.name.toLowerCase().includes(gameSearchTerm.toLowerCase())) ||
      (game.provider_name && game.provider_name.toLowerCase().includes(gameSearchTerm.toLowerCase()))
    );
  }, [availableGames, selectedGames, gameSearchTerm]);

  return (
    <div className="space-y-4">
      {/* Provider Selection */}
      <div className="border border-dark-600 rounded-lg bg-dark-800/30">
        <div
          className="flex items-center justify-between p-4 cursor-pointer hover:bg-dark-700/30 transition-colors"
          onClick={() => setIsProviderSectionExpanded(!isProviderSectionExpanded)}
        >
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {isProviderSectionExpanded ? (
                <ChevronUp className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              )}
              <h4 className="text-md font-medium text-gray-200">Game Providers</h4>
            </div>
            {selectedProviders.length > 0 && (
              <span className="px-2 py-1 bg-primary-500/20 text-primary-400 text-xs rounded-full">
                {selectedProviders.length} selected
              </span>
            )}
          </div>
          <p className="text-sm text-gray-400">
            {isProviderSectionExpanded ? 'Click to collapse' : 'Click to expand'}
          </p>
        </div>

        {isProviderSectionExpanded && (
          <div className="px-4 pb-4 space-y-4">
            <p className="text-sm text-gray-400">Choose one or more game providers.</p>

            {/* Provider Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search providers..."
                value={providerSearchTerm}
                onChange={(e) => setProviderSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-gray-200 placeholder-gray-400 focus:outline-none focus:border-primary-500 text-sm"
                disabled={disabled}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
              {filteredProviders.map((provider) => (
                <div
                  key={provider.id}
                  onClick={() => toggleProvider(provider.id)}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    disabled
                      ? 'opacity-50 cursor-not-allowed'
                      : selectedProviders.includes(provider.id)
                        ? 'border-primary-500 bg-primary-500/10'
                        : 'border-dark-600 hover:border-dark-500'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-medium text-gray-200 text-sm">{provider.name}</h5>
                      <p className="text-xs text-gray-400">{provider.total} games</p>
                    </div>
                    <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                      selectedProviders.includes(provider.id)
                        ? 'border-primary-500 bg-primary-500'
                        : 'border-gray-400'
                    }`}>
                      {selectedProviders.includes(provider.id) && (
                        <Check className="w-2.5 h-2.5 text-white" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredProviders.length === 0 && providerSearchTerm && (
              <div className="text-center py-4 text-gray-400 text-sm">
                No providers found matching "{providerSearchTerm}"
              </div>
            )}
          </div>
        )}
      </div>

      {/* Game Selection */}
      {selectedProviders.length > 0 && (
        <div className="border border-dark-600 rounded-lg bg-dark-800/30">
          <div
            className="flex items-center justify-between p-4 cursor-pointer hover:bg-dark-700/30 transition-colors"
            onClick={() => setIsGameSectionExpanded(!isGameSectionExpanded)}
          >
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                {isGameSectionExpanded ? (
                  <ChevronUp className="w-4 h-4 text-gray-400" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                )}
                <h4 className="text-md font-medium text-gray-200">Game Selection</h4>
              </div>
              {selectedGames.length > 0 && (
                <span className="px-2 py-1 bg-primary-500/20 text-primary-400 text-xs rounded-full">
                  {selectedGames.length} selected
                </span>
              )}
            </div>
            <p className="text-sm text-gray-400">
              {isGameSectionExpanded ? 'Click to collapse' : 'Click to expand'}
            </p>
          </div>

          {isGameSectionExpanded && (
            <div className="px-4 pb-4 space-y-4">
              <p className="text-sm text-gray-400">Choose specific games from the selected providers.</p>

              {/* Error Display */}
              {error && (
                <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
                  <span className="text-red-200 text-sm">{error}</span>
                </div>
              )}

              {loadingGames ? (
                <div className="text-center py-6">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto mb-3"></div>
                  <div className="text-gray-400 text-sm">Loading games...</div>
                </div>
              ) : availableGames.length > 0 ? (
                <div className="space-y-4">
                  {/* Game Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search games..."
                      value={gameSearchTerm}
                      onChange={(e) => setGameSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-gray-200 placeholder-gray-400 focus:outline-none focus:border-primary-500 text-sm"
                      disabled={disabled}
                    />
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {/* Available Games */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h5 className="text-sm font-medium text-gray-200">Available Games</h5>
                        <span className="text-xs text-gray-400">
                          {filteredAvailableGames.length} available
                        </span>
                      </div>

                      <div className="border border-dark-600 rounded-lg p-3 bg-dark-800/50 max-h-64 overflow-y-auto">
                        {filteredAvailableGames.length > 0 ? (
                          <div className="space-y-2">
                            {filteredAvailableGames.map((game) => (
                              <div
                                key={game.id}
                                className={`p-2 rounded-lg border border-dark-600 transition-all cursor-pointer ${
                                  disabled
                                    ? 'opacity-50 cursor-not-allowed'
                                    : 'hover:border-dark-500'
                                }`}
                                onClick={() => addGameToSelected(game)}
                              >
                                <div className="flex items-center justify-between">
                                  <div>
                                    <h6 className="font-medium text-gray-200 text-xs">{game.name}</h6>
                                    <p className="text-xs text-gray-400">{game.provider_name}</p>
                                  </div>
                                  <div className="text-primary-400 hover:text-primary-300">
                                    <ChevronRight className="w-3 h-3" />
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : gameSearchTerm ? (
                          <div className="text-center py-6 text-gray-400 text-xs">
                            No games found matching "{gameSearchTerm}"
                          </div>
                        ) : (
                          <div className="text-center py-6 text-gray-400 text-xs">
                            All games have been selected
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Selected Games */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h5 className="text-sm font-medium text-gray-200">Selected Games</h5>
                        <span className="text-xs text-gray-400">
                          {selectedGames.length} selected
                        </span>
                      </div>

                      <div className="border border-dark-600 rounded-lg p-3 bg-dark-800/50 max-h-64 overflow-y-auto">
                        {selectedGames.length > 0 ? (
                          <div className="space-y-2">
                            {selectedGames.map((game) => (
                              <div
                                key={game.id}
                                className="p-2 rounded-lg border border-primary-500/30 bg-primary-500/5"
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <h6 className="font-medium text-gray-200 text-xs">{game.name}</h6>
                                    <p className="text-xs text-gray-400">{game.provider_name}</p>
                                  </div>
                                  <button
                                    onClick={() => removeGameFromSelected(game.id)}
                                    className={`text-red-400 hover:text-red-300 p-1 ${
                                      disabled ? 'opacity-50 cursor-not-allowed' : ''
                                    }`}
                                    title="Remove game"
                                    disabled={disabled}
                                  >
                                    <ChevronLeft className="w-3 h-3" />
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-6 text-gray-400 text-xs">
                            No games selected yet.<br />
                            Click on games from the left to add them.
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ) : selectedProviders.length > 0 && !loadingGames && (
                <div className="text-center py-6">
                  <div className="text-gray-400 text-sm">No games available for selected providers</div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProviderGameSelector;
