import React, { useState, useEffect } from 'react';
import { Edit, AlertCircle } from 'lucide-react';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import Input from '../ui/Input';
import {
  updateMissionObjective,
  OBJECTIVE_TYPE_OPTIONS,
  OBJECTIVE_OPERATOR_OPTIONS,
  getSubtypeOptions,
  type UpdateMissionObjectiveRequest,
  type ObjectiveType,
  type ObjectiveOperator,
  type MissionObjective
} from '../../utils/api/mission-objectives';
import { type Mission } from '../../utils/api/missions';
import ProviderGameSelector from './ProviderGameSelector';

interface EditMissionObjectiveModalProps {
  isOpen: boolean;
  onClose: () => void;
  mission: Mission;
  objective: MissionObjective;
  onObjectiveUpdated: (objective: MissionObjective) => void;
}

interface SelectedGame {
  id: number;
  name: string;
  provider_name: string;
  provider_id: number;
}

interface ObjectiveFormData {
  objectiveType: ObjectiveType;
  subtype: string;
  operator: ObjectiveOperator;
  targetValue: string;
  description: string;
  timeframeStartDuration: string;
  timeframeStartUnit: 'minutes' | 'hours' | 'days';
  timeframeEndDuration: string;
  timeframeEndUnit: 'minutes' | 'hours' | 'days';
  game: string;
  metadata: string;
  selectedProviders: number[];
  selectedGames: SelectedGame[];
}

const EditMissionObjectiveModal: React.FC<EditMissionObjectiveModalProps> = ({
  isOpen,
  onClose,
  mission,
  objective,
  onObjectiveUpdated
}) => {
  const [formData, setFormData] = useState<ObjectiveFormData>({
    objectiveType: 'slot',
    subtype: 'total_wins',
    operator: 'ge',
    targetValue: '',
    description: '',
    timeframeStartDuration: '',
    timeframeStartUnit: 'hours',
    timeframeEndDuration: '',
    timeframeEndUnit: 'hours',
    game: '',
    metadata: '',
    selectedProviders: [],
    selectedGames: []
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Populate form when modal opens or objective changes
  useEffect(() => {
    if (isOpen && objective) {
      populateForm();
      setError('');
    }
  }, [isOpen, objective]);

  // Update subtype when objective type changes
  useEffect(() => {
    const subtypeOptions = getSubtypeOptions(formData.objectiveType);
    if (subtypeOptions.length > 0 && !subtypeOptions.some(opt => opt.value === formData.subtype)) {
      setFormData(prev => ({
        ...prev,
        subtype: subtypeOptions[0].value
      }));
    }
  }, [formData.objectiveType]);

  const populateForm = () => {
    // Handle metadata extraction
    let gameValue = '';
    let metadataValue = '';
    let selectedProviders: number[] = [];
    let selectedGames: SelectedGame[] = [];

    if (objective.metadata) {
      if (objective.objectiveType === 'slot') {
        // Handle both old and new metadata structures
        if (objective.metadata.providers) {
          if (Array.isArray(objective.metadata.providers)) {
            // Old structure: providers is an array
            selectedProviders = objective.metadata.providers;
          } else if (typeof objective.metadata.providers === 'object') {
            // New structure: providers is an object with provider IDs as keys
            selectedProviders = Object.keys(objective.metadata.providers).map(id => parseInt(id));

            // Extract games from the new structure
            Object.entries(objective.metadata.providers).forEach(([providerId, gameIds]) => {
              if (Array.isArray(gameIds)) {
                gameIds.forEach((gameId: number) => {
                  // We need to reconstruct the game objects
                  // For now, we'll create minimal game objects
                  selectedGames.push({
                    id: gameId,
                    name: `Game ${gameId}`, // Placeholder name
                    provider_name: `Provider ${providerId}`, // Placeholder provider name
                    provider_id: parseInt(providerId)
                  });
                });
              }
            });
          }
        }

        // Handle old games structure for backward compatibility
        if (objective.metadata.games && Array.isArray(objective.metadata.games)) {
          selectedGames = objective.metadata.games.map((game: any) => ({
            id: game.id,
            name: game.name,
            provider_name: game.provider_name,
            provider_id: game.provider_id || 0 // Default to 0 if not available
          }));
        }
      } else if (objective.objectiveType === 'liveCasino' && objective.metadata.game) {
        // Legacy support for liveCasino objectives
        gameValue = objective.metadata.game;
      } else {
        metadataValue = JSON.stringify(objective.metadata, null, 2);
      }
    }

    // Convert existing timeframe to offset format
    let timeframeStartDuration = '';
    let timeframeStartUnit: 'minutes' | 'hours' | 'days' = 'hours';
    let timeframeEndDuration = '';
    let timeframeEndUnit: 'minutes' | 'hours' | 'days' = 'hours';

    const now = Date.now();

    if (objective.timeframeStart) {
      const startOffsetMs = objective.timeframeStart - now;
      const startOffsetSeconds = Math.max(0, Math.floor(startOffsetMs / 1000));

      // Convert to the most appropriate unit
      if (startOffsetSeconds % (24 * 60 * 60) === 0) {
        timeframeStartDuration = String(startOffsetSeconds / (24 * 60 * 60));
        timeframeStartUnit = 'days';
      } else if (startOffsetSeconds % (60 * 60) === 0) {
        timeframeStartDuration = String(startOffsetSeconds / (60 * 60));
        timeframeStartUnit = 'hours';
      } else {
        timeframeStartDuration = String(Math.floor(startOffsetSeconds / 60));
        timeframeStartUnit = 'minutes';
      }
    }

    if (objective.timeframeEnd) {
      const endOffsetMs = objective.timeframeEnd - now;
      const endOffsetSeconds = Math.max(0, Math.floor(endOffsetMs / 1000));

      // Convert to the most appropriate unit
      if (endOffsetSeconds % (24 * 60 * 60) === 0) {
        timeframeEndDuration = String(endOffsetSeconds / (24 * 60 * 60));
        timeframeEndUnit = 'days';
      } else if (endOffsetSeconds % (60 * 60) === 0) {
        timeframeEndDuration = String(endOffsetSeconds / (60 * 60));
        timeframeEndUnit = 'hours';
      } else {
        timeframeEndDuration = String(Math.floor(endOffsetSeconds / 60));
        timeframeEndUnit = 'minutes';
      }
    }

    setFormData({
      objectiveType: objective.objectiveType,
      subtype: objective.subtype || '',
      operator: objective.operator,
      targetValue: objective.targetValue,
      description: objective.description,
      timeframeStartDuration,
      timeframeStartUnit,
      timeframeEndDuration,
      timeframeEndUnit,
      game: gameValue,
      metadata: metadataValue,
      selectedProviders,
      selectedGames
    });
  };

  const handleInputChange = (field: keyof ObjectiveFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleProvidersChange = (providers: number[]) => {
    setFormData(prev => ({
      ...prev,
      selectedProviders: providers
    }));
    // Clear error when user makes changes
    if (error) setError('');
  };

  const handleGamesChange = (games: SelectedGame[]) => {
    setFormData(prev => ({
      ...prev,
      selectedGames: games
    }));
    // Clear error when user makes changes
    if (error) setError('');
  };

  const validateForm = (): string | null => {
    if (!formData.description.trim()) {
      return 'Objective description is required';
    }
    if (!formData.targetValue.trim()) {
      return 'Target value is required';
    }
    if (isNaN(Number(formData.targetValue)) || Number(formData.targetValue) < 0) {
      return 'Target value must be a valid positive number';
    }
    if (!formData.subtype.trim()) {
      return 'Subtype is required';
    }

    // Validate provider and game selection for slot objectives
    if (formData.objectiveType === 'slot') {
      if (formData.selectedProviders.length === 0) {
        return 'At least one provider must be selected for Slot Games objectives';
      }
      if (formData.selectedGames.length === 0) {
        return 'At least one game must be selected for Slot Games objectives';
      }
    }

    // Validate game field for liveCasino objectives
    if (formData.objectiveType === 'liveCasino' && !formData.game.trim()) {
      return 'Game name is required for Live Casino objectives';
    }

    // Validate metadata JSON if provided
    if (formData.metadata.trim()) {
      try {
        JSON.parse(formData.metadata);
      } catch {
        return 'Metadata must be valid JSON';
      }
    }

    // Validate timeframe start
    if (formData.timeframeStartDuration) {
      const startDuration = Number(formData.timeframeStartDuration);
      if (isNaN(startDuration) || startDuration < 0) {
        return 'Timeframe start duration must be a non-negative number';
      }
    }

    // Validate timeframe end
    if (formData.timeframeEndDuration) {
      const endDuration = Number(formData.timeframeEndDuration);
      if (isNaN(endDuration) || endDuration <= 0) {
        return 'Timeframe end duration must be a positive number';
      }
    }

    // Validate that end is after start if both are specified
    if (formData.timeframeStartDuration && formData.timeframeEndDuration) {
      const startSeconds = convertTimeframeToSeconds(formData.timeframeStartDuration, formData.timeframeStartUnit);
      const endSeconds = convertTimeframeToSeconds(formData.timeframeEndDuration, formData.timeframeEndUnit);

      if (startSeconds && endSeconds && endSeconds <= startSeconds) {
        return 'Timeframe end must be after timeframe start';
      }
    }

    return null;
  };

  const convertTimeframeToSeconds = (duration: string, unit: 'minutes' | 'hours' | 'days'): number | null => {
    if (!duration) return null;

    const durationNum = Number(duration);
    if (isNaN(durationNum) || durationNum <= 0) return null;

    const multipliers = {
      minutes: 60,
      hours: 60 * 60,
      days: 60 * 60 * 24
    };

    return durationNum * multipliers[unit];
  };

  const constructMetadata = (): Record<string, any> | null => {
    // For slot objectives, use provider and game selection
    if (formData.objectiveType === 'slot') {
      if (formData.selectedProviders.length > 0) {
        // Create providers object with provider ID as key and array of game IDs as value
        const providers: Record<string, number[]> = {};

        // Initialize all selected providers with empty arrays
        formData.selectedProviders.forEach(providerId => {
          providers[providerId.toString()] = [];
        });

        // Add games to their respective providers (if any)
        formData.selectedGames.forEach(game => {
          const providerIdStr = game.provider_id.toString();

          // Only add the game if its provider is in the selected providers
          if (formData.selectedProviders.includes(game.provider_id)) {
            if (!providers[providerIdStr].includes(game.id)) {
              providers[providerIdStr].push(game.id);
            }
          }
        });

        return { providers };
      }

      return null;
    }

    // For liveCasino objectives, use game field (legacy support)
    if (formData.objectiveType === 'liveCasino') {
      if (formData.game.trim()) {
        return { game: formData.game.trim() };
      }
      return null;
    }

    // For other objective types, use metadata field if provided
    if (formData.metadata.trim()) {
      try {
        return JSON.parse(formData.metadata);
      } catch {
        return null;
      }
    }

    return null;
  };

  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const now = Date.now();

      // Calculate timeframe start (offset from now)
      const startOffsetSeconds = convertTimeframeToSeconds(formData.timeframeStartDuration, formData.timeframeStartUnit);
      const timeframeStart = startOffsetSeconds ? now + (startOffsetSeconds * 1000) : null;

      // Calculate timeframe end (offset from now)
      const endOffsetSeconds = convertTimeframeToSeconds(formData.timeframeEndDuration, formData.timeframeEndUnit);
      const timeframeEnd = endOffsetSeconds ? now + (endOffsetSeconds * 1000) : null;

      const objectiveData: UpdateMissionObjectiveRequest = {
        missionId: mission.id,
        objectiveType: formData.objectiveType,
        subtype: formData.subtype,
        operator: formData.operator,
        targetValue: formData.targetValue,
        description: formData.description,
        timeframeStart,
        timeframeEnd,
        metadata: constructMetadata()
      };

      const response = await updateMissionObjective(objective.id, objectiveData);

      if (response.success && response.data) {
        onObjectiveUpdated(response.data);
        onClose();
      } else {
        setError(response.error || 'Failed to update mission objective');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update mission objective');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
      setError('');
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Edit Mission Objective"
      size="lg"
    >
      <div className="space-y-6">
        {/* Mission Info */}
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-md p-4">
          <p className="text-sm text-blue-400">
            <strong>Editing objective for:</strong> {mission.name}
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-md p-4 flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
            <p className="text-sm text-red-400">{error}</p>
          </div>
        )}

        {/* Form Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Objective Type</label>
            <select
              value={formData.objectiveType}
              onChange={(e) => handleInputChange('objectiveType', e.target.value)}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              disabled={isLoading}
            >
              {OBJECTIVE_TYPE_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Operator</label>
            <select
              value={formData.operator}
              onChange={(e) => handleInputChange('operator', e.target.value)}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              disabled={isLoading}
            >
              {OBJECTIVE_OPERATOR_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">Subtype</label>
          <select
            value={formData.subtype}
            onChange={(e) => handleInputChange('subtype', e.target.value)}
            className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            disabled={isLoading}
          >
            {getSubtypeOptions(formData.objectiveType).map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <Input
          label="Target Value"
          type="text"
          value={formData.targetValue}
          onChange={(e) => handleInputChange('targetValue', e.target.value)}
          placeholder="Enter target value..."
          required
          disabled={isLoading}
        />

        {/* Provider and Game Selection for slot objectives */}
        {formData.objectiveType === 'slot' && (
          <div className="space-y-4">
            <ProviderGameSelector
              selectedProviders={formData.selectedProviders}
              selectedGames={formData.selectedGames}
              onProvidersChange={handleProvidersChange}
              onGamesChange={handleGamesChange}
              disabled={isLoading}
            />
          </div>
        )}

        {/* Game field for liveCasino objectives (legacy support) */}
        {formData.objectiveType === 'liveCasino' && (
          <Input
            label="Game Name"
            type="text"
            value={formData.game}
            onChange={(e) => handleInputChange('game', e.target.value)}
            placeholder="Enter game name..."
            required
            disabled={isLoading}
          />
        )}

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-300">Description</label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter objective description..."
            rows={3}
            className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
            required
            disabled={isLoading}
          />
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Timeframe Start Offset (Optional)</label>
            <div className="grid grid-cols-2 gap-4">
              <Input
                type="number"
                value={formData.timeframeStartDuration}
                onChange={(e) => handleInputChange('timeframeStartDuration', e.target.value)}
                placeholder="Enter start offset..."
                disabled={isLoading}
                min="0"
                step="1"
              />
              <select
                value={formData.timeframeStartUnit}
                onChange={(e) => handleInputChange('timeframeStartUnit', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                disabled={isLoading}
              >
                <option value="minutes">Minutes</option>
                <option value="hours">Hours</option>
                <option value="days">Days</option>
              </select>
            </div>
            <p className="text-xs text-gray-500">
              Time offset from now when the objective becomes active (0 = immediately).
            </p>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Timeframe End Offset (Optional)</label>
            <div className="grid grid-cols-2 gap-4">
              <Input
                type="number"
                value={formData.timeframeEndDuration}
                onChange={(e) => handleInputChange('timeframeEndDuration', e.target.value)}
                placeholder="Enter end offset..."
                disabled={isLoading}
                min="1"
                step="1"
              />
              <select
                value={formData.timeframeEndUnit}
                onChange={(e) => handleInputChange('timeframeEndUnit', e.target.value)}
                className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                disabled={isLoading}
              >
                <option value="minutes">Minutes</option>
                <option value="hours">Hours</option>
                <option value="days">Days</option>
              </select>
            </div>
            <p className="text-xs text-gray-500">
              Time offset from now when the objective expires.
            </p>
          </div>
        </div>

        {/* Metadata field - only show for non-slot/liveCasino objectives */}
        {formData.objectiveType !== 'slot' && formData.objectiveType !== 'liveCasino' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Metadata (Optional JSON)</label>
            <textarea
              value={formData.metadata}
              onChange={(e) => handleInputChange('metadata', e.target.value)}
              placeholder='{"bonusRequired": true, "minBonusAmount": 50}'
              rows={4}
              className="w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none font-mono text-sm"
              disabled={isLoading}
            />
            <p className="text-xs text-gray-500">
              Optional JSON metadata for additional objective configuration. Leave empty if not needed.
            </p>
          </div>
        )}

        {/* Info message for slot objectives */}
        {formData.objectiveType === 'slot' && (
          <div className="bg-green-500/10 border border-green-500/20 rounded-md p-4">
            <p className="text-sm text-green-400">
              <strong>Automatic Metadata:</strong> For Slot Games objectives,
              metadata will be automatically generated based on the providers and games you select above.
            </p>
          </div>
        )}

        {/* Info message for liveCasino objectives */}
        {formData.objectiveType === 'liveCasino' && (
          <div className="bg-green-500/10 border border-green-500/20 rounded-md p-4">
            <p className="text-sm text-green-400">
              <strong>Automatic Metadata:</strong> For Live Casino objectives,
              metadata will be automatically generated based on the game name you specify above.
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
          <Button
            onClick={handleClose}
            variant="outline"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="primary"
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <Edit className="w-4 h-4" />
                Update Objective
              </>
            )}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default EditMissionObjectiveModal;
