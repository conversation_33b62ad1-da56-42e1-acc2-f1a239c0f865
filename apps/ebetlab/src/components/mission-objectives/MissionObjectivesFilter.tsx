import React from 'react';
import { Search, X, Filter } from 'lucide-react';
import { OBJECTIVE_TYPE_OPTIONS, OBJECTIVE_OPERATOR_OPTIONS, OBJECTIVE_SUBTYPE_OPTIONS, getSubtypeOptions } from '../../utils/api/mission-objectives';

interface MissionObjectivesFilterProps {
  filters: any;
  onFiltersChange: (filters: any) => void;
  onClearFilters: () => void;
  compact?: boolean;
}

const MissionObjectivesFilter: React.FC<MissionObjectivesFilterProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
  compact = false
}) => {
  const handleFilterChange = (key: string, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value || undefined
    });
  };

  const formatDateForInput = (timestamp?: number) => {
    if (!timestamp) return '';
    return new Date(timestamp * 1000).toISOString().slice(0, 16);
  };

  const parseDateFromInput = (dateString: string) => {
    if (!dateString) return undefined;
    return Math.floor(new Date(dateString).getTime() / 1000);
  };

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== null && value !== ''
  );

  if (compact) {
    return (
      <div className="bg-dark-800 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <h3 className="text-sm font-medium text-gray-300">Filter Objectives</h3>
          </div>
          {hasActiveFilters && (
            <button
              onClick={onClearFilters}
              className="px-2 py-1 text-xs bg-dark-600 hover:bg-dark-500 text-gray-300 rounded flex items-center gap-1"
            >
              <X className="w-3 h-3" />
              Clear
            </button>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
          <input
            type="number"
            value={filters.missionId || ''}
            onChange={(e) => handleFilterChange('missionId', e.target.value ? parseInt(e.target.value) : undefined)}
            className="px-2 py-1 text-sm bg-dark-700 border border-dark-600 rounded text-gray-100 focus:outline-none focus:border-primary-500"
            placeholder="Mission ID"
          />
          
          <select
            value={filters.objectiveType || ''}
            onChange={(e) => handleFilterChange('objectiveType', e.target.value)}
            className="px-2 py-1 text-sm bg-dark-700 border border-dark-600 rounded text-gray-100 focus:outline-none focus:border-primary-500"
          >
            <option value="">All Types</option>
            {OBJECTIVE_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
          
          <select
            value={filters.operator || ''}
            onChange={(e) => handleFilterChange('operator', e.target.value)}
            className="px-2 py-1 text-sm bg-dark-700 border border-dark-600 rounded text-gray-100 focus:outline-none focus:border-primary-500"
          >
            <option value="">All Operators</option>
            {OBJECTIVE_OPERATOR_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>

          <select
            value={filters.subtype || ''}
            onChange={(e) => handleFilterChange('subtype', e.target.value)}
            className="px-2 py-1 text-sm bg-dark-700 border border-dark-600 rounded text-gray-100 focus:outline-none focus:border-primary-500"
          >
            <option value="">All Subtypes</option>
            {filters.objectiveType && getSubtypeOptions(filters.objectiveType).map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>

          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
            <input
              type="text"
              value={filters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pl-7 pr-2 py-1 text-sm bg-dark-700 border border-dark-600 rounded text-gray-100 focus:outline-none focus:border-primary-500"
              placeholder="Search objectives..."
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-dark-800 rounded-lg p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-100">Mission Objectives Filters</h3>
        </div>
        {hasActiveFilters && (
          <button
            onClick={onClearFilters}
            className="px-3 py-1 bg-dark-600 hover:bg-dark-500 text-gray-100 rounded-md text-sm flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            Clear All Filters
          </button>
        )}
      </div>

      <div className="space-y-6">
        {/* Basic Filters */}
        <div>
          <h4 className="text-sm font-medium text-gray-300 mb-3">Basic Filters</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Mission ID</label>
              <input
                type="number"
                value={filters.missionId || ''}
                onChange={(e) => handleFilterChange('missionId', e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                placeholder="Filter by mission ID..."
                min="1"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Objective Type</label>
              <select
                value={filters.objectiveType || ''}
                onChange={(e) => handleFilterChange('objectiveType', e.target.value)}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Types</option>
                {OBJECTIVE_TYPE_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Operator</label>
              <select
                value={filters.operator || ''}
                onChange={(e) => handleFilterChange('operator', e.target.value)}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Operators</option>
                {OBJECTIVE_OPERATOR_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Subtype</label>
              <select
                value={filters.subtype || ''}
                onChange={(e) => handleFilterChange('subtype', e.target.value)}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Subtypes</option>
                {filters.objectiveType && getSubtypeOptions(filters.objectiveType).map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Text Search Filters */}
        <div>
          <h4 className="text-sm font-medium text-gray-300 mb-3">Text Search</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Target Value</label>
              <input
                type="text"
                value={filters.targetValue || ''}
                onChange={(e) => handleFilterChange('targetValue', e.target.value)}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                placeholder="Filter by target value..."
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Description</label>
              <input
                type="text"
                value={filters.description || ''}
                onChange={(e) => handleFilterChange('description', e.target.value)}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                placeholder="Filter by description..."
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">General Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={filters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full pl-10 pr-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
                  placeholder="Search description & target value..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Timeframe Filters */}
        <div>
          <h4 className="text-sm font-medium text-gray-300 mb-3">Timeframe Filters</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Timeframe Start From</label>
              <input
                type="datetime-local"
                value={formatDateForInput(filters.timeframeStartFrom)}
                onChange={(e) => handleFilterChange('timeframeStartFrom', parseDateFromInput(e.target.value))}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Timeframe Start To</label>
              <input
                type="datetime-local"
                value={formatDateForInput(filters.timeframeStartTo)}
                onChange={(e) => handleFilterChange('timeframeStartTo', parseDateFromInput(e.target.value))}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Timeframe End From</label>
              <input
                type="datetime-local"
                value={formatDateForInput(filters.timeframeEndFrom)}
                onChange={(e) => handleFilterChange('timeframeEndFrom', parseDateFromInput(e.target.value))}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Timeframe End To</label>
              <input
                type="datetime-local"
                value={formatDateForInput(filters.timeframeEndTo)}
                onChange={(e) => handleFilterChange('timeframeEndTo', parseDateFromInput(e.target.value))}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-100 focus:outline-none focus:border-primary-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MissionObjectivesFilter;
