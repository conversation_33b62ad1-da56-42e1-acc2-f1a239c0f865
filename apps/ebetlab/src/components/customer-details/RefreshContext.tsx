import React, { createContext, useContext, useState, useCallback } from 'react';

interface RefreshContextType {
  isRefreshing: boolean;
  refreshTrigger: number;
  triggerRefresh: () => void;
  setRefreshing: (refreshing: boolean) => void;
}

const RefreshContext = createContext<RefreshContextType | undefined>(undefined);

export const useRefresh = () => {
  const context = useContext(RefreshContext);
  if (!context) {
    throw new Error('useRefresh must be used within a RefreshProvider');
  }
  return context;
};

interface RefreshProviderProps {
  children: React.ReactNode;
}

export const RefreshProvider: React.FC<RefreshProviderProps> = ({ children }) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const triggerRefresh = useCallback(() => {
    setRefreshTrigger(prev => prev + 1);
  }, []);

  const setRefreshing = useCallback((refreshing: boolean) => {
    setIsRefreshing(refreshing);
  }, []);

  return (
    <RefreshContext.Provider value={{
      isRefreshing,
      refreshTrigger,
      triggerRefresh,
      setRefreshing
    }}>
      {children}
    </RefreshContext.Provider>
  );
};
