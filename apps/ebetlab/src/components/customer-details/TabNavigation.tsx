import React from 'react';
import { User, DollarSign, TrendingUp, Gift, Settings, Activity, Users, ChevronDown, ChevronRight } from 'lucide-react';

// Tab categories and definitions
const CUSTOMER_TAB_CATEGORIES = [
  {
    id: 'overview',
    label: 'Overview',
    icon: 'User',
    tabs: [
      { id: 'general', label: 'General' },
      { id: 'dashboard', label: 'Dashboard' }
    ]
  },
  {
    id: 'financial',
    label: 'Financial',
    icon: 'DollarSign',
    tabs: [
      { id: 'transactions', label: 'Transactions' },
      { id: 'corrections', label: 'Corrections' },
      { id: 'wallets', label: 'Wallets' },
      { id: 'vaults', label: 'Vaults' }
    ]
  },
  {
    id: 'gaming',
    label: 'Gaming & Bets',
    icon: 'TrendingUp',
    tabs: [
      { id: 'casino-bets', label: 'Casino Bets' },
      { id: 'sportsbook-bets', label: 'Sportsbook Bets' },
      { id: 'poker-klas', label: 'Poker Klas' },
      { id: 'trade-bets', label: 'Trade Bets' }
    ]
  },
  {
    id: 'bonuses',
    label: 'Bonuses & Rewards',
    icon: 'Gift',
    tabs: [
      { id: 'bonuses', label: 'Bonuses' },
      { id: 'bonus-drops', label: 'Bonus Drops' },
      { id: 'reloads', label: 'Reloads' },
      { id: 'rakebacks', label: 'Rakebacks' },
      { id: 'tips', label: 'Tips' },
      { id: 'discounts', label: 'Discounts' },
      { id: 'rains', label: 'Rains' }
    ]
  },
  {
    id: 'management',
    label: 'Account Management',
    icon: 'Settings',
    tabs: [
      { id: 'general-limits', label: 'General Limits' },
      { id: 'debit-limits', label: 'Debit Limits' },
      { id: 'vip-updates', label: 'VIP Updates' },
      { id: 'commits', label: 'Commits' }
    ]
  },
  {
    id: 'activity',
    label: 'Activity & Logs',
    icon: 'Activity',
    tabs: [
      { id: 'sessions', label: 'Sessions' },
      { id: 'notifications', label: 'Notifications' },
      { id: 'player-journal', label: 'Player Journal' },
      { id: 'documents', label: 'Documents' }
    ]
  },
  {
    id: 'network',
    label: 'Network & Social',
    icon: 'Users',
    tabs: [
      { id: 'player-list', label: 'Player List' },
      { id: 'affiliate', label: 'Affiliate' }
    ]
  }
];

interface TabNavigationProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
  collapsedCategories: Set<string>;
  onToggleCategory: (categoryId: string) => void;
  compact?: boolean;
}

const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  onTabChange,
  collapsedCategories,
  onToggleCategory,
  compact = false
}) => {
  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: React.ComponentType<any> } = {
      User,
      DollarSign,
      TrendingUp,
      Gift,
      Settings,
      Activity,
      Users
    };
    const IconComponent = iconMap[iconName] || User;
    return <IconComponent size={16} />;
  };

  return (
    <div className={compact ? '' : 'bg-dark-700 rounded-lg border border-dark-600 p-4'}>
      {!compact && <h3 className="text-lg font-semibold text-gray-100 mb-4">Navigation</h3>}
      <div className="space-y-2">
        {CUSTOMER_TAB_CATEGORIES.map((category) => (
          <div key={category.id} className="space-y-1">
            {/* Category Header */}
            <button
              onClick={() => onToggleCategory(category.id)}
              className="w-full flex items-center justify-between px-3 py-2 text-sm font-medium text-gray-300 hover:text-gray-100 hover:bg-dark-600 rounded-md transition-colors"
            >
              <div className="flex items-center gap-2">
                {getIconComponent(category.icon)}
                <span>{category.label}</span>
              </div>
              {collapsedCategories.has(category.id) ? (
                <ChevronRight size={14} />
              ) : (
                <ChevronDown size={14} />
              )}
            </button>

            {/* Category Tabs */}
            {!collapsedCategories.has(category.id) && (
              <div className="ml-4 space-y-1">
                {category.tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => onTabChange(tab.id)}
                    className={`w-full text-left px-3 py-2 text-sm rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-500 text-dark-800 font-medium'
                        : 'text-gray-400 hover:text-gray-200 hover:bg-dark-600'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TabNavigation;
export { CUSTOMER_TAB_CATEGORIES };
