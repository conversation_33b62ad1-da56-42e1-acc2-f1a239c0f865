import React from 'react';
import { RefreshCw } from 'lucide-react';
import { useRefresh } from './RefreshContext';

const RefreshButton: React.FC = () => {
  const { isRefreshing, triggerRefresh } = useRefresh();

  return (
    <button
      onClick={triggerRefresh}
      disabled={isRefreshing}
      className={`
        flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-all duration-200
        ${isRefreshing 
          ? 'bg-dark-600 border-dark-500 text-gray-500 cursor-not-allowed' 
          : 'bg-dark-700 border-dark-500 text-gray-300 hover:bg-dark-600 hover:border-dark-400 hover:text-gray-100'
        }
      `}
      title="Refresh all data in this tab"
    >
      <RefreshCw 
        size={16} 
        className={`${isRefreshing ? 'animate-spin' : ''} transition-transform duration-200`} 
      />
      <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
    </button>
  );
};

export default RefreshButton;
