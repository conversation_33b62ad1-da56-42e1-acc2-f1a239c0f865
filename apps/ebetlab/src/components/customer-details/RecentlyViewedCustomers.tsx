import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Clock, X, User, Trash2 } from 'lucide-react';
import { useRecentlyViewedCustomers } from '../../hooks/useRecentlyViewedCustomers';

const RecentlyViewedCustomers: React.FC = () => {
  const navigate = useNavigate();
  const { id: currentCustomerId } = useParams<{ id: string }>();
  const { recentCustomers, removeCustomer, clearCustomers } = useRecentlyViewedCustomers();

  // Include all customers (including current one)
  const allCustomers = recentCustomers;

  const handleCustomerClick = (customerId: number) => {
    // Don't navigate if clicking on current customer
    if (customerId.toString() === currentCustomerId) {
      return;
    }
    navigate(`/customers/details/${customerId}`);
  };

  const handleRemoveCustomer = (e: React.MouseEvent, customerId: number) => {
    e.stopPropagation();
    removeCustomer(customerId);
  };

  const handleClearAll = () => {
    clearCustomers();
  };

  const formatTimeAgo = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div className="bg-dark-700 rounded-lg border border-dark-600 p-4 mb-6">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-primary-500" />
          <h3 className="text-sm font-medium text-gray-300">Recently Viewed Customers</h3>
          <span className="text-xs text-gray-500">({allCustomers.length})</span>
        </div>
        {allCustomers.length > 0 && (
          <button
            onClick={handleClearAll}
            className="text-xs text-gray-500 hover:text-gray-300 flex items-center gap-1 transition-colors"
            title="Clear all"
          >
            <Trash2 className="w-3 h-3" />
            Clear
          </button>
        )}
      </div>

      {allCustomers.length === 0 ? (
        <div className="text-center py-4">
          <p className="text-sm text-gray-500">No recently viewed customers yet</p>
        </div>
      ) : (
        <div className="flex flex-wrap gap-2">
          {allCustomers.slice(0, 8).map((customer) => {
            const isCurrentCustomer = customer.id.toString() === currentCustomerId;
            return (
            <div
              key={customer.id}
              onClick={() => handleCustomerClick(customer.id)}
              className={`group flex items-center gap-2 rounded-md px-3 py-2 transition-all duration-200 ${
                isCurrentCustomer
                  ? 'bg-primary-500/20 border border-primary-500 cursor-default'
                  : 'bg-dark-600 hover:bg-dark-500 border border-dark-500 hover:border-primary-500/50 cursor-pointer'
              }`}
            >
              <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                isCurrentCustomer ? 'bg-primary-500' : 'bg-primary-500/20'
              }`}>
                <User className={`w-3 h-3 ${isCurrentCustomer ? 'text-dark-800' : 'text-primary-500'}`} />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium truncate max-w-[120px] ${
                    isCurrentCustomer ? 'text-primary-300' : 'text-gray-200'
                  }`}>
                    {customer.name}
                    {isCurrentCustomer && <span className="ml-1 text-xs">(current)</span>}
                  </span>
                  <span className="text-xs text-gray-500">
                    {isCurrentCustomer ? 'viewing now' : formatTimeAgo(customer.viewedAt)}
                  </span>
                </div>
                {customer.username && (
                  <div className={`text-xs truncate max-w-[120px] ${
                    isCurrentCustomer ? 'text-primary-400' : 'text-gray-400'
                  }`}>
                    @{customer.username}
                  </div>
                )}
              </div>

              <button
                onClick={(e) => handleRemoveCustomer(e, customer.id)}
                className="opacity-0 group-hover:opacity-100 text-gray-500 hover:text-red-400 transition-all duration-200"
                title="Remove from recent"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          );})}

          {allCustomers.length > 8 && (
            <div className="flex items-center px-3 py-2 text-xs text-gray-500">
              +{allCustomers.length - 8} more
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RecentlyViewedCustomers;
