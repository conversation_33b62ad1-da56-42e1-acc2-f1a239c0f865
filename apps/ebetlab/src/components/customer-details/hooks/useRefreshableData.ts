import { useState, useEffect, useCallback } from 'react';
import { useRefresh } from '../RefreshContext';

interface UseRefreshableDataOptions<T> {
  fetchFn: () => Promise<T>;
  dependencies?: any[];
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
}

interface UseRefreshableDataReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useRefreshableData<T>({
  fetchFn,
  dependencies = [],
  onSuccess,
  onError
}: UseRefreshableDataOptions<T>): UseRefreshableDataReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { refreshTrigger, setRefreshing } = useRefresh();

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    setRefreshing(true);

    try {
      const result = await fetchFn();
      setData(result);
      onSuccess?.(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [fetchFn, onSuccess, onError, setRefreshing]);

  // Initial load and dependency changes
  useEffect(() => {
    fetchData();
  }, [...dependencies, fetchData]);

  // Refresh trigger
  useEffect(() => {
    if (refreshTrigger > 0) {
      fetchData();
    }
  }, [refreshTrigger, fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}
