# Customer Details Components

This directory contains the modular customer details components that allow for independent development of each tab.

## Structure

```
customer-details/
├── index.ts                    # Main exports
├── CustomerDetailsLayout.tsx   # Main layout component
├── CustomerHeader.tsx          # Customer header component
├── TabNavigation.tsx          # Tab navigation sidebar
├── RefreshButton.tsx          # Smart refresh button component
├── RefreshContext.tsx         # Refresh state management context
├── hooks/
│   └── useRefreshableData.ts  # Hook for refreshable data fetching
└── tabs/
    ├── index.ts               # Tab exports
    ├── general/               # General tab components
    │   ├── index.ts
    │   ├── GeneralTab.tsx     # Main general tab component
    │   ├── PersonalInfo.tsx   # Personal information section
    │   ├── AccountInfo.tsx    # Account information section
    │   ├── GamingStats.tsx    # Gaming statistics section
    │   ├── DepositInfo.tsx    # Deposit information section
    │   ├── ConflictingIPs.tsx # Conflicting IP addresses section
    │   ├── SummaryRange.tsx   # Financial summary (day/week/month/year)
    │   ├── CustomerInfo.tsx   # Recent activity (deposits, withdraws, bonuses)
    │   └── LimitsInfo.tsx     # Account limits overview (read-only)
    ├── dashboard/             # Dashboard tab components
    │   ├── index.ts
    │   └── DashboardTab.tsx   # Dashboard tab (placeholder)
    ├── general-limits/        # General limits tab components
    │   ├── index.ts
    │   └── GeneralLimitsTab.tsx # Editable limits configuration
    └── [other-categories]/    # Future tab categories
        └── [respective-tabs]
```

## Key Features

### 1. Modular Development
- Each tab has its own folder with related components
- Components can be developed and tested independently
- Clear separation of concerns

### 2. New API Integration
- Uses the new `fetchCustomerDetails` API endpoint
- Proper TypeScript interfaces for the new data structure
- Handles the updated response format

### 3. Improved Data Display
- More comprehensive customer information
- Better organization of data sections
- Enhanced privacy settings display
- Improved status indicators

### 4. Smart Refresh System
- Context-based refresh management
- Automatic coordination of all API calls in a tab
- Visual feedback with loading states
- Reusable across all tabs

### 5. Consistent Styling
- Maintains the existing design system
- Responsive layout
- Consistent component patterns

## Usage

The main `CustomerDetails` page now simply imports and renders the `CustomerDetailsLayout`:

```tsx
import { CustomerDetailsLayout } from '../components/customer-details';

const CustomerDetails: React.FC = () => {
  return <CustomerDetailsLayout />;
};
```

## Adding New Tabs

To add a new tab:

1. Create a new folder under `tabs/` for your category
2. Create the main tab component
3. Export it from the category's `index.ts`
4. Add the export to the main `tabs/index.ts`
5. Update the tab registry in `TabNavigation.tsx`

## API Changes

The customer details now use multiple endpoints:

### Customer Details
- **Endpoint**: `POST /api/operator/customers/show/{id}`
- **Request Body**: `{ id: string }`
- **Response**: Comprehensive customer data with profile, phone, and account information

### Session Check (Conflicting IPs)
- **Endpoint**: `POST /api/operator/sessions/check/{id}`
- **Request Body**: `{ id: string }`
- **Response**: Array of IP addresses with conflicting usernames

### Customer Summary Range
- **Endpoint**: `POST /api/operator/customers/summary-range/{id}`
- **Request Body**: `{ id: string }`
- **Response**: Deposit/withdraw amounts for day/week/month/year periods

### Customer Info
- **Endpoint**: `POST /api/operator/customers/info/{id}`
- **Request Body**: `{ customer_id: string, id: string }`
- **Response**: Last deposit, withdraw, discount, bonus, and corrections data

### General Limits
- **Endpoint**: `POST /api/operator/general-limits/show/{id}`
- **Request Body**: `{ id: string }`
- **Response**: Account restrictions and feature access controls

## Components Overview

### CustomerDetailsLayout
Main layout component that handles:
- Data fetching from the new API
- Tab state management
- Error handling and loading states
- Overall page structure

### CustomerHeader
Displays customer summary information:
- Avatar and public profile indicator
- Customer name and username
- Customer ID
- Rank badge
- Account status

### TabNavigation
Collapsible sidebar navigation:
- Categorized tab organization
- Expandable/collapsible categories
- Active tab highlighting
- Icon-based navigation

### General Tab Components

#### PersonalInfo
- Full name, username, email
- Phone number and verification status
- Country information
- Language, birthday, identity number
- City, postal code, occupation

#### AccountInfo
- Customer ID and account status
- Registration and activity dates
- Verification levels
- Email/phone verification status
- Privacy settings
- 2FA and social auth status
- IP address information

#### GamingStats
- Current rank and percentage
- Total turnover and wager limits
- Privacy settings (ghost mode, statistics visibility)
- Marketing preferences

#### DepositInfo
- Merchant and website information
- Account exclusion status
- OTP requirements
- Referral and affiliate information
- Placeholder for future deposit data

#### ConflictingIPs
- Real-time IP conflict detection
- Summary statistics (conflicting IPs, total users, unique users)
- Expandable IP details with username lists
- Color-coded severity indicators:
  - 🟡 Yellow: 1-2 users (low risk)
  - 🟠 Orange: 3-5 users (medium risk)
  - 🔴 Red: 6+ users (high risk)
- Security warnings and recommendations
- Automatic data loading and error handling
- Integrated with refresh system for real-time updates

#### SummaryRange
- Compact financial overview for multiple time periods
- Day/week/month/year deposit and withdraw amounts
- Net calculation with color-coded indicators (green=profit, red=loss)
- Responsive grid layout (2 columns on mobile, 4 on desktop)
- Real-time data with refresh system integration
- Clean, card-based design with clear visual hierarchy

#### CustomerInfo
- Recent activity overview with last transactions and bonuses
- Last deposit information with amount, date, provider, and transaction ID
- Last withdraw information with amount, date, provider, and transaction ID
- Last discount details with before/after balance and amount
- Last bonus information (when available)
- Corrections counter when applicable
- Color-coded transaction types (green=deposit, red=withdraw, blue=discount, purple=bonus)
- Responsive grid layout with detailed transaction information
- Integrated with refresh system for real-time updates

#### LimitsInfo
- Read-only overview of account restrictions and limits
- Categorized display (Core Features, Gaming, Promotions, Security)
- Color-coded status indicators (green=enabled, red=disabled)
- Special handling for inverted flags (like pokerklas_enabled)
- **Full Access Detection**: Shows special indicator when no restrictions exist
- **Null Handling**: Gracefully handles customers with no limits (full access)
- Organized layout with clear section headers
- Integrated with refresh system for real-time updates

## General Limits Tab

### GeneralLimitsTab
- Full editable interface for account restrictions
- **Full Access Checkbox**: Master control that unchecks all restrictions
- **Smart State Management**: Automatically detects and indicates full access state
- **Null Handling**: Creates default unrestricted state for customers with no limits
- Organized into logical categories with descriptions
- Real-time change tracking with unsaved changes indicator
- Reset functionality to discard changes
- Apply button (UI ready, business logic pending)
- Comprehensive form validation and state management
- Responsive checkbox layout with detailed descriptions
- Special handling for numeric vs boolean limit types
- **Interactive Logic**: Checking any restriction automatically unchecks Full Access

## Refresh System

### RefreshContext
Provides centralized refresh state management:
- `isRefreshing`: Global loading state
- `refreshTrigger`: Counter that triggers refreshes
- `triggerRefresh()`: Function to initiate refresh
- `setRefreshing()`: Function to update loading state

### useRefreshableData Hook
Smart data fetching hook that:
- Automatically handles loading, error, and success states
- Responds to refresh triggers from the context
- Supports dependency-based refetching
- Provides consistent error handling

### RefreshButton
Smart refresh button that:
- Shows loading state with spinning icon
- Triggers refresh for all data in the current tab
- Provides visual feedback during refresh
- Automatically disables during refresh operations

### Usage Example
```tsx
// In a tab component
const { data, loading, error } = useRefreshableData({
  fetchFn: () => fetchSomeData(customerId),
  dependencies: [customerId]
});

// The refresh button automatically coordinates all useRefreshableData hooks
```
