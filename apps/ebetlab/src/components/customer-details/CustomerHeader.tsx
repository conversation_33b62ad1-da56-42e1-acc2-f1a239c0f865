import React from 'react';
import { User, Shield, Activity } from 'lucide-react';
import { CustomerDetailsData } from '../../utils/api';
import { getVipLevelDisplayName, getVipLevelColor } from '../../constants/vipLevels';

interface CustomerHeaderProps {
  customer: CustomerDetailsData;
}

const CustomerHeader: React.FC<CustomerHeaderProps> = ({ customer }) => {
  const getRankBadge = (rank: string): React.ReactNode => {
    const colorClass = getVipLevelColor(rank || 'no-vip');
    const displayText = getVipLevelDisplayName(rank || 'no-vip');

    return (
      <span className={`inline-flex items-center px-3 py-1 text-sm font-medium rounded-full ${colorClass}`}>
        {displayText}
      </span>
    );
  };

  const getStatusBadge = (isActive: boolean, isSuspended: boolean): React.ReactNode => {
    let colorClass = 'bg-gray-500/20 text-gray-400';
    let text = 'Unknown';

    if (isSuspended) {
      colorClass = 'bg-red-500/20 text-red-500';
      text = 'Suspended';
    } else if (isActive) {
      colorClass = 'bg-green-500/20 text-green-500';
      text = 'Active';
    } else {
      colorClass = 'bg-yellow-500/20 text-yellow-500';
      text = 'Inactive';
    }

    return (
      <span className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${colorClass}`}>
        <Activity className="w-4 h-4" />
        {text}
      </span>
    );
  };

  return (
    <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
      <div className="flex items-center gap-6">
        <div className="relative">
          <div className="w-24 h-24 bg-primary-500 rounded-full flex items-center justify-center text-dark-800 border-4 border-primary-500">
            <User size={32} />
          </div>
          {customer.is_public && (
            <div className="absolute bottom-2 right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-dark-700"></div>
          )}
        </div>
        
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-100 mb-2">
            {customer.profile?.name && customer.profile?.surname 
              ? `${customer.profile.name} ${customer.profile.surname}`
              : customer.username || `Customer #${customer.id}`
            }
          </h1>
          <p className="text-lg text-gray-300 mb-1">@{customer.username || 'N/A'}</p>
          <div className="flex items-center gap-2 text-sm">
            <Shield className="w-4 h-4 text-primary-500" />
            <span className="text-primary-500 font-medium">Customer ID: {customer.id}</span>
          </div>
        </div>
        
        <div className="flex flex-col items-end gap-3">
          <div className="flex items-center justify-end">
            {getRankBadge(customer.rank || 'no-vip')}
          </div>
          <div className="flex items-center justify-end">
            {getStatusBadge(customer.is_active, customer.is_suspended)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerHeader;
