import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Eye, PanelLeftClose, PanelLeftOpen, Menu } from 'lucide-react';
import { fetchCustomerDetails, type CustomerDetailsData } from '../../utils/api';
import CustomerHeader from './CustomerHeader';
import TabNavigation, { CUSTOMER_TAB_CATEGORIES } from './TabNavigation';
import RecentlyViewedCustomers from './RecentlyViewedCustomers';
import { GeneralTab, DashboardTab, GeneralLimitsTab, TransactionsTab, CorrectionsTab, CasinoBetsTab, SportsbookBetsTab, TradeBetsTab, DiscountsTab, NotificationsTab, RakebacksTab, PlayerListTab, WalletsTab, VaultsTab, VipUpdatesTab, PlayerJournalTab, CommitsTab, TipsTab, SessionsTab, BonusesTab } from './tabs';
import { RefreshProvider } from './RefreshContext';
import RefreshButton from './RefreshButton';
import { useRefreshableData } from './hooks/useRefreshableData';
import { useRecentlyViewedCustomers } from '../../hooks/useRecentlyViewedCustomers';


const CustomerDetailsLayoutContent: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('general');
  const [collapsedCategories, setCollapsedCategories] = useState<Set<string>>(
    new Set(CUSTOMER_TAB_CATEGORIES.map(category => category.id))
  );
  const [navigationCollapsed, setNavigationCollapsed] = useState(false);
  const [showFloatingNav, setShowFloatingNav] = useState(false);
  const { addCustomer } = useRecentlyViewedCustomers();

  const fetchCustomerData = useCallback(async () => {
    if (!id) {
      throw new Error('Invalid customer ID');
    }

    const result = await fetchCustomerDetails(id);

    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load customer');
    }
  }, [id]);

  const { data: customer, loading, error } = useRefreshableData<CustomerDetailsData>({
    fetchFn: fetchCustomerData,
    dependencies: [id]
  });



  // Auto-expand category containing the active tab
  useEffect(() => {
    const tabInfo = findTabInCategories(activeTab);
    if (tabInfo) {
      const newCollapsed = new Set(collapsedCategories);
      newCollapsed.delete(tabInfo.category.id);
      setCollapsedCategories(newCollapsed);
    }
  }, [activeTab]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Toggle navigation with Ctrl/Cmd + B
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        toggleNavigation();
      }

      // Close floating nav with Escape
      if (event.key === 'Escape' && showFloatingNav) {
        setShowFloatingNav(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showFloatingNav]);

  // Track recently viewed customer when data is loaded
  useEffect(() => {
    if (customer) {
      addCustomer({
        id: customer.id,
        name: customer.profile?.name,
        surname: customer.profile?.surname,
        username: customer.username
      });
    }
  }, [customer, addCustomer]);

  // Helper functions for category management
  const toggleCategory = (categoryId: string) => {
    const newCollapsed = new Set(collapsedCategories);
    if (newCollapsed.has(categoryId)) {
      newCollapsed.delete(categoryId);
    } else {
      newCollapsed.add(categoryId);
    }
    setCollapsedCategories(newCollapsed);
  };

  // Toggle navigation panel
  const toggleNavigation = () => {
    setNavigationCollapsed(!navigationCollapsed);
    if (!navigationCollapsed) {
      setShowFloatingNav(false);
    }
  };

  // Toggle floating navigation menu
  const toggleFloatingNav = () => {
    setShowFloatingNav(!showFloatingNav);
  };

  const findTabInCategories = (tabId: string) => {
    for (const category of CUSTOMER_TAB_CATEGORIES) {
      const tab = category.tabs.find(t => t.id === tabId);
      if (tab) {
        return { category, tab };
      }
    }
    return null;
  };

  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: React.ComponentType<any> } = {
      User: () => <Eye size={16} />,
      DollarSign: () => <Eye size={16} />,
      TrendingUp: () => <Eye size={16} />,
      Gift: () => <Eye size={16} />,
      Settings: () => <Eye size={16} />,
      Activity: () => <Eye size={16} />,
      Users: () => <Eye size={16} />
    };
    const IconComponent = iconMap[iconName] || (() => <Eye size={16} />);
    return <IconComponent />;
  };



  // Render tab content
  const renderTabContent = () => {
    if (!customer) return null;

    switch (activeTab) {
      case 'general':
        return <GeneralTab customer={customer} onNavigateToTab={setActiveTab} />;

      case 'dashboard':
        return <DashboardTab customer={customer} />;

      case 'transactions':
        return <TransactionsTab customer={customer} />;

      case 'corrections':
        return <CorrectionsTab customer={customer} />;

      case 'casino-bets':
        return <CasinoBetsTab customer={customer} />;

      case 'sportsbook-bets':
        return <SportsbookBetsTab customer={customer} />;

      case 'trade-bets':
        return <TradeBetsTab customer={customer} />;

      case 'discounts':
        return <DiscountsTab customer={customer} />;

      case 'notifications':
        return <NotificationsTab customer={customer} />;

      case 'rakebacks':
        return <RakebacksTab customer={customer} />;

      case 'tips':
        return <TipsTab customer={customer} />;

      case 'player-list':
        return <PlayerListTab customer={customer} />;

      case 'general-limits':
        return <GeneralLimitsTab customer={customer} />;

      case 'wallets':
        return <WalletsTab customer={customer} />;

      case 'vaults':
        return <VaultsTab customer={customer} />;

      case 'vip-updates':
        return <VipUpdatesTab customer={customer} />;

      case 'player-journal':
        return <PlayerJournalTab customer={customer} />;

      case 'commits':
        return <CommitsTab customer={customer} />;

      case 'sessions':
        return <SessionsTab customer={customer} />;

      case 'bonuses':
        return <BonusesTab customer={customer} />;

      default:
        const tabInfo = findTabInCategories(activeTab);
        return (
          <div className="text-center py-12">
            <div className="bg-dark-600 rounded-lg border border-dark-500 p-8 max-w-md mx-auto">
              <h3 className="text-xl font-semibold text-gray-100 mb-2">
                {tabInfo?.tab.label || 'Unknown Tab'}
              </h3>
              <p className="text-gray-400 mb-4">
                This tab is not yet implemented. Business logic and content will be added here.
              </p>
              <div className="text-sm text-gray-500">
                Category: <code className="bg-dark-700 px-2 py-1 rounded">{tabInfo?.category.label}</code>
                <br />
                Tab ID: <code className="bg-dark-700 px-2 py-1 rounded">{activeTab}</code>
              </div>
            </div>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading customer details...</p>
        </div>
      </div>
    );
  }

  if (error || !customer) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/customers/list')}
            className="btn btn-outline flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            Back to Customer List
          </button>
        </div>
        
        <div className="bg-error-500/20 border border-error-500 rounded-md p-6 text-center">
          <p className="text-error-500 text-lg">{error || 'Customer not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Back Button and Refresh */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/customers/list')}
            className="btn btn-outline flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            Back to Customer List
          </button>

          <div className="flex items-center gap-2 text-gray-400">
            <Eye size={16} />
            <span>Customer Details</span>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={toggleNavigation}
            className="btn btn-outline flex items-center gap-2"
            title={`${navigationCollapsed ? 'Show Navigation' : 'Hide Navigation'} (Ctrl+B)`}
          >
            {navigationCollapsed ? (
              <PanelLeftOpen size={16} />
            ) : (
              <PanelLeftClose size={16} />
            )}
            <span className="hidden sm:inline">
              {navigationCollapsed ? 'Show Nav' : 'Hide Nav'}
            </span>
          </button>
          <RefreshButton />
        </div>
      </div>

      {/* Recently Viewed Customers */}
      <RecentlyViewedCustomers />

      {/* Customer Header */}
      <CustomerHeader customer={customer} />



      {/* Categorized Tab Navigation */}
      <div className={`grid gap-6 transition-all duration-300 ${
        navigationCollapsed
          ? 'grid-cols-1'
          : 'grid-cols-1 lg:grid-cols-4'
      }`}>
        {/* Tab Categories Sidebar */}
        {!navigationCollapsed && (
          <div className="lg:col-span-1">
            <TabNavigation
              activeTab={activeTab}
              onTabChange={setActiveTab}
              collapsedCategories={collapsedCategories}
              onToggleCategory={toggleCategory}
            />
          </div>
        )}

        {/* Tab Content */}
        <div className={navigationCollapsed ? 'col-span-1' : 'lg:col-span-3'}>
          <div className="bg-dark-700 rounded-lg border border-dark-600">
            {/* Content Header */}
            <div className="border-b border-dark-600 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {(() => {
                    const tabInfo = findTabInCategories(activeTab);
                    return (
                      <>
                        {tabInfo && getIconComponent(tabInfo.category.icon)}
                        <div>
                          <h2 className="text-xl font-semibold text-gray-100">
                            {tabInfo?.tab.label || 'Unknown Tab'}
                          </h2>
                          <p className="text-sm text-gray-400">
                            {tabInfo?.category.label}
                          </p>
                        </div>
                      </>
                    );
                  })()}
                </div>

                {/* Quick Navigation Toggle (when collapsed) */}
                {navigationCollapsed && (
                  <button
                    onClick={toggleFloatingNav}
                    className="btn btn-outline btn-sm flex items-center gap-2"
                    title="Quick Navigation"
                  >
                    <Menu size={14} />
                    <span>Quick Nav</span>
                  </button>
                )}
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>

      {/* Floating Navigation Menu (when navigation is collapsed) */}
      {navigationCollapsed && showFloatingNav && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setShowFloatingNav(false)}
          />

          {/* Floating Navigation Panel */}
          <div className="fixed top-20 right-6 w-80 max-h-[calc(100vh-8rem)] bg-dark-700 rounded-lg border border-dark-600 shadow-2xl z-50 overflow-hidden">
            <div className="p-4 border-b border-dark-600">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-100">Quick Navigation</h3>
                <button
                  onClick={() => setShowFloatingNav(false)}
                  className="text-gray-400 hover:text-gray-200 p-1"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto p-4">
              <TabNavigation
                activeTab={activeTab}
                onTabChange={(tabId) => {
                  setActiveTab(tabId);
                  setShowFloatingNav(false);
                }}
                collapsedCategories={collapsedCategories}
                onToggleCategory={toggleCategory}
                compact={true}
              />
            </div>

            <div className="p-4 border-t border-dark-600">
              <button
                onClick={() => {
                  toggleNavigation();
                  setShowFloatingNav(false);
                }}
                className="w-full btn btn-primary flex items-center justify-center gap-2"
              >
                <PanelLeftOpen size={16} />
                Show Full Navigation
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

const CustomerDetailsLayout: React.FC = () => {
  return (
    <RefreshProvider>
      <CustomerDetailsLayoutContent />
    </RefreshProvider>
  );
};

export default CustomerDetailsLayout;
