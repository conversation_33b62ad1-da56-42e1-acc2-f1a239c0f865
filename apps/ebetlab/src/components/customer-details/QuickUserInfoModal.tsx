import React, { useState, useEffect } from 'react';
import { X, User, Mail, Phone, MapPin, Calendar, Shield, Eye, ExternalLink, Crown, DollarSign, Users, Link } from 'lucide-react';
import { fetchCustomerDetails, type CustomerDetailsData } from '../../utils/api';
import { getVipLevelDisplayName, getVipLevelColor } from '../../constants/vipLevels';
import { useNavigate } from 'react-router-dom';

interface QuickUserInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  customerId: string | number;
}

const QuickUserInfoModal: React.FC<QuickUserInfoModalProps> = ({
  isOpen,
  onClose,
  customerId
}) => {
  const [customer, setCustomer] = useState<CustomerDetailsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const navigate = useNavigate();

  useEffect(() => {
    if (isOpen && customerId) {
      loadCustomerData();
    }
  }, [isOpen, customerId]);

  const loadCustomerData = async () => {
    setLoading(true);
    setError('');

    try {
      const result = await fetchCustomerDetails(customerId.toString());

      if (result.success && result.data) {
        setCustomer(result.data.data);
      } else {
        setError(result.error || 'Failed to load customer data');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleViewFullDetails = () => {
    navigate(`/customers/details/${customerId}`);
    onClose();
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleDateString();
  };

  const formatCurrency = (amount: string | undefined): string => {
    if (!amount) return '-';
    return `$${parseFloat(amount).toFixed(2)}`;
  };

  const getRankBadge = (rank: string): React.ReactNode => {
    const colorClass = getVipLevelColor(rank || 'no-vip');
    const displayText = getVipLevelDisplayName(rank || 'no-vip');

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
        {displayText}
      </span>
    );
  };

  const getStatusBadge = (isActive: boolean, isSuspended: boolean): React.ReactNode => {
    if (isSuspended) {
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 rounded text-sm bg-red-500/10 text-red-500">
          <Shield size={14} />
          Suspended
        </span>
      );
    }

    if (isActive) {
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 rounded text-sm bg-green-500/10 text-green-500">
          <Shield size={14} />
          Active
        </span>
      );
    }

    return (
      <span className="inline-flex items-center gap-1 px-2 py-1 rounded text-sm bg-gray-500/10 text-gray-400">
        <Shield size={14} />
        Inactive
      </span>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[9999]">
      <div className="bg-dark-700 rounded-lg border border-dark-600 shadow-2xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <User className="w-6 h-6 text-primary-500" />
            <div>
              <h2 className="text-xl font-semibold text-gray-100">Quick User Info</h2>
              <p className="text-sm text-gray-400">Customer ID: {customerId}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-200 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
              <p className="text-gray-400">Loading customer information...</p>
            </div>
          </div>
        ) : error ? (
          <div className="bg-error-500/20 border border-error-500 rounded-md p-6 text-center">
            <p className="text-error-500 text-lg mb-2">Failed to load customer data</p>
            <p className="text-gray-400 mb-4">{error}</p>
            <button
              onClick={loadCustomerData}
              className="btn btn-primary"
            >
              Try Again
            </button>
          </div>
        ) : customer ? (
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
                  Personal Information
                </h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Full Name</label>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-100">
                      {customer.profile?.name && customer.profile?.surname 
                        ? `${customer.profile.name} ${customer.profile.surname}`
                        : '-'
                      }
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
                  <p className="text-gray-100 font-mono">{customer.username || '-'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Email</label>
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-100">{customer.email || '-'}</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Phone</label>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-100">{customer.phone?.full || '-'}</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Registration Date</label>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-100">{formatDate(customer.registration_ts)}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
                  Account Status
                </h3>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
                  {getStatusBadge(customer.is_active, customer.is_suspended)}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">VIP Rank</label>
                  <div className="flex items-center gap-2">
                    <Crown className="w-4 h-4 text-gray-400" />
                    <div className="flex items-center gap-2">
                      {getRankBadge(customer.rank || 'no-vip')}
                      <span className="text-gray-300">({customer.rank_percentage || '0'}%)</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Total Turnover</label>
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-100 font-medium">{formatCurrency(customer.total_turnover)}</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Verification Level</label>
                  <div className="flex items-center gap-2">
                    <Shield className="w-4 h-4 text-gray-400" />
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      (customer.profile?.verification_level || 0) >= 2
                        ? 'bg-success-500/20 text-success-500'
                        : 'bg-warning-500/20 text-warning-500'
                    }`}>
                      Level {customer.profile?.verification_level || 0}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Country</label>
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-100 uppercase">{customer.registration_country || '-'}</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">Public Profile</label>
                  <div className="flex items-center gap-2">
                    {customer.is_public ? <Eye className="w-4 h-4 text-green-500" /> : <Eye className="w-4 h-4 text-gray-400" />}
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      customer.is_public
                        ? 'bg-green-500/10 text-green-500'
                        : 'bg-gray-500/10 text-gray-400'
                    }`}>
                      {customer.is_public ? 'Public' : 'Private'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Referrer & Affiliate Information */}
            {(customer.ref_id || customer.affiliator_id || customer.ref_code || customer.referrer || customer.affiliator) && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2 flex items-center gap-2">
                  <Users className="w-5 h-5 text-primary-500" />
                  Referrer & Affiliate Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Referrer Information */}
                  {(customer.ref_id || customer.referrer) && (
                    <div className="space-y-3">
                      <h4 className="text-md font-medium text-gray-200">Referrer</h4>

                      {customer.ref_id && (
                        <div>
                          <label className="block text-sm font-medium text-gray-400 mb-1">Referrer ID</label>
                          <div className="flex items-center gap-2">
                            <User className="w-4 h-4 text-gray-400" />
                            <p className="text-gray-100 font-mono">{customer.ref_id}</p>
                          </div>
                        </div>
                      )}

                      {customer.referrer && (
                        <div>
                          <label className="block text-sm font-medium text-gray-400 mb-1">Referrer Details</label>
                          <div className="bg-dark-600 rounded p-3 space-y-2">
                            {customer.referrer.username && (
                              <div className="flex items-center gap-2">
                                <User className="w-3 h-3 text-gray-400" />
                                <span className="text-sm text-gray-300">Username: {customer.referrer.username}</span>
                              </div>
                            )}
                            {customer.referrer.email && (
                              <div className="flex items-center gap-2">
                                <Mail className="w-3 h-3 text-gray-400" />
                                <span className="text-sm text-gray-300">Email: {customer.referrer.email}</span>
                              </div>
                            )}
                            {customer.referrer.id && (
                              <div className="flex items-center gap-2">
                                <Shield className="w-3 h-3 text-gray-400" />
                                <span className="text-sm text-gray-300">ID: {customer.referrer.id}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Affiliate Information */}
                  {(customer.affiliator_id || customer.affiliator) && (
                    <div className="space-y-3">
                      <h4 className="text-md font-medium text-gray-200">Affiliate</h4>

                      {customer.affiliator_id && (
                        <div>
                          <label className="block text-sm font-medium text-gray-400 mb-1">Affiliator ID</label>
                          <div className="flex items-center gap-2">
                            <Link className="w-4 h-4 text-gray-400" />
                            <p className="text-gray-100 font-mono">{customer.affiliator_id}</p>
                          </div>
                        </div>
                      )}

                      {customer.affiliator && (
                        <div>
                          <label className="block text-sm font-medium text-gray-400 mb-1">Affiliator Details</label>
                          <div className="bg-dark-600 rounded p-3 space-y-2">
                            {customer.affiliator.username && (
                              <div className="flex items-center gap-2">
                                <User className="w-3 h-3 text-gray-400" />
                                <span className="text-sm text-gray-300">Username: {customer.affiliator.username}</span>
                              </div>
                            )}
                            {customer.affiliator.email && (
                              <div className="flex items-center gap-2">
                                <Mail className="w-3 h-3 text-gray-400" />
                                <span className="text-sm text-gray-300">Email: {customer.affiliator.email}</span>
                              </div>
                            )}
                            {customer.affiliator.id && (
                              <div className="flex items-center gap-2">
                                <Shield className="w-3 h-3 text-gray-400" />
                                <span className="text-sm text-gray-300">ID: {customer.affiliator.id}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Referral Code */}
                  {customer.ref_code && (
                    <div className="md:col-span-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Referral Code</label>
                        <div className="flex items-center gap-2">
                          <Link className="w-4 h-4 text-gray-400" />
                          <p className="text-gray-100 font-mono bg-dark-600 px-3 py-1 rounded">{customer.ref_code}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4 border-t border-dark-600">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors"
              >
                Close
              </button>
              <button
                onClick={handleViewFullDetails}
                className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors flex items-center justify-center gap-2"
              >
                <ExternalLink size={16} />
                View Full Details
              </button>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default QuickUserInfoModal;
