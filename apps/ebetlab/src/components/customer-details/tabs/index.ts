// Tab component exports
export { default as GeneralTab } from './general';
export { default as DashboardTab } from './dashboard';
export { default as GeneralLimitsTab } from './general-limits';
export { default as TransactionsTab } from './transactions/TransactionsTab';
export { default as CorrectionsTab } from './corrections/CorrectionsTab';
export { CasinoBetsTab } from './casino-bets';
export { SportsbookBetsTab } from './sportsbook-bets';
export { TradeBetsTab } from './trade-bets';
export { DiscountsTab } from './discounts';
export { NotificationsTab } from './notifications';
export { RakebacksTab } from './rakebacks';
export { PlayerListTab } from './player-list';
export { WalletsTab } from './wallets';
export { VaultsTab } from './vaults';
export { VipUpdatesTab } from './vip-updates';
export { PlayerJournalTab } from './player-journal';
export { CommitsTab } from './commits';
export { TipsTab } from './tips';
export { SessionsTab } from './sessions';
export { BonusesTab } from './bonuses';

// Add other tabs as they are implemented
// export { default as CorrectionsTab } from './financial/CorrectionsTab';
// ... etc
