import React, { useState, useCallback } from 'react';
import { Percent, Download, Filter, RefreshCw, Eye, DollarSign, TrendingUp, Calendar, User } from 'lucide-react';
import { fetchDiscounts, type DiscountData, type DiscountSearchParams } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface DiscountsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface DiscountDetailsModalProps {
  discount: DiscountData;
}

const DiscountDetailsModal: React.FC<DiscountDetailsModalProps> = ({ discount }) => {
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string, currency: string): string => {
    return `${parseFloat(amount).toFixed(8)} ${currency}`;
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Discount ID</label>
            <p className="text-gray-100 font-mono">#{discount.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Customer ID</label>
            <p className="text-gray-100 font-mono">{discount.customer_id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Currency</label>
            <p className="text-gray-100">{discount.code}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Deposit ID</label>
            <p className="text-gray-100 font-mono">#{discount.deposit_id}</p>
          </div>
        </div>

        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Discount Amount</label>
            <p className="text-gray-100 text-lg font-semibold">{formatCurrency(discount.amount, discount.code)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Deposit Amount</label>
            <p className="text-gray-100">{formatCurrency(discount.depositAmount, discount.code)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Before Balance</label>
            <p className="text-gray-100">{formatCurrency(discount.before_balance, discount.code)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">After Balance</label>
            <p className="text-gray-100">{formatCurrency(discount.after_balance, discount.code)}</p>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="border-t border-dark-600 pt-4">
        <h4 className="text-lg font-medium text-gray-100 mb-3">Customer Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
            <p className="text-gray-100">{discount.customer.username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Masked Username</label>
            <p className="text-gray-100 font-mono">{discount.customer.masked_username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Last Action</label>
            <p className="text-gray-100">{formatTimestamp(discount.customer.last_action)}</p>
          </div>
        </div>
      </div>

      {/* Transaction Information */}
      <div className="border-t border-dark-600 pt-4">
        <h4 className="text-lg font-medium text-gray-100 mb-3">Related Transaction</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Transaction ID</label>
            <p className="text-gray-100 font-mono">#{discount.transaction.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Transaction Amount</label>
            <p className="text-gray-100">{formatCurrency(discount.transaction.amount, discount.code)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">USD Amount</label>
            <p className="text-gray-100">${parseFloat(discount.transaction.usd_amount).toFixed(2)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Provider</label>
            <p className="text-gray-100">{discount.transaction.provider || 'N/A'}</p>
          </div>
        </div>
      </div>

      {/* Timestamps */}
      <div className="border-t border-dark-600 pt-4">
        <h4 className="text-lg font-medium text-gray-100 mb-3">Timestamps</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Discount Created</label>
            <p className="text-gray-100">{formatTimestamp(discount.timestamp)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Transaction Created</label>
            <p className="text-gray-100">{formatTimestamp(discount.transaction.timestamp)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const DiscountsTab: React.FC<DiscountsTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedDiscount, setSelectedDiscount] = useState<DiscountData | null>(null);
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<DiscountSearchParams>>({
    username: customer.username
  });

  // Fetch discounts data
  const fetchDiscountsData = useCallback(async () => {
    const result = await fetchDiscounts(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load discounts');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: discountsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchDiscountsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleFilterChange = (key: string, value: string) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value || null
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleDiscountClick = (discount: DiscountData) => {
    setSelectedDiscount(discount);
    setShowDiscountModal(true);
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string, currency: string): string => {
    return `${parseFloat(amount).toFixed(8)} ${currency}`;
  };

  // Export discounts data
  const handleExport = () => {
    if (!discountsData?.data) return;

    const csvData = discountsData.data.map(discount => ({
      'Discount ID': discount.id,
      'Customer ID': discount.customer_id,
      'Customer Username': discount.customer.username,
      'Masked Username': discount.customer.masked_username,
      'Currency': discount.code,
      'Discount Amount': discount.amount,
      'Deposit Amount': discount.depositAmount,
      'Before Balance': discount.before_balance,
      'After Balance': discount.after_balance,
      'Deposit ID': discount.deposit_id,
      'Transaction ID': discount.transaction.id,
      'Transaction Amount': discount.transaction.amount,
      'USD Amount': discount.transaction.usd_amount,
      'Provider': discount.transaction.provider || 'N/A',
      'Discount Timestamp': formatTimestamp(discount.timestamp),
      'Transaction Timestamp': formatTimestamp(discount.transaction.timestamp),
      'Customer Last Action': formatTimestamp(discount.customer.last_action)
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(value => `"${value}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `discounts_${customer.username}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Percent className="w-12 h-12 text-primary-400 mx-auto mb-4 animate-pulse" />
          <h3 className="text-lg font-medium text-gray-100 mb-2">Loading discounts...</h3>
          <p className="text-gray-400">Please wait while we fetch the discount data.</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Percent className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-100 mb-2">Failed to load discounts</h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <button
            onClick={refetch}
            className="btn btn-primary flex items-center gap-2 mx-auto"
          >
            <RefreshCw size={16} />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const discounts = discountsData?.data || [];
  const total = discountsData?.total || 0;
  const totalPages = Math.ceil(total / itemsPerPage);

  // Calculate summary statistics
  const totalDiscountAmount = discounts.reduce((sum, discount) => sum + parseFloat(discount.amount), 0);
  const totalDepositAmount = discounts.reduce((sum, discount) => sum + parseFloat(discount.depositAmount), 0);
  const averageDiscountAmount = discounts.length > 0 ? totalDiscountAmount / discounts.length : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Percent className="w-6 h-6 text-primary-400" />
          <div>
            <h2 className="text-xl font-semibold text-gray-100">Discounts</h2>
            <p className="text-gray-400 text-sm">
              {total > 0 ? `${total} discount${total !== 1 ? 's' : ''} found` : 'No discounts found'}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn flex items-center gap-2 ${showFilters ? 'btn-primary' : 'btn-outline'}`}
          >
            <Filter size={16} />
            Filters
          </button>

          <button
            onClick={handleExport}
            disabled={discounts.length === 0}
            className="btn btn-outline flex items-center gap-2"
          >
            <Download size={16} />
            Export CSV
          </button>

          <button
            onClick={refetch}
            disabled={loading}
            className="btn btn-outline flex items-center gap-2"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Statistics */}
      {discounts.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <DollarSign className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Total Discounts</p>
                <p className="text-lg font-semibold text-gray-100">${totalDiscountAmount.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <TrendingUp className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Total Deposits</p>
                <p className="text-lg font-semibold text-gray-100">${totalDepositAmount.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Percent className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Average Discount</p>
                <p className="text-lg font-semibold text-gray-100">${averageDiscountAmount.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-100 mb-4">Filter Discounts</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Username
              </label>
              <input
                type="text"
                value={searchParams.username || ''}
                onChange={(e) => handleFilterChange('username', e.target.value)}
                placeholder="Enter username"
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Currency
              </label>
              <input
                type="text"
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                placeholder="Enter currency code"
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              />
            </div>
          </div>

          <div className="flex justify-end mt-4 pt-4 border-t border-dark-600">
            <button
              onClick={() => {
                setSearchParams({ username: customer.username });
                setCurrentPage(1);
              }}
              className="btn btn-outline"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Discounts Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-dark-600">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Discount ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Currency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Discount Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Deposit Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Balance Change
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {discounts.length > 0 ? (
                discounts.map((discount) => (
                  <tr
                    key={discount.id}
                    className="hover:bg-dark-600/50 cursor-pointer transition-colors"
                    onClick={() => handleDiscountClick(discount)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-100">
                        #{discount.id}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        {discount.customer.username}
                      </div>
                      <div className="text-xs text-gray-500 font-mono">
                        {discount.customer.masked_username}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        {discount.code}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300 font-mono">
                        {formatCurrency(discount.amount, discount.code)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300 font-mono">
                        {formatCurrency(discount.depositAmount, discount.code)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        <div className="text-xs text-gray-500">Before: {parseFloat(discount.before_balance).toFixed(8)}</div>
                        <div className="text-xs text-gray-500">After: {parseFloat(discount.after_balance).toFixed(8)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        {formatTimestamp(discount.timestamp)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDiscountClick(discount);
                        }}
                        className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                        title="View Details"
                      >
                        <Eye size={14} />
                        Details
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <Percent className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No discounts found</p>
                      <p className="text-sm">This customer has no discount history.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {total > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={total}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        )}
      </div>

      {/* Discount Details Modal */}
      {selectedDiscount && (
        <Modal
          isOpen={showDiscountModal}
          onClose={() => setShowDiscountModal(false)}
          title={`Discount #${selectedDiscount.id}`}
          size="lg"
        >
          <DiscountDetailsModal discount={selectedDiscount} />
        </Modal>
      )}
    </div>
  );
};

export default DiscountsTab;
