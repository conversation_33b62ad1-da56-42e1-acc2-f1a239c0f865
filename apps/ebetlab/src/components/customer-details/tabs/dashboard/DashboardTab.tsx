import React, { useState, useEffect, useCallback } from 'react';
import { CustomerDetailsData, fetchCustomerGameWinLose, fetchCustomerBigWinLose, type BigWinLoseItem, type GameWinItem, type GameLoseItem } from '../../../../utils/api';
import { BarChart3, TrendingUp, TrendingDown, DollarSign, Activity, RefreshCw, Target, Gamepad2 } from 'lucide-react';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import <PERSON><PERSON>hart from '../../../dashboard/PieChart';
import Donut<PERSON>hart from '../../../dashboard/DonutChart';

interface DashboardTabProps {
  customer: CustomerDetailsData;
}

const DashboardTab: React.FC<DashboardTabProps> = ({ customer }) => {
  // Fetch customer-specific game win/lose data
  const fetchGameWinLoseData = useCallback(async () => {
    const result = await fetchCustomerGameWinLose(customer.id.toString());

    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load game win/lose data');
    }
  }, [customer.id]);

  // Fetch customer-specific big win/lose data
  const fetchBigWinLoseData = useCallback(async () => {
    const result = await fetchCustomerBigWinLose(customer.id.toString());

    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load big win/lose data');
    }
  }, [customer.id]);

  const { data: gameWinLoseData, loading: gameLoading, error: gameError, refetch: refetchGame } = useRefreshableData({
    fetchFn: fetchGameWinLoseData,
    dependencies: [customer.id]
  });

  const { data: bigWinLoseData, loading: bigLoading, error: bigError, refetch: refetchBig } = useRefreshableData({
    fetchFn: fetchBigWinLoseData,
    dependencies: [customer.id]
  });

  // Utility function to round numbers to 2 decimal places
  const roundToTwo = (num: number): number => {
    return Math.round((num + Number.EPSILON) * 100) / 100;
  };

  // Helper function to generate blue/aqua colors for pie chart legend
  const getPieChartColor = (index: number, _total: number, type: 'win' | 'lose') => {
    const primaryShades = [
      '#18cffb', '#33dffa', '#66e7fc', '#99effd', '#ccf7fe',
      '#0ca5c8', '#097c96', '#065264'
    ];

    const secondaryShades = [
      '#0ac3c6', '#33f9fa', '#66fbfc', '#99fcfd', '#ccfefe',
      '#089c9e', '#067577', '#044e4f'
    ];

    const colors = type === 'win' ? primaryShades : secondaryShades;
    return colors[index % colors.length];
  };

  // Calculate key metrics from big win/lose data
  const calculateMetrics = () => {
    if (!bigWinLoseData) {
      return {
        totalWins: 0,
        totalLosses: 0,
        totalWinAmount: 0,
        totalLossAmount: 0,
        netAmount: 0,
        totalGames: 0,
        winRate: 0
      };
    }

    // Calculate amounts using the income field for wins and amount field for losses
    const totalWinAmount = bigWinLoseData.win.reduce((sum, item) => sum + parseFloat(item.income), 0);
    const totalLossAmount = bigWinLoseData.lose.reduce((sum, item) => sum + parseFloat(item.amount), 0);
    const totalNetAmount = bigWinLoseData.win.reduce((sum, item) => sum + parseFloat(item.net), 0) +
                          bigWinLoseData.lose.reduce((sum, item) => sum + parseFloat(item.net), 0);

    const totalGames = bigWinLoseData.win.length + bigWinLoseData.lose.length;
    const winRate = totalGames > 0 ? (bigWinLoseData.win.length / totalGames) * 100 : 0;

    return {
      totalWins: bigWinLoseData.win.length,
      totalLosses: bigWinLoseData.lose.length,
      totalWinAmount: roundToTwo(totalWinAmount),
      totalLossAmount: roundToTwo(totalLossAmount),
      netAmount: roundToTwo(totalNetAmount),
      totalGames,
      winRate: roundToTwo(winRate)
    };
  };

  const metrics = calculateMetrics();

  // Prepare game performance data for charts
  const gamePerformanceData = () => {
    if (!gameWinLoseData) return { win: [], lose: [] };

    const winData = gameWinLoseData.win.slice(0, 10).map((game) => ({
      game_name: game.game_name,
      amount_usd: game.total_win
    }));

    const loseData = gameWinLoseData.lose.slice(0, 10).map((game) => ({
      game_name: game.game_name,
      amount_usd: game.total_loss
    }));

    return { win: winData, lose: loseData };
  };

  // Prepare donut chart data for losses
  const donutChartData = () => {
    if (!gameWinLoseData) return [];

    return gameWinLoseData.lose.slice(0, 10).map((game) => ({
      label: game.game_name,
      value: roundToTwo(parseFloat(game.total_loss))
    }));
  };

  const chartData = gamePerformanceData();
  const donutData = donutChartData();

  // Recent activity data from big win/lose
  const recentActivity = () => {
    if (!bigWinLoseData) return [];

    const allActivity = [...bigWinLoseData.win, ...bigWinLoseData.lose]
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10);

    return allActivity;
  };

  const activity = recentActivity();

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string): string => {
    return `$${parseFloat(amount).toFixed(2)}`;
  };

  const handleRefreshAll = () => {
    refetchGame();
    refetchBig();
  };

  if (gameError || bigError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <BarChart3 className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-100 mb-2">Failed to load dashboard data</h3>
          <p className="text-gray-400 mb-4">{gameError || bigError}</p>
          <button
            onClick={handleRefreshAll}
            className="btn btn-primary flex items-center gap-2 mx-auto"
          >
            <RefreshCw size={16} />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-100">Customer Dashboard</h2>
          <p className="text-sm text-gray-400 mt-2">
            Gaming analytics and statistics for <span className="text-primary-400 font-medium">{customer.username}</span>
          </p>
        </div>

        <button
          onClick={handleRefreshAll}
          disabled={gameLoading || bigLoading}
          className="btn btn-outline flex items-center gap-2 hover:bg-primary-500/10 hover:border-primary-500/50 transition-all"
        >
          <RefreshCw size={16} className={(gameLoading || bigLoading) ? 'animate-spin' : ''} />
          Refresh Data
        </button>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6 hover:border-blue-500/50 transition-colors">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-400 mb-2">Total Games</p>
              <p className="text-3xl font-bold text-blue-400">
                {metrics.totalGames}
              </p>
            </div>
            <div className="p-3 bg-blue-500/10 rounded-lg">
              <Gamepad2 className="w-8 h-8 text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6 hover:border-green-500/50 transition-colors">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-400 mb-2">Win Rate</p>
              <p className="text-3xl font-bold text-green-400">
                {metrics.winRate}%
              </p>
            </div>
            <div className="p-3 bg-green-500/10 rounded-lg">
              <Target className="w-8 h-8 text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6 hover:border-green-500/50 transition-colors">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-400 mb-2">Total Wins</p>
              <p className="text-3xl font-bold text-green-400">
                ${metrics.totalWinAmount}
              </p>
            </div>
            <div className="p-3 bg-green-500/10 rounded-lg">
              <TrendingUp className="w-8 h-8 text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6 hover:border-primary-500/50 transition-colors">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-400 mb-2">Net Amount</p>
              <p className={`text-3xl font-bold ${
                metrics.netAmount >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                {metrics.netAmount >= 0 ? '+' : ''}${Math.abs(metrics.netAmount)}
              </p>
            </div>
            <div className={`p-3 rounded-lg ${
              metrics.netAmount >= 0 ? 'bg-green-500/10' : 'bg-red-500/10'
            }`}>
              <DollarSign className={`w-8 h-8 ${
                metrics.netAmount >= 0 ? 'text-green-400' : 'text-red-400'
              }`} />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Game Wins Chart */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
          <div className="flex items-center justify-between p-6 pb-4">
            <h3 className="text-lg font-semibold text-gray-100">Top Winning Games</h3>
            <TrendingUp className="w-5 h-5 text-green-400" />
          </div>
          {gameLoading ? (
            <div className="px-6 pb-6">
              <div className="h-[400px] flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
                  <p>Loading winning games data...</p>
                </div>
              </div>
            </div>
          ) : chartData.win.length > 0 ? (
            <div className="px-6 pb-6">
              <div className="relative w-full" style={{ height: '400px' }}>
                <PieChart
                  data={chartData.win}
                  type="win"
                  showLegend={true}
                />
              </div>
            </div>
          ) : (
            <div className="px-6 pb-6">
              <div className="h-[400px] flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <Gamepad2 className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>No winning games data available</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Game Losses Chart */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
          <div className="flex items-center justify-between p-6 pb-4">
            <h3 className="text-lg font-semibold text-gray-100">Top Losing Games</h3>
            <TrendingDown className="w-5 h-5 text-red-400" />
          </div>
          {gameLoading ? (
            <div className="px-6 pb-6">
              <div className="h-[400px] flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-secondary-500 mx-auto mb-4"></div>
                  <p>Loading losing games data...</p>
                </div>
              </div>
            </div>
          ) : donutData.length > 0 ? (
            <div className="px-6 pb-6">
              <div className="relative w-full" style={{ height: '400px' }}>
                <DonutChart
                  data={donutData}
                  showLegend={true}
                />
              </div>
            </div>
          ) : (
            <div className="px-6 pb-6">
              <div className="h-[400px] flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <Gamepad2 className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>No losing games data available</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="flex items-center justify-between p-6 pb-4 border-b border-dark-600">
          <h3 className="text-lg font-semibold text-gray-100">Recent Gaming Activity</h3>
          <Activity className="w-5 h-5 text-blue-400" />
        </div>

        {activity.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-dark-800/50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Game
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Income
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Net
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-dark-600">
                {activity.map((item) => {
                  const isWin = parseFloat(item.income) > 0;
                  const netAmount = parseFloat(item.net);

                  return (
                    <tr key={item.id} className="hover:bg-dark-600/30 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-100">{item.game_name}</div>
                        <div className="text-xs text-gray-400">ID: {item.game_id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-100">
                          {formatCurrency(item.amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          isWin ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {formatCurrency(item.income)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-semibold ${
                          netAmount >= 0 ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {netAmount >= 0 ? '+' : ''}${Math.abs(netAmount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-100">
                          {formatTimestamp(item.timestamp)}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-400">
            <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">No recent activity</p>
            <p className="text-sm">This customer has no recent gaming activity.</p>
          </div>
        )}
      </div>

      {/* Win/Loss Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
          <div className="flex items-center justify-between p-6 pb-4 border-b border-dark-600 bg-green-500/5">
            <h3 className="text-lg font-semibold text-gray-100">Win Summary</h3>
            <TrendingUp className="w-5 h-5 text-green-400" />
          </div>
          <div className="p-6 space-y-4">
            <div className="flex justify-between items-center py-2">
              <span className="text-gray-400 text-sm">Total Wins:</span>
              <span className="text-gray-100 font-semibold text-lg">{metrics.totalWins}</span>
            </div>
            <div className="flex justify-between items-center py-2 border-t border-dark-600">
              <span className="text-gray-400 text-sm">Win Amount:</span>
              <span className="text-green-400 font-bold text-lg">${metrics.totalWinAmount}</span>
            </div>
            <div className="flex justify-between items-center py-2 border-t border-dark-600">
              <span className="text-gray-400 text-sm">Win Rate:</span>
              <span className="text-green-400 font-bold text-lg">{metrics.winRate}%</span>
            </div>
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
          <div className="flex items-center justify-between p-6 pb-4 border-b border-dark-600 bg-red-500/5">
            <h3 className="text-lg font-semibold text-gray-100">Loss Summary</h3>
            <TrendingDown className="w-5 h-5 text-red-400" />
          </div>
          <div className="p-6 space-y-4">
            <div className="flex justify-between items-center py-2">
              <span className="text-gray-400 text-sm">Total Losses:</span>
              <span className="text-gray-100 font-semibold text-lg">{metrics.totalLosses}</span>
            </div>
            <div className="flex justify-between items-center py-2 border-t border-dark-600">
              <span className="text-gray-400 text-sm">Loss Amount:</span>
              <span className="text-red-400 font-bold text-lg">${metrics.totalLossAmount}</span>
            </div>
            <div className="flex justify-between items-center py-2 border-t border-dark-600">
              <span className="text-gray-400 text-sm">Loss Rate:</span>
              <span className="text-red-400 font-bold text-lg">{roundToTwo(100 - metrics.winRate)}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardTab;
