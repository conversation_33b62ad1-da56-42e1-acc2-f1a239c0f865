import React from 'react';
import { TrendingUp, DollarSign, Target, Award } from 'lucide-react';
import { CustomerDetailsData } from '../../../../utils/api';
import { getVipLevelDisplayName, getVipLevelColor } from '../../../../constants/vipLevels';

interface GamingStatsProps {
  customer: CustomerDetailsData;
}

const GamingStats: React.FC<GamingStatsProps> = ({ customer }) => {
  const formatCurrency = (amount: string | undefined): string => {
    if (!amount) return '-';
    return `$${parseFloat(amount).toFixed(2)}`;
  };

  const getRankBadge = (rank: string): React.ReactNode => {
    const colorClass = getVipLevelColor(rank || 'no-vip');
    const displayText = getVipLevelDisplayName(rank || 'no-vip');

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
        {displayText}
      </span>
    );
  };

  return (
    <div className="bg-dark-600 rounded-lg border border-dark-500 p-6">
      <h3 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
        <TrendingUp className="w-5 h-5 text-primary-500" />
        Gaming Statistics
      </h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Current Rank</label>
          <div className="flex items-center gap-2">
            <Award className="w-4 h-4 text-gray-400" />
            <div className="flex items-center gap-2">
              {getRankBadge(customer.rank || 'no-vip')}
              <span className="text-gray-300">({customer.rank_percentage || '0'}%)</span>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Total Turnover</label>
          <div className="flex items-center gap-2">
            <DollarSign className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100 font-medium text-lg">{formatCurrency(customer.total_turnover)}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Next Wager Limit</label>
          <div className="flex items-center gap-2">
            <Target className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100">{formatCurrency(customer.next_wager_limit)}</p>
          </div>
        </div>

        {/* Privacy Settings */}
        <div className="border-t border-dark-500 pt-4 mt-6">
          <h4 className="text-lg font-medium text-gray-200 mb-3">Privacy Settings</h4>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Ghost Mode</label>
              <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
                customer.profile?.ghost_mode
                  ? 'bg-purple-500/10 text-purple-500'
                  : 'bg-gray-500/10 text-gray-400'
              }`}>
                {customer.profile?.ghost_mode ? 'Enabled' : 'Disabled'}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Hide Statistics</label>
              <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
                customer.profile?.hide_statistics
                  ? 'bg-orange-500/10 text-orange-500'
                  : 'bg-gray-500/10 text-gray-400'
              }`}>
                {customer.profile?.hide_statistics ? 'Hidden' : 'Visible'}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Hide Race Statistics</label>
              <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
                customer.profile?.hide_race_statistics
                  ? 'bg-orange-500/10 text-orange-500'
                  : 'bg-gray-500/10 text-gray-400'
              }`}>
                {customer.profile?.hide_race_statistics ? 'Hidden' : 'Visible'}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Exclude from Rain</label>
              <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
                customer.profile?.exclude_rain
                  ? 'bg-red-500/10 text-red-500'
                  : 'bg-green-500/10 text-green-500'
              }`}>
                {customer.profile?.exclude_rain ? 'Excluded' : 'Included'}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Marketing Emails</label>
              <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
                customer.profile?.receive_marketing_mails
                  ? 'bg-blue-500/10 text-blue-500'
                  : 'bg-gray-500/10 text-gray-400'
              }`}>
                {customer.profile?.receive_marketing_mails ? 'Subscribed' : 'Unsubscribed'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GamingStats;
