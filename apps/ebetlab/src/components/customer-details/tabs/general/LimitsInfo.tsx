import React, { useCallback } from 'react';
import { Shield, CheckCircle, XCircle, Loader2, ExternalLink } from 'lucide-react';
import { fetchGeneralLimits, type GeneralLimitsData } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';

interface LimitsInfoProps {
  customerId: string;
  onNavigateToLimits?: () => void;
}

const LimitsInfo: React.FC<LimitsInfoProps> = ({ customerId, onNavigateToLimits }) => {
  // Default limits data when no limits exist (full access)
  const getDefaultLimitsData = (): GeneralLimitsData => ({
    login_disabled: false,
    bets_disabled: false,
    bonus_disabled: false,
    aml_disabled: false,
    local_aml_disabled: false,
    withdraw_disabled: false,
    chat_disabled: false,
    tip_disabled: false,
    rakeback_disabled: false,
    raffle_disabled: false,
    race_disabled: false,
    casino_bets_disabled: false,
    live_casino_bets_disabled: false,
    sportsbook_disabled: false,
    withdrawal_bypass_otp: false,
    instant_discount_disabled: 0,
    weekly_discount_disabled: 0,
    monthly_discount_disabled: 0,
    pokerklas_enabled: 1
  });

  const fetchLimitsData = useCallback(async () => {
    const result = await fetchGeneralLimits(customerId);

    if (result.success && result.data) {
      // Handle null case - user has full access
      return result.data.data || getDefaultLimitsData();
    } else {
      throw new Error(result.error || 'Failed to load limits data');
    }
  }, [customerId]);

  const { data: limitsData, loading, error } = useRefreshableData<GeneralLimitsData>({
    fetchFn: fetchLimitsData,
    dependencies: [customerId]
  });

  const getStatusIcon = (isDisabled: boolean | number): React.ReactNode => {
    const disabled = Boolean(isDisabled);
    return disabled ? (
      <XCircle className="w-4 h-4 text-red-500" />
    ) : (
      <CheckCircle className="w-4 h-4 text-green-500" />
    );
  };

  const getStatusText = (isDisabled: boolean | number): string => {
    return Boolean(isDisabled) ? 'Disabled' : 'Enabled';
  };

  const getStatusColor = (isDisabled: boolean | number): string => {
    return Boolean(isDisabled) ? 'text-red-500' : 'text-green-500';
  };

  if (loading) {
    return (
      <div className="bg-dark-600 rounded-lg border border-dark-500 p-6">
        <h3 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
          <Shield className="w-5 h-5 text-primary-500" />
          Account Limits
        </h3>
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-gray-400">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>Loading limits...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-dark-600 rounded-lg border border-dark-500 p-6">
        <h3 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
          <Shield className="w-5 h-5 text-primary-500" />
          Account Limits
        </h3>
        <div className="bg-error-500/20 border border-error-500 rounded-md p-4">
          <p className="text-error-500 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!limitsData) {
    return null;
  }

  // Check if user has full access (all restrictions are false/disabled)
  const hasFullAccess = !limitsData.login_disabled &&
                       !limitsData.bets_disabled &&
                       !limitsData.bonus_disabled &&
                       !limitsData.aml_disabled &&
                       !limitsData.local_aml_disabled &&
                       !limitsData.withdraw_disabled &&
                       !limitsData.chat_disabled &&
                       !limitsData.tip_disabled &&
                       !limitsData.rakeback_disabled &&
                       !limitsData.raffle_disabled &&
                       !limitsData.race_disabled &&
                       !limitsData.casino_bets_disabled &&
                       !limitsData.live_casino_bets_disabled &&
                       !limitsData.sportsbook_disabled &&
                       !limitsData.withdrawal_bypass_otp &&
                       limitsData.instant_discount_disabled === 0 &&
                       limitsData.weekly_discount_disabled === 0 &&
                       limitsData.monthly_discount_disabled === 0 &&
                       limitsData.pokerklas_enabled === 1;

  // Group limits by category for better organization
  const limitCategories = [
    {
      title: 'Core Features',
      limits: [
        { key: 'login_disabled', label: 'Login' },
        { key: 'bets_disabled', label: 'Betting' },
        { key: 'withdraw_disabled', label: 'Withdrawals' },
        { key: 'chat_disabled', label: 'Chat' }
      ]
    },
    {
      title: 'Gaming',
      limits: [
        { key: 'casino_bets_disabled', label: 'Casino Bets' },
        { key: 'live_casino_bets_disabled', label: 'Live Casino' },
        { key: 'sportsbook_disabled', label: 'Sportsbook' },
        { key: 'pokerklas_enabled', label: 'Poker Klas', inverted: true }
      ]
    },
    {
      title: 'Promotions',
      limits: [
        { key: 'bonus_disabled', label: 'Bonuses' },
        { key: 'tip_disabled', label: 'Tips' },
        { key: 'rakeback_disabled', label: 'Rakeback' },
        { key: 'raffle_disabled', label: 'Raffles' },
        { key: 'race_disabled', label: 'Races' }
      ]
    },
    {
      title: 'Security & Compliance',
      limits: [
        { key: 'aml_disabled', label: 'AML Checks' },
        { key: 'local_aml_disabled', label: 'Local AML' },
        { key: 'withdrawal_bypass_otp', label: 'Bypass OTP', inverted: true }
      ]
    }
  ];

  return (
    <div>
      {onNavigateToLimits && (
        <div className="flex justify-end mb-4">
          <button
            onClick={onNavigateToLimits}
            className="flex items-center gap-2 px-3 py-2 text-sm text-primary-400 hover:text-primary-300 hover:bg-primary-500/10 rounded-md transition-colors"
            title="Open General Limits tab"
          >
            <ExternalLink className="w-4 h-4" />
            <span>Manage Limits</span>
          </button>
        </div>
      )}

      {/* Full Access Indicator */}
      {hasFullAccess && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <div>
              <p className="text-green-500 font-medium">Full Access Granted</p>
              <p className="text-green-400 text-sm">This customer has unrestricted access to all features.</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {limitCategories.map((category) => (
          <div key={category.title} className="space-y-3">
            <h4 className="text-sm font-medium text-gray-300 uppercase tracking-wide">
              {category.title}
            </h4>
            <div className="space-y-2">
              {category.limits.map((limit) => {
                const value = limitsData[limit.key as keyof GeneralLimitsData];
                // For inverted limits (like pokerklas_enabled), we show the opposite
                const isDisabled = limit.inverted ? !Boolean(value) : Boolean(value);
                
                return (
                  <div key={limit.key} className="flex items-center justify-between py-1">
                    <span className="text-sm text-gray-400">{limit.label}</span>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(isDisabled)}
                      <span className={`text-sm font-medium ${getStatusColor(isDisabled)}`}>
                        {getStatusText(isDisabled)}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Discount Limits - Special handling for numeric values */}
      <div className="mt-6 pt-4 border-t border-dark-500">
        <h4 className="text-sm font-medium text-gray-300 uppercase tracking-wide mb-3">
          Discount Limits
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-400">Instant Discount</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(limitsData.instant_discount_disabled)}
              <span className={`text-sm font-medium ${getStatusColor(limitsData.instant_discount_disabled)}`}>
                {getStatusText(limitsData.instant_discount_disabled)}
              </span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-400">Weekly Discount</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(limitsData.weekly_discount_disabled)}
              <span className={`text-sm font-medium ${getStatusColor(limitsData.weekly_discount_disabled)}`}>
                {getStatusText(limitsData.weekly_discount_disabled)}
              </span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-400">Monthly Discount</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(limitsData.monthly_discount_disabled)}
              <span className={`text-sm font-medium ${getStatusColor(limitsData.monthly_discount_disabled)}`}>
                {getStatusText(limitsData.monthly_discount_disabled)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LimitsInfo;
