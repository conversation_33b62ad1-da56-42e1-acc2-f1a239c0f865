import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Shield, AlertCircle, AlertTriangle, DollarSign, Key } from 'lucide-react';
import { CustomerDetailsData } from '../../../../utils/api';
import { useRefresh } from '../../RefreshContext';
import EditablePersonalInfo from './EditablePersonalInfo';
import AccountInfo from './AccountInfo';
import GamingStats from './GamingStats';
import DepositInfo from './DepositInfo';
import ConflictingIPs from './ConflictingIPs';
import SummaryRange from './SummaryRange';
import CustomerInfo from './CustomerInfo';
import LimitsInfo from './LimitsInfo';
import FinancialLimits from './FinancialLimits';
import PasswordUpdateModal from './PasswordUpdateModal';

interface GeneralTabProps {
  customer: CustomerDetailsData;
  onNavigateToTab?: (tabId: string) => void;
}

interface CollapsibleSectionProps {
  title: string;
  icon: React.ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  icon,
  isExpanded,
  onToggle,
  children
}) => {
  return (
    <div className="bg-dark-600 rounded-lg border border-dark-500">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-4 text-left hover:bg-dark-500 transition-colors rounded-t-lg"
      >
        <div className="flex items-center gap-2">
          {icon}
          <h3 className="text-lg font-semibold text-gray-100">{title}</h3>
        </div>
        {isExpanded ? (
          <ChevronDown className="w-5 h-5 text-gray-400" />
        ) : (
          <ChevronRight className="w-5 h-5 text-gray-400" />
        )}
      </button>
      <div className={`overflow-hidden transition-all duration-300 ${
        isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'
      }`}>
        <div className="p-4 pt-0">
          {children}
        </div>
      </div>
    </div>
  );
};

const GeneralTab: React.FC<GeneralTabProps> = ({ customer, onNavigateToTab }) => {
  const { triggerRefresh } = useRefresh();

  // State for collapsible sections (all closed by default)
  const [expandedSections, setExpandedSections] = useState({
    accountLimits: false,
    financialLimits: false,
    recentActivity: false,
    conflictingIPs: false
  });

  // State for password update modal
  const [showPasswordModal, setShowPasswordModal] = useState(false);

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleProfileUpdate = () => {
    triggerRefresh(); // Refresh the customer data after profile update
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="flex justify-end">
        <button
          onClick={() => setShowPasswordModal(true)}
          className="btn btn-outline btn-sm flex items-center gap-2"
        >
          <Key size={16} />
          Update Password
        </button>
      </div>

      {/* Financial Summary - compact overview */}
      <SummaryRange customerId={customer.id.toString()} />

      {/* Main customer information grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <EditablePersonalInfo customer={customer} onUpdate={handleProfileUpdate} />
        <AccountInfo customer={customer} onUpdate={handleProfileUpdate} />
        <GamingStats customer={customer} />
        <DepositInfo customer={customer} />
      </div>

      {/* Account Limits - collapsible */}
      <CollapsibleSection
        title="Account Limits"
        icon={<Shield className="w-5 h-5 text-primary-500" />}
        isExpanded={expandedSections.accountLimits}
        onToggle={() => toggleSection('accountLimits')}
      >
        <LimitsInfo
          customerId={customer.id.toString()}
          onNavigateToLimits={onNavigateToTab ? () => onNavigateToTab('general-limits') : undefined}
        />
      </CollapsibleSection>

      {/* Financial Limits - collapsible */}
      <CollapsibleSection
        title="Financial Limits"
        icon={<DollarSign className="w-5 h-5 text-primary-500" />}
        isExpanded={expandedSections.financialLimits}
        onToggle={() => toggleSection('financialLimits')}
      >
        <FinancialLimits customerId={customer.id.toString()} />
      </CollapsibleSection>

      {/* Recent Activity - collapsible */}
      <CollapsibleSection
        title="Recent Activity"
        icon={<AlertCircle className="w-5 h-5 text-primary-500" />}
        isExpanded={expandedSections.recentActivity}
        onToggle={() => toggleSection('recentActivity')}
      >
        <CustomerInfo customerId={customer.id.toString()} />
      </CollapsibleSection>

      {/* Conflicting IPs - collapsible */}
      <CollapsibleSection
        title="Conflicting IP Addresses"
        icon={<AlertTriangle className="w-5 h-5 text-warning-500" />}
        isExpanded={expandedSections.conflictingIPs}
        onToggle={() => toggleSection('conflictingIPs')}
      >
        <ConflictingIPs customerId={customer.id.toString()} />
      </CollapsibleSection>

      {/* Password Update Modal */}
      <PasswordUpdateModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        customerId={customer.id.toString()}
        customerUsername={customer.username}
      />
    </div>
  );
};

export default GeneralTab;
