import React, { useCallback } from 'react';
import { TrendingUp, TrendingDown, Calendar, Loader2 } from 'lucide-react';
import { fetchCustomerSummaryRange, type CustomerSummaryRangeData } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';

interface SummaryRangeProps {
  customerId: string;
}

const SummaryRange: React.FC<SummaryRangeProps> = ({ customerId }) => {
  const fetchSummaryData = useCallback(async () => {
    const result = await fetchCustomerSummaryRange(customerId);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load summary data');
    }
  }, [customerId]);

  const { data: summaryData, loading, error } = useRefreshableData<CustomerSummaryRangeData>({
    fetchFn: fetchSummaryData,
    dependencies: [customerId]
  });

  const formatCurrency = (amount: number | string): string => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `$${(isNaN(numAmount) ? 0 : numAmount).toFixed(2)}`;
  };

  const getNetAmount = (deposit: number | string, withdraw: number | string): number => {
    const numDeposit = typeof deposit === 'string' ? parseFloat(deposit) : deposit;
    const numWithdraw = typeof withdraw === 'string' ? parseFloat(withdraw) : withdraw;
    return (isNaN(numDeposit) ? 0 : numDeposit) - (isNaN(numWithdraw) ? 0 : numWithdraw);
  };

  const getNetColor = (net: number): string => {
    if (net > 0) return 'text-green-500';
    if (net < 0) return 'text-red-500';
    return 'text-gray-400';
  };

  if (loading) {
    return (
      <div className="bg-dark-600 rounded-lg border border-dark-500 p-4 mb-6">
        <div className="flex items-center justify-center py-4">
          <div className="flex items-center gap-2 text-gray-400">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm">Loading summary...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-dark-600 rounded-lg border border-dark-500 p-4 mb-6">
        <div className="bg-error-500/20 border border-error-500 rounded-md p-3">
          <p className="text-error-500 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!summaryData) {
    return null;
  }

  const periods = [
    { key: 'day', label: 'Day', data: summaryData.day },
    { key: 'week', label: 'Week', data: summaryData.week },
    { key: 'month', label: 'Month', data: summaryData.month },
    { key: 'year', label: 'Year', data: summaryData.year }
  ];

  return (
    <div className="bg-dark-600 rounded-lg border border-dark-500 p-4 mb-6">
      <div className="flex items-center gap-2 mb-4">
        <Calendar className="w-4 h-4 text-primary-500" />
        <h3 className="text-lg font-semibold text-gray-100">Financial Summary</h3>
      </div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {periods.map((period) => {
          const net = getNetAmount(period.data.deposit, period.data.withdraw);
          
          return (
            <div key={period.key} className="bg-dark-700 rounded-lg p-3 border border-dark-500">
              <div className="text-center">
                <p className="text-xs font-medium text-gray-400 uppercase tracking-wide mb-2">
                  {period.label}
                </p>
                
                {/* Deposit */}
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-1">
                    <TrendingUp className="w-3 h-3 text-green-500" />
                    <span className="text-xs text-gray-400">In</span>
                  </div>
                  <span className="text-sm font-medium text-green-500">
                    {formatCurrency(period.data.deposit)}
                  </span>
                </div>

                {/* Withdraw */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-1">
                    <TrendingDown className="w-3 h-3 text-red-500" />
                    <span className="text-xs text-gray-400">Out</span>
                  </div>
                  <span className="text-sm font-medium text-red-500">
                    {formatCurrency(period.data.withdraw)}
                  </span>
                </div>

                {/* Net */}
                <div className="border-t border-dark-500 pt-2">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-400">Net</span>
                    <span className={`text-sm font-bold ${getNetColor(net)}`}>
                      {net >= 0 ? '+' : ''}{formatCurrency(net)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SummaryRange;
