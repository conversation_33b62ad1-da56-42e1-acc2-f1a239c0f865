import React, { useState } from 'react';
import { Shield, Eye, EyeOff, Mail, Phone, Calendar, Activity, Edit, Save, X } from 'lucide-react';
import { CustomerDetailsData, updateCustomerReferralCode } from '../../../../utils/api';

interface AccountInfoProps {
  customer: CustomerDetailsData;
  onUpdate?: () => void;
}

const AccountInfo: React.FC<AccountInfoProps> = ({ customer, onUpdate }) => {
  const [isEditingReferralCode, setIsEditingReferralCode] = useState(false);
  const [referralCodeValue, setReferralCodeValue] = useState(customer.ref_code || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  const handleReferralCodeEdit = () => {
    setReferralCodeValue(customer.ref_code || '');
    setIsEditingReferralCode(true);
    setError('');
    setSuccess('');
  };

  const handleReferralCodeCancel = () => {
    setReferralCodeValue(customer.ref_code || '');
    setIsEditingReferralCode(false);
    setError('');
    setSuccess('');
  };

  const handleReferralCodeSave = async () => {
    if (!referralCodeValue.trim()) {
      setError('Referral code cannot be empty');
      return;
    }

    setIsSubmitting(true);
    setError('');
    setSuccess('');

    try {
      const result = await updateCustomerReferralCode(customer.id.toString(), referralCodeValue.trim());

      if (result.success) {
        setSuccess('Referral code updated successfully!');
        setIsEditingReferralCode(false);
        if (onUpdate) {
          onUpdate(); // Refresh the customer data
        }

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess('');
        }, 3000);
      } else {
        setError(result.error || 'Failed to update referral code');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };
  const formatTimestamp = (timestamp: number | null | undefined): string => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatDate = (timestamp: number | null | undefined): string => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleDateString();
  };

  const getVerificationBadge = (level: number | undefined): React.ReactNode => {
    const isVerified = (level || 0) >= 2;
    const colorClass = isVerified ? 'bg-success-500/20 text-success-500' : 'bg-warning-500/20 text-warning-500';

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
        Level {level || 0}
      </span>
    );
  };

  const getStatusBadge = (isActive: boolean, isSuspended: boolean): React.ReactNode => {
    let colorClass = 'bg-gray-500/20 text-gray-400';
    let text = 'Unknown';

    if (isSuspended) {
      colorClass = 'bg-red-500/20 text-red-500';
      text = 'Suspended';
    } else if (isActive) {
      colorClass = 'bg-green-500/20 text-green-500';
      text = 'Active';
    } else {
      colorClass = 'bg-yellow-500/20 text-yellow-500';
      text = 'Inactive';
    }

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClass}`}>
        {text}
      </span>
    );
  };

  return (
    <div className="bg-dark-600 rounded-lg border border-dark-500 p-6">
      <h3 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
        <Shield className="w-5 h-5 text-primary-500" />
        Account Information
      </h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Customer ID</label>
          <p className="text-gray-100 font-mono">{customer.id}</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Account Status</label>
          {getStatusBadge(customer.is_active, customer.is_suspended)}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Registration Date</label>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100">{formatDate(customer.registration_ts)}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Last Online</label>
          <div className="flex items-center gap-2">
            <Activity className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100">{formatTimestamp(customer.last_online_at)}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Last Action</label>
          <p className="text-gray-100">{formatTimestamp(customer.last_action)}</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Verification Level</label>
          {getVerificationBadge(customer.profile?.verification_level)}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Email Verified</label>
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4 text-gray-400" />
            <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
              customer.email_verified_at
                ? 'bg-green-500/10 text-green-500'
                : 'bg-red-500/10 text-red-500'
            }`}>
              {customer.email_verified_at ? 'Verified' : 'Not Verified'}
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Phone Verified</label>
          <div className="flex items-center gap-2">
            <Phone className="w-4 h-4 text-gray-400" />
            <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
              customer.phone_verified_at
                ? 'bg-green-500/10 text-green-500'
                : 'bg-red-500/10 text-red-500'
            }`}>
              {customer.phone_verified_at ? 'Verified' : 'Not Verified'}
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Public Profile</label>
          <div className="flex items-center gap-2">
            {customer.is_public ? <Eye className="w-4 h-4 text-green-500" /> : <EyeOff className="w-4 h-4 text-gray-400" />}
            <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
              customer.is_public
                ? 'bg-green-500/10 text-green-500'
                : 'bg-gray-500/10 text-gray-400'
            }`}>
              {customer.is_public ? 'Public' : 'Private'}
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Referral Code</label>
          {isEditingReferralCode ? (
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={referralCodeValue}
                onChange={(e) => setReferralCodeValue(e.target.value)}
                className="flex-1 px-3 py-1 bg-dark-700 border border-dark-500 rounded text-gray-100 font-mono text-sm focus:outline-none focus:border-primary-500"
                placeholder="Enter referral code"
                disabled={isSubmitting}
              />
              <button
                onClick={handleReferralCodeSave}
                disabled={isSubmitting}
                className="p-1 text-green-500 hover:text-green-400 disabled:opacity-50"
                title="Save"
              >
                <Save size={16} />
              </button>
              <button
                onClick={handleReferralCodeCancel}
                disabled={isSubmitting}
                className="p-1 text-gray-400 hover:text-gray-300 disabled:opacity-50"
                title="Cancel"
              >
                <X size={16} />
              </button>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <p className="text-gray-100 font-mono flex-1">{customer.ref_code || '-'}</p>
              <button
                onClick={handleReferralCodeEdit}
                className="p-1 text-gray-400 hover:text-primary-500 transition-colors"
                title="Edit referral code"
              >
                <Edit size={16} />
              </button>
            </div>
          )}
          {error && (
            <p className="text-red-400 text-sm mt-1">{error}</p>
          )}
          {success && (
            <p className="text-green-400 text-sm mt-1">{success}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">2FA Enabled</label>
          <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
            customer.authenticator_enabled
              ? 'bg-green-500/10 text-green-500'
              : 'bg-gray-500/10 text-gray-400'
          }`}>
            {customer.authenticator_enabled ? 'Enabled' : 'Disabled'}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Social Auth</label>
          <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
            customer.social_auth
              ? 'bg-blue-500/10 text-blue-500'
              : 'bg-gray-500/10 text-gray-400'
          }`}>
            {customer.social_auth ? 'Connected' : 'Not Connected'}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Registration IP</label>
          <p className="text-gray-100 font-mono">{customer.registration_ip || '-'}</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Last IP</label>
          <p className="text-gray-100 font-mono">{customer.last_ip || '-'}</p>
        </div>
      </div>
    </div>
  );
};

export default AccountInfo;
