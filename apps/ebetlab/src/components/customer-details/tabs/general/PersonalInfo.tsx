import React from 'react';
import { User, Mail, Phone, Calendar, MapPin } from 'lucide-react';
import { CustomerDetailsData } from '../../../../utils/api';

interface PersonalInfoProps {
  customer: CustomerDetailsData;
}

const PersonalInfo: React.FC<PersonalInfoProps> = ({ customer }) => {
  return (
    <div className="bg-dark-600 rounded-lg border border-dark-500 p-6">
      <h3 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
        <User className="w-5 h-5 text-primary-500" />
        Personal Information
      </h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Full Name</label>
          <div className="flex items-center gap-2">
            <User className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100">
              {customer.profile?.name && customer.profile?.surname 
                ? `${customer.profile.name} ${customer.profile.surname}`
                : '-'
              }
            </p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
          <p className="text-gray-100 font-mono">{customer.username || '-'}</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Email Address</label>
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100">{customer.email || '-'}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Phone Number</label>
          <div className="flex items-center gap-2">
            <Phone className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100">{customer.phone?.full || '-'}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Registration Country</label>
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100 uppercase">{customer.registration_country || '-'}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Current Country</label>
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100 uppercase">{customer.last_country || '-'}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Language</label>
          <p className="text-gray-100 uppercase">{customer.lang || '-'}</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Birthday</label>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100">{customer.profile?.birthday || '-'}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Identity Number</label>
          <p className="text-gray-100 font-mono">{customer.profile?.identity_no || '-'}</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">City</label>
          <p className="text-gray-100">{customer.profile?.city || '-'}</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Postal Code</label>
          <p className="text-gray-100">{customer.profile?.postal_code || '-'}</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Occupation</label>
          <p className="text-gray-100">{customer.profile?.occupation || '-'}</p>
        </div>
      </div>
    </div>
  );
};

export default PersonalInfo;
