import React from 'react';
import { CreditCard, Calendar, Building, Banknote } from 'lucide-react';
import { CustomerDetailsData } from '../../../../utils/api';

interface DepositInfoProps {
  customer: CustomerDetailsData;
}

const DepositInfo: React.FC<DepositInfoProps> = ({ customer }) => {
  const formatTimestamp = (timestamp: number | null | undefined): string => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Note: The new API doesn't include deposit information in the same format
  // This component is prepared for when deposit data becomes available
  // For now, we'll show a placeholder message

  return (
    <div className="bg-dark-600 rounded-lg border border-dark-500 p-6">
      <h3 className="text-xl font-semibold text-gray-100 mb-4 flex items-center gap-2">
        <CreditCard className="w-5 h-5 text-primary-500" />
        Financial Information
      </h3>

      <div className="space-y-4">
        {/* Merchant Information */}
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Merchant ID</label>
          <div className="flex items-center gap-2">
            <Building className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100 font-mono">{customer.merchant_id}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Website ID</label>
          <div className="flex items-center gap-2">
            <Building className="w-4 h-4 text-gray-400" />
            <p className="text-gray-100 font-mono">{customer.website_id}</p>
          </div>
        </div>

        {/* Exclusion Information */}
        {customer.excluded_till && (
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Excluded Until</label>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-red-400" />
              <p className="text-red-400">{customer.excluded_till}</p>
            </div>
          </div>
        )}

        {/* OTP Information */}
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">OTP Step Required</label>
          <div className={`inline-flex items-center gap-2 px-2 py-1 rounded text-sm ${
            customer.otp_step
              ? 'bg-yellow-500/10 text-yellow-500'
              : 'bg-green-500/10 text-green-500'
          }`}>
            {customer.otp_step ? 'Required' : 'Not Required'}
          </div>
        </div>

        {/* Placeholder for deposit information */}
        <div className="border-t border-dark-500 pt-4 mt-6">
          <div className="text-center py-8">
            <CreditCard className="w-12 h-12 text-gray-500 mx-auto mb-2" />
            <p className="text-gray-400 mb-2">Deposit Information</p>
            <p className="text-sm text-gray-500">
              Detailed deposit history will be available in the Transactions tab
            </p>
          </div>
        </div>

        {/* Referral Information */}
        {customer.ref_id && (
          <div className="border-t border-dark-500 pt-4">
            <h4 className="text-lg font-medium text-gray-200 mb-3">Referral Information</h4>
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Referred By ID</label>
              <p className="text-gray-100 font-mono">{customer.ref_id}</p>
            </div>
          </div>
        )}

        {/* Affiliator Information */}
        {customer.affiliator_id && (
          <div className="border-t border-dark-500 pt-4">
            <h4 className="text-lg font-medium text-gray-200 mb-3">Affiliate Information</h4>
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Affiliator ID</label>
              <div className="flex items-center gap-2">
                <Banknote className="w-4 h-4 text-gray-400" />
                <p className="text-gray-100 font-mono">{customer.affiliator_id}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DepositInfo;
