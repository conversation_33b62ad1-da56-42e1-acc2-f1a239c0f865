import React, { useState, useCallback } from 'react';
import { DollarSign, CheckCircle, XCircle, Loader2, Edit, Save, X } from 'lucide-react';
import { fetchFinancialLimits, updateFinancialLimits, type FinancialLimitsData } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';

interface FinancialLimitsProps {
  customerId: string;
}

const FinancialLimits: React.FC<FinancialLimitsProps> = ({ customerId }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');
  const [formData, setFormData] = useState<FinancialLimitsData | null>(null);

  const fetchLimitsData = useCallback(async () => {
    const result = await fetchFinancialLimits(customerId);

    if (result.success && result.data) {
      // The API response is wrapped by proxy server, so data is at result.data.data
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load financial limits data');
    }
  }, [customerId]);

  const { data: limitsData, loading, error: fetchError, refetch } = useRefreshableData<FinancialLimitsData>({
    fetchFn: fetchLimitsData,
    dependencies: [customerId]
  });

  const handleEdit = () => {
    if (limitsData) {
      setFormData({ ...limitsData });
      setIsEditing(true);
      setError('');
      setSuccess('');
    }
  };

  const handleCancel = () => {
    setFormData(null);
    setIsEditing(false);
    setError('');
    setSuccess('');
  };

  const handleInputChange = (field: keyof FinancialLimitsData, value: string | boolean) => {
    if (!formData) return;
    
    setFormData(prev => ({
      ...prev!,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData) return;

    setIsSubmitting(true);
    setError('');
    setSuccess('');

    try {
      const result = await updateFinancialLimits(customerId, formData);

      if (result.success) {
        setSuccess('Financial limits updated successfully!');
        setIsEditing(false);
        setFormData(null);
        await refetch(); // Refresh the data
        
        // Clear success message after 5 seconds
        setTimeout(() => {
          setSuccess('');
        }, 5000);
      } else {
        setError(result.error || 'Failed to update financial limits');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusIcon = (isEnabled: boolean): React.ReactNode => {
    return isEnabled ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const formatCurrency = (amount: string): string => {
    const num = parseFloat(amount);
    return isNaN(num) ? '$0.00' : `$${num.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-6 h-6 animate-spin text-primary-500" />
        <span className="ml-2 text-gray-400">Loading financial limits...</span>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
        <p className="text-red-400 text-sm">Error loading financial limits: {fetchError}</p>
      </div>
    );
  }

  if (!limitsData) {
    return (
      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
        <p className="text-yellow-400 text-sm">No financial limits data available</p>
      </div>
    );
  }

  if (isEditing && formData) {
    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* Withdrawal Settings */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Withdrawal Settings</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-300 mb-2">
                <input
                  type="checkbox"
                  checked={formData.withdraw_able}
                  onChange={(e) => handleInputChange('withdraw_able', e.target.checked)}
                  className="rounded border-dark-500 bg-dark-600 text-primary-500 focus:ring-primary-500"
                />
                Withdrawal Enabled
              </label>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Auto Withdraw Limit
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.auto_withdraw_limit}
                onChange={(e) => handleInputChange('auto_withdraw_limit', e.target.value)}
                className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Minimum Withdraw
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.min_withdraw}
                onChange={(e) => handleInputChange('min_withdraw', e.target.value)}
                className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Maximum Withdraw
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.max_withdraw}
                onChange={(e) => handleInputChange('max_withdraw', e.target.value)}
                className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
        </div>

        {/* Feature Permissions */}
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Feature Permissions</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { key: 'rakeback_able', label: 'Rakeback' },
              { key: 'reload_able', label: 'Reload' },
              { key: 'tips_able', label: 'Tips' },
              { key: 'affiliate_funds_able', label: 'Affiliate Funds' },
              { key: 'boost_able', label: 'Boost' },
              { key: 'race_able', label: 'Race' },
              { key: 'raffle_able', label: 'Raffle' }
            ].map((feature) => (
              <div key={feature.key}>
                <label className="flex items-center gap-2 text-sm font-medium text-gray-300">
                  <input
                    type="checkbox"
                    checked={formData[feature.key as keyof FinancialLimitsData] as boolean}
                    onChange={(e) => handleInputChange(feature.key as keyof FinancialLimitsData, e.target.checked)}
                    className="rounded border-dark-500 bg-dark-600 text-primary-500 focus:ring-primary-500"
                  />
                  {feature.label} Enabled
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            type="submit"
            disabled={isSubmitting}
            className="btn btn-primary flex items-center gap-2"
          >
            {isSubmitting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
          <button
            type="button"
            onClick={handleCancel}
            disabled={isSubmitting}
            className="btn btn-outline flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            Cancel
          </button>
        </div>
      </form>
    );
  }

  // Display mode
  return (
    <div className="space-y-6">
      {success && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
          <p className="text-green-400 text-sm">{success}</p>
        </div>
      )}

      <div className="flex justify-end">
        <button
          onClick={handleEdit}
          className="btn btn-outline btn-sm flex items-center gap-2"
        >
          <Edit size={16} />
          Edit Limits
        </button>
      </div>

      {/* Withdrawal Settings Display */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
        <h4 className="text-lg font-semibold text-gray-100 mb-4">Withdrawal Settings</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center justify-between">
            <span className="text-gray-300">Withdrawal Status</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(limitsData.withdraw_able)}
              <span className={limitsData.withdraw_able ? 'text-green-400' : 'text-red-400'}>
                {limitsData.withdraw_able ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-300">Auto Withdraw Limit</span>
            <span className="text-gray-100 font-mono">{formatCurrency(limitsData.auto_withdraw_limit)}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-300">Minimum Withdraw</span>
            <span className="text-gray-100 font-mono">{formatCurrency(limitsData.min_withdraw)}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-300">Maximum Withdraw</span>
            <span className="text-gray-100 font-mono">{formatCurrency(limitsData.max_withdraw)}</span>
          </div>
        </div>
      </div>

      {/* Feature Permissions Display */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
        <h4 className="text-lg font-semibold text-gray-100 mb-4">Feature Permissions</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {[
            { key: 'rakeback_able', label: 'Rakeback' },
            { key: 'reload_able', label: 'Reload' },
            { key: 'tips_able', label: 'Tips' },
            { key: 'affiliate_funds_able', label: 'Affiliate Funds' },
            { key: 'boost_able', label: 'Boost' },
            { key: 'race_able', label: 'Race' },
            { key: 'raffle_able', label: 'Raffle' }
          ].map((feature) => (
            <div key={feature.key} className="flex items-center justify-between">
              <span className="text-gray-300">{feature.label}</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(limitsData[feature.key as keyof FinancialLimitsData] as boolean)}
                <span className={(limitsData[feature.key as keyof FinancialLimitsData] as boolean) ? 'text-green-400' : 'text-red-400'}>
                  {(limitsData[feature.key as keyof FinancialLimitsData] as boolean) ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FinancialLimits;
