import React, { useState } from 'react';
import { User, Mail, Phone, MapPin, Calendar, Save, X, Edit } from 'lucide-react';
import { CustomerDetailsData, updateCustomerProfile } from '../../../../utils/api';

interface EditablePersonalInfoProps {
  customer: CustomerDetailsData;
  onUpdate: () => void;
}

const EditablePersonalInfo: React.FC<EditablePersonalInfoProps> = ({ customer, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');
  
  // Form state
  const [formData, setFormData] = useState({
    username: customer.username || '',
    phone: customer.phone?.number || '',
    country_code: customer.phone?.code || '',
    email: customer.email || '',
    name: customer.profile?.name || '',
    surname: customer.profile?.surname || '',
    occupation: customer.profile?.occupation || '',
    identity_no: customer.profile?.identity_no || '',
    residential: customer.profile?.residential || '',
    birthday: customer.profile?.birthday || '',
    city: customer.profile?.city || '',
    ghost_mode: customer.profile?.ghost_mode || false,
    hide_statistics: customer.profile?.hide_statistics || false,
    hide_race_statistics: customer.profile?.hide_race_statistics || false,
    exclude_rain: customer.profile?.exclude_rain || false,
    receive_marketing_mails: customer.profile?.receive_marketing_mails || false
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const profileId = customer.profile?.id;
      if (!profileId) {
        setError('Profile ID not found');
        return;
      }

      const result = await updateCustomerProfile(profileId, formData);

      if (result.success) {
        setIsEditing(false);
        onUpdate(); // Refresh the customer data
      } else {
        setError(result.error || 'Failed to update profile');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    // Reset form data to original values
    setFormData({
      username: customer.username || '',
      phone: customer.phone?.number || '',
      country_code: customer.phone?.code || '',
      email: customer.email || '',
      name: customer.profile?.name || '',
      surname: customer.profile?.surname || '',
      occupation: customer.profile?.occupation || '',
      identity_no: customer.profile?.identity_no || '',
      residential: customer.profile?.residential || '',
      birthday: customer.profile?.birthday || '',
      city: customer.profile?.city || '',
      ghost_mode: customer.profile?.ghost_mode || false,
      hide_statistics: customer.profile?.hide_statistics || false,
      hide_race_statistics: customer.profile?.hide_race_statistics || false,
      exclude_rain: customer.profile?.exclude_rain || false,
      receive_marketing_mails: customer.profile?.receive_marketing_mails || false
    });
    setIsEditing(false);
    setError('');
  };

  if (!isEditing) {
    // Display mode
    return (
      <div className="bg-dark-600 rounded-lg border border-dark-500 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-100 flex items-center gap-2">
            <User className="w-5 h-5 text-primary-500" />
            Personal Information
          </h3>
          <button
            onClick={() => setIsEditing(true)}
            className="btn btn-outline btn-sm flex items-center gap-2"
          >
            <Edit size={16} />
            Edit
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Full Name</label>
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-400" />
              <p className="text-gray-100">
                {customer.profile?.name && customer.profile?.surname 
                  ? `${customer.profile.name} ${customer.profile.surname}`
                  : '-'
                }
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
            <p className="text-gray-100 font-mono">{customer.username || '-'}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Email Address</label>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-gray-400" />
              <p className="text-gray-100">{customer.email || '-'}</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Phone Number</label>
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4 text-gray-400" />
              <p className="text-gray-100">{customer.phone?.full || '-'}</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Registration Country</label>
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-gray-400" />
              <p className="text-gray-100 uppercase">{customer.registration_country || '-'}</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Current Country</label>
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-gray-400" />
              <p className="text-gray-100 uppercase">{customer.last_country || '-'}</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Language</label>
            <p className="text-gray-100 uppercase">{customer.lang || '-'}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Birthday</label>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-400" />
              <p className="text-gray-100">{customer.profile?.birthday || '-'}</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Identity Number</label>
            <p className="text-gray-100 font-mono">{customer.profile?.identity_no || '-'}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">City</label>
            <p className="text-gray-100">{customer.profile?.city || '-'}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Occupation</label>
            <p className="text-gray-100">{customer.profile?.occupation || '-'}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Residential Address</label>
            <p className="text-gray-100">{customer.profile?.residential || '-'}</p>
          </div>
        </div>
      </div>
    );
  }

  // Edit mode
  return (
    <div className="bg-dark-600 rounded-lg border border-dark-500 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold text-gray-100 flex items-center gap-2">
          <User className="w-5 h-5 text-primary-500" />
          Edit Personal Information
        </h3>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
              First Name
            </label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              disabled={isSubmitting}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div>
            <label htmlFor="surname" className="block text-sm font-medium text-gray-300 mb-2">
              Last Name
            </label>
            <input
              type="text"
              id="surname"
              value={formData.surname}
              onChange={(e) => handleInputChange('surname', e.target.value)}
              disabled={isSubmitting}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2">
            Username
          </label>
          <input
            type="text"
            id="username"
            value={formData.username}
            onChange={(e) => handleInputChange('username', e.target.value)}
            disabled={isSubmitting}
            className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            disabled={isSubmitting}
            className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        {/* Phone Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="country_code" className="block text-sm font-medium text-gray-300 mb-2">
              Country Code
            </label>
            <input
              type="text"
              id="country_code"
              value={formData.country_code}
              onChange={(e) => handleInputChange('country_code', e.target.value)}
              disabled={isSubmitting}
              placeholder="380"
              className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div className="md:col-span-2">
            <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
              Phone Number
            </label>
            <input
              type="text"
              id="phone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              disabled={isSubmitting}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Additional Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="birthday" className="block text-sm font-medium text-gray-300 mb-2">
              Birthday (DD-MM-YYYY)
            </label>
            <input
              type="text"
              id="birthday"
              value={formData.birthday}
              onChange={(e) => handleInputChange('birthday', e.target.value)}
              disabled={isSubmitting}
              placeholder="01-01-1980"
              className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-300 mb-2">
              City
            </label>
            <input
              type="text"
              id="city"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              disabled={isSubmitting}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        <div>
          <label htmlFor="identity_no" className="block text-sm font-medium text-gray-300 mb-2">
            Identity Number
          </label>
          <input
            type="text"
            id="identity_no"
            value={formData.identity_no}
            onChange={(e) => handleInputChange('identity_no', e.target.value)}
            disabled={isSubmitting}
            className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        <div>
          <label htmlFor="occupation" className="block text-sm font-medium text-gray-300 mb-2">
            Occupation
          </label>
          <input
            type="text"
            id="occupation"
            value={formData.occupation}
            onChange={(e) => handleInputChange('occupation', e.target.value)}
            disabled={isSubmitting}
            className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        <div>
          <label htmlFor="residential" className="block text-sm font-medium text-gray-300 mb-2">
            Residential Address
          </label>
          <textarea
            id="residential"
            value={formData.residential}
            onChange={(e) => handleInputChange('residential', e.target.value)}
            disabled={isSubmitting}
            rows={3}
            className="w-full px-3 py-2 bg-dark-700 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        {/* Privacy Settings */}
        <div className="border-t border-dark-500 pt-4">
          <h4 className="text-lg font-medium text-gray-200 mb-3">Privacy Settings</h4>

          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="ghost_mode"
                checked={formData.ghost_mode}
                onChange={(e) => handleInputChange('ghost_mode', e.target.checked)}
                disabled={isSubmitting}
                className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-500 rounded focus:ring-primary-500 focus:ring-2"
              />
              <label htmlFor="ghost_mode" className="text-sm text-gray-300">
                Enable Ghost Mode
              </label>
            </div>

            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="hide_statistics"
                checked={formData.hide_statistics}
                onChange={(e) => handleInputChange('hide_statistics', e.target.checked)}
                disabled={isSubmitting}
                className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-500 rounded focus:ring-primary-500 focus:ring-2"
              />
              <label htmlFor="hide_statistics" className="text-sm text-gray-300">
                Hide Statistics
              </label>
            </div>

            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="hide_race_statistics"
                checked={formData.hide_race_statistics}
                onChange={(e) => handleInputChange('hide_race_statistics', e.target.checked)}
                disabled={isSubmitting}
                className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-500 rounded focus:ring-primary-500 focus:ring-2"
              />
              <label htmlFor="hide_race_statistics" className="text-sm text-gray-300">
                Hide Race Statistics
              </label>
            </div>

            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="exclude_rain"
                checked={formData.exclude_rain}
                onChange={(e) => handleInputChange('exclude_rain', e.target.checked)}
                disabled={isSubmitting}
                className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-500 rounded focus:ring-primary-500 focus:ring-2"
              />
              <label htmlFor="exclude_rain" className="text-sm text-gray-300">
                Exclude from Rain
              </label>
            </div>

            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="receive_marketing_mails"
                checked={formData.receive_marketing_mails}
                onChange={(e) => handleInputChange('receive_marketing_mails', e.target.checked)}
                disabled={isSubmitting}
                className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-500 rounded focus:ring-primary-500 focus:ring-2"
              />
              <label htmlFor="receive_marketing_mails" className="text-sm text-gray-300">
                Receive Marketing Emails
              </label>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-error-500/20 border border-error-500 rounded-md p-3">
            <p className="text-error-500 text-sm">{error}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <button
            type="button"
            onClick={handleCancel}
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 bg-dark-700 text-gray-300 rounded-md hover:bg-dark-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            <X size={16} />
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Saving...
              </>
            ) : (
              <>
                <Save size={16} />
                Save Changes
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditablePersonalInfo;
