import React, { useState, useCallback } from 'react';
import { AlertTriangle, Users, Globe, ChevronDown, ChevronUp, Loader2 } from 'lucide-react';
import { fetchCustomerSessionCheck, type SessionCheckData } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';

interface ConflictingIPsProps {
  customerId: string;
}

const ConflictingIPs: React.FC<ConflictingIPsProps> = ({ customerId }) => {
  const [expandedIPs, setExpandedIPs] = useState<Set<string>>(new Set());

  const fetchSessionData = useCallback(async () => {
    const result = await fetchCustomerSessionCheck(customerId);

    if (result.success && result.data) {
      // The API response has an extra layer: data.data.data
      return result.data.data?.data || [];
    } else {
      throw new Error(result.error || 'Failed to load session data');
    }
  }, [customerId]);

  const { data: sessionData, loading, error } = useRefreshableData<SessionCheckData[]>({
    fetchFn: fetchSessionData,
    dependencies: [customerId]
  });

  const toggleIPExpansion = (ip: string) => {
    const newExpanded = new Set(expandedIPs);
    if (newExpanded.has(ip)) {
      newExpanded.delete(ip);
    } else {
      newExpanded.add(ip);
    }
    setExpandedIPs(newExpanded);
  };

  const getTotalConflictingUsers = () => {
    return sessionData?.reduce((total, item) => total + item.usernames.length, 0) || 0;
  };

  const getUniqueConflictingUsers = () => {
    if (!sessionData) return 0;
    const allUsernames = sessionData.flatMap(item => item.usernames);
    return new Set(allUsernames).size;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center gap-2 text-gray-400">
          <Loader2 className="w-5 h-5 animate-spin" />
          <span>Loading session data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-error-500/20 border border-error-500 rounded-md p-4">
        <p className="text-error-500 text-sm">{error}</p>
      </div>
    );
  }

  if (!sessionData || !sessionData.length) {
    return (
      <div className="text-center py-8">
        <div className="bg-success-500/20 border border-success-500 rounded-md p-4">
          <p className="text-success-500 text-sm font-medium">No conflicting IP addresses found</p>
          <p className="text-gray-400 text-xs mt-1">This customer has no shared IP addresses with other users</p>
        </div>
      </div>
    );
  }

  return (
    <div>

      {/* Summary Statistics */}
      <div className="bg-dark-700 rounded-lg p-4 border border-dark-500 mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Users className="w-4 h-4 text-red-500" />
          <span className="text-sm font-medium text-gray-400">Conflicting Users</span>
        </div>
        <div className="flex items-center gap-4">
          <div>
            <p className="text-2xl font-bold text-red-500">{getUniqueConflictingUsers()}</p>
            <p className="text-xs text-gray-500">Unique Users</p>
          </div>
          <div className="text-gray-500">/</div>
          <div>
            <p className="text-2xl font-bold text-orange-500">{getTotalConflictingUsers()}</p>
            <p className="text-xs text-gray-500">Total Instances</p>
          </div>
        </div>
      </div>

      {/* IP Address Details */}
      <div className="space-y-3">
        {sessionData?.map((item, index) => (
          <div key={index} className="bg-dark-700 rounded-lg border border-dark-500">
            <button
              onClick={() => toggleIPExpansion(item.ip)}
              className="w-full flex items-center justify-between p-4 text-left hover:bg-dark-600 transition-colors rounded-lg"
            >
              <div className="flex items-center gap-3">
                <Globe className="w-4 h-4 text-warning-500" />
                <div>
                  <p className="font-mono text-gray-100 font-medium">{item.ip}</p>
                  <p className="text-sm text-gray-400">
                    {item.usernames.length} conflicting user{item.usernames.length !== 1 ? 's' : ''}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  item.usernames.length > 5
                    ? 'bg-red-500/20 text-red-500'
                    : item.usernames.length > 2
                    ? 'bg-orange-500/20 text-orange-500'
                    : 'bg-yellow-500/20 text-yellow-500'
                }`}>
                  {item.usernames.length} users
                </span>
                {expandedIPs.has(item.ip) ? (
                  <ChevronUp className="w-4 h-4 text-gray-400" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                )}
              </div>
            </button>

            {expandedIPs.has(item.ip) && (
              <div className="px-4 pb-4">
                <div className="border-t border-dark-500 pt-3">
                  <p className="text-sm font-medium text-gray-300 mb-2">Usernames sharing this IP:</p>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                    {item.usernames.map((username, userIndex) => (
                      <div
                        key={userIndex}
                        className="bg-dark-600 rounded px-2 py-1 text-sm font-mono text-gray-200 border border-dark-400"
                      >
                        {username}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Warning Notice */}
      <div className="mt-6 bg-warning-500/10 border border-warning-500/20 rounded-lg p-4">
        <div className="flex items-start gap-2">
          <AlertTriangle className="w-4 h-4 text-warning-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-warning-500 font-medium text-sm">Security Notice</p>
            <p className="text-gray-400 text-xs mt-1">
              Multiple users sharing the same IP address may indicate account sharing, VPN usage, or potential fraud. 
              Please investigate these connections carefully.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConflictingIPs;
