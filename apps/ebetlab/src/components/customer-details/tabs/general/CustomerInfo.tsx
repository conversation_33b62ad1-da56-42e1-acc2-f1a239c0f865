import React, { useCallback } from 'react';
import { ArrowDownCircle, ArrowUpCircle, Gift, Percent, Calendar, Loader2, AlertCircle } from 'lucide-react';
import { fetchCustomerInfo, type CustomerInfoData } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';

interface CustomerInfoProps {
  customerId: string;
}

const CustomerInfo: React.FC<CustomerInfoProps> = ({ customerId }) => {
  const fetchInfoData = useCallback(async () => {
    const result = await fetchCustomerInfo(customerId);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load customer info');
    }
  }, [customerId]);

  const { data: infoData, loading, error } = useRefreshableData<CustomerInfoData>({
    fetchFn: fetchInfoData,
    dependencies: [customerId]
  });

  const formatCurrency = (amount: string, currency: string): string => {
    return `${parseFloat(amount).toFixed(2)} ${currency}`;
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center gap-2 text-gray-400">
          <Loader2 className="w-5 h-5 animate-spin" />
          <span>Loading recent activity...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-error-500/20 border border-error-500 rounded-md p-4">
        <p className="text-error-500 text-sm">{error}</p>
      </div>
    );
  }

  if (!infoData) {
    return null;
  }

  return (
    <div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Last Deposit */}
        <div className="bg-dark-700 rounded-lg p-4 border border-dark-500">
          <div className="flex items-center gap-2 mb-3">
            <ArrowDownCircle className="w-4 h-4 text-green-500" />
            <h4 className="font-medium text-gray-200">Last Deposit</h4>
          </div>
          
          {infoData.last_deposit ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Amount</span>
                <span className="font-medium text-green-500">
                  {formatCurrency(infoData.last_deposit.amount, infoData.last_deposit.currency)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Date</span>
                <span className="text-sm text-gray-300">
                  {formatTimestamp(infoData.last_deposit.timestamp)}
                </span>
              </div>
              {infoData.last_deposit.provider && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400">Provider</span>
                  <span className="text-sm text-gray-300">{infoData.last_deposit.provider}</span>
                </div>
              )}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">ID</span>
                <span className="text-xs font-mono text-gray-400">#{infoData.last_deposit.id}</span>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500 text-sm">No deposits found</p>
            </div>
          )}
        </div>

        {/* Last Withdraw */}
        <div className="bg-dark-700 rounded-lg p-4 border border-dark-500">
          <div className="flex items-center gap-2 mb-3">
            <ArrowUpCircle className="w-4 h-4 text-red-500" />
            <h4 className="font-medium text-gray-200">Last Withdraw</h4>
          </div>
          
          {infoData.last_withdraw ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Amount</span>
                <span className="font-medium text-red-500">
                  {formatCurrency(infoData.last_withdraw.amount, infoData.last_withdraw.currency)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Date</span>
                <span className="text-sm text-gray-300">
                  {formatTimestamp(infoData.last_withdraw.timestamp)}
                </span>
              </div>
              {infoData.last_withdraw.provider && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400">Provider</span>
                  <span className="text-sm text-gray-300">{infoData.last_withdraw.provider}</span>
                </div>
              )}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">ID</span>
                <span className="text-xs font-mono text-gray-400">#{infoData.last_withdraw.id}</span>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500 text-sm">No withdrawals found</p>
            </div>
          )}
        </div>

        {/* Last Discount */}
        <div className="bg-dark-700 rounded-lg p-4 border border-dark-500">
          <div className="flex items-center gap-2 mb-3">
            <Percent className="w-4 h-4 text-blue-500" />
            <h4 className="font-medium text-gray-200">Last Discount</h4>
          </div>
          
          {infoData.last_discount ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Amount</span>
                <span className="font-medium text-blue-500">
                  {formatCurrency(infoData.last_discount.amount, infoData.last_discount.code)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Date</span>
                <span className="text-sm text-gray-300">
                  {formatTimestamp(infoData.last_discount.timestamp)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Before</span>
                <span className="text-sm text-gray-300">
                  {formatCurrency(infoData.last_discount.before_balance, infoData.last_discount.code)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">After</span>
                <span className="text-sm text-gray-300">
                  {formatCurrency(infoData.last_discount.after_balance, infoData.last_discount.code)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">ID</span>
                <span className="text-xs font-mono text-gray-400">#{infoData.last_discount.id}</span>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500 text-sm">No discounts found</p>
            </div>
          )}
        </div>

        {/* Last Bonus */}
        <div className="bg-dark-700 rounded-lg p-4 border border-dark-500">
          <div className="flex items-center gap-2 mb-3">
            <Gift className="w-4 h-4 text-purple-500" />
            <h4 className="font-medium text-gray-200">Last Bonus</h4>
          </div>
          
          {infoData.last_bonus ? (
            <div className="space-y-2">
              {/* Bonus details would go here when available */}
              <div className="text-center py-4">
                <p className="text-purple-500 text-sm">Bonus data available</p>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500 text-sm">No bonuses found</p>
            </div>
          )}
        </div>
      </div>

      {/* Corrections Info */}
      {infoData.corrections && infoData.corrections.length > 0 && (
        <div className="mt-4 bg-dark-700 rounded-lg p-4 border border-dark-500">
          <div className="flex items-center gap-2 mb-3">
            <Calendar className="w-4 h-4 text-orange-500" />
            <h4 className="font-medium text-gray-200">Corrections</h4>
          </div>
          <p className="text-orange-500 text-sm">
            {infoData.corrections.length} correction(s) available
          </p>
        </div>
      )}
    </div>
  );
};

export default CustomerInfo;
