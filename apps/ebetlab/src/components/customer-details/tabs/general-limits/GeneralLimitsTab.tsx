import React, { useState, useCallback, useEffect } from 'react';
import { Shield, Save, Loader2, RotateCcw, CheckCircle, AlertCircle } from 'lucide-react';
import { fetchGeneralLimits, applyGeneralLimits, type GeneralLimitsData } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';

interface GeneralLimitsTabProps {
  customer: { id: number };
}

const GeneralLimitsTab: React.FC<GeneralLimitsTabProps> = ({ customer }) => {
  const [formData, setFormData] = useState<GeneralLimitsData | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [isFullAccess, setIsFullAccess] = useState(false);
  const [isApplying, setIsApplying] = useState(false);
  const [applySuccess, setApplySuccess] = useState<string | null>(null);
  const [applyError, setApplyError] = useState<string | null>(null);

  // Default limits data when no limits exist (full access)
  const getDefaultLimitsData = (): GeneralLimitsData => ({
    login_disabled: false,
    bets_disabled: false,
    bonus_disabled: false,
    aml_disabled: false,
    local_aml_disabled: false,
    withdraw_disabled: false,
    chat_disabled: false,
    tip_disabled: false,
    rakeback_disabled: false,
    raffle_disabled: false,
    race_disabled: false,
    casino_bets_disabled: false,
    live_casino_bets_disabled: false,
    sportsbook_disabled: false,
    withdrawal_bypass_otp: false,
    instant_discount_disabled: 0,
    weekly_discount_disabled: 0,
    monthly_discount_disabled: 0,
    pokerklas_enabled: 1
  });

  const fetchLimitsData = useCallback(async () => {
    const result = await fetchGeneralLimits(customer.id.toString());

    if (result.success && result.data) {
      // Handle null case - user has full access
      return result.data.data || getDefaultLimitsData();
    } else {
      throw new Error(result.error || 'Failed to load limits data');
    }
  }, [customer.id]);

  const { data: limitsData, loading, error, refetch } = useRefreshableData<GeneralLimitsData>({
    fetchFn: fetchLimitsData,
    dependencies: [customer.id]
  });

  // Check if user has full access (all restrictions are false/disabled)
  const checkFullAccess = (data: GeneralLimitsData): boolean => {
    return !data.login_disabled &&
           !data.bets_disabled &&
           !data.bonus_disabled &&
           !data.aml_disabled &&
           !data.local_aml_disabled &&
           !data.withdraw_disabled &&
           !data.chat_disabled &&
           !data.tip_disabled &&
           !data.rakeback_disabled &&
           !data.raffle_disabled &&
           !data.race_disabled &&
           !data.casino_bets_disabled &&
           !data.live_casino_bets_disabled &&
           !data.sportsbook_disabled &&
           !data.withdrawal_bypass_otp &&
           data.instant_discount_disabled === 0 &&
           data.weekly_discount_disabled === 0 &&
           data.monthly_discount_disabled === 0 &&
           data.pokerklas_enabled === 1;
  };

  // Initialize form data when limits data is loaded
  useEffect(() => {
    if (limitsData) {
      setFormData({ ...limitsData });
      setIsFullAccess(checkFullAccess(limitsData));
      setHasChanges(false);
      // Clear any previous apply messages
      setApplySuccess(null);
      setApplyError(null);
    }
  }, [limitsData]);

  const handleFullAccessChange = (checked: boolean) => {
    if (!formData) return;

    // Only allow unchecking if there are currently no restrictions
    // If checked is true, we allow it (to clear all restrictions)
    if (checked) {
      setIsFullAccess(true);
      // Set all restrictions to false/disabled (full access)
      setFormData(getDefaultLimitsData());
      setHasChanges(true);
    }
    // If checked is false, we don't do anything because the checkbox should be disabled
    // when there are no restrictions (it should automatically be checked)
  };

  const handleCheckboxChange = (key: keyof GeneralLimitsData, value: boolean | number) => {
    if (!formData) return;

    const updatedData = { ...formData, [key]: value };
    setFormData(updatedData);

    // Always check if all restrictions are now disabled (full access)
    // This will automatically check the full access checkbox when no restrictions are active
    setIsFullAccess(checkFullAccess(updatedData));

    setHasChanges(true);
  };

  const handleReset = () => {
    if (limitsData) {
      setFormData({ ...limitsData });
      setIsFullAccess(checkFullAccess(limitsData));
      setHasChanges(false);
      setApplySuccess(null);
      setApplyError(null);
    }
  };

  const handleApply = async () => {
    if (!formData) return;

    setIsApplying(true);
    setApplySuccess(null);
    setApplyError(null);

    try {
      const result = await applyGeneralLimits(customer.id.toString(), formData);

      if (result.success && result.data?.success) {
        setApplySuccess('General limits have been successfully applied!');
        setHasChanges(false);
        // Refresh the data to get the latest state
        await refetch();

        // Clear success message after 5 seconds
        setTimeout(() => {
          setApplySuccess(null);
        }, 5000);
      } else {
        setApplyError(result.error || 'Failed to apply general limits');
      }
    } catch (error) {
      setApplyError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsApplying(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading general limits...</p>
        </div>
      </div>
    );
  }

  if (error || !formData) {
    return (
      <div className="bg-error-500/20 border border-error-500 rounded-md p-6 text-center">
        <p className="text-error-500 text-lg">{error || 'Failed to load limits data'}</p>
      </div>
    );
  }

  // Group limits by category for better organization
  const limitCategories = [
    {
      title: 'Core Features',
      description: 'Basic account functionality controls',
      limits: [
        { key: 'login_disabled', label: 'Disable Login', description: 'Prevent user from logging in' },
        { key: 'bets_disabled', label: 'Disable Betting', description: 'Block all betting activities' },
        { key: 'withdraw_disabled', label: 'Disable Withdrawals', description: 'Block withdrawal requests' },
        { key: 'chat_disabled', label: 'Disable Chat', description: 'Block chat functionality' }
      ]
    },
    {
      title: 'Gaming Features',
      description: 'Game-specific access controls',
      limits: [
        { key: 'casino_bets_disabled', label: 'Disable Casino Bets', description: 'Block casino game betting' },
        { key: 'live_casino_bets_disabled', label: 'Disable Live Casino', description: 'Block live casino games' },
        { key: 'sportsbook_disabled', label: 'Disable Sportsbook', description: 'Block sports betting' },
        { key: 'pokerklas_enabled', label: 'Enable Poker Klas', description: 'Allow poker game access', inverted: true }
      ]
    },
    {
      title: 'Promotional Features',
      description: 'Bonus and promotional controls',
      limits: [
        { key: 'bonus_disabled', label: 'Disable Bonuses', description: 'Block bonus eligibility' },
        { key: 'tip_disabled', label: 'Disable Tips', description: 'Block tip functionality' },
        { key: 'rakeback_disabled', label: 'Disable Rakeback', description: 'Block rakeback rewards' },
        { key: 'raffle_disabled', label: 'Disable Raffles', description: 'Block raffle participation' },
        { key: 'race_disabled', label: 'Disable Races', description: 'Block race participation' }
      ]
    },
    {
      title: 'Security & Compliance',
      description: 'Security and compliance controls',
      limits: [
        { key: 'aml_disabled', label: 'Disable AML Checks', description: 'Skip AML verification' },
        { key: 'local_aml_disabled', label: 'Disable Local AML', description: 'Skip local AML checks' },
        { key: 'withdrawal_bypass_otp', label: 'Bypass OTP for Withdrawals', description: 'Skip OTP verification', inverted: true }
      ]
    },
    {
      title: 'Discount Controls',
      description: 'Discount and promotional credit controls',
      limits: [
        { key: 'instant_discount_disabled', label: 'Disable Instant Discounts', description: 'Block instant discount offers', numeric: true },
        { key: 'weekly_discount_disabled', label: 'Disable Weekly Discounts', description: 'Block weekly discount offers', numeric: true },
        { key: 'monthly_discount_disabled', label: 'Disable Monthly Discounts', description: 'Block monthly discount offers', numeric: true }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-100 flex items-center gap-2">
            <Shield className="w-6 h-6 text-primary-500" />
            General Limits
          </h2>
          <p className="text-gray-400 mt-1">Configure account restrictions and feature access</p>
        </div>
        
        <div className="flex items-center gap-3">
          {hasChanges && (
            <button
              onClick={handleReset}
              disabled={isApplying}
              className="btn btn-outline flex items-center gap-2"
            >
              <RotateCcw size={16} />
              Reset
            </button>
          )}
          <button
            onClick={handleApply}
            disabled={!hasChanges || isApplying}
            className={`btn flex items-center gap-2 ${
              hasChanges && !isApplying
                ? 'btn-primary'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isApplying ? (
              <Loader2 size={16} className="animate-spin" />
            ) : (
              <Save size={16} />
            )}
            {isApplying ? 'Applying...' : 'Apply Changes'}
          </button>
        </div>
      </div>

      {/* Full Access Toggle */}
      <div className={`rounded-lg p-4 border ${
        isFullAccess
          ? 'bg-green-500/10 border-green-500/20'
          : 'bg-primary-500/10 border-primary-500/20'
      }`}>
        <div className="flex items-start gap-3">
          <input
            type="checkbox"
            id="full-access"
            checked={isFullAccess}
            disabled={isFullAccess}
            onChange={(e) => handleFullAccessChange(e.target.checked)}
            className={`mt-1 w-5 h-5 rounded focus:ring-2 ${
              isFullAccess
                ? 'text-green-500 bg-dark-500 border-green-400 focus:ring-green-500 cursor-not-allowed'
                : 'text-primary-500 bg-dark-500 border-primary-400 focus:ring-primary-500 cursor-pointer'
            }`}
          />
          <div className="flex-1">
            <label
              htmlFor="full-access"
              className={`text-base font-semibold ${
                isFullAccess
                  ? 'text-green-400 cursor-not-allowed'
                  : 'text-primary-400 cursor-pointer'
              }`}
            >
              Full Access {isFullAccess && '✓'}
            </label>
            <p className={`text-sm mt-1 ${
              isFullAccess
                ? 'text-green-300'
                : 'text-primary-300'
            }`}>
              {isFullAccess
                ? 'This customer has unrestricted access to all features. Click any restriction below to modify access.'
                : 'Click to grant unrestricted access to all features. This will clear all restrictions below.'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Limits Categories */}
      <div className="space-y-8">
        {limitCategories.map((category) => (
          <div key={category.title} className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-100">{category.title}</h3>
              <p className="text-sm text-gray-400 mt-1">{category.description}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {category.limits.map((limit) => {
                const value = formData[limit.key as keyof GeneralLimitsData];
                // For inverted limits, we show the opposite of the stored value
                const isChecked = limit.inverted ? Boolean(value) : Boolean(value);
                
                return (
                  <div key={limit.key} className="flex items-start gap-3 p-3 bg-dark-600 rounded-lg border border-dark-500">
                    <input
                      type="checkbox"
                      id={limit.key}
                      checked={isChecked}
                      onChange={(e) => {
                        const newValue = limit.inverted ? e.target.checked : e.target.checked;
                        // For numeric fields, convert boolean to number
                        const finalValue = limit.numeric ? (newValue ? 1 : 0) : newValue;
                        handleCheckboxChange(limit.key as keyof GeneralLimitsData, finalValue);
                      }}
                      className="mt-1 w-4 h-4 text-primary-500 bg-dark-500 border-dark-400 rounded focus:ring-primary-500 focus:ring-2"
                    />
                    <div className="flex-1">
                      <label htmlFor={limit.key} className="text-sm font-medium text-gray-200 cursor-pointer">
                        {limit.label}
                      </label>
                      <p className="text-xs text-gray-400 mt-1">{limit.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Status Messages */}
      {applySuccess && (
        <div className="bg-success-500/20 border border-success-500 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-success-500" />
            <p className="text-success-500 text-sm font-medium">{applySuccess}</p>
          </div>
        </div>
      )}

      {applyError && (
        <div className="bg-error-500/20 border border-error-500 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-error-500" />
            <p className="text-error-500 text-sm font-medium">{applyError}</p>
          </div>
        </div>
      )}

      {/* Changes Indicator */}
      {hasChanges && !applySuccess && (
        <div className="bg-warning-500/20 border border-warning-500 rounded-lg p-4">
          <p className="text-warning-500 text-sm font-medium">
            You have unsaved changes. Click "Apply Changes" to save or "Reset" to discard.
          </p>
        </div>
      )}
    </div>
  );
};

export default GeneralLimitsTab;
