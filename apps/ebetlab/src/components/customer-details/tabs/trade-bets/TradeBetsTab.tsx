import React from 'react';
import { fetchTradeBets } from '../../../../utils/api/trade-bets';
import BettingTable from '../../../ui/BettingTable';

interface TradeBetsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

// Simplified component using BettingTable

const TradeBetsTab: React.FC<TradeBetsTabProps> = ({ customer }) => {

  return (
    <BettingTable
      type="trade"
      customer={customer}
      fetchFunction={fetchTradeBets}
      title="Trade Bets"
    />
  );

};

export default TradeBetsTab;
