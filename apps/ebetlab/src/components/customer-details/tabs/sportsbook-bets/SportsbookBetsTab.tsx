import React from 'react';
import { fetchSportsbookBets } from '../../../../utils/api';
import BettingTable from '../../../ui/BettingTable';

interface SportsbookBetsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

// Simplified component using BettingTable

const SportsbookBetsTab: React.FC<SportsbookBetsTabProps> = ({ customer }) => {

  return (
    <BettingTable
      type="sportsbook"
      customer={customer}
      fetchFunction={fetchSportsbookBets}
      title="Sportsbook Bets"
    />
  );

};

export default SportsbookBetsTab;
