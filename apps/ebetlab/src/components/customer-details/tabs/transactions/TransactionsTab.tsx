import React, { useState, useCallback } from 'react';
import { DollarSign, Download, Filter, RefreshCw, Eye, CheckCircle, XCircle, Clock, AlertCircle, Copy } from 'lucide-react';
import { fetchTransactions, type TransactionData, type TransactionSearchParams } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface TransactionsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface TransactionDetailsModalProps {
  transaction: TransactionData;
}

const TransactionDetailsModal: React.FC<TransactionDetailsModalProps> = ({ transaction }) => {
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const truncateUniqueId = (uniqueId: string) => {
    if (uniqueId.length <= 20) return uniqueId;
    return `${uniqueId.slice(0, 8)}...${uniqueId.slice(-8)}`;
  };

  const getTypeLabel = (type: number) => {
    switch (type) {
      case 1: return 'Deposit';
      case 2: return 'Withdraw';
      default: return 'Other';
    }
  };

  const getStatusBadge = (status: { id: number; name: string; key: string }) => {
    const statusColors = {
      completed: 'bg-green-500/20 text-green-400 border-green-500/30',
      pending: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      failed: 'bg-red-500/20 text-red-400 border-red-500/30',
      cancelled: 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    };

    const colorClass = statusColors[status.key as keyof typeof statusColors] || statusColors.pending;

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full border ${colorClass}`}>
        {status.name}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
            Transaction Information
          </h3>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Transaction ID</label>
              <p className="text-gray-100 font-mono">#{transaction.id}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Unique ID</label>
              <div className="flex items-center gap-2">
                <p
                  className="text-gray-100 font-mono text-sm flex-1"
                  title={transaction.unique_id}
                >
                  {truncateUniqueId(transaction.unique_id)}
                </p>
                <button
                  onClick={() => copyToClipboard(transaction.unique_id, 'unique_id')}
                  className="p-1 text-gray-400 hover:text-primary-500 transition-colors"
                  title="Copy full unique ID"
                >
                  {copiedField === 'unique_id' ? (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Type</label>
              <p className="text-gray-100">{getTypeLabel(transaction.type)}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
              {getStatusBadge(transaction.status)}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Method</label>
              <p className="text-gray-100">{transaction.method}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Provider</label>
              <p className="text-gray-100">{transaction.provider}</p>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
            Amount & Currency
          </h3>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Original Amount</label>
              <p className="text-xl font-bold text-gray-100">
                {formatCurrency(transaction.amount, transaction.currency)}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">USD Equivalent</label>
              <p className="text-lg font-semibold text-gray-300">
                ${parseFloat(transaction.usd_amount).toFixed(2)} USD
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Currency</label>
              <p className="text-gray-100">{transaction.currency}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Wallet ID</label>
              <p className="text-gray-100 font-mono">#{transaction.wallet_id}</p>
            </div>

            {transaction.aml_met && (
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">AML Status</label>
                <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full bg-green-500/20 text-green-400 border border-green-500/30">
                  <CheckCircle className="w-3 h-3" />
                  AML Verified
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
          Customer Information
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Customer ID</label>
            <p className="text-gray-100 font-mono">#{transaction.customer.id}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
            <p className="text-gray-100">{transaction.customer.username}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Full Name</label>
            <p className="text-gray-100">
              {transaction.customer.profile.name} {transaction.customer.profile.surname}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Affiliator</label>
            <p className="text-gray-100">{transaction.customer.affiliator.name}</p>
          </div>
        </div>
      </div>

      {/* Payment Provider Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
          Payment Provider
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Provider Name</label>
            <p className="text-gray-100">{transaction.payment_provider.name}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Provider ID</label>
            <p className="text-gray-100 font-mono">#{transaction.payment_provider_id}</p>
          </div>

          {transaction.network && (
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Network</label>
              <p className="text-gray-100">{transaction.network}</p>
            </div>
          )}

          {transaction.target_address && (
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Target Address</label>
              <p className="text-gray-100 font-mono text-sm break-all">{transaction.target_address}</p>
            </div>
          )}
        </div>
      </div>

      {/* Timestamps */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
          Timestamps
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Created</label>
            <p className="text-gray-100">{formatTimestamp(transaction.timestamp)}</p>
          </div>

          {transaction.completed_at && (
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Completed</label>
              <p className="text-gray-100">{formatTimestamp(transaction.completed_at)}</p>
            </div>
          )}

          {transaction.last_action && (
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Last Action</label>
              <p className="text-gray-100">{formatTimestamp(transaction.last_action)}</p>
            </div>
          )}
        </div>
      </div>

      {/* Additional Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
          Additional Details
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {transaction.tx_id && (
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Transaction Hash</label>
              <div className="flex items-start gap-2">
                <p className="text-gray-100 font-mono text-sm break-all flex-1">{transaction.tx_id}</p>
                <button
                  onClick={() => copyToClipboard(transaction.tx_id, 'tx_id')}
                  className="p-1 text-gray-400 hover:text-primary-500 transition-colors flex-shrink-0 mt-0.5"
                  title="Copy transaction hash"
                >
                  {copiedField === 'tx_id' ? (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Manual Transaction</label>
            <p className="text-gray-100">{transaction.is_manuel ? 'Yes' : 'No'}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Discount Eligible</label>
            <p className="text-gray-100">{transaction.discount_able ? 'Yes' : 'No'}</p>
          </div>

          {transaction.description && (
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-400 mb-1">Description</label>
              <p className="text-gray-100">{transaction.description}</p>
            </div>
          )}

          {transaction.approve_details && (
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-400 mb-1">Approval Details</label>
              <p className="text-gray-100">{transaction.approve_details}</p>
            </div>
          )}
        </div>
      </div>

      {/* Discount Information */}
      {transaction.discount && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
            Discount Information
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Discount Code</label>
              <p className="text-gray-100 font-mono">{transaction.discount.code}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Percentage</label>
              <p className="text-gray-100">{transaction.discount.percentage}%</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Before Balance</label>
              <p className="text-gray-100">{transaction.discount.before_balance}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">After Balance</label>
              <p className="text-gray-100">{transaction.discount.after_balance}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Discount Amount</label>
              <p className="text-gray-100">{transaction.discount.amount}</p>
            </div>

            {transaction.discount.note && (
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">Note</label>
                <p className="text-gray-100">{transaction.discount.note}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const TransactionsTab: React.FC<TransactionsTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<TransactionData | null>(null);
  const [showTransactionModal, setShowTransactionModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<TransactionSearchParams>>({
    customer_id: customer.id.toString()
  });

  // Fetch transactions data
  const fetchTransactionsData = useCallback(async () => {
    const result = await fetchTransactions(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load transactions');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: transactionsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchTransactionsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  // Format currency with proper symbol
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Handle transaction modal
  const handleTransactionClick = (transaction: TransactionData) => {
    setSelectedTransaction(transaction);
    setShowTransactionModal(true);
  };

  const handleCloseModal = () => {
    setShowTransactionModal(false);
    setSelectedTransaction(null);
  };

  // Get status badge
  const getStatusBadge = (status: { id: number; name: string; key: string }) => {
    const statusConfig: { [key: string]: { color: string; icon: React.ReactNode } } = {
      'completed': { color: 'bg-green-500/20 text-green-400', icon: <CheckCircle size={14} /> },
      'pending': { color: 'bg-yellow-500/20 text-yellow-400', icon: <Clock size={14} /> },
      'failed': { color: 'bg-red-500/20 text-red-400', icon: <XCircle size={14} /> },
      'cancelled': { color: 'bg-gray-500/20 text-gray-400', icon: <XCircle size={14} /> },
      'processing': { color: 'bg-blue-500/20 text-blue-400', icon: <Clock size={14} /> }
    };

    const config = statusConfig[status.key] || { color: 'bg-gray-500/20 text-gray-400', icon: <AlertCircle size={14} /> };

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full ${config.color}`}>
        {config.icon}
        {status.name}
      </span>
    );
  };

  // Get transaction type badge
  const getTypeBadge = (type: number) => {
    const typeConfig: { [key: number]: { label: string; color: string } } = {
      1: { label: 'Deposit', color: 'bg-green-500/20 text-green-400' },
      2: { label: 'Withdraw', color: 'bg-red-500/20 text-red-400' },
      3: { label: 'Bonus', color: 'bg-purple-500/20 text-purple-400' },
      4: { label: 'Correction', color: 'bg-blue-500/20 text-blue-400' }
    };

    const config = typeConfig[type] || { label: 'Unknown', color: 'bg-gray-500/20 text-gray-400' };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.color}`}>
        {config.label}
      </span>
    );
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof TransactionSearchParams, value: string) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value || null
    }));
    setCurrentPage(1);
  };

  // Clear filters
  const clearFilters = () => {
    setSearchParams({
      customer_id: customer.id.toString()
    });
    setCurrentPage(1);
  };

  // Export transactions data
  const handleExport = () => {
    if (!transactionsData?.data) return;

    const csvData = transactionsData.data.map(transaction => ({
      ID: transaction.id,
      Type: transaction.type === 1 ? 'Deposit' : transaction.type === 2 ? 'Withdraw' : 'Other',
      Method: transaction.method,
      Amount: transaction.amount,
      Currency: transaction.currency,
      'USD Amount': transaction.usd_amount,
      Status: transaction.status.name,
      Provider: transaction.provider,
      'Payment Provider': transaction.payment_provider.name,
      'Completed At': transaction.completed_at ? formatTimestamp(transaction.completed_at) : 'N/A',
      Timestamp: formatTimestamp(transaction.timestamp),
      'Unique ID': transaction.unique_id,
      'AML Met': transaction.aml_met ? 'Yes' : 'No'
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(value => `"${value}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transactions_${customer.username}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading transactions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-error-500/20 border border-error-500 rounded-md p-6 text-center">
        <p className="text-error-500 text-lg mb-2">Failed to load transactions</p>
        <p className="text-gray-400 mb-4">{error}</p>
        <button
          onClick={refetch}
          className="btn btn-primary flex items-center gap-2 mx-auto"
        >
          <RefreshCw size={16} />
          Try Again
        </button>
      </div>
    );
  }

  const transactions = transactionsData?.data || [];
  const total = transactionsData?.total || 0;
  const totalPages = Math.ceil(total / itemsPerPage);

  // Calculate summary statistics - only include completed transactions in amounts
  const summary = transactions.reduce((acc, transaction) => {
    const amount = parseFloat(transaction.usd_amount);
    const isCompleted = transaction.status.key === 'completed';

    if (transaction.type === 1) { // Deposit
      acc.totalDepositCount++;
      if (isCompleted) {
        acc.totalDeposits += amount;
        acc.completedDepositCount++;
      }
    } else if (transaction.type === 2) { // Withdraw
      acc.totalWithdrawCount++;
      if (isCompleted) {
        acc.totalWithdrawals += amount;
        acc.completedWithdrawCount++;
      }
    }

    if (isCompleted) {
      acc.completedCount++;
    }

    return acc;
  }, {
    totalDeposits: 0,
    totalWithdrawals: 0,
    totalDepositCount: 0,
    totalWithdrawCount: 0,
    completedDepositCount: 0,
    completedWithdrawCount: 0,
    completedCount: 0
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <DollarSign className="w-6 h-6 text-primary-500" />
          <div>
            <h2 className="text-xl font-semibold text-gray-100">Transactions</h2>
            <p className="text-sm text-gray-400">
              {total} total transactions for {customer.username}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn flex items-center gap-2 ${showFilters ? 'btn-primary' : 'btn-outline'}`}
          >
            <Filter size={16} />
            Filters
          </button>

          <button
            onClick={handleExport}
            disabled={transactions.length === 0}
            className="btn btn-outline flex items-center gap-2"
          >
            <Download size={16} />
            Export CSV
          </button>

          <button
            onClick={refetch}
            disabled={loading}
            className="btn btn-outline flex items-center gap-2"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards - Displayed Transactions Only */}
      {transactions.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold text-gray-100">Summary Statistics</h3>
            <span className="text-sm text-gray-400 bg-dark-600 px-2 py-1 rounded">
              Amounts from {summary.completedCount} completed transactions only
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Deposits</p>
                  <p className="text-2xl font-bold text-green-400">
                    ${summary.totalDeposits.toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {summary.completedDepositCount} completed of {summary.totalDepositCount} total
                  </p>
                </div>
                <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-green-400" />
                </div>
              </div>
            </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Withdrawals</p>
                <p className="text-2xl font-bold text-red-400">
                  ${summary.totalWithdrawals.toFixed(2)}
                </p>
                <p className="text-xs text-gray-500">
                  {summary.completedWithdrawCount} completed of {summary.totalWithdrawCount} total
                </p>
              </div>
              <div className="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-red-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Net Balance</p>
                <p className={`text-2xl font-bold ${
                  summary.totalDeposits - summary.totalWithdrawals >= 0
                    ? 'text-green-400'
                    : 'text-red-400'
                }`}>
                  ${(summary.totalDeposits - summary.totalWithdrawals).toFixed(2)}
                </p>
                <p className="text-xs text-gray-500">Deposits - Withdrawals</p>
              </div>
              <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Success Rate</p>
                <p className="text-2xl font-bold text-primary-400">
                  {transactions.length > 0 ? ((summary.completedCount / transactions.length) * 100).toFixed(1) : 0}%
                </p>
                <p className="text-xs text-gray-500">{summary.completedCount} of {transactions.length}</p>
              </div>
              <div className="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-primary-400" />
              </div>
            </div>
          </div>
        </div>
        </div>
      )}

      {/* Filters Section */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Transaction Type
              </label>
              <select
                value={searchParams.type || ''}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Types</option>
                <option value="1">Deposit</option>
                <option value="2">Withdraw</option>
                <option value="3">Bonus</option>
                <option value="4">Correction</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Status
              </label>
              <select
                value={searchParams.status_id || ''}
                onChange={(e) => handleFilterChange('status_id', e.target.value)}
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Statuses</option>
                <option value="1">Pending</option>
                <option value="2">Processing</option>
                <option value="3">Failed</option>
                <option value="4">Cancelled</option>
                <option value="5">Completed</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Currency
              </label>
              <select
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="TRY">TRY</option>
                <option value="GBP">GBP</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Amount Range (USD)
              </label>
              <div className="flex gap-2">
                <input
                  type="number"
                  placeholder="Min"
                  value={searchParams.usd_min || ''}
                  onChange={(e) => handleFilterChange('usd_min', e.target.value)}
                  className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
                />
                <input
                  type="number"
                  placeholder="Max"
                  value={searchParams.usd_max || ''}
                  onChange={(e) => handleFilterChange('usd_max', e.target.value)}
                  className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between mt-4 pt-4 border-t border-dark-600">
            <div className="text-sm text-gray-400">
              {Object.keys(searchParams).filter(key => key !== 'customer_id' && searchParams[key as keyof TransactionSearchParams]).length} filters applied
            </div>
            <button
              onClick={clearFilters}
              className="btn btn-outline btn-sm"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Transactions Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-dark-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Provider
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {transactions.length > 0 ? (
                transactions.map((transaction) => (
                  <tr
                    key={transaction.id}
                    className="hover:bg-dark-600/50 cursor-pointer transition-colors"
                    onClick={() => handleTransactionClick(transaction)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-100">
                        #{transaction.id}
                      </div>
                      <div className="text-xs text-gray-400">
                        {transaction.unique_id.slice(-8)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getTypeBadge(transaction.type)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100">{transaction.method}</div>
                      <div className="text-xs text-gray-400">{transaction.provider}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-100">
                        {formatCurrency(transaction.amount, transaction.currency)}
                      </div>
                      <div className="text-xs text-gray-400">
                        ${transaction.usd_amount} USD
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(transaction.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100">{transaction.payment_provider.name}</div>
                      {transaction.aml_met && (
                        <div className="text-xs text-green-400">AML Verified</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100">
                        {formatTimestamp(transaction.timestamp)}
                      </div>
                      {transaction.completed_at && (
                        <div className="text-xs text-gray-400">
                          Completed: {formatTimestamp(transaction.completed_at)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTransactionClick(transaction);
                        }}
                        className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                        title="View Details"
                      >
                        <Eye size={14} />
                        Details
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <DollarSign className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No transactions found</p>
                      <p className="text-sm">This customer has no transaction history.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {total > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={total}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        )}
      </div>

      {/* Transaction Details Modal */}
      <Modal
        isOpen={showTransactionModal}
        onClose={handleCloseModal}
        title="Transaction Details"
        size="lg"
      >
        {selectedTransaction && (
          <TransactionDetailsModal transaction={selectedTransaction} />
        )}
      </Modal>
    </div>
  );
};

export default TransactionsTab;
