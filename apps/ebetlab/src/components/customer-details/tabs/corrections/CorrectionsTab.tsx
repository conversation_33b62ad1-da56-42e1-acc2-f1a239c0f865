import React, { useState, useCallback } from 'react';
import { DollarSign, Download, Filter, RefreshCw, Eye, TrendingUp, TrendingDown, AlertCircle, Copy } from 'lucide-react';
import { fetchCorrections, fetchCorrectionsSummary, type CorrectionData, type CorrectionsSearchParams } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface CorrectionsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface CorrectionDetailsModalProps {
  correction: CorrectionData;
}

const CorrectionDetailsModal: React.FC<CorrectionDetailsModalProps> = ({ correction }) => {
  const [copySuccess, setCopySuccess] = useState(false);

  const handleCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-gray-100">Correction Details</h3>
        <div className="flex items-center gap-2">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            correction.way === 'up' 
              ? 'bg-green-500/20 text-green-400' 
              : 'bg-red-500/20 text-red-400'
          }`}>
            {correction.way === 'up' ? 'Credit' : 'Debit'}
          </span>
        </div>
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Basic Information</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">ID:</span>
              <span className="text-gray-100 font-mono">{correction.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Model:</span>
              <span className="text-gray-100 capitalize">{correction.model}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Currency:</span>
              <span className="text-gray-100">{correction.currency}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Wallet ID:</span>
              <span className="text-gray-100 font-mono">{correction.wallet_id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Active:</span>
              <span className={`${correction.is_active ? 'text-green-400' : 'text-red-400'}`}>
                {correction.is_active ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Amount Details</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Amount:</span>
              <span className="text-gray-100 font-semibold">
                {formatCurrency(correction.amount, correction.currency)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">USD Amount:</span>
              <span className="text-gray-100 font-semibold">
                ${parseFloat(correction.usd_amount).toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Before Balance:</span>
              <span className="text-gray-100">
                {formatCurrency(correction.before_balance, correction.currency)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">After Balance:</span>
              <span className="text-gray-100">
                {formatCurrency(correction.after_balance, correction.currency)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Wagering Information */}
      {correction.model === 'bonus' && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h4 className="text-lg font-semibold text-gray-100 mb-4">Wagering Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Required Wager:</span>
                <span className="text-gray-100">${parseFloat(correction.required_wager).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Wagered:</span>
                <span className="text-gray-100">${parseFloat(correction.wagered).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Completed:</span>
                <span className={`${correction.completed ? 'text-green-400' : 'text-yellow-400'}`}>
                  {correction.completed ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Required Wager (Currency):</span>
                <span className="text-gray-100">
                  {formatCurrency(correction.required_wager_currency, correction.currency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Wagered (Currency):</span>
                <span className="text-gray-100">
                  {formatCurrency(correction.wagered_currency, correction.currency)}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Note and Timestamps */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
        <h4 className="text-lg font-semibold text-gray-100 mb-4">Additional Information</h4>
        <div className="space-y-3">
          {correction.note && (
            <div>
              <span className="text-gray-400 block mb-1">Note:</span>
              <div className="flex items-start gap-2">
                <span className="text-gray-100 flex-1">{correction.note}</span>
                <button
                  onClick={() => handleCopyToClipboard(correction.note)}
                  className="text-gray-400 hover:text-gray-200 p-1"
                  title="Copy note"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
          )}
          <div className="flex justify-between">
            <span className="text-gray-400">Created:</span>
            <span className="text-gray-100">{formatTimestamp(correction.timestamp)}</span>
          </div>
          {correction.completed_at && (
            <div className="flex justify-between">
              <span className="text-gray-400">Completed:</span>
              <span className="text-gray-100">{formatTimestamp(correction.completed_at)}</span>
            </div>
          )}
          {correction.operator && (
            <div className="flex justify-between">
              <span className="text-gray-400">Operator:</span>
              <span className="text-gray-100">{correction.operator.username || 'System'}</span>
            </div>
          )}
        </div>
      </div>

      {copySuccess && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg">
          Copied to clipboard!
        </div>
      )}
    </div>
  );
};

const CorrectionsTab: React.FC<CorrectionsTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCorrection, setSelectedCorrection] = useState<CorrectionData | null>(null);
  const [showCorrectionModal, setShowCorrectionModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<CorrectionsSearchParams>>({
    username: customer.username
  });

  // Fetch corrections data
  const fetchCorrectionsData = useCallback(async () => {
    const result = await fetchCorrections(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load corrections');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  // Fetch summary data
  const fetchSummaryData = useCallback(async () => {
    const result = await fetchCorrectionsSummary(customer.username);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load corrections summary');
    }
  }, [customer.username]);

  const { data: correctionsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchCorrectionsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  const { data: summaryData, loading: summaryLoading } = useRefreshableData({
    fetchFn: fetchSummaryData,
    dependencies: [customer.username]
  });

  // Format currency with proper symbol
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Handle correction modal
  const handleCorrectionClick = (correction: CorrectionData) => {
    setSelectedCorrection(correction);
    setShowCorrectionModal(true);
  };

  const handleCloseModal = () => {
    setShowCorrectionModal(false);
    setSelectedCorrection(null);
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {summaryData && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-100">Total Deposits</h3>
                <p className="text-2xl font-bold text-green-400">
                  ${parseFloat(summaryData.deposit.deposit).toFixed(2)}
                </p>
                <p className="text-sm text-gray-400">{summaryData.deposit.total} corrections</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-400" />
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-100">Total Withdrawals</h3>
                <p className="text-2xl font-bold text-red-400">
                  ${parseFloat(summaryData.withdraw.withdraw).toFixed(2)}
                </p>
                <p className="text-sm text-gray-400">{summaryData.withdraw.total} corrections</p>
              </div>
              <TrendingDown className="w-8 h-8 text-red-400" />
            </div>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-100">Balance Corrections</h2>

        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn flex items-center gap-2 ${showFilters ? 'btn-primary' : 'btn-outline'}`}
          >
            <Filter size={16} />
            Filters
          </button>

          <button
            onClick={refetch}
            disabled={loading}
            className="btn btn-outline flex items-center gap-2"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <h3 className="text-lg font-semibold text-gray-100 mb-4">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Model</label>
              <select
                value={searchParams.model || ''}
                onChange={(e) => handleFilterChange('model', e.target.value)}
                className="input w-full"
              >
                <option value="">All Models</option>
                <option value="bonus">Bonus</option>
                <option value="manual">Manual</option>
                <option value="system">System</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Way</label>
              <select
                value={searchParams.way || ''}
                onChange={(e) => handleFilterChange('way', e.target.value)}
                className="input w-full"
              >
                <option value="">All Ways</option>
                <option value="up">Credit (Up)</option>
                <option value="down">Debit (Down)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Currency</label>
              <select
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="input w-full"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="TRY">TRY</option>
                <option value="GBP">GBP</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">USD Amount Range</label>
              <div className="flex gap-2">
                <input
                  type="number"
                  placeholder="Min"
                  value={searchParams.usd_min || ''}
                  onChange={(e) => handleFilterChange('usd_min', e.target.value)}
                  className="input flex-1"
                />
                <input
                  type="number"
                  placeholder="Max"
                  value={searchParams.usd_max || ''}
                  onChange={(e) => handleFilterChange('usd_max', e.target.value)}
                  className="input flex-1"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
              <div className="flex gap-2">
                <input
                  type="date"
                  value={searchParams.from || ''}
                  onChange={(e) => handleFilterChange('from', e.target.value)}
                  className="input flex-1"
                />
                <input
                  type="date"
                  value={searchParams.to || ''}
                  onChange={(e) => handleFilterChange('to', e.target.value)}
                  className="input flex-1"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Note Contains</label>
              <input
                type="text"
                placeholder="Search in notes..."
                value={searchParams.note || ''}
                onChange={(e) => handleFilterChange('note', e.target.value)}
                className="input w-full"
              />
            </div>
          </div>

          <div className="flex justify-end mt-4">
            <button
              onClick={clearFilters}
              className="btn btn-outline"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-500/20 border border-red-500 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-400" />
          <span className="text-red-400">{error}</span>
        </div>
      )}

      {/* Table */}
      {!loading && !error && correctionsData && (
        <>
          <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-dark-600">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Model
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Way
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Note
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {correctionsData.data.map((correction) => (
                    <tr key={correction.id} className="hover:bg-dark-600/50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-mono text-gray-100">{correction.id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-100 capitalize">{correction.model}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-100">
                          {formatCurrency(correction.amount, correction.currency)}
                        </div>
                        <div className="text-xs text-gray-400">
                          ${parseFloat(correction.usd_amount).toFixed(2)} USD
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          correction.way === 'up'
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-red-500/20 text-red-400'
                        }`}>
                          {correction.way === 'up' ? (
                            <>
                              <TrendingUp size={12} className="mr-1" />
                              Credit
                            </>
                          ) : (
                            <>
                              <TrendingDown size={12} className="mr-1" />
                              Debit
                            </>
                          )}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-100 max-w-xs truncate" title={correction.note}>
                          {correction.note || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-100">
                          {formatTimestamp(correction.timestamp)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          correction.is_active
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-gray-500/20 text-gray-400'
                        }`}>
                          {correction.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCorrectionClick(correction);
                          }}
                          className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                          title="View Details"
                        >
                          <Eye size={14} />
                          Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil((correctionsData?.total || 0) / itemsPerPage)}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            totalItems={correctionsData?.total || 0}
          />
        </>
      )}

      {/* Correction Details Modal */}
      {showCorrectionModal && selectedCorrection && (
        <Modal
          isOpen={showCorrectionModal}
          onClose={handleCloseModal}
          title="Correction Details"
          size="lg"
        >
          <CorrectionDetailsModal correction={selectedCorrection} />
        </Modal>
      )}
    </div>
  );

  // Helper functions
  function handleItemsPerPageChange(newItemsPerPage: number) {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  }

  function handleFilterChange(key: keyof CorrectionsSearchParams, value: string) {
    setSearchParams(prev => ({
      ...prev,
      [key]: value || null
    }));
    setCurrentPage(1);
  }

  function clearFilters() {
    setSearchParams({
      username: customer.username
    });
    setCurrentPage(1);
  }
};

export default CorrectionsTab;
