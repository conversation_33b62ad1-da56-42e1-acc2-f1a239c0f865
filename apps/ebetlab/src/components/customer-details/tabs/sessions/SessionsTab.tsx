import React, { useState, useCallback } from 'react';
import { Monitor, RefreshCw, Eye, Globe, User, Clock, CheckCircle, XCircle } from 'lucide-react';
import { fetchSessions, type SessionData, type SessionSearchParams } from '../../../../utils/api/sessions';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import UnifiedTable, { TableColumn } from '../../../ui/UnifiedTable';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface SessionsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface SessionDetailsModalProps {
  session: SessionData;
}

const SessionDetailsModal: React.FC<SessionDetailsModalProps> = ({ session }) => {
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400">Session ID</label>
          <p className="text-gray-100 font-mono">#{session.id}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400">Status</label>
          <div className="flex items-center gap-2">
            {session.is_active ? (
              <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-green-500 bg-green-500/10">
                <CheckCircle size={12} />
                Active
              </span>
            ) : (
              <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-gray-500 bg-gray-500/10">
                <XCircle size={12} />
                Inactive
              </span>
            )}
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400">IP Address</label>
          <p className="text-gray-100 font-mono">{session.ip}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400">Country</label>
          <p className="text-gray-100">{session.country}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400">Browser</label>
          <p className="text-gray-100">{session.browser}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400">Started</label>
          <p className="text-gray-100">{formatTimestamp(session.timestamp)}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400">Customer</label>
          <p className="text-gray-100">{session.customer.username}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400">Last Action</label>
          <p className="text-gray-100">{formatTimestamp(session.customer.last_action)}</p>
        </div>
      </div>
    </div>
  );
};

const SessionsTab: React.FC<SessionsTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [selectedSession, setSelectedSession] = useState<SessionData | null>(null);
  const [showSessionModal, setShowSessionModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<SessionSearchParams>>({
    customer_id: customer.id.toString()
  });

  // Fetch sessions data
  const fetchSessionsData = useCallback(async () => {
    const result = await fetchSessions(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load sessions');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: sessionsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchSessionsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  // Format timestamp
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Handle session click
  const handleSessionClick = (session: SessionData) => {
    setSelectedSession(session);
    setShowSessionModal(true);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Table columns configuration
  const columns: TableColumn<SessionData>[] = [
    {
      key: 'id',
      label: 'Session ID',
      width: '120px',
      render: (session) => (
        <span className="font-mono text-sm font-medium text-gray-100">
          #{session.id}
        </span>
      )
    },
    {
      key: 'ip',
      label: 'IP Address',
      width: '140px',
      render: (session) => (
        <span className="font-mono text-sm text-gray-100">
          {session.ip}
        </span>
      )
    },
    {
      key: 'country',
      label: 'Country',
      width: '120px',
      render: (session) => (
        <div className="flex items-center gap-2">
          <Globe size={14} className="text-gray-400" />
          <span className="text-gray-100">{session.country}</span>
        </div>
      )
    },
    {
      key: 'browser',
      label: 'Browser',
      width: '120px',
      render: (session) => (
        <span className="text-gray-100">{session.browser}</span>
      )
    },
    {
      key: 'is_active',
      label: 'Status',
      width: '100px',
      render: (session) => (
        session.is_active ? (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-green-500 bg-green-500/10">
            <CheckCircle size={12} />
            Active
          </span>
        ) : (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-gray-500 bg-gray-500/10">
            <XCircle size={12} />
            Inactive
          </span>
        )
      )
    },
    {
      key: 'timestamp',
      label: 'Started',
      width: '180px',
      render: (session) => (
        <div className="flex items-center gap-2">
          <Clock size={14} className="text-gray-400" />
          <span className="text-sm text-gray-300">
            {formatTimestamp(session.timestamp)}
          </span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      sticky: true,
      render: (session) => (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleSessionClick(session);
          }}
          className="btn btn-outline text-xs py-1 px-2"
        >
          <Eye size={14} className="mr-1" />
          View
        </button>
      )
    }
  ];

  const sessions = sessionsData?.data || [];
  const total = sessionsData?.total || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-100">Sessions</h3>
          <p className="text-sm text-gray-400">
            Total: {total.toLocaleString()} sessions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={refetch}
            className="btn btn-outline"
            disabled={loading}
          >
            <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Sessions Table */}
      <UnifiedTable
        data={sessions}
        columns={columns}
        isLoading={loading}
        error={error}
        onRowClick={handleSessionClick}
        onRetry={refetch}
        minWidth="1000px"
        emptyState={{
          icon: <Monitor className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No sessions found',
          description: 'This customer has no session activity.'
        }}
      />

      {/* Pagination */}
      {total > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(total / itemsPerPage)}
          itemsPerPage={itemsPerPage}
          totalItems={total}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
        />
      )}

      {/* Session Details Modal */}
      {selectedSession && (
        <Modal
          isOpen={showSessionModal}
          onClose={() => setShowSessionModal(false)}
          title="Session Details"
        >
          <SessionDetailsModal session={selectedSession} />
        </Modal>
      )}
    </div>
  );
};

export default SessionsTab;
