import React, { useCallback } from 'react';
import { Shield, RefreshCw, DollarSign, CreditCard, TrendingUp } from 'lucide-react';
import { fetchVaults, type VaultData } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';

interface VaultsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

const VaultsTab: React.FC<VaultsTabProps> = ({ customer }) => {
  // Fetch vaults data
  const fetchVaultsData = useCallback(async () => {
    const result = await fetchVaults(customer.id.toString());
    
    if (result.success && result.data) {
      return result.data;
    } else {
      throw new Error(result.error || 'Failed to load vaults');
    }
  }, [customer.id]);

  const { data: vaultsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchVaultsData,
    dependencies: [customer.id]
  });

  // Format balance with proper precision
  const formatBalance = (balance: string, currency: string) => {
    const value = parseFloat(balance);
    // Most currencies use 2 decimal places, crypto might use more
    const precision = ['BTC', 'ETH', 'LTC'].includes(currency) ? 8 : 2;
    return `${value.toFixed(precision)} ${currency}`;
  };

  // Get currency badge color
  const getCurrencyBadgeColor = (currency: string) => {
    const colorMap: { [key: string]: string } = {
      'BTC': 'bg-orange-500/20 text-orange-400',
      'ETH': 'bg-blue-500/20 text-blue-400',
      'TRX': 'bg-red-500/20 text-red-400',
      'TRY': 'bg-green-500/20 text-green-400',
      'USD': 'bg-emerald-500/20 text-emerald-400',
      'EUR': 'bg-purple-500/20 text-purple-400',
      'USDT': 'bg-teal-500/20 text-teal-400'
    };
    return colorMap[currency] || 'bg-gray-500/20 text-gray-400';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading vaults...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-error-500/20 border border-error-500 rounded-md p-6 text-center">
        <p className="text-error-500 text-lg mb-2">Failed to load vaults</p>
        <p className="text-gray-400 mb-4">{error}</p>
        <button
          onClick={refetch}
          className="btn btn-primary flex items-center gap-2 mx-auto"
        >
          <RefreshCw size={16} />
          Try Again
        </button>
      </div>
    );
  }

  // Ensure vaults is always an array
  const vaults = Array.isArray(vaultsData) ? vaultsData : [];
  const total = vaults.length;

  // Calculate summary statistics
  const activeVaults = vaults.filter(vault => parseFloat(vault.balance) > 0);
  const totalValue = vaults.reduce((sum, vault) => {
    return sum + parseFloat(vault.balance);
  }, 0);

  // Group vaults by currency for better organization
  const vaultsByCurrency = vaults.reduce((acc, vault) => {
    if (!acc[vault.currency]) {
      acc[vault.currency] = [];
    }
    acc[vault.currency].push(vault);
    return acc;
  }, {} as { [key: string]: VaultData[] });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Shield className="w-6 h-6 text-primary-500" />
          <div>
            <h2 className="text-xl font-semibold text-gray-100">Vaults</h2>
            <p className="text-sm text-gray-400">
              {total} total vaults for {customer.username}
            </p>
          </div>
        </div>

        <button
          onClick={refetch}
          disabled={loading}
          className="btn btn-outline flex items-center gap-2"
        >
          <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
          Refresh
        </button>
      </div>

      {/* Summary Cards */}
      {vaults.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Vaults</p>
                <p className="text-2xl font-bold text-gray-100">{total}</p>
              </div>
              <div className="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <Shield className="w-5 h-5 text-primary-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Active Vaults</p>
                <p className="text-2xl font-bold text-green-400">{activeVaults.length}</p>
                <p className="text-xs text-gray-500">With balance &gt; 0</p>
              </div>
              <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Empty Vaults</p>
                <p className="text-2xl font-bold text-gray-400">{total - activeVaults.length}</p>
                <p className="text-xs text-gray-500">Zero balance</p>
              </div>
              <div className="w-10 h-10 bg-gray-500/20 rounded-lg flex items-center justify-center">
                <CreditCard className="w-5 h-5 text-gray-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Currencies</p>
                <p className="text-2xl font-bold text-primary-400">{Object.keys(vaultsByCurrency).length}</p>
                <p className="text-xs text-gray-500">Different types</p>
              </div>
              <div className="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-primary-400" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Vaults Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left">
            <thead className="bg-dark-600">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Vault ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Currency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Balance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {vaults.length > 0 ? (
                vaults.map((vault) => (
                  <tr key={vault.id} className="hover:bg-dark-600/50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-100">#{vault.id}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getCurrencyBadgeColor(vault.currency)}`}>
                        {vault.currency}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-100">
                        {formatBalance(vault.balance, vault.currency)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        parseFloat(vault.balance) > 0
                          ? 'bg-green-500/20 text-green-400' 
                          : 'bg-gray-500/20 text-gray-400'
                      }`}>
                        {parseFloat(vault.balance) > 0 ? 'Active' : 'Empty'}
                      </span>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <Shield className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No vaults found</p>
                      <p className="text-sm">This customer has no vault data.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default VaultsTab;
