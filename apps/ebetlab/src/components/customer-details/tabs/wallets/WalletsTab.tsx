import React, { useCallback } from 'react';
import { Wallet, RefreshCw, DollarSign, Network, CreditCard } from 'lucide-react';
import { fetchWallets, type WalletData } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';

interface WalletsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

const WalletsTab: React.FC<WalletsTabProps> = ({ customer }) => {
  // Fetch wallets data
  const fetchWalletsData = useCallback(async () => {
    const result = await fetchWallets(customer.id.toString());
    
    if (result.success && result.data) {
      return result.data;
    } else {
      throw new Error(result.error || 'Failed to load wallets');
    }
  }, [customer.id]);

  const { data: walletsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchWalletsData,
    dependencies: [customer.id]
  });

  // Format balance with proper precision
  const formatBalance = (balance: string, precision: number, currencyCode: string) => {
    const value = parseFloat(balance);
    return `${value.toFixed(precision)} ${currencyCode}`;
  };

  // Get network badge color
  const getNetworkBadgeColor = (networkKey: string) => {
    const colorMap: { [key: string]: string } = {
      'BTC': 'bg-orange-500/20 text-orange-400',
      'ETH': 'bg-blue-500/20 text-blue-400',
      'TRX': 'bg-red-500/20 text-red-400',
      'TRY': 'bg-green-500/20 text-green-400',
      'USD': 'bg-emerald-500/20 text-emerald-400',
      'EUR': 'bg-purple-500/20 text-purple-400'
    };
    return colorMap[networkKey] || 'bg-gray-500/20 text-gray-400';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading wallets...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-error-500/20 border border-error-500 rounded-md p-6 text-center">
        <p className="text-error-500 text-lg mb-2">Failed to load wallets</p>
        <p className="text-gray-400 mb-4">{error}</p>
        <button
          onClick={refetch}
          className="btn btn-primary flex items-center gap-2 mx-auto"
        >
          <RefreshCw size={16} />
          Try Again
        </button>
      </div>
    );
  }

  const wallets = walletsData?.data || [];
  const total = walletsData?.total || 0;

  // Calculate summary statistics
  const activeWallets = wallets.filter(wallet => parseFloat(wallet.balance) > 0);
  const totalBalance = wallets.reduce((sum, wallet) => {
    // For simplicity, we'll just count non-zero balances
    return sum + (parseFloat(wallet.balance) > 0 ? 1 : 0);
  }, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Wallet className="w-6 h-6 text-primary-500" />
          <div>
            <h2 className="text-xl font-semibold text-gray-100">Wallets</h2>
            <p className="text-sm text-gray-400">
              {total} total wallets for {customer.username}
            </p>
          </div>
        </div>

        <button
          onClick={refetch}
          disabled={loading}
          className="btn btn-outline flex items-center gap-2"
        >
          <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
          Refresh
        </button>
      </div>

      {/* Summary Cards */}
      {wallets.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Wallets</p>
                <p className="text-2xl font-bold text-gray-100">{total}</p>
              </div>
              <div className="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <Wallet className="w-5 h-5 text-primary-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Active Wallets</p>
                <p className="text-2xl font-bold text-green-400">{activeWallets.length}</p>
                <p className="text-xs text-gray-500">With balance &gt; 0</p>
              </div>
              <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Empty Wallets</p>
                <p className="text-2xl font-bold text-gray-400">{total - activeWallets.length}</p>
                <p className="text-xs text-gray-500">Zero balance</p>
              </div>
              <div className="w-10 h-10 bg-gray-500/20 rounded-lg flex items-center justify-center">
                <CreditCard className="w-5 h-5 text-gray-400" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Wallets Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left">
            <thead className="bg-dark-600">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Currency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Balance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Network
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Address
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {wallets.length > 0 ? (
                wallets.map((wallet) => (
                  <tr key={wallet.id} className="hover:bg-dark-600/50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary-500/20 rounded-full flex items-center justify-center">
                          <span className="text-xs font-bold text-primary-400">
                            {wallet.code.slice(0, 2)}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-100">{wallet.code}</div>
                          <div className="text-xs text-gray-400">ID: {wallet.id}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-100">
                        {formatBalance(wallet.balance, wallet.currency.precision, wallet.code)}
                      </div>
                      <div className="text-xs text-gray-400">
                        Min transfer: {wallet.currency.min_transfer}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getNetworkBadgeColor(wallet.currency.network.key)}`}>
                        {wallet.currency.network.name}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {wallet.address ? (
                        <div className="text-sm text-gray-100 font-mono break-all max-w-xs">
                          {wallet.address}
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">No address</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        wallet.currency.is_active 
                          ? 'bg-green-500/20 text-green-400' 
                          : 'bg-red-500/20 text-red-400'
                      }`}>
                        {wallet.currency.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <Wallet className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No wallets found</p>
                      <p className="text-sm">This customer has no wallet data.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default WalletsTab;
