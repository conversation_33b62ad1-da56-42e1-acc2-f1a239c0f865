import React, { useState, useCallback } from 'react';
import { Gift, RefreshCw, Eye, DollarSign, User, Calendar, CheckCircle, XCircle, Clock } from 'lucide-react';
import { fetchBonusRedeems, type BonusRedeemData, type BonusRedeemSearchParams } from '../../../../utils/api/bonus-redeems';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import UnifiedTable, { TableColumn } from '../../../ui/UnifiedTable';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface BonusesTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface BonusDetailsModalProps {
  bonus: BonusRedeemData;
}

const BonusDetailsModal: React.FC<BonusDetailsModalProps> = ({ bonus }) => {
  const formatTimestamp = (timestamp: string | number): string => {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    return new Date(ts * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string, currency: string = 'USD') => {
    const value = parseFloat(amount);
    if (isNaN(value)) return '$0.00';
    return `$${value.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div>
        <h4 className="text-lg font-semibold text-gray-100 mb-3">Bonus Redeem Details</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400">Redeem ID</label>
            <p className="text-gray-100 font-mono">#{bonus.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Status</label>
            <div className="flex items-center gap-2">
              {bonus.is_active ? (
                <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-green-500 bg-green-500/10">
                  <CheckCircle size={12} />
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-gray-500 bg-gray-500/10">
                  <XCircle size={12} />
                  Inactive
                </span>
              )}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Amount</label>
            <p className="text-blue-400 font-medium">{formatCurrency(bonus.amount, bonus.bonus.currency)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Multiplier</label>
            <p className="text-purple-400 font-medium">{bonus.multiplier}x</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Required</label>
            <p className="text-gray-100">{formatCurrency(bonus.required, bonus.bonus.currency)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Current</label>
            <p className="text-gray-100">{formatCurrency(bonus.current, bonus.bonus.currency)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Completed</label>
            <div className="flex items-center gap-2">
              {bonus.is_completed ? (
                <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-green-500 bg-green-500/10">
                  <CheckCircle size={12} />
                  Yes
                </span>
              ) : (
                <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-yellow-500 bg-yellow-500/10">
                  <Clock size={12} />
                  In Progress
                </span>
              )}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Redeemed</label>
            <p className="text-gray-100">{formatTimestamp(bonus.timestamp)}</p>
          </div>
        </div>
      </div>

      {/* Bonus Information */}
      <div>
        <h4 className="text-lg font-semibold text-gray-100 mb-3">Bonus Information</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400">Bonus Name</label>
            <p className="text-gray-100 font-medium">{bonus.bonus.name}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Type</label>
            <p className="text-gray-100 capitalize">{bonus.bonus.model}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Currency</label>
            <p className="text-gray-100">{bonus.bonus.currency}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Product</label>
            <p className="text-gray-100 capitalize">{bonus.bonus.product}</p>
          </div>
          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-400">Description</label>
            <p className="text-gray-100 text-sm">{bonus.bonus.description}</p>
          </div>
          {bonus.bonus.note && (
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-400">Note</label>
              <p className="text-gray-100 text-sm">{bonus.bonus.note}</p>
            </div>
          )}
        </div>
      </div>

      {/* Operator Information */}
      <div>
        <h4 className="text-lg font-semibold text-gray-100 mb-3">Assigned By</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400">Operator</label>
            <p className="text-gray-100">{bonus.operator.name}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400">Email</label>
            <p className="text-gray-100">{bonus.operator.email}</p>
          </div>
          {bonus.note && (
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-400">Operator Note</label>
              <p className="text-gray-100 text-sm">{bonus.note}</p>
            </div>
          )}
        </div>
      </div>

      {/* Free Spin Details (if applicable) */}
      {bonus.bonus.model === 'freespin' && bonus.bonus.typeable && (
        <div>
          <h4 className="text-lg font-semibold text-gray-100 mb-3">Free Spin Details</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400">Quantity</label>
              <p className="text-gray-100">{bonus.bonus.typeable.quantity} spins</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-400">Bet Level</label>
              <p className="text-gray-100">{bonus.bonus.typeable.bet_level}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-400">Max Win</label>
              <p className="text-gray-100">{formatCurrency(bonus.bonus.typeable.max_win, bonus.bonus.currency)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-400">Game Provider</label>
              <p className="text-gray-100 capitalize">{bonus.bonus.typeable.game_merchant_name}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const BonusesTab: React.FC<BonusesTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [selectedBonus, setSelectedBonus] = useState<BonusRedeemData | null>(null);
  const [showBonusModal, setShowBonusModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<BonusRedeemSearchParams>>({
    customer_id: customer.id.toString()
  });

  // Fetch bonus redeems data
  const fetchBonusRedeemsData = useCallback(async () => {
    const result = await fetchBonusRedeems(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load bonus redeems');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: bonusRedeemsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchBonusRedeemsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  // Format timestamp
  const formatTimestamp = (timestamp: string | number): string => {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    return new Date(ts * 1000).toLocaleString();
  };

  // Format currency
  const formatCurrency = (amount: string, currency: string = 'USD') => {
    const value = parseFloat(amount);
    if (isNaN(value)) return '$0.00';
    return `$${value.toFixed(2)}`;
  };

  // Handle bonus click
  const handleBonusClick = (bonus: BonusRedeemData) => {
    setSelectedBonus(bonus);
    setShowBonusModal(true);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Table columns configuration
  const columns: TableColumn<BonusRedeemData>[] = [
    {
      key: 'id',
      label: 'Redeem ID',
      width: '120px',
      render: (bonus) => (
        <span className="font-mono text-sm font-medium text-gray-100">
          #{bonus.id}
        </span>
      )
    },
    {
      key: 'bonus_name',
      label: 'Bonus Name',
      width: '200px',
      render: (bonus) => (
        <div className="font-medium text-gray-100">
          {bonus.bonus.name}
        </div>
      )
    },
    {
      key: 'type',
      label: 'Type',
      width: '100px',
      render: (bonus) => (
        <span className="text-gray-100 capitalize">{bonus.bonus.model}</span>
      )
    },
    {
      key: 'amount',
      label: 'Amount',
      width: '120px',
      render: (bonus) => (
        <span className="font-medium text-blue-400">
          {formatCurrency(bonus.amount, bonus.bonus.currency)}
        </span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      width: '100px',
      render: (bonus) => (
        bonus.is_active ? (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-green-500 bg-green-500/10">
            <CheckCircle size={12} />
            Active
          </span>
        ) : (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-gray-500 bg-gray-500/10">
            <XCircle size={12} />
            Inactive
          </span>
        )
      )
    },
    {
      key: 'completed',
      label: 'Completed',
      width: '100px',
      render: (bonus) => (
        bonus.is_completed ? (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-green-500 bg-green-500/10">
            <CheckCircle size={12} />
            Yes
          </span>
        ) : (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-yellow-500 bg-yellow-500/10">
            <Clock size={12} />
            In Progress
          </span>
        )
      )
    },
    {
      key: 'operator',
      label: 'Assigned By',
      width: '140px',
      render: (bonus) => (
        <div className="flex items-center gap-2">
          <User size={14} className="text-gray-400" />
          <span className="text-gray-100">{bonus.operator.name}</span>
        </div>
      )
    },
    {
      key: 'timestamp',
      label: 'Redeemed',
      width: '180px',
      render: (bonus) => (
        <div className="flex items-center gap-2">
          <Calendar size={14} className="text-gray-400" />
          <span className="text-sm text-gray-300">
            {formatTimestamp(bonus.timestamp)}
          </span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      render: (bonus) => (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleBonusClick(bonus);
          }}
          className="btn btn-outline text-xs py-1 px-2 flex items-center"
        >
          <Eye size={14} className="mr-1" />
          View
        </button>
      )
    }
  ];

  const bonuses = bonusRedeemsData?.data || [];
  const total = bonusRedeemsData?.total || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-100">Bonuses</h3>
          <p className="text-sm text-gray-400">
            Total: {total.toLocaleString()} bonus redeems
          </p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={refetch}
            className="btn btn-outline"
            disabled={loading}
          >
            <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Bonuses Table */}
      <UnifiedTable
        data={bonuses}
        columns={columns}
        isLoading={loading}
        error={error}
        onRowClick={handleBonusClick}
        onRetry={refetch}
        minWidth="1200px"
        emptyState={{
          icon: <Gift className="w-12 h-12 text-gray-400 mb-4" />,
          title: 'No bonuses found',
          description: 'This customer has no bonus redemption history.'
        }}
      />

      {/* Pagination */}
      {total > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(total / itemsPerPage)}
          itemsPerPage={itemsPerPage}
          totalItems={total}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
        />
      )}

      {/* Bonus Details Modal */}
      {selectedBonus && (
        <Modal
          isOpen={showBonusModal}
          onClose={() => setShowBonusModal(false)}
          title="Bonus Details"
          size="large"
        >
          <BonusDetailsModal bonus={selectedBonus} />
        </Modal>
      )}
    </div>
  );
};

export default BonusesTab;
