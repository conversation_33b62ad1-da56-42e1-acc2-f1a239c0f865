import React, { useState, useCallback, useMemo } from 'react';
import { Gift, RefreshCw, Eye, DollarSign, User, ArrowRight } from 'lucide-react';
import { fetchTips, type TipData, type TipsSearchParams } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface TipsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface TipDetailsModalProps {
  tip: TipData;
}

const TipDetailsModal: React.FC<TipDetailsModalProps> = ({ tip }) => {
  const formatTimestamp = (timestamp: string): string => {
    return new Date(parseInt(timestamp) * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string, currency: string): string => {
    const value = parseFloat(amount);
    return `${value.toFixed(8)} ${currency}`;
  };

  const formatUSD = (amount: string): string => {
    const value = parseFloat(amount);
    return `$${value.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* Tip Overview */}
      <div className="bg-dark-600 rounded-lg p-4 border border-dark-500">
        <div className="flex items-center gap-3 mb-4">
          <Gift className="w-6 h-6 text-primary-500" />
          <div>
            <h3 className="text-lg font-semibold text-gray-100">Tip #{tip.id}</h3>
            <p className="text-sm text-gray-400">Transaction Details</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Amount</label>
            <p className="text-gray-100 font-mono">{formatCurrency(tip.amount, tip.code)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">USD Value</label>
            <p className="text-gray-100 font-mono">{formatUSD(tip.usd_amount)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Date & Time</label>
            <p className="text-gray-100">{formatTimestamp(tip.timestamp)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Wallet ID</label>
            <p className="text-gray-100 font-mono">#{tip.tipped_wallet_id}</p>
          </div>
        </div>
      </div>

      {/* Sender Information */}
      <div className="bg-dark-600 rounded-lg p-4 border border-dark-500">
        <h4 className="text-lg font-semibold text-gray-100 mb-4 flex items-center gap-2">
          <User className="w-5 h-5 text-blue-500" />
          Sender (Tipper)
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Customer ID</label>
            <p className="text-gray-100 font-mono">#{tip.customer.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
            <p className="text-gray-100">@{tip.customer.username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Masked Username</label>
            <p className="text-gray-100 font-mono">{tip.customer.masked_username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">VIP Rank</label>
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              tip.customer.rank === 'no-vip' 
                ? 'bg-gray-500/20 text-gray-400' 
                : 'bg-yellow-500/20 text-yellow-400'
            }`}>
              {tip.customer.rank}
            </span>
          </div>
        </div>
      </div>

      {/* Recipient Information */}
      <div className="bg-dark-600 rounded-lg p-4 border border-dark-500">
        <h4 className="text-lg font-semibold text-gray-100 mb-4 flex items-center gap-2">
          <User className="w-5 h-5 text-green-500" />
          Recipient (Tipped)
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Customer ID</label>
            <p className="text-gray-100 font-mono">#{tip.tipped.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
            <p className="text-gray-100">@{tip.tipped.username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Email</label>
            <p className="text-gray-100">{tip.tipped.email}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Masked Username</label>
            <p className="text-gray-100 font-mono">{tip.tipped.masked_username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">VIP Rank</label>
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              tip.tipped.rank === 'no-vip' 
                ? 'bg-gray-500/20 text-gray-400' 
                : 'bg-yellow-500/20 text-yellow-400'
            }`}>
              {tip.tipped.rank}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

const TipsTab: React.FC<TipsTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [selectedTip, setSelectedTip] = useState<TipData | null>(null);
  const [showTipModal, setShowTipModal] = useState(false);

  // Search parameters - filter by customer ID (memoized to prevent infinite re-renders)
  const searchParams = useMemo<TipsSearchParams>(() => ({
    customer_id: customer.id.toString()
  }), [customer.id]);

  // Fetch tips data
  const fetchTipsData = useCallback(async () => {
    const result = await fetchTips(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      // The API response is wrapped by proxy server, so data is at result.data.data
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load tips');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: tipsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchTipsData,
    dependencies: [currentPage, itemsPerPage, customer.id]
  });

  // Format currency with proper symbol
  const formatCurrency = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbols: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'TRY': '₺',
      'GBP': '£'
    };
    const symbol = symbols[currency] || currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(parseInt(timestamp) * 1000).toLocaleString();
  };

  // Handle tip modal
  const handleTipClick = (tip: TipData) => {
    setSelectedTip(tip);
    setShowTipModal(true);
  };

  const handleCloseModal = () => {
    setShowTipModal(false);
    setSelectedTip(null);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1); // Reset to first page
  };

  const tips = tipsData?.data || [];
  const total = tipsData?.total || 0;
  const totalPages = Math.ceil(total / itemsPerPage);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Gift className="w-6 h-6 text-primary-500" />
          <div>
            <h2 className="text-2xl font-bold text-gray-100">Tips</h2>
            <p className="text-gray-400">Customer tip transactions</p>
          </div>
        </div>
        <button
          onClick={() => refetch()}
          disabled={loading}
          className="btn btn-outline btn-sm flex items-center gap-2"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <p className="text-red-400 text-sm">Error loading tips: {error}</p>
        </div>
      )}

      {/* Tips Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-dark-600 border-b border-dark-500">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Tip ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Direction
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  USD Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Other Party
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center">
                    <div className="flex items-center justify-center">
                      <RefreshCw className="w-6 h-6 animate-spin text-primary-500 mr-2" />
                      <span className="text-gray-400">Loading tips...</span>
                    </div>
                  </td>
                </tr>
              ) : tips.length > 0 ? (
                tips.map((tip) => {
                  const isSender = tip.customer_id === customer.id;
                  const otherParty = isSender ? tip.tipped : tip.customer;
                  
                  return (
                    <tr
                      key={tip.id}
                      className="hover:bg-dark-600/50 cursor-pointer transition-colors"
                      onClick={() => handleTipClick(tip)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-100">
                          #{tip.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {isSender ? (
                            <>
                              <ArrowRight className="w-4 h-4 text-red-400" />
                              <span className="text-red-400 text-sm font-medium">Sent</span>
                            </>
                          ) : (
                            <>
                              <ArrowRight className="w-4 h-4 text-green-400 rotate-180" />
                              <span className="text-green-400 text-sm font-medium">Received</span>
                            </>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-100 font-mono">
                          {parseFloat(tip.amount).toFixed(8)} {tip.code}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-100 font-mono">
                          ${parseFloat(tip.usd_amount).toFixed(2)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-100">
                          @{otherParty.username}
                        </div>
                        <div className="text-xs text-gray-400">
                          ID: {otherParty.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-300">
                          {formatTimestamp(tip.timestamp)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleTipClick(tip);
                          }}
                          className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                          title="View Details"
                        >
                          <Eye size={14} />
                          Details
                        </button>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <Gift className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No tips found</p>
                      <p className="text-sm">This customer has no tip transactions.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {total > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={total}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        )}
      </div>

      {/* Tip Details Modal */}
      <Modal
        isOpen={showTipModal}
        onClose={handleCloseModal}
        title="Tip Details"
        size="lg"
      >
        {selectedTip && (
          <TipDetailsModal tip={selectedTip} />
        )}
      </Modal>
    </div>
  );
};

export default TipsTab;
