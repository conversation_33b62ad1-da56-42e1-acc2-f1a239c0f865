import React, { useState, useCallback } from 'react';
import { Gift, Download, RefreshCw, Eye, DollarSign, TrendingUp, Calendar, Clock } from 'lucide-react';
import { fetchRakebackUsages, fetchRakebackAvailables, type RakebackUsage, type RakebackAvailable, type RakebackSearchParams } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';
import DateRangePicker from '../../../ui/DateRangePicker';

interface RakebacksTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface RakebackUsageDetailsModalProps {
  usage: RakebackUsage;
}

interface RakebackAvailableDetailsModalProps {
  available: RakebackAvailable;
}

interface DateRange {
  from: Date | null;
  to: Date | null;
}

const RakebackUsageDetailsModal: React.FC<RakebackUsageDetailsModalProps> = ({ usage }) => {
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  const formatCurrency = (amount: string): string => {
    return `${parseFloat(amount).toFixed(8)}`;
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Usage ID</label>
            <p className="text-gray-100 font-mono">#{usage.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Customer ID</label>
            <p className="text-gray-100 font-mono">{usage.customer_id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Wallet ID</label>
            <p className="text-gray-100 font-mono">{usage.wallet_id}</p>
          </div>
        </div>

        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Rakeback Amount</label>
            <p className="text-gray-100 text-lg font-semibold">{formatCurrency(usage.amount)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">USD Amount</label>
            <p className="text-gray-100">${parseFloat(usage.amount_usd).toFixed(2)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Timestamp</label>
            <p className="text-gray-100">{formatTimestamp(usage.timestamp)}</p>
          </div>
        </div>
      </div>

      {/* Balance Information */}
      <div className="border-t border-dark-600 pt-4">
        <h4 className="text-lg font-medium text-gray-100 mb-3">Balance Changes</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Before Balance</label>
            <p className="text-gray-100">{formatCurrency(usage.before_balance)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">After Balance</label>
            <p className="text-gray-100">{formatCurrency(usage.after_balance)}</p>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="border-t border-dark-600 pt-4">
        <h4 className="text-lg font-medium text-gray-100 mb-3">Customer Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
            <p className="text-gray-100">{usage.customer.username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Email</label>
            <p className="text-gray-100">{usage.customer.email}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Rank</label>
            <p className="text-gray-100 capitalize">{usage.customer.rank}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Masked Username</label>
            <p className="text-gray-100 font-mono">{usage.customer.masked_username}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const RakebackAvailableDetailsModal: React.FC<RakebackAvailableDetailsModalProps> = ({ available }) => {
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatCurrency = (amount: string, currency: string): string => {
    return `${parseFloat(amount).toFixed(8)} ${currency}`;
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Available ID</label>
            <p className="text-gray-100 font-mono">#{available.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Customer ID</label>
            <p className="text-gray-100 font-mono">{available.customer_id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Last Time</label>
            <p className="text-gray-100">{formatTimestamp(available.lasttime)}</p>
          </div>
        </div>

        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">USD Amount</label>
            <p className="text-gray-100 text-lg font-semibold">${parseFloat(available.usd_amount).toFixed(2)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Wallet Amount</label>
            <p className="text-gray-100">{formatCurrency(available.wallet_amount, available.wallet_currency)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Wallet Currency</label>
            <p className="text-gray-100">{available.wallet_currency}</p>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="border-t border-dark-600 pt-4">
        <h4 className="text-lg font-medium text-gray-100 mb-3">Customer Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
            <p className="text-gray-100">{available.customer.username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Email</label>
            <p className="text-gray-100">{available.customer.email}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Rank</label>
            <p className="text-gray-100 capitalize">{available.customer.rank}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Masked Username</label>
            <p className="text-gray-100 font-mono">{available.customer.masked_username}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const RakebacksTab: React.FC<RakebacksTabProps> = ({ customer }) => {
  // State for pagination
  const [usagesCurrentPage, setUsagesCurrentPage] = useState(1);
  const [availablesCurrentPage, setAvailablesCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  
  // State for modals
  const [selectedUsage, setSelectedUsage] = useState<RakebackUsage | null>(null);
  const [selectedAvailable, setSelectedAvailable] = useState<RakebackAvailable | null>(null);
  const [showUsageModal, setShowUsageModal] = useState(false);
  const [showAvailableModal, setShowAvailableModal] = useState(false);

  // Date range state
  const [dateRange, setDateRange] = useState<DateRange>(() => {
    const to = new Date();
    to.setHours(23, 59, 59, 999);
    const from = new Date();
    from.setDate(from.getDate() - 29); // Last 30 days
    from.setHours(0, 0, 0, 0);
    return { from, to };
  });

  // Search parameters
  const [searchParams, setSearchParams] = useState<Partial<RakebackSearchParams>>(() => {
    const params: Partial<RakebackSearchParams> = {
      customer_id: customer.id.toString()
    };
    
    if (dateRange.from && dateRange.to) {
      params.from = Math.floor(dateRange.from.getTime() / 1000);
      params.to = Math.floor(dateRange.to.getTime() / 1000);
    }
    
    return params;
  });

  // Update search params when date range changes
  React.useEffect(() => {
    setSearchParams(prev => ({
      ...prev,
      from: dateRange.from ? Math.floor(dateRange.from.getTime() / 1000) : null,
      to: dateRange.to ? Math.floor(dateRange.to.getTime() / 1000) : null
    }));
    setUsagesCurrentPage(1);
    setAvailablesCurrentPage(1);
  }, [dateRange]);

  // Fetch rakeback usages data
  const fetchUsagesData = useCallback(async () => {
    const result = await fetchRakebackUsages(usagesCurrentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load rakeback usages');
    }
  }, [usagesCurrentPage, itemsPerPage, searchParams]);

  // Fetch rakeback availables data
  const fetchAvailablesData = useCallback(async () => {
    const result = await fetchRakebackAvailables(availablesCurrentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load rakeback availables');
    }
  }, [availablesCurrentPage, itemsPerPage, searchParams]);

  const { data: usagesData, loading: usagesLoading, error: usagesError, refetch: refetchUsages } = useRefreshableData({
    fetchFn: fetchUsagesData,
    dependencies: [usagesCurrentPage, itemsPerPage, searchParams]
  });

  const { data: availablesData, loading: availablesLoading, error: availablesError, refetch: refetchAvailables } = useRefreshableData({
    fetchFn: fetchAvailablesData,
    dependencies: [availablesCurrentPage, itemsPerPage, searchParams]
  });

  const handleUsageClick = (usage: RakebackUsage) => {
    setSelectedUsage(usage);
    setShowUsageModal(true);
  };

  const handleAvailableClick = (available: RakebackAvailable) => {
    setSelectedAvailable(available);
    setShowAvailableModal(true);
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  const formatCurrency = (amount: string, currency?: string): string => {
    return `${parseFloat(amount).toFixed(8)}${currency ? ` ${currency}` : ''}`;
  };

  // Export function for usages
  const handleExportUsages = () => {
    if (!usagesData?.data) return;

    const csvData = usagesData.data.map(usage => ({
      'Usage ID': usage.id,
      'Customer ID': usage.customer_id,
      'Customer Username': usage.customer.username,
      'Customer Email': usage.customer.email,
      'Customer Rank': usage.customer.rank,
      'Wallet ID': usage.wallet_id,
      'Amount': usage.amount,
      'USD Amount': usage.amount_usd,
      'Before Balance': usage.before_balance,
      'After Balance': usage.after_balance,
      'Timestamp': formatTimestamp(usage.timestamp)
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(value => `"${value}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rakeback_usages_${customer.username}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Export function for availables
  const handleExportAvailables = () => {
    if (!availablesData?.data) return;

    const csvData = availablesData.data.map(available => ({
      'Available ID': available.id,
      'Customer ID': available.customer_id,
      'Customer Username': available.customer.username,
      'Customer Email': available.customer.email,
      'Customer Rank': available.customer.rank,
      'USD Amount': available.usd_amount,
      'Wallet Amount': available.wallet_amount,
      'Wallet Currency': available.wallet_currency,
      'Last Time': new Date(available.lasttime * 1000).toLocaleString()
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(value => `"${value}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rakeback_availables_${customer.username}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const refreshAll = () => {
    refetchUsages();
    refetchAvailables();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Gift className="w-6 h-6 text-primary-400" />
          <div>
            <h2 className="text-xl font-semibold text-gray-100">Rakebacks</h2>
            <p className="text-gray-400 text-sm">
              {dateRange.from && dateRange.to
                ? `${dateRange.from.toLocaleDateString()} - ${dateRange.to.toLocaleDateString()}`
                : 'Select date range'}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
          />

          <button
            onClick={refreshAll}
            disabled={usagesLoading || availablesLoading}
            className="btn btn-outline flex items-center gap-2"
          >
            <RefreshCw size={16} className={(usagesLoading || availablesLoading) ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Total Usages</p>
              <p className="text-2xl font-bold text-green-400">
                {usagesData?.total || 0}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Total Available</p>
              <p className="text-2xl font-bold text-blue-400">
                {availablesData?.total || 0}
              </p>
            </div>
            <Clock className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Used Amount (USD)</p>
              <p className="text-2xl font-bold text-purple-400">
                ${usagesData?.data?.reduce((sum, usage) => sum + parseFloat(usage.amount_usd), 0).toFixed(2) || '0.00'}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Available Amount (USD)</p>
              <p className="text-2xl font-bold text-yellow-400">
                ${availablesData?.data?.reduce((sum, available) => sum + parseFloat(available.usd_amount), 0).toFixed(2) || '0.00'}
              </p>
            </div>
            <Gift className="w-8 h-8 text-yellow-400" />
          </div>
        </div>
      </div>

      {/* Rakeback Usages Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-100">Rakeback Usages</h3>
          <button
            onClick={handleExportUsages}
            disabled={!usagesData?.data || usagesData.data.length === 0}
            className="btn btn-outline flex items-center gap-2"
          >
            <Download size={16} />
            Export CSV
          </button>
        </div>

        {usagesLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Gift className="w-12 h-12 text-primary-400 mx-auto mb-4 animate-pulse" />
              <h3 className="text-lg font-medium text-gray-100 mb-2">Loading rakeback usages...</h3>
              <p className="text-gray-400">Please wait while we fetch the data.</p>
            </div>
          </div>
        ) : usagesError ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Gift className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-100 mb-2">Failed to load rakeback usages</h3>
              <p className="text-gray-400 mb-4">{usagesError}</p>
              <button
                onClick={refetchUsages}
                className="btn btn-primary flex items-center gap-2 mx-auto"
              >
                <RefreshCw size={16} />
                Try Again
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-dark-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      USD Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Balance Change
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {usagesData?.data && usagesData.data.length > 0 ? (
                    usagesData.data.map((usage) => (
                      <tr
                        key={usage.id}
                        className="hover:bg-dark-600/50 cursor-pointer transition-colors"
                        onClick={() => handleUsageClick(usage)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-100">
                            #{usage.id}
                          </div>
                          <div className="text-xs text-gray-400">
                            Wallet: {usage.wallet_id}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-100">{usage.customer.username}</div>
                          <div className="text-xs text-gray-400 capitalize">{usage.customer.rank}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-100 font-semibold">
                            {formatCurrency(usage.amount)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-semibold text-green-400">
                            ${parseFloat(usage.amount_usd).toFixed(2)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-xs text-gray-400">Before: {parseFloat(usage.before_balance).toFixed(8)}</div>
                          <div className="text-xs text-gray-400">After: {parseFloat(usage.after_balance).toFixed(8)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-100">
                            {formatTimestamp(usage.timestamp)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleUsageClick(usage);
                            }}
                            className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                            title="View Details"
                          >
                            <Eye size={14} />
                            Details
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="px-6 py-12 text-center">
                        <div className="text-gray-400">
                          <Gift className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <p className="text-lg font-medium mb-2">No rakeback usages found</p>
                          <p className="text-sm">No rakeback usages in the selected date range.</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Usages Pagination */}
            {usagesData?.total && usagesData.total > 0 && (
              <Pagination
                currentPage={usagesCurrentPage}
                totalPages={Math.ceil(usagesData.total / itemsPerPage)}
                totalItems={usagesData.total}
                itemsPerPage={itemsPerPage}
                onPageChange={setUsagesCurrentPage}
                onItemsPerPageChange={(newItemsPerPage) => {
                  setItemsPerPage(newItemsPerPage);
                  setUsagesCurrentPage(1);
                }}
                isLoading={usagesLoading}
              />
            )}
          </div>
        )}
      </div>

      {/* Rakeback Availables Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-100">Available Rakebacks</h3>
          <button
            onClick={handleExportAvailables}
            disabled={!availablesData?.data || availablesData.data.length === 0}
            className="btn btn-outline flex items-center gap-2"
          >
            <Download size={16} />
            Export CSV
          </button>
        </div>

        {availablesLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Clock className="w-12 h-12 text-primary-400 mx-auto mb-4 animate-pulse" />
              <h3 className="text-lg font-medium text-gray-100 mb-2">Loading available rakebacks...</h3>
              <p className="text-gray-400">Please wait while we fetch the data.</p>
            </div>
          </div>
        ) : availablesError ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Clock className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-100 mb-2">Failed to load available rakebacks</h3>
              <p className="text-gray-400 mb-4">{availablesError}</p>
              <button
                onClick={refetchAvailables}
                className="btn btn-primary flex items-center gap-2 mx-auto"
              >
                <RefreshCw size={16} />
                Try Again
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-dark-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      USD Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Wallet Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Currency
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Last Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-600">
                  {availablesData?.data && availablesData.data.length > 0 ? (
                    availablesData.data.map((available) => (
                      <tr
                        key={available.id}
                        className="hover:bg-dark-600/50 cursor-pointer transition-colors"
                        onClick={() => handleAvailableClick(available)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-100">
                            #{available.id}
                          </div>
                          <div className="text-xs text-gray-400">
                            Customer: {available.customer_id}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-100">{available.customer.username}</div>
                          <div className="text-xs text-gray-400 capitalize">{available.customer.rank}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-semibold text-blue-400">
                            ${parseFloat(available.usd_amount).toFixed(2)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-100 font-semibold">
                            {formatCurrency(available.wallet_amount)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-100">
                            {available.wallet_currency}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-100">
                            {new Date(available.lasttime * 1000).toLocaleString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAvailableClick(available);
                            }}
                            className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                            title="View Details"
                          >
                            <Eye size={14} />
                            Details
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="px-6 py-12 text-center">
                        <div className="text-gray-400">
                          <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <p className="text-lg font-medium mb-2">No available rakebacks found</p>
                          <p className="text-sm">No available rakebacks in the selected date range.</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Availables Pagination */}
            {availablesData?.total && availablesData.total > 0 && (
              <Pagination
                currentPage={availablesCurrentPage}
                totalPages={Math.ceil(availablesData.total / itemsPerPage)}
                totalItems={availablesData.total}
                itemsPerPage={itemsPerPage}
                onPageChange={setAvailablesCurrentPage}
                onItemsPerPageChange={(newItemsPerPage) => {
                  setItemsPerPage(newItemsPerPage);
                  setAvailablesCurrentPage(1);
                }}
                isLoading={availablesLoading}
              />
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      {selectedUsage && (
        <Modal
          isOpen={showUsageModal}
          onClose={() => setShowUsageModal(false)}
          title={`Rakeback Usage #${selectedUsage.id}`}
          size="lg"
        >
          <RakebackUsageDetailsModal usage={selectedUsage} />
        </Modal>
      )}

      {selectedAvailable && (
        <Modal
          isOpen={showAvailableModal}
          onClose={() => setShowAvailableModal(false)}
          title={`Available Rakeback #${selectedAvailable.id}`}
          size="lg"
        >
          <RakebackAvailableDetailsModal available={selectedAvailable} />
        </Modal>
      )}
    </div>
  );
};

export default RakebacksTab;
