import React, { useState, useCallback } from 'react';
import { GitCommit, Download, Filter, RefreshCw, Eye, TrendingUp, TrendingDown, DollarSign, Activity } from 'lucide-react';
import { fetchCommits, type CommitData, type CommitSearchParams, COMMIT_MODELS } from '../../../../utils/api/commits';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface CommitsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface CommitDetailsModalProps {
  commit: CommitData;
}

const CommitDetailsModal: React.FC<CommitDetailsModalProps> = ({ commit }) => {
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const formatAmount = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    return `${value.toFixed(4)} ${currency}`;
  };

  return (
    <div className="space-y-6">
      {/* Basic Info */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Commit ID</label>
          <p className="text-gray-100 font-mono">#{commit.id}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Timestamp</label>
          <p className="text-gray-100">{formatTimestamp(commit.timestamp)}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Customer</label>
          <p className="text-gray-100">{commit.customer.username} (#{commit.customer_id})</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Currency</label>
          <p className="text-gray-100 font-semibold">{commit.wallet_currency}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Way</label>
          <p className={`text-sm font-medium ${
            commit.way === 'deposit' ? 'text-green-400' : 'text-red-400'
          }`}>
            {commit.way}
          </p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Model</label>
          <p className="text-gray-100 font-medium">{commit.model}</p>
        </div>
      </div>

      {/* Amount Details */}
      <div className="bg-dark-600 rounded-lg p-4">
        <h4 className="text-lg font-semibold text-gray-100 mb-4">Balance Changes</h4>
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Before Balance</label>
            <p className="text-gray-100 font-mono">{formatAmount(commit.before_balance, commit.wallet_currency)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Amount</label>
            <p className={`font-mono font-semibold ${
              commit.way === 'deposit' ? 'text-green-400' : 'text-red-400'
            }`}>
              {commit.way === 'deposit' ? '+' : '-'}{formatAmount(commit.amount, commit.wallet_currency)}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">After Balance</label>
            <p className="text-gray-100 font-mono">{formatAmount(commit.after_balance, commit.wallet_currency)}</p>
          </div>
        </div>
      </div>

      {/* Related Object */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">Related Object</label>
        <div className="bg-dark-600 rounded-lg p-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Type</label>
              <p className="text-gray-100 font-mono">{commit.commitable_type}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">ID</label>
              <p className="text-gray-100 font-mono">#{commit.commitable_id}</p>
            </div>
          </div>
          {commit.commitable && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">Object Data</label>
              <pre className="text-sm text-gray-100 whitespace-pre-wrap overflow-x-auto bg-dark-700 p-3 rounded">
                {JSON.stringify(commit.commitable, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>

      {/* Customer Info */}
      <div className="bg-dark-600 rounded-lg p-4">
        <h4 className="text-lg font-semibold text-gray-100 mb-4">Customer Information</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Username</label>
            <p className="text-gray-100">{commit.customer.username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Email</label>
            <p className="text-gray-100">{commit.customer.email}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Masked Username</label>
            <p className="text-gray-100 font-mono">{commit.customer.masked_username}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Last Action</label>
            <p className="text-gray-100">{formatTimestamp(commit.customer.last_action)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const CommitsTab: React.FC<CommitsTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [selectedCommit, setSelectedCommit] = useState<CommitData | null>(null);
  const [showCommitModal, setShowCommitModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<CommitSearchParams>>({
    customer_id: customer.id.toString()
  });

  // Fetch commits data
  const fetchCommitsData = useCallback(async () => {
    const result = await fetchCommits(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load commits');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: commitsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchCommitsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format amount with currency
  const formatAmount = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    return `${value.toFixed(4)} ${currency}`;
  };

  // Handle commit modal
  const handleCommitClick = (commit: CommitData) => {
    setSelectedCommit(commit);
    setShowCommitModal(true);
  };

  const handleCloseModal = () => {
    setShowCommitModal(false);
    setSelectedCommit(null);
  };

  // Get way badge
  const getWayBadge = (way: string) => {
    const wayConfig: { [key: string]: { color: string; icon: React.ReactNode } } = {
      'deposit': { color: 'bg-green-500/20 text-green-400', icon: <TrendingUp size={14} /> },
      'withdraw': { color: 'bg-red-500/20 text-red-400', icon: <TrendingDown size={14} /> }
    };

    const config = wayConfig[way] || { color: 'bg-gray-500/20 text-gray-400', icon: <Activity size={14} /> };

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full ${config.color}`}>
        {config.icon}
        {way}
      </span>
    );
  };

  // Get model badge color
  const getModelBadge = (model: string) => {
    const modelColors: { [key: string]: string } = {
      'debit': 'bg-blue-500/20 text-blue-400',
      'deposit': 'bg-green-500/20 text-green-400',
      'bonus-drop': 'bg-purple-500/20 text-purple-400',
      'correction-up': 'bg-cyan-500/20 text-cyan-400',
      'correction-down': 'bg-orange-500/20 text-orange-400',
      'discount': 'bg-yellow-500/20 text-yellow-400',
      'rakeback': 'bg-indigo-500/20 text-indigo-400'
    };

    const color = modelColors[model] || 'bg-gray-500/20 text-gray-400';

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${color}`}>
        {model}
      </span>
    );
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof CommitSearchParams, value: string | string[]) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value || null
    }));
    setCurrentPage(1);
  };

  // Clear filters
  const clearFilters = () => {
    setSearchParams({
      customer_id: customer.id.toString()
    });
    setCurrentPage(1);
  };

  // Export commits data
  const handleExport = () => {
    if (!commitsData?.data) return;

    const csvData = commitsData.data.map(commit => ({
      ID: commit.id,
      'Customer ID': commit.customer_id,
      'Customer Username': commit.customer.username,
      'Timestamp': formatTimestamp(commit.timestamp),
      'Currency': commit.wallet_currency,
      'Way': commit.way,
      'Model': commit.model,
      'Amount': commit.amount,
      'Before Balance': commit.before_balance,
      'After Balance': commit.after_balance,
      'Commitable Type': commit.commitable_type,
      'Commitable ID': commit.commitable_id
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(value => `"${value}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `commits_${customer.username}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading commits...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-error-500/20 border border-error-500 rounded-md p-6 text-center">
        <p className="text-error-500 text-lg mb-2">Failed to load commits</p>
        <p className="text-gray-400 mb-4">{error}</p>
        <button
          onClick={refetch}
          className="btn btn-primary flex items-center gap-2 mx-auto"
        >
          <RefreshCw size={16} />
          Try Again
        </button>
      </div>
    );
  }

  const commits = commitsData?.data || [];
  const total = commitsData?.total || 0;
  const totalPages = Math.ceil(total / itemsPerPage);

  // Calculate summary statistics
  const summary = commits.reduce((acc, commit) => {
    const amount = parseFloat(commit.amount);

    if (commit.way === 'deposit') {
      acc.totalDeposits += amount;
      acc.depositCount++;
    } else if (commit.way === 'withdraw') {
      acc.totalWithdrawals += amount;
      acc.withdrawalCount++;
    }

    return acc;
  }, {
    totalDeposits: 0,
    totalWithdrawals: 0,
    depositCount: 0,
    withdrawalCount: 0
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <GitCommit className="w-6 h-6 text-primary-500" />
          <div>
            <h2 className="text-xl font-semibold text-gray-100">Commits</h2>
            <p className="text-sm text-gray-400">
              {total} total commits for {customer.username}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn flex items-center gap-2 ${showFilters ? 'btn-primary' : 'btn-outline'}`}
          >
            <Filter size={16} />
            Filters
          </button>

          <button
            onClick={handleExport}
            disabled={commits.length === 0}
            className="btn btn-outline flex items-center gap-2"
          >
            <Download size={16} />
            Export CSV
          </button>

          <button
            onClick={refetch}
            disabled={loading}
            className="btn btn-outline flex items-center gap-2"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {commits.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold text-gray-100">Summary Statistics</h3>
            <span className="text-sm text-gray-400 bg-dark-600 px-2 py-1 rounded">
              Based on {commits.length} displayed commits
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Deposits</p>
                  <p className="text-2xl font-bold text-green-400">
                    {summary.totalDeposits.toFixed(4)}
                  </p>
                  <p className="text-xs text-gray-500">{summary.depositCount} commits</p>
                </div>
                <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-green-400" />
                </div>
              </div>
            </div>

            <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Withdrawals</p>
                  <p className="text-2xl font-bold text-red-400">
                    {summary.totalWithdrawals.toFixed(4)}
                  </p>
                  <p className="text-xs text-gray-500">{summary.withdrawalCount} commits</p>
                </div>
                <div className="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center">
                  <TrendingDown className="w-5 h-5 text-red-400" />
                </div>
              </div>
            </div>

            <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Net Balance</p>
                  <p className={`text-2xl font-bold ${
                    summary.totalDeposits - summary.totalWithdrawals >= 0
                      ? 'text-green-400'
                      : 'text-red-400'
                  }`}>
                    {(summary.totalDeposits - summary.totalWithdrawals).toFixed(4)}
                  </p>
                  <p className="text-xs text-gray-500">Deposits - Withdrawals</p>
                </div>
                <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-blue-400" />
                </div>
              </div>
            </div>

            <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Commits</p>
                  <p className="text-2xl font-bold text-primary-400">{total}</p>
                  <p className="text-xs text-gray-500">All time</p>
                </div>
                <div className="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                  <GitCommit className="w-5 h-5 text-primary-400" />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters Section */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Way
              </label>
              <select
                value={searchParams.way || ''}
                onChange={(e) => handleFilterChange('way', e.target.value)}
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Ways</option>
                <option value="deposit">Deposit</option>
                <option value="withdraw">Withdraw</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Currency
              </label>
              <select
                value={searchParams.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="TRY">TRY</option>
                <option value="GBP">GBP</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Amount Range
              </label>
              <div className="flex gap-2">
                <input
                  type="number"
                  placeholder="Min"
                  value={searchParams.amount_min || ''}
                  onChange={(e) => handleFilterChange('amount_min', e.target.value)}
                  className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
                />
                <input
                  type="number"
                  placeholder="Max"
                  value={searchParams.amount_max || ''}
                  onChange={(e) => handleFilterChange('amount_max', e.target.value)}
                  className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Model
              </label>
              <select
                value={Array.isArray(searchParams.model) ? searchParams.model[0] || '' : ''}
                onChange={(e) => handleFilterChange('model', e.target.value ? [e.target.value] : COMMIT_MODELS)}
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Models</option>
                {COMMIT_MODELS.map(model => (
                  <option key={model} value={model}>{model}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex items-center justify-between mt-4 pt-4 border-t border-dark-600">
            <div className="text-sm text-gray-400">
              {Object.keys(searchParams).filter(key => key !== 'customer_id' && searchParams[key as keyof CommitSearchParams]).length} filters applied
            </div>
            <button
              onClick={clearFilters}
              className="btn btn-outline btn-sm"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Commits Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-dark-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Way
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Model
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Balance Change
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {commits.length > 0 ? (
                commits.map((commit) => (
                  <tr
                    key={commit.id}
                    className="hover:bg-dark-600/50 cursor-pointer transition-colors"
                    onClick={() => handleCommitClick(commit)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-100">
                        #{commit.id}
                      </div>
                      <div className="text-xs text-gray-400">
                        {commit.commitable_type}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getWayBadge(commit.way)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getModelBadge(commit.model)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-medium ${
                        commit.way === 'deposit' ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {commit.way === 'deposit' ? '+' : '-'}{formatAmount(commit.amount, commit.wallet_currency)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100">
                        {formatAmount(commit.before_balance, commit.wallet_currency)} → {formatAmount(commit.after_balance, commit.wallet_currency)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100">
                        {formatTimestamp(commit.timestamp)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCommitClick(commit);
                        }}
                        className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                        title="View Details"
                      >
                        <Eye size={14} />
                        Details
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <GitCommit className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No commits found</p>
                      <p className="text-sm">This customer has no recorded commits.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {total > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={total}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        )}
      </div>

      {/* Commit Details Modal */}
      {selectedCommit && (
        <Modal
          isOpen={showCommitModal}
          onClose={handleCloseModal}
          title={`Commit #${selectedCommit.id} - ${selectedCommit.model}`}
          size="lg"
        >
          <CommitDetailsModal commit={selectedCommit} />
        </Modal>
      )}
    </div>
  );
};

export default CommitsTab;
