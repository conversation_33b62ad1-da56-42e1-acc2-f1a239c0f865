import React from 'react';
import { fetchCasinoBets } from '../../../../utils/api';
import BettingTable from '../../../ui/BettingTable';

interface CasinoBetsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

// Simplified component using BettingTable

const CasinoBetsTab: React.FC<CasinoBetsTabProps> = ({ customer }) => {

  return (
    <BettingTable
      type="casino"
      customer={customer}
      fetchFunction={fetchCasinoBets}
      title="Casino Bets"
    />
  );

};

export default CasinoBetsTab;
