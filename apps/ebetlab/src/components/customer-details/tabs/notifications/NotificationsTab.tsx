import React, { useState, useCallback } from 'react';
import { Bell, Download, Filter, RefreshCw, Eye, AlertCircle, CheckCircle, Info, TrendingUp, TrendingDown, Calendar } from 'lucide-react';
import { fetchNotifications, type NotificationItem, type NotificationSearchParams } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface NotificationsTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface NotificationDetailsModalProps {
  notification: NotificationItem;
}

// Utility function to get notification type icon and color
const getNotificationTypeInfo = (type: string, langKey: string) => {
  const typeInfo = {
    icon: Bell,
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/20',
    label: type
  };

  switch (type.toLowerCase()) {
    case 'deposit':
      typeInfo.icon = TrendingUp;
      typeInfo.color = 'text-green-400';
      typeInfo.bgColor = 'bg-green-500/20';
      typeInfo.label = 'Deposit';
      break;
    case 'withdraw':
      typeInfo.icon = TrendingDown;
      typeInfo.color = 'text-red-400';
      typeInfo.bgColor = 'bg-red-500/20';
      typeInfo.label = 'Withdraw';
      break;
    case 'bonus':
      typeInfo.icon = CheckCircle;
      typeInfo.color = 'text-purple-400';
      typeInfo.bgColor = 'bg-purple-500/20';
      typeInfo.label = 'Bonus';
      break;
    case 'alert':
    case 'warning':
      typeInfo.icon = AlertCircle;
      typeInfo.color = 'text-yellow-400';
      typeInfo.bgColor = 'bg-yellow-500/20';
      typeInfo.label = 'Alert';
      break;
    case 'info':
      typeInfo.icon = Info;
      typeInfo.color = 'text-blue-400';
      typeInfo.bgColor = 'bg-blue-500/20';
      typeInfo.label = 'Info';
      break;
    default:
      typeInfo.label = type.charAt(0).toUpperCase() + type.slice(1);
  }

  return typeInfo;
};

// Utility function to format language key for display
const formatLangKey = (langKey: string): string => {
  return langKey
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Flexible notification data renderer
const NotificationDataRenderer: React.FC<{ data: any; type: string }> = ({ data, type }) => {
  if (!data || typeof data !== 'object') {
    return <span className="text-gray-400">No additional data</span>;
  }

  const entries = Object.entries(data);
  
  if (entries.length === 0) {
    return <span className="text-gray-400">No additional data</span>;
  }

  return (
    <div className="space-y-2">
      {entries.map(([key, value]) => (
        <div key={key} className="flex justify-between">
          <span className="text-gray-400 capitalize">{key.replace('_', ' ')}:</span>
          <span className="text-gray-200">
            {typeof value === 'string' || typeof value === 'number' 
              ? value.toString() 
              : JSON.stringify(value)
            }
          </span>
        </div>
      ))}
    </div>
  );
};

const NotificationDetailsModal: React.FC<NotificationDetailsModalProps> = ({ notification }) => {
  const formatTimestamp = (timestamp: string): string => {
    return new Date(parseInt(timestamp) * 1000).toLocaleString();
  };

  const typeInfo = getNotificationTypeInfo(notification.type, notification.lang_key);
  const IconComponent = typeInfo.icon;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className={`p-3 rounded-lg ${typeInfo.bgColor}`}>
          <IconComponent className={`w-6 h-6 ${typeInfo.color}`} />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-100">{typeInfo.label}</h3>
          <p className="text-gray-400">{formatLangKey(notification.lang_key)}</p>
        </div>
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Notification ID</label>
            <p className="text-gray-100 font-mono">#{notification.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Customer ID</label>
            <p className="text-gray-100 font-mono">{notification.customer_id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Type</label>
            <p className="text-gray-100 capitalize">{notification.type}</p>
          </div>
        </div>

        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Language Key</label>
            <p className="text-gray-100 font-mono">{notification.lang_key}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Timestamp</label>
            <p className="text-gray-100">{formatTimestamp(notification.timestamp)}</p>
          </div>
        </div>
      </div>

      {/* Notification Data */}
      <div className="border-t border-dark-600 pt-4">
        <h4 className="text-lg font-medium text-gray-100 mb-3">Notification Data</h4>
        <div className="bg-dark-600 rounded-lg p-4">
          <NotificationDataRenderer data={notification.data} type={notification.type} />
        </div>
      </div>

      {/* Raw Data (for debugging/completeness) */}
      <div className="border-t border-dark-600 pt-4">
        <h4 className="text-lg font-medium text-gray-100 mb-3">Raw Data</h4>
        <div className="bg-dark-600 rounded-lg p-4">
          <pre className="text-xs text-gray-300 overflow-x-auto">
            {JSON.stringify(notification, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

const NotificationsTab: React.FC<NotificationsTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<NotificationItem | null>(null);
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<NotificationSearchParams>>({
    customer_id: customer.id.toString()
  });

  // Fetch notifications data
  const fetchNotificationsData = useCallback(async () => {
    const result = await fetchNotifications(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load notifications');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: notificationsData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchNotificationsData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleFilterChange = (key: string, value: string) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value || null
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleNotificationClick = (notification: NotificationItem) => {
    setSelectedNotification(notification);
    setShowNotificationModal(true);
  };

  const formatTimestamp = (timestamp: string): string => {
    return new Date(parseInt(timestamp) * 1000).toLocaleString();
  };

  // Export notifications data
  const handleExport = () => {
    if (!notificationsData?.data) return;

    const csvData = notificationsData.data.map(notification => ({
      'Notification ID': notification.id,
      'Customer ID': notification.customer_id,
      'Type': notification.type,
      'Language Key': notification.lang_key,
      'Formatted Message': formatLangKey(notification.lang_key),
      'Timestamp': formatTimestamp(notification.timestamp),
      'Data': JSON.stringify(notification.data),
      // Flatten common data fields
      'Currency': notification.data?.currency || 'N/A',
      'Amount': notification.data?.amount || 'N/A'
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(value => `"${value}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `notifications_${customer.username}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Bell className="w-12 h-12 text-primary-400 mx-auto mb-4 animate-pulse" />
          <h3 className="text-lg font-medium text-gray-100 mb-2">Loading notifications...</h3>
          <p className="text-gray-400">Please wait while we fetch the notification data.</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Bell className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-100 mb-2">Failed to load notifications</h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <button
            onClick={refetch}
            className="btn btn-primary flex items-center gap-2 mx-auto"
          >
            <RefreshCw size={16} />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const notifications = notificationsData?.data || [];
  const total = notificationsData?.total || 0;
  const totalPages = Math.ceil(total / itemsPerPage);

  // Calculate notification type statistics
  const typeStats = notifications.reduce((acc, notification) => {
    acc[notification.type] = (acc[notification.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bell className="w-6 h-6 text-primary-400" />
          <div>
            <h2 className="text-xl font-semibold text-gray-100">Notifications</h2>
            <p className="text-gray-400 text-sm">
              {total > 0 ? `${total} notification${total !== 1 ? 's' : ''} found` : 'No notifications found'}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn flex items-center gap-2 ${showFilters ? 'btn-primary' : 'btn-outline'}`}
          >
            <Filter size={16} />
            Filters
          </button>

          <button
            onClick={handleExport}
            disabled={notifications.length === 0}
            className="btn btn-outline flex items-center gap-2"
          >
            <Download size={16} />
            Export CSV
          </button>

          <button
            onClick={refetch}
            disabled={loading}
            className="btn btn-outline flex items-center gap-2"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Type Statistics */}
      {Object.keys(typeStats).length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {Object.entries(typeStats).map(([type, count]) => {
            const typeInfo = getNotificationTypeInfo(type, '');
            const IconComponent = typeInfo.icon;
            
            return (
              <div key={type} className="bg-dark-700 rounded-lg border border-dark-600 p-3">
                <div className="flex items-center gap-2">
                  <div className={`p-1 rounded ${typeInfo.bgColor}`}>
                    <IconComponent className={`w-4 h-4 ${typeInfo.color}`} />
                  </div>
                  <div>
                    <p className="text-xs text-gray-400 capitalize">{type}</p>
                    <p className="text-sm font-semibold text-gray-100">{count}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="bg-dark-700 rounded-lg border border-dark-600 p-6">
          <h3 className="text-lg font-medium text-gray-100 mb-4">Filter Notifications</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Type
              </label>
              <select
                value={searchParams.type || ''}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              >
                <option value="">All Types</option>
                <option value="deposit">Deposit</option>
                <option value="withdraw">Withdraw</option>
                <option value="bonus">Bonus</option>
                <option value="alert">Alert</option>
                <option value="info">Info</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Language Key
              </label>
              <input
                type="text"
                value={searchParams.lang_key || ''}
                onChange={(e) => handleFilterChange('lang_key', e.target.value)}
                placeholder="Enter language key"
                className="w-full bg-dark-600 border border-dark-500 rounded-md px-3 py-2 text-gray-200 focus:outline-none focus:border-primary-500"
              />
            </div>
          </div>

          <div className="flex justify-end mt-4 pt-4 border-t border-dark-600">
            <button
              onClick={() => {
                setSearchParams({ customer_id: customer.id.toString() });
                setCurrentPage(1);
              }}
              className="btn btn-outline"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Notifications Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-dark-600">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Message
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Data
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {notifications.length > 0 ? (
                notifications.map((notification) => {
                  const typeInfo = getNotificationTypeInfo(notification.type, notification.lang_key);
                  const IconComponent = typeInfo.icon;
                  
                  return (
                    <tr
                      key={notification.id}
                      className="hover:bg-dark-600/50 cursor-pointer transition-colors"
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${typeInfo.bgColor}`}>
                            <IconComponent className={`w-4 h-4 ${typeInfo.color}`} />
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-100 capitalize">
                              {notification.type}
                            </div>
                            <div className="text-xs text-gray-500">
                              #{notification.id}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-300">
                          {formatLangKey(notification.lang_key)}
                        </div>
                        <div className="text-xs text-gray-500 font-mono">
                          {notification.lang_key}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-300">
                          <NotificationDataRenderer data={notification.data} type={notification.type} />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-300">
                          {formatTimestamp(notification.timestamp)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleNotificationClick(notification);
                          }}
                          className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                          title="View Details"
                        >
                          <Eye size={14} />
                          Details
                        </button>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No notifications found</p>
                      <p className="text-sm">This customer has no notification history.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {total > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={total}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        )}
      </div>

      {/* Notification Details Modal */}
      {selectedNotification && (
        <Modal
          isOpen={showNotificationModal}
          onClose={() => setShowNotificationModal(false)}
          title={`Notification #${selectedNotification.id}`}
          size="lg"
        >
          <NotificationDetailsModal notification={selectedNotification} />
        </Modal>
      )}
    </div>
  );
};

export default NotificationsTab;
