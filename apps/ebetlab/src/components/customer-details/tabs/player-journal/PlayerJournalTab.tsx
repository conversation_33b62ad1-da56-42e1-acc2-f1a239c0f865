import React, { useState, useCallback } from 'react';
import { BookOpen, Download, RefreshCw, Eye, Activity, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { fetchPlayerJournal, type PlayerActionData, type PlayerJournalSearchParams } from '../../../../utils/api/player-journal';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface PlayerJournalTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface PlayerActionDetailsModalProps {
  action: PlayerActionData;
}

const PlayerActionDetailsModal: React.FC<PlayerActionDetailsModalProps> = ({ action }) => {
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Basic Info */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Action ID</label>
          <p className="text-gray-100 font-mono">#{action.id}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Timestamp</label>
          <p className="text-gray-100">{formatTimestamp(action.timestamp)}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Requested Action</label>
          <p className="text-gray-100 font-medium">{action.requested}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Customer ID</label>
          <p className="text-gray-100 font-mono">#{action.customer_id}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Merchant ID</label>
          <p className="text-gray-100 font-mono">#{action.merchant_id}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">Website ID</label>
          <p className="text-gray-100 font-mono">#{action.website_id}</p>
        </div>
        {action.wallet_id && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Wallet ID</label>
            <p className="text-gray-100 font-mono">#{action.wallet_id}</p>
          </div>
        )}
        {action.wallet_currency && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Wallet Currency</label>
            <p className="text-gray-100 font-semibold">{action.wallet_currency}</p>
          </div>
        )}
      </div>

      {/* Result */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">Result</label>
        <div className="bg-dark-600 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              action.result.status === 'success' 
                ? 'bg-green-500/20 text-green-400'
                : action.result.status === 'error'
                ? 'bg-red-500/20 text-red-400'
                : 'bg-yellow-500/20 text-yellow-400'
            }`}>
              {action.result.status}
            </span>
          </div>
          <p className="text-gray-100">{action.result.message}</p>
        </div>
      </div>

      {/* Details */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">Action Details</label>
        <div className="bg-dark-600 rounded-lg p-4">
          <pre className="text-sm text-gray-100 whitespace-pre-wrap overflow-x-auto">
            {JSON.stringify(action.details, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

const PlayerJournalTab: React.FC<PlayerJournalTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [selectedAction, setSelectedAction] = useState<PlayerActionData | null>(null);
  const [showActionModal, setShowActionModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<PlayerJournalSearchParams>>({
    customer_id: customer.id.toString()
  });

  // Fetch player journal data
  const fetchPlayerJournalData = useCallback(async () => {
    const result = await fetchPlayerJournal(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load player journal');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: playerJournalData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchPlayerJournalData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Handle action modal
  const handleActionClick = (action: PlayerActionData) => {
    setSelectedAction(action);
    setShowActionModal(true);
  };

  const handleCloseModal = () => {
    setShowActionModal(false);
    setSelectedAction(null);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    const statusConfig: { [key: string]: { color: string; icon: React.ReactNode } } = {
      'success': { color: 'bg-green-500/20 text-green-400', icon: <CheckCircle size={14} /> },
      'error': { color: 'bg-red-500/20 text-red-400', icon: <XCircle size={14} /> },
      'warning': { color: 'bg-yellow-500/20 text-yellow-400', icon: <AlertCircle size={14} /> },
      'pending': { color: 'bg-blue-500/20 text-blue-400', icon: <Activity size={14} /> }
    };

    const config = statusConfig[status] || { color: 'bg-gray-500/20 text-gray-400', icon: <AlertCircle size={14} /> };

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full ${config.color}`}>
        {config.icon}
        {status}
      </span>
    );
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Export player journal data
  const handleExport = () => {
    if (!playerJournalData?.data) return;

    const csvData = playerJournalData.data.map(action => ({
      ID: action.id,
      'Customer ID': action.customer_id,
      'Merchant ID': action.merchant_id,
      'Website ID': action.website_id,
      'Wallet ID': action.wallet_id || 'N/A',
      'Wallet Currency': action.wallet_currency || 'N/A',
      'Requested Action': action.requested,
      'Result Status': action.result.status,
      'Result Message': action.result.message,
      'Timestamp': formatTimestamp(action.timestamp),
      'Details': JSON.stringify(action.details)
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(value => `"${value}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `player_journal_${customer.username}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading player journal...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-error-500/20 border border-error-500 rounded-md p-6 text-center">
        <p className="text-error-500 text-lg mb-2">Failed to load player journal</p>
        <p className="text-gray-400 mb-4">{error}</p>
        <button
          onClick={refetch}
          className="btn btn-primary flex items-center gap-2 mx-auto"
        >
          <RefreshCw size={16} />
          Try Again
        </button>
      </div>
    );
  }

  const actions = playerJournalData?.data || [];
  const total = playerJournalData?.total || 0;
  const totalPages = Math.ceil(total / itemsPerPage);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <BookOpen className="w-6 h-6 text-primary-500" />
          <div>
            <h2 className="text-xl font-semibold text-gray-100">Player Journal</h2>
            <p className="text-sm text-gray-400">
              {total} total player actions for {customer.username}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={handleExport}
            disabled={actions.length === 0}
            className="btn btn-outline flex items-center gap-2"
          >
            <Download size={16} />
            Export CSV
          </button>

          <button
            onClick={refetch}
            disabled={loading}
            className="btn btn-outline flex items-center gap-2"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Player Journal Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-dark-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {actions.length > 0 ? (
                actions.map((action) => (
                  <tr
                    key={action.id}
                    className="hover:bg-dark-600/50 cursor-pointer transition-colors"
                    onClick={() => handleActionClick(action)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-100">
                        #{action.id}
                      </div>
                      <div className="text-xs text-gray-400">
                        Customer #{action.customer_id}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100 font-medium">{action.requested}</div>
                      <div className="text-xs text-gray-400">
                        {action.result.message.length > 50 
                          ? `${action.result.message.substring(0, 50)}...`
                          : action.result.message
                        }
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(action.result.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100">
                        {formatTimestamp(action.timestamp)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleActionClick(action);
                        }}
                        className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                        title="View Details"
                      >
                        <Eye size={14} />
                        Details
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <BookOpen className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No player actions found</p>
                      <p className="text-sm">This customer has no recorded player actions.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {total > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={total}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        )}
      </div>

      {/* Player Action Details Modal */}
      {selectedAction && (
        <Modal
          isOpen={showActionModal}
          onClose={handleCloseModal}
          title={`Player Action #${selectedAction.id} - ${selectedAction.requested}`}
          size="lg"
        >
          <PlayerActionDetailsModal action={selectedAction} />
        </Modal>
      )}
    </div>
  );
};

export default PlayerJournalTab;
