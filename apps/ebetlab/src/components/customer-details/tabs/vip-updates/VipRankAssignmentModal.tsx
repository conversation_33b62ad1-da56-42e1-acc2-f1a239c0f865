import React, { useState } from 'react';
import { X, Crown, Gift } from 'lucide-react';
import { assignVipRank } from '../../../../utils/api';
import { getVipLevelOptions, getVipLevelDisplayName, getVipLevelColor } from '../../../../constants/vipLevels';

interface VipRankAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: {
    id: number;
    username: string;
    rank?: string;
  };
  onSuccess: () => void;
}

const VipRankAssignmentModal: React.FC<VipRankAssignmentModalProps> = ({
  isOpen,
  onClose,
  customer,
  onSuccess
}) => {
  const [selectedRank, setSelectedRank] = useState<string>(customer.rank || 'no-vip');
  const [addGift, setAddGift] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const vipLevelOptions = getVipLevelOptions();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const result = await assignVipRank(customer.id.toString(), selectedRank, addGift);

      if (result.success) {
        onSuccess();
        onClose();
        // Reset form
        setSelectedRank(customer.rank || 'no-vip');
        setAddGift(false);
      } else {
        setError(result.error || 'Failed to assign VIP rank');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      setError('');
      setSelectedRank(customer.rank || 'no-vip');
      setAddGift(false);
    }
  };

  if (!isOpen) return null;

  const currentRankInfo = {
    colorClass: getVipLevelColor(customer.rank || 'no-vip'),
    displayText: getVipLevelDisplayName(customer.rank || 'no-vip')
  };

  const selectedRankInfo = {
    colorClass: getVipLevelColor(selectedRank),
    displayText: getVipLevelDisplayName(selectedRank)
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-dark-700 rounded-lg border border-dark-600 p-6 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Crown className="w-6 h-6 text-primary-500" />
            <div>
              <h2 className="text-xl font-semibold text-gray-100">Assign VIP Rank</h2>
              <p className="text-sm text-gray-400">Update VIP rank for {customer.username}</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-200 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Current Rank Display */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Current VIP Rank
          </label>
          <div className="flex items-center gap-2">
            <span className={`px-3 py-2 text-sm font-medium rounded-lg ${currentRankInfo.colorClass}`}>
              {currentRankInfo.displayText}
            </span>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* New Rank Selection */}
          <div>
            <label htmlFor="vip-rank" className="block text-sm font-medium text-gray-300 mb-2">
              New VIP Rank
            </label>
            <select
              id="vip-rank"
              value={selectedRank}
              onChange={(e) => setSelectedRank(e.target.value)}
              disabled={isSubmitting}
              className="w-full px-3 py-2 bg-dark-600 border border-dark-500 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {vipLevelOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            
            {/* Selected Rank Preview */}
            <div className="mt-2 flex items-center gap-2">
              <span className="text-sm text-gray-400">Preview:</span>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${selectedRankInfo.colorClass}`}>
                {selectedRankInfo.displayText}
              </span>
            </div>
          </div>

          {/* Add Gift Option */}
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="add-gift"
              checked={addGift}
              onChange={(e) => setAddGift(e.target.checked)}
              disabled={isSubmitting}
              className="w-4 h-4 text-primary-600 bg-dark-600 border-dark-500 rounded focus:ring-primary-500 focus:ring-2"
            />
            <label htmlFor="add-gift" className="flex items-center gap-2 text-sm text-gray-300">
              <Gift size={16} className="text-primary-400" />
              Add gift with rank assignment
            </label>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-error-500/20 border border-error-500 rounded-md p-3">
              <p className="text-error-500 text-sm">{error}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 bg-dark-600 text-gray-300 rounded-md hover:bg-dark-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || selectedRank === customer.rank}
              className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Assigning...
                </>
              ) : (
                <>
                  <Crown size={16} />
                  Assign Rank
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VipRankAssignmentModal;
