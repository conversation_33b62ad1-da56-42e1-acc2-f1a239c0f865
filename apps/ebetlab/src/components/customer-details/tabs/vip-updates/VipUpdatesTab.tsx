import React, { useCallback, useState } from 'react';
import { Crown, RefreshCw, TrendingUp, TrendingDown, ArrowRight, Calendar, Edit } from 'lucide-react';
import { fetchVipUpdates, type VipUpdateData } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import VipRankAssignmentModal from './VipRankAssignmentModal';

interface VipUpdatesTabProps {
  customer: {
    id: number;
    username: string;
    rank: string;
  };
}

const VipUpdatesTab: React.FC<VipUpdatesTabProps> = ({ customer }) => {
  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch VIP updates data
  const fetchVipUpdatesData = useCallback(async () => {
    const result = await fetchVipUpdates(customer.id.toString());

    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load VIP updates');
    }
  }, [customer.id]);

  const { data: vipUpdatesData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchVipUpdatesData,
    dependencies: [customer.id]
  });

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(parseInt(timestamp) * 1000).toLocaleString();
  };

  // Format wager amount
  const formatWager = (amount: string) => {
    const value = parseFloat(amount);
    return `$${value.toFixed(2)}`;
  };

  // Get VIP level badge color and display name
  const getVipLevelInfo = (level: string) => {
    const levelMap: { [key: string]: { color: string; name: string } } = {
      'no-vip': { color: 'bg-gray-500/20 text-gray-400', name: 'No VIP' },
      'betroz': { color: 'bg-blue-500/20 text-blue-400', name: 'Betroz' },
      'iron': { color: 'bg-slate-500/20 text-slate-400', name: 'Iron' },
      'steel': { color: 'bg-zinc-500/20 text-zinc-300', name: 'Steel' },
      'bronze': { color: 'bg-amber-600/20 text-amber-500', name: 'Bronze' },
      'silver': { color: 'bg-gray-300/20 text-gray-200', name: 'Silver' },
      'gold': { color: 'bg-yellow-500/20 text-yellow-400', name: 'Gold' },
      'platinum': { color: 'bg-cyan-500/20 text-cyan-400', name: 'Platinum' },
      'diamond': { color: 'bg-purple-500/20 text-purple-400', name: 'Diamond' }
    };
    return levelMap[level] || { color: 'bg-gray-500/20 text-gray-400', name: level };
  };

  // Get update type icon
  const getUpdateIcon = (from: string, to: string) => {
    if (from === to) {
      return <ArrowRight className="w-4 h-4 text-gray-400" />;
    }
    
    const levelOrder = ['no-vip', 'betroz', 'iron', 'steel', 'bronze', 'silver', 'gold', 'platinum', 'diamond'];
    const fromIndex = levelOrder.indexOf(from);
    const toIndex = levelOrder.indexOf(to);
    
    if (toIndex > fromIndex) {
      return <TrendingUp className="w-4 h-4 text-green-400" />;
    } else {
      return <TrendingDown className="w-4 h-4 text-red-400" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading VIP updates...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-error-500/20 border border-error-500 rounded-md p-6 text-center">
        <p className="text-error-500 text-lg mb-2">Failed to load VIP updates</p>
        <p className="text-gray-400 mb-4">{error}</p>
        <button
          onClick={refetch}
          className="btn btn-primary flex items-center gap-2 mx-auto"
        >
          <RefreshCw size={16} />
          Try Again
        </button>
      </div>
    );
  }

  const vipUpdates = vipUpdatesData?.data || [];
  const total = vipUpdatesData?.total || 0;

  // Calculate summary statistics
  const promotions = vipUpdates.filter(update => {
    const levelOrder = ['no-vip', 'betroz', 'iron', 'steel', 'bronze', 'silver', 'gold', 'platinum', 'diamond'];
    const fromIndex = levelOrder.indexOf(update.from);
    const toIndex = levelOrder.indexOf(update.to);
    return toIndex > fromIndex;
  });

  const demotions = vipUpdates.filter(update => {
    const levelOrder = ['no-vip', 'betroz', 'iron', 'steel', 'bronze', 'silver', 'gold', 'platinum', 'diamond'];
    const fromIndex = levelOrder.indexOf(update.from);
    const toIndex = levelOrder.indexOf(update.to);
    return toIndex < fromIndex;
  });

  const totalWager = vipUpdates.reduce((sum, update) => sum + parseFloat(update.total_wager), 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Crown className="w-6 h-6 text-primary-500" />
          <div>
            <h2 className="text-xl font-semibold text-gray-100">VIP Updates</h2>
            <p className="text-sm text-gray-400">
              {total} total VIP state changes for {customer.username}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={() => setIsModalOpen(true)}
            className="btn btn-primary flex items-center gap-2"
          >
            <Edit size={16} />
            Assign VIP Rank
          </button>
          <button
            onClick={refetch}
            disabled={loading}
            className="btn btn-outline flex items-center gap-2"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {vipUpdates.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Updates</p>
                <p className="text-2xl font-bold text-gray-100">{total}</p>
              </div>
              <div className="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <Crown className="w-5 h-5 text-primary-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Promotions</p>
                <p className="text-2xl font-bold text-green-400">{promotions.length}</p>
                <p className="text-xs text-gray-500">Level upgrades</p>
              </div>
              <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Demotions</p>
                <p className="text-2xl font-bold text-red-400">{demotions.length}</p>
                <p className="text-xs text-gray-500">Level downgrades</p>
              </div>
              <div className="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center">
                <TrendingDown className="w-5 h-5 text-red-400" />
              </div>
            </div>
          </div>

          <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Wager</p>
                <p className="text-2xl font-bold text-primary-400">{formatWager(totalWager.toString())}</p>
                <p className="text-xs text-gray-500">Across all updates</p>
              </div>
              <div className="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-primary-400" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* VIP Updates Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left">
            <thead className="bg-dark-600">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  VIP Change
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Total Wager
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Type
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {vipUpdates.length > 0 ? (
                vipUpdates.map((update) => {
                  const fromInfo = getVipLevelInfo(update.from);
                  const toInfo = getVipLevelInfo(update.to);
                  
                  return (
                    <tr key={update.id} className="hover:bg-dark-600/50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-100">
                          {formatTimestamp(update.timestamp)}
                        </div>
                        <div className="text-xs text-gray-400">ID: {update.id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <span className={`w-16 px-2 py-1 text-xs font-medium rounded-full text-center ${fromInfo.color}`}>
                            {fromInfo.name}
                          </span>
                          {getUpdateIcon(update.from, update.to)}
                          <span className={`w-16 px-2 py-1 text-xs font-medium rounded-full text-center ${toInfo.color}`}>
                            {toInfo.name}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-100">
                          {formatWager(update.total_wager)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-1">
                          {getUpdateIcon(update.from, update.to)}
                          <span className={`text-xs font-medium ${
                            update.from === update.to 
                              ? 'text-gray-400' 
                              : promotions.some(p => p.id === update.id)
                                ? 'text-green-400'
                                : 'text-red-400'
                          }`}>
                            {update.from === update.to ? 'No Change' : 
                             promotions.some(p => p.id === update.id) ? 'Promotion' : 'Demotion'}
                          </span>
                        </div>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <Crown className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No VIP updates found</p>
                      <p className="text-sm">This customer has no VIP state change history.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* VIP Rank Assignment Modal */}
      <VipRankAssignmentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        customer={customer}
        onSuccess={() => {
          refetch(); // Refresh the VIP updates data after successful assignment
        }}
      />
    </div>
  );
};

export default VipUpdatesTab;
