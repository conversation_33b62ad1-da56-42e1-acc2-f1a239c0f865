import React, { useState, useCallback } from 'react';
import { Users, Download, RefreshCw, Eye, User, Wallet, Globe } from 'lucide-react';
import { fetchPlayerList, type PlayerData, type PlayerListSearchParams } from '../../../../utils/api';
import { useRefreshableData } from '../../hooks/useRefreshableData';
import Pagination from '../../../ui/Pagination';
import Modal from '../../../ui/Modal';

interface PlayerListTabProps {
  customer: {
    id: number;
    username: string;
  };
}

interface PlayerDetailsModalProps {
  player: PlayerData;
}

const PlayerDetailsModal: React.FC<PlayerDetailsModalProps> = ({ player }) => {
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Player Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
            Player Information
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Player ID:</span>
              <span className="text-gray-100 font-mono">#{player.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Customer ID:</span>
              <span className="text-gray-100 font-mono">#{player.customer_id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Username:</span>
              <span className="text-gray-100">{player.customer.username}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Masked Username:</span>
              <span className="text-gray-100 font-mono">{player.customer.masked_username}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Rank:</span>
              <div className="flex items-center gap-2">
                <span className="text-gray-100 capitalize">{player.customer.rankc}</span>
                {player.customer.ranki && (
                  <img 
                    src={player.customer.ranki} 
                    alt={`${player.customer.rankc} rank`}
                    className="w-6 h-6"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                )}
              </div>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Last Action:</span>
              <span className="text-gray-100">{formatTimestamp(player.customer.last_action)}</span>
            </div>
          </div>
        </div>

        {/* Account Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-100 border-b border-dark-600 pb-2">
            Account Details
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Currency:</span>
              <span className="text-gray-100 font-semibold">{player.currency}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Wallet ID:</span>
              <span className="text-gray-100 font-mono">#{player.wallet_id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Sportsbook ID:</span>
              <span className="text-gray-100 font-mono">{player.sportsbook_id}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PlayerListTab: React.FC<PlayerListTabProps> = ({ customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [selectedPlayer, setSelectedPlayer] = useState<PlayerData | null>(null);
  const [showPlayerModal, setShowPlayerModal] = useState(false);
  const [searchParams, setSearchParams] = useState<Partial<PlayerListSearchParams>>({
    customer_id: customer.id.toString()
  });

  // Fetch player list data
  const fetchPlayerListData = useCallback(async () => {
    const result = await fetchPlayerList(currentPage, itemsPerPage, searchParams);
    
    if (result.success && result.data) {
      return result.data.data;
    } else {
      throw new Error(result.error || 'Failed to load player list');
    }
  }, [currentPage, itemsPerPage, searchParams]);

  const { data: playerListData, loading, error, refetch } = useRefreshableData({
    fetchFn: fetchPlayerListData,
    dependencies: [currentPage, itemsPerPage, searchParams]
  });

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handlePlayerClick = (player: PlayerData) => {
    setSelectedPlayer(player);
    setShowPlayerModal(true);
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Export player list data
  const handleExport = () => {
    if (!playerListData?.data) return;

    const csvData = playerListData.data.map(player => ({
      'Player ID': player.id,
      'Customer ID': player.customer_id,
      'Username': player.customer.username,
      'Masked Username': player.customer.masked_username,
      'Rank': player.customer.rankc,
      'Currency': player.currency,
      'Wallet ID': player.wallet_id,
      'Sportsbook ID': player.sportsbook_id,
      'Last Action': formatTimestamp(player.customer.last_action)
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `player-list-${customer.username}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Users className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-100 mb-2">Failed to load player list</h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <button
            onClick={refetch}
            className="btn btn-primary flex items-center gap-2 mx-auto"
          >
            <RefreshCw size={16} />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const players = playerListData?.data || [];
  const total = playerListData?.total || 0;
  const totalPages = Math.ceil(total / itemsPerPage);

  // Calculate summary statistics
  const summary = players.reduce((acc, player) => {
    acc.currencies.add(player.currency);
    acc.totalPlayers++;
    return acc;
  }, {
    currencies: new Set<string>(),
    totalPlayers: 0
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-100">Player List</h2>
          <p className="text-sm text-gray-400 mt-1">
            Sportsbook players for {customer.username}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={handleExport}
            disabled={players.length === 0}
            className="btn btn-outline flex items-center gap-2"
          >
            <Download size={16} />
            Export CSV
          </button>

          <button
            onClick={refetch}
            disabled={loading}
            className="btn btn-outline flex items-center gap-2"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {players.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold text-gray-100">Summary</h3>
            <span className="text-sm text-gray-400 bg-dark-600 px-2 py-1 rounded">
              Based on {players.length} displayed players
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Players</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {summary.totalPlayers}
                  </p>
                </div>
                <Users className="w-8 h-8 text-blue-400" />
              </div>
            </div>

            <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Currencies</p>
                  <p className="text-2xl font-bold text-green-400">
                    {summary.currencies.size}
                  </p>
                </div>
                <Globe className="w-8 h-8 text-green-400" />
              </div>
            </div>

            <div className="bg-dark-700 rounded-lg border border-dark-600 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Records</p>
                  <p className="text-2xl font-bold text-purple-400">
                    {total}
                  </p>
                </div>
                <Wallet className="w-8 h-8 text-purple-400" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Player List Table */}
      <div className="bg-dark-700 rounded-lg border border-dark-600 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-dark-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Player ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Username
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Rank
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Currency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Wallet ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Sportsbook ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Last Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-600">
              {players.length > 0 ? (
                players.map((player) => (
                  <tr
                    key={player.id}
                    className="hover:bg-dark-600/50 cursor-pointer transition-colors"
                    onClick={() => handlePlayerClick(player)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-100">
                        #{player.id}
                      </div>
                      <div className="text-xs text-gray-400">
                        Customer: #{player.customer_id}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100">{player.customer.username}</div>
                      <div className="text-xs text-gray-400 font-mono">{player.customer.masked_username}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-100 capitalize">{player.customer.rankc}</span>
                        {player.customer.ranki && (
                          <img 
                            src={player.customer.ranki} 
                            alt={`${player.customer.rankc} rank`}
                            className="w-5 h-5"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-100 font-semibold">{player.currency}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100 font-mono">#{player.wallet_id}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100 font-mono">{player.sportsbook_id}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-100">
                        {formatTimestamp(player.customer.last_action)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePlayerClick(player);
                        }}
                        className="text-primary-400 hover:text-primary-300 text-sm flex items-center gap-1"
                        title="View Details"
                      >
                        <Eye size={14} />
                        Details
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No players found</p>
                      <p className="text-sm">This customer has no sportsbook players.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {total > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={total}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            isLoading={loading}
          />
        )}
      </div>

      {/* Player Details Modal */}
      {selectedPlayer && (
        <Modal
          isOpen={showPlayerModal}
          onClose={() => setShowPlayerModal(false)}
          title={`Player #${selectedPlayer.id} - ${selectedPlayer.customer.username}`}
          size="lg"
        >
          <PlayerDetailsModal player={selectedPlayer} />
        </Modal>
      )}
    </div>
  );
};

export default PlayerListTab;
