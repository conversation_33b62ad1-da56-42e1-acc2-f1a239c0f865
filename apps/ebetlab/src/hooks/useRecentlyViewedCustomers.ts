// Hook for managing recently viewed customers
import { useState, useEffect, useCallback } from 'react';
import { 
  RecentlyViewedCustomer, 
  getRecentlyViewedCustomers, 
  addRecentlyViewedCustomer, 
  removeRecentlyViewedCustomer,
  clearRecentlyViewedCustomers
} from '../utils/recentlyViewedCustomers';

export const useRecentlyViewedCustomers = () => {
  const [recentCustomers, setRecentCustomers] = useState<RecentlyViewedCustomer[]>([]);

  // Load recently viewed customers on mount
  useEffect(() => {
    const loadRecentCustomers = () => {
      const customers = getRecentlyViewedCustomers();
      setRecentCustomers(customers);
    };

    loadRecentCustomers();

    // Listen for storage changes (in case of multiple tabs)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'recentlyViewedCustomers') {
        loadRecentCustomers();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Add a customer to recently viewed
  const addCustomer = useCallback((customer: {
    id: number;
    name?: string;
    surname?: string;
    username?: string;
  }) => {
    addRecentlyViewedCustomer(customer);
    // Update local state
    const updated = getRecentlyViewedCustomers();
    setRecentCustomers(updated);
  }, []);

  // Remove a customer from recently viewed
  const removeCustomer = useCallback((customerId: number) => {
    removeRecentlyViewedCustomer(customerId);
    // Update local state
    const updated = getRecentlyViewedCustomers();
    setRecentCustomers(updated);
  }, []);

  // Clear all recently viewed customers
  const clearCustomers = useCallback(() => {
    clearRecentlyViewedCustomers();
    setRecentCustomers([]);
  }, []);

  return {
    recentCustomers,
    addCustomer,
    removeCustomer,
    clearCustomers
  };
};
