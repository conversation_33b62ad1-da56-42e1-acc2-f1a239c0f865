import { useAppContext } from '../contexts/AppContext';
import type { 
  Configurations, 
  CurrencyRates, 
  UserData, 
  WebsiteConfig,
  GameCategory,
  Provider,
  Language,
  Role,
  User
} from '../contexts/AppContext';

/**
 * Hook to get all app context data
 */
export const useAppData = () => {
  const context = useAppContext();
  return {
    configurations: context.configurations,
    currencyRates: context.currencyRates,
    userData: context.userData,
    isLoading: context.isLoading,
    error: context.error,
    refetchData: context.refetchData
  };
};

/**
 * Hook to get configurations data
 */
export const useConfigurations = (): Configurations | null => {
  const { configurations } = useAppContext();
  return configurations;
};

/**
 * Hook to get website configuration
 */
export const useWebsiteConfig = (): WebsiteConfig | null => {
  const { configurations } = useAppContext();
  return configurations?.website || null;
};

/**
 * Hook to get currency rates
 */
export const useCurrencyRates = (): CurrencyRates | null => {
  const { currencyRates } = useAppContext();
  return currencyRates;
};

/**
 * Hook to get user data
 */
export const useUserData = (): UserData | null => {
  const { userData } = useAppContext();
  return userData;
};

/**
 * Hook to get current user information
 */
export const useCurrentUser = (): User | null => {
  const { userData } = useAppContext();
  return userData?.user || null;
};

/**
 * Hook to get available currencies
 */
export const useCurrencies = (): string[] => {
  const { configurations } = useAppContext();
  return configurations?.currencies || [];
};

/**
 * Hook to get game categories
 */
export const useGameCategories = (): GameCategory[] => {
  const { configurations } = useAppContext();
  return configurations?.game_categories || [];
};

/**
 * Hook to get providers
 */
export const useProviders = (): Provider[] => {
  const { configurations } = useAppContext();
  return configurations?.providers || [];
};

/**
 * Hook to get languages
 */
export const useLanguages = (): Language[] => {
  const { configurations } = useAppContext();
  return configurations?.languages || [];
};

/**
 * Hook to get roles
 */
export const useRoles = (): Role[] => {
  const { configurations } = useAppContext();
  return configurations?.roles || [];
};

/**
 * Hook to get permissions
 */
export const usePermissions = (): string[] => {
  const { configurations } = useAppContext();
  return configurations?.permissions || [];
};

/**
 * Hook to check if user has specific permission
 */
export const useHasPermission = (permission: string): boolean => {
  const { configurations, userData } = useAppContext();
  
  if (!configurations || !userData) {
    return false;
  }

  // Check if permission exists in the system
  if (!configurations.permissions.includes(permission)) {
    return false;
  }

  // Get user's role
  const userRole = configurations.roles.find(role => role.id === userData.user.role_id);
  
  if (!userRole) {
    return false;
  }

  // Check if user's role has the permission
  return userRole.permissions.includes(permission);
};

/**
 * Hook to get currency conversion rate
 */
export const useCurrencyRate = (currency: string): number | null => {
  const { currencyRates } = useAppContext();
  
  if (!currencyRates || !currency) {
    return null;
  }

  return currencyRates[currency] || null;
};

/**
 * Hook to convert amount between currencies
 */
export const useCurrencyConverter = () => {
  const { currencyRates } = useAppContext();

  const convertCurrency = (
    amount: number,
    fromCurrency: string,
    toCurrency: string = 'USD'
  ): number | null => {
    if (!currencyRates || !amount) {
      return null;
    }

    const fromRate = currencyRates[fromCurrency];
    const toRate = currencyRates[toCurrency];

    if (!fromRate || !toRate) {
      return null;
    }

    // Convert to USD first, then to target currency
    const usdAmount = amount / fromRate;
    return usdAmount * toRate;
  };

  const formatCurrency = (
    amount: number,
    currency: string,
    options?: Intl.NumberFormatOptions
  ): string => {
    const defaultOptions: Intl.NumberFormatOptions = {
      style: 'currency',
      currency: currency === 'USDT' || currency === 'USDC' ? 'USD' : currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 8,
      ...options
    };

    try {
      return new Intl.NumberFormat('en-US', defaultOptions).format(amount);
    } catch {
      // Fallback for unsupported currencies
      return `${amount.toFixed(2)} ${currency}`;
    }
  };

  return {
    convertCurrency,
    formatCurrency,
    rates: currencyRates
  };
};

/**
 * Hook to get loading state
 */
export const useAppLoading = (): boolean => {
  const { isLoading } = useAppContext();
  return isLoading;
};

/**
 * Hook to get error state
 */
export const useAppError = (): string | null => {
  const { error } = useAppContext();
  return error;
};

/**
 * Hook to refresh app data
 */
export const useRefreshAppData = () => {
  const { refetchData } = useAppContext();
  return refetchData;
};
