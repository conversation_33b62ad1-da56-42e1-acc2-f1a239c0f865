import { useState, useEffect, useCallback } from 'react';

interface UseDataFetchingOptions<T> {
  fetchFn: () => Promise<T>;
  dependencies?: any[];
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
}

interface UseDataFetchingReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Simple data fetching hook for standalone pages
 * This is a simplified version of useRefreshableData that doesn't depend on RefreshContext
 */
export function useDataFetching<T>({
  fetchFn,
  dependencies = [],
  onSuccess,
  onError
}: UseDataFetchingOptions<T>): UseDataFetchingReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await fetchFn();
      setData(result);
      onSuccess?.(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [fetchFn, onSuccess, onError]);

  // Initial load and dependency changes
  useEffect(() => {
    fetchData();
  }, [...dependencies, fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}
