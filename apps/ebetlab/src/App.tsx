import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { AppProvider } from './contexts/AppContext';
import { useAppData } from './hooks/useAppData';
import Layout from './components/layout/Layout';
import AppLoader from './components/ui/AppLoader';
import Dashboard from './pages/Dashboard';
import AdminProfile from './pages/AdminProfile';
import CssSettings from './pages/CssSettings';
import CssSettingEdit from './pages/CssSettingEdit';
import CustomerList from './pages/CustomerList';
import CustomerDetails from './pages/CustomerDetails';
import SelfExclusionList from './pages/SelfExclusionList';
import DebugEbetlabApi from './pages/DebugEbetlabApi';
import DebugBackendApi from './pages/DebugBackendApi';
import SlackBotStatus from './pages/SlackBotStatus';
import VerificationCodes from './pages/VerificationCodes';
import ChatBlacklist from './pages/ChatBlacklist';
import Bonuses from './pages/Bonuses';
import BonusCreate from './pages/BonusCreate';
import BonusEdit from './pages/BonusEdit';
import BonusView from './pages/BonusView';
import AutoBonusRulesList from './pages/AutoBonusRulesList';
import AutoBonusRulesCreate from './pages/AutoBonusRulesCreate';
import AutoBonusRulesEdit from './pages/AutoBonusRulesEdit';
import AutoBonusRulesDetails from './pages/AutoBonusRulesDetails';
import AutoBonusRulesClaims from './pages/AutoBonusRulesClaims';
import BalanceCorrectionRules from './pages/BalanceCorrectionRules';
import BalanceCorrectionRulesClaims from './pages/BalanceCorrectionRulesClaims';
import WelcomeBonuses from './pages/WelcomeBonuses';
import WelcomeBonusDetails from './pages/WelcomeBonusDetails';
import WelcomeBonusCreate from './pages/WelcomeBonusCreate';
import WelcomeBonusEdit from './pages/WelcomeBonusEdit';
import BonusDropsUsage from './pages/BonusDropsUsage';
import BonusDropsList from './pages/BonusDropsList';
import BonusDropDetails from './pages/BonusDropDetails';
import BonusDropCreate from './pages/BonusDropCreate';
import BonusDropEdit from './pages/BonusDropEdit';
import Corrections from './pages/Corrections';
import DiscountsAdmin from './pages/DiscountsAdmin';
import FTDReports from './pages/FTDReports';
import FTWReports from './pages/FTWReports';
import DepositsWithdraws from './pages/DepositsWithdraws';
import WaitingDeposits from './pages/WaitingDeposits';
import CancelledWithdraws from './pages/CancelledWithdraws';
import Missions from './pages/Missions';
import MissionsPoints from './pages/MissionsPoints';
import MissionsMarket from './pages/MissionsMarket';
import MissionsMarketRequests from './pages/MissionsMarketRequests';
import Transactions from './pages/Transactions';
import CrudManager from './pages/CrudManager';
import LoginModal from './components/auth/LoginModal';

// Component that handles app data loading after authentication
const AppDataLoader: React.FC = () => {
  const { isLoading, error, refetchData } = useAppData();

  if (isLoading || error) {
    return <AppLoader isLoading={isLoading} error={error} onRetry={refetchData} />;
  }

  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/profile" element={<AdminProfile />} />
          <Route path="/settings/css" element={<CssSettings />} />
          <Route path="/settings/css/edit/:id" element={<CssSettingEdit />} />
          <Route path="/customers/list" element={<CustomerList />} />
          <Route path="/customers/details/:id" element={<CustomerDetails />} />
          <Route path="/customers/self-exclusions" element={<SelfExclusionList />} />
          <Route path="/customers/chat-blacklist" element={<ChatBlacklist />} />
          <Route path="/debug/ebetlab-api" element={<DebugEbetlabApi />} />
          <Route path="/debug/backend-api" element={<DebugBackendApi />} />
          <Route path="/debug/slack-bots" element={<SlackBotStatus />} />
          <Route path="/verification-codes" element={<VerificationCodes />} />
          <Route path="/bonuses" element={<Bonuses />} />
          <Route path="/bonuses/create" element={<BonusCreate />} />
          <Route path="/bonuses/edit/:id" element={<BonusEdit />} />
          <Route path="/bonuses/view/:id" element={<BonusView />} />
          <Route path="/auto-bonus-rules/list" element={<AutoBonusRulesList />} />
          <Route path="/auto-bonus-rules/create" element={<AutoBonusRulesCreate />} />
          <Route path="/auto-bonus-rules/edit/:id" element={<AutoBonusRulesEdit />} />
          <Route path="/auto-bonus-rules/details/:id" element={<AutoBonusRulesDetails />} />
          <Route path="/auto-bonus-rules/:id/claims" element={<AutoBonusRulesClaims />} />
          <Route path="/balance-correction-rules" element={<BalanceCorrectionRules />} />
          <Route path="/balance-correction-rules/:id/claims" element={<BalanceCorrectionRulesClaims />} />
          <Route path="/welcome-bonus" element={<WelcomeBonuses />} />
          <Route path="/welcome-bonus/create" element={<WelcomeBonusCreate />} />
          <Route path="/welcome-bonus/edit/:id" element={<WelcomeBonusEdit />} />
          <Route path="/welcome-bonus/details/:id" element={<WelcomeBonusDetails />} />
          <Route path="/bonus-drops/list" element={<BonusDropsList />} />
          <Route path="/bonus-drops/create" element={<BonusDropCreate />} />
          <Route path="/bonus-drops/edit/:id" element={<BonusDropEdit />} />
          <Route path="/bonus-drops/details/:id" element={<BonusDropDetails />} />
          <Route path="/bonus-drops/usage" element={<BonusDropsUsage />} />
          <Route path="/finance/corrections" element={<Corrections />} />
          <Route path="/finance/discounts" element={<DiscountsAdmin />} />
          <Route path="/finance/ftd-reports" element={<FTDReports />} />
          <Route path="/finance/ftw-reports" element={<FTWReports />} />
          <Route path="/finance/deposits-withdraws" element={<DepositsWithdraws />} />
          <Route path="/finance/waiting-deposits" element={<WaitingDeposits />} />
          <Route path="/finance/cancelled-withdraws" element={<CancelledWithdraws />} />
          <Route path="/missions" element={<Missions />} />
          <Route path="/missions/points" element={<MissionsPoints />} />
          <Route path="/missions/market" element={<MissionsMarket />} />
          <Route path="/missions/market-requests" element={<MissionsMarketRequests />} />
          <Route path="/missions/transactions" element={<Transactions />} />
          <Route path="/crud-manager" element={<CrudManager />} />
        </Routes>
      </Layout>
    </Router>
  );
};

const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-dark-800">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Authenticating...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <LoginModal isOpen={!isAuthenticated} />
      {isAuthenticated && (
        <AppProvider>
          <AppDataLoader />
        </AppProvider>
      )}
    </>
  );
};

function App() {

  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
