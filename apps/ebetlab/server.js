const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// Use global fetch if available (Node 18+), otherwise require node-fetch
const fetch = globalThis.fetch || require('node-fetch');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Authentication endpoint
app.post('/auth/login', async (req, res) => {
  try {
    const { username, password, otp } = req.body;

    // Forward authentication request to EbetLab API
    const authResponse = await fetch('https://service.ebetlab.com/api/operator/configuration/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username,
        password,
        otp
      })
    });

    const authResult = await authResponse.json();

    if (authResponse.ok && authResult.success) {
      res.json({
        success: true,
        data: {
          token: authResult.data.token,
          user: authResult.data.user || { username }
        }
      });
    } else {
      res.json({
        success: false,
        error: authResult.error || 'Authentication failed'
      });
    }
  } catch (error) {
    console.error('Auth error:', error);
    res.json({
      success: false,
      error: 'Authentication service unavailable'
    });
  }
});

// EbetLab API proxy (existing endpoints)
app.all('/api/operator/*', async (req, res) => {
  try {
    const path = req.path.replace('/api/operator', '');
    const url = `https://service.ebetlab.com/api/operator${path}`;

    const response = await fetch(url, {
      method: req.method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': req.headers.authorization || ''
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    });

    const result = await response.json();
    res.json(result);
  } catch (error) {
    console.error('EbetLab API error:', error);
    res.status(500).json({
      success: false,
      error: 'API service unavailable'
    });
  }
});

// Debug endpoint for EbetLab API
app.all('/debug/api/*', async (req, res) => {
  try {
    const path = req.path.replace('/debug', '');
    const url = `https://service.ebetlab.com${path}`;

    const response = await fetch(url, {
      method: req.method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': req.headers.authorization || ''
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    });

    const result = await response.json();
    res.json({
      success: true,
      data: result,
      status: response.status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Debug API error:', error);
    res.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Makroz API proxy (mission objectives, extended users, etc.)
app.all('/api/v1/makroz/admin/*', async (req, res) => {
  try {
    // For now, we'll simulate the Makroz API responses
    // In a real implementation, this would proxy to the actual Makroz backend
    const path = req.path;
    const method = req.method;

    console.log(`Makroz API Request: ${method} ${path}`);
    console.log('Request body:', req.body);

    // Simulate different endpoints
    if (path.includes('/mission-participations/mission/') && path.includes('/stats')) {
      // Return mock mission participation statistics
      const missionId = path.split('/mission/')[1].split('/stats')[0];
      res.json({
        success: true,
        data: {
          missionId: parseInt(missionId),
          statistics: {
            totalParticipations: 1,
            completedParticipations: 1,
            pendingParticipations: 0,
            uniqueParticipants: 1,
            totalExtendedUsers: 1,
            completionRate: 100.0, // This is already a percentage
            participationRate: 100.0 // This is already a percentage
          },
          breakdown: {
            completed: {
              count: 1,
              percentage: 100.0
            },
            pending: {
              count: 0,
              percentage: 0.0
            }
          }
        }
      });
    } else if (path.includes('/mission-participations/mission/')) {
      // Return mock mission participations
      const missionId = path.split('/mission/')[1];
      res.json({
        success: true,
        data: [
          {
            id: 1,
            userId: 1,
            missionId: parseInt(missionId),
            isCompleted: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      });
    } else if (path.includes('/mission-objectives')) {
      if (method === 'GET') {
        // Return paginated list of mission objectives
        res.json({
          success: true,
          data: [],
          meta: {
            page: 1,
            limit: 20,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false
          }
        });
      } else if (method === 'POST') {
        // Create mission objective
        const newObjective = {
          id: Date.now(),
          ...req.body,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        console.log('Created mission objective:', newObjective);
        
        res.json({
          success: true,
          data: newObjective
        });
      } else if (method === 'PATCH') {
        // Update mission objective
        const updatedObjective = {
          id: parseInt(path.split('/').pop()),
          ...req.body,
          updatedAt: new Date().toISOString()
        };
        
        res.json({
          success: true,
          data: updatedObjective
        });
      } else if (method === 'DELETE') {
        // Delete mission objective
        res.json({
          success: true,
          data: { message: 'Mission objective deleted successfully' }
        });
      }
    } else if (path.includes('/extended-users')) {
      if (method === 'GET') {
        res.json({
          success: true,
          data: [],
          meta: {
            page: 1,
            limit: 20,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false
          }
        });
      } else if (method === 'POST') {
        const newUser = {
          id: Date.now(),
          ...req.body,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        res.json({
          success: true,
          data: newUser
        });
      }
    } else if (path.includes('/slack-bots')) {
      // Return mock slack bots data
      res.json({
        success: true,
        message: "Retrieved 3 registered Slack bots",
        data: {
          bots: [
            {
              id: "bot_1752236756972_6g0xbjz6k",
              route: "/pronet/v1/call-requests",
              channel: "makro-call-talep",
              registeredAt: "2025-07-11T12:25:56.972Z",
              status: "active",
              description: "Handles call service demand requests and sends customer information to Slack",
              type: "call-service"
            },
            {
              id: "bot_1752236756972_p941py17d",
              route: "/pronet/v1/bonus-requests",
              channel: "makro-call-talep",
              registeredAt: "2025-07-11T12:25:56.972Z",
              status: "active",
              description: "Handles bonus requests and sends simple notification to Slack",
              type: "bonus"
            },
            {
              id: "bot_1752236756972_pzbttr1vq",
              route: "/pronet/v1/market-requests",
              channel: "makro-call-talep",
              registeredAt: "2025-07-11T12:25:56.972Z",
              status: "active",
              description: "Handles market product requests and sends customer information with product details to Slack",
              type: "market"
            }
          ],
          stats: {
            totalBots: 3,
            activeChannels: 1,
            isSlackConfigured: true,
            registrationTimeRange: {
              earliest: 1752236756972,
              latest: 1752236756972
            }
          }
        }
      });
    } else {
      // Generic response for other Makroz endpoints
      res.json({
        success: true,
        data: method === 'GET' ? [] : { id: Date.now(), ...req.body },
        meta: method === 'GET' ? {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        } : undefined
      });
    }
  } catch (error) {
    console.error('Makroz API error:', error);
    res.status(500).json({
      success: false,
      error: 'Makroz API service unavailable'
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Proxy server is running',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Proxy server running on http://localhost:${PORT}`);
  console.log(`📡 Proxying EbetLab API: /api/operator/* -> https://service.ebetlab.com/api/operator/*`);
  console.log(`🔧 Simulating Makroz API: /api/v1/makroz/admin/*`);
  console.log(`🔍 Debug endpoint: /debug/api/*`);
  console.log(`🔐 Auth endpoint: /auth/login`);
});
