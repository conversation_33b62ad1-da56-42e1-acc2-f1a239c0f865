# Root Panel - Admin Dashboard Hub

The Root Panel serves as a centralized dashboard for accessing and managing multiple admin panels within the NX monorepo. It provides a unified authentication system and seamless navigation between different administrative interfaces.

## Features

### 🔐 Authentication System
- **Root Panel Login**: Secure access to the dashboard hub using real API authentication
- **API Endpoint**: Uses `VITE_API_URL/api/internal/auth/logins` for authentication
- **Session Management**: Persistent login state with localStorage

### 🎛️ Dashboard Interface
- **Panel Cards**: Visual representation of available admin panels
- **Status Indicators**: Shows panel availability and configuration status
- **Quick Access**: One-click access to configured panels
- **Settings Management**: Easy credential configuration

### 🔑 Cross-Panel Authentication
- **Credential Storage**: Secure storage of panel-specific credentials
- **Auto-Login**: Automatic authentication when accessing panels
- **Token Management**: Proper token storage for each panel's requirements

### ⚙️ Settings Management
- **Root Panel Security**: Change root panel password
- **Panel Credentials**: Configure credentials for each admin panel
- **Password Visibility**: Toggle password visibility for easy entry

## Available Panels

### Betroz Admin Panel
- **Description**: EbetLab administration interface for managing CSS settings, users, and reports
- **Requirements**: Username, Password, and optional OTP
- **Storage Key**: `betroz_auth` (compatible with existing ebetlab app)

## Getting Started

### 1. Start the Root Panel
```bash
npx nx serve root
```

### 2. Login to Root Panel
- Navigate to `http://localhost:4200`
- Use your valid credentials that authenticate against the API endpoint

### 3. Configure Panel Credentials
- Go to Settings → Panel Credentials
- Enter your credentials for the Betroz Admin Panel
- Click "Save Credentials"

### 4. Access Panels
- Return to Dashboard
- Click "Access Panel" on any configured panel
- You'll be automatically logged in and redirected

## Technical Implementation

### Authentication Flow
1. **Root Panel Authentication**: User logs into the root panel
2. **Credential Storage**: Panel credentials are stored securely in localStorage
3. **Cross-Panel Login**: When accessing a panel:
   - Credentials are retrieved from storage
   - Authentication request is sent to the panel's login endpoint
   - Received token is stored in the panel's expected localStorage key
   - User is redirected to the panel

### File Structure
```
apps/root/src/
├── components/
│   ├── auth/
│   │   └── LoginModal.tsx          # Root panel login interface
│   └── layout/
│       └── Layout.tsx              # Main dashboard layout
├── contexts/
│   └── AuthContext.tsx             # Root panel authentication context
├── pages/
│   ├── Dashboard.tsx               # Main dashboard with panel cards
│   └── Settings.tsx                # Settings management interface
├── services/
│   └── panelAuth.ts                # Cross-panel authentication service
└── app/
    └── app.tsx                     # Main application component
```

### Storage Keys
- **Root Panel**: `root_panel_auth`
- **Betroz Admin**: `betroz_auth` and `betroz_login` (for compatibility)

## Development

### Adding New Panels
1. Update `PANEL_CONFIGS` in `src/services/panelAuth.ts`
2. Add panel configuration with:
   - `id`: Unique panel identifier
   - `name`: Display name
   - `loginEndpoint`: Panel's authentication endpoint
   - `tokenStorageKey`: localStorage key expected by the panel
   - `redirectUrl`: Panel's URL

### Environment Configuration
The panel authentication service uses hardcoded URLs for development:
- **Betroz Admin**: `http://localhost:3001`

For production, these should be configured via environment variables.

## Security Considerations

### Current Implementation
- Credentials are stored in localStorage (development only)
- Real API authentication for root panel using `VITE_API_URL/api/internal/auth/logins`
- No encryption of stored credentials

### Production Recommendations
- Implement proper encryption for stored credentials
- Use secure authentication backend for root panel
- Add session timeout and refresh mechanisms
- Implement proper CORS and security headers
- Use environment variables for panel URLs

## Troubleshooting

### Panel Access Issues
1. **"Configure First" Message**: Credentials not set in Settings
2. **Authentication Failed**: Check credentials in Settings
3. **Network Errors**: Ensure target panel is running
4. **Token Issues**: Clear localStorage and reconfigure

### Common Solutions
- Clear browser localStorage: `localStorage.clear()`
- Restart both root panel and target panels
- Check browser console for detailed error messages
- Verify panel URLs and endpoints are correct

## Future Enhancements

- [ ] Encrypted credential storage
- [ ] Multi-factor authentication
- [ ] Panel health monitoring
- [ ] Audit logging
- [ ] Role-based access control
- [ ] Panel-specific settings
- [ ] Bulk credential management
- [ ] SSO integration
