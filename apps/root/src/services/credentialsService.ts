import httpClient from '../utils/httpClient';

export interface FetchedCredential {
  userId: number;
  panelName: string;
  login: string;
  password: string;
  otpSecret: string;
  createdAt: string;
  updatedAt: string;
}

export interface CredentialsResponse {
  success: boolean;
  message: string;
  data: {
    credentials: FetchedCredential[];
  };
  timestamp: string;
}

export interface PanelCredentialsMap {
  [panelId: string]: FetchedCredential;
}

export interface SaveCredentialsRequest {
  panelName: string;
  login: string;
  password: string;
  otpSecret: string;
}

export interface SaveCredentialsResponse {
  success: boolean;
  message: string;
  data: {
    userId: number;
    panelName: string;
    login: string;
    password: string;
    otpSecret: string;
    createdAt: string;
    updatedAt: string;
  };
  timestamp: string;
}

/**
 * Fetch panel credentials from the API
 */
export const fetchPanelCredentials = async (): Promise<PanelCredentialsMap> => {
  try {
    const response = await httpClient.get<CredentialsResponse>('/api/internal/users/credentials');
    
    if (response.data.success && response.data.data?.credentials) {
      // Convert array to map for easier access
      const credentialsMap: PanelCredentialsMap = {};
      
      response.data.data.credentials.forEach(credential => {
        credentialsMap[credential.panelName] = credential;
      });
      
      return credentialsMap;
    } else {
      console.error('Failed to fetch credentials:', response.data.message);
      return {};
    }
  } catch (error) {
    console.error('Error fetching panel credentials:', error);
    return {};
  }
};

/**
 * Store credentials in memory (not localStorage for security)
 */
let credentialsCache: PanelCredentialsMap = {};

/**
 * Set the credentials cache
 */
export const setCredentialsCache = (credentials: PanelCredentialsMap): void => {
  credentialsCache = credentials;
};

/**
 * Get credentials from cache
 */
export const getCredentialsCache = (): PanelCredentialsMap => {
  return credentialsCache;
};

/**
 * Get credentials for a specific panel
 */
export const getPanelCredentialsFromCache = (panelId: string): FetchedCredential | null => {
  return credentialsCache[panelId] || null;
};

/**
 * Check if credentials exist for a panel
 */
export const hasPanelCredentialsInCache = (panelId: string): boolean => {
  return !!credentialsCache[panelId];
};

/**
 * Clear credentials cache
 */
export const clearCredentialsCache = (): void => {
  credentialsCache = {};
};

/**
 * Get all panel IDs that have credentials
 */
export const getPanelIdsWithCredentials = (): string[] => {
  return Object.keys(credentialsCache);
};

/**
 * Save panel credentials to the API
 */
export const savePanelCredentials = async (
  panelName: string,
  login: string,
  password: string,
  otpSecret?: string
): Promise<SaveCredentialsResponse> => {
  try {
    const requestBody: SaveCredentialsRequest = {
      panelName,
      login: login.trim(),
      password: password.trim(),
      otpSecret: otpSecret?.trim() || ''
    };

    const response = await httpClient.post<SaveCredentialsResponse>(
      '/api/internal/users/credentials',
      requestBody
    );

    // If save was successful, update the local cache immediately
    if (response.data.success && response.data.data) {
      const savedCredential: FetchedCredential = {
        userId: response.data.data.userId,
        panelName: response.data.data.panelName,
        login: response.data.data.login,
        password: response.data.data.password,
        otpSecret: response.data.data.otpSecret,
        createdAt: response.data.data.createdAt,
        updatedAt: response.data.data.updatedAt
      };

      // Update the cache with the saved credential
      const currentCache = getCredentialsCache();
      currentCache[panelName] = savedCredential;
      setCredentialsCache(currentCache);
    }

    return response.data;
  } catch (error) {
    console.error('Error saving panel credentials:', error);
    throw error;
  }
};
