/**
 * Credentials Service Test
 * 
 * This file demonstrates and tests the credentials service functionality
 */

import { 
  fetchPanelCredentials, 
  setCredentialsCache, 
  getCredentialsCache,
  getPanelCredentialsFromCache,
  hasPanelCredentialsInCache,
  clearCredentialsCache,
  getPanelIdsWithCredentials,
  type FetchedCredential,
  type PanelCredentialsMap
} from './credentialsService';

// Mock httpClient
jest.mock('../utils/httpClient', () => ({
  get: jest.fn()
}));

import httpClient from '../utils/httpClient';

describe('CredentialsService', () => {
  const mockCredentials: FetchedCredential[] = [
    {
      userId: 1,
      panelName: 'ebetlab',
      login: 'testuser',
      password: 'testpass',
      otpSecret: 'JBSWY3DPEHPK3PXP',
      createdAt: '2025-07-17T20:22:41.628Z',
      updatedAt: '2025-07-17T20:23:01.059Z'
    },
    {
      userId: 1,
      panelName: 'pronet',
      login: 'prouser',
      password: 'propass',
      otpSecret: 'ABCDEFGHIJKLMNOP',
      createdAt: '2025-07-17T20:22:41.628Z',
      updatedAt: '2025-07-17T20:23:01.059Z'
    }
  ];

  const mockApiResponse = {
    data: {
      success: true,
      message: 'Credentials retrieved successfully',
      data: {
        credentials: mockCredentials
      },
      timestamp: '2025-07-17T21:03:18.660Z'
    }
  };

  beforeEach(() => {
    // Clear cache before each test
    clearCredentialsCache();
    jest.clearAllMocks();
  });

  describe('fetchPanelCredentials', () => {
    it('should fetch and return credentials map on success', async () => {
      (httpClient.get as jest.Mock).mockResolvedValue(mockApiResponse);

      const result = await fetchPanelCredentials();

      expect(httpClient.get).toHaveBeenCalledWith('/api/internal/users/credentials');
      expect(result).toEqual({
        'ebetlab': mockCredentials[0],
        'pronet': mockCredentials[1]
      });
    });

    it('should return empty object on API failure', async () => {
      (httpClient.get as jest.Mock).mockResolvedValue({
        data: {
          success: false,
          message: 'Failed to fetch credentials'
        }
      });

      const result = await fetchPanelCredentials();

      expect(result).toEqual({});
    });

    it('should return empty object on network error', async () => {
      (httpClient.get as jest.Mock).mockRejectedValue(new Error('Network error'));

      const result = await fetchPanelCredentials();

      expect(result).toEqual({});
    });
  });

  describe('credentials cache management', () => {
    const testCredentialsMap: PanelCredentialsMap = {
      'ebetlab': mockCredentials[0],
      'pronet': mockCredentials[1]
    };

    it('should set and get credentials cache', () => {
      setCredentialsCache(testCredentialsMap);
      const result = getCredentialsCache();

      expect(result).toEqual(testCredentialsMap);
    });

    it('should get specific panel credentials', () => {
      setCredentialsCache(testCredentialsMap);

      const ebetlabCreds = getPanelCredentialsFromCache('ebetlab');
      const pronetCreds = getPanelCredentialsFromCache('pronet');
      const nonExistentCreds = getPanelCredentialsFromCache('nonexistent');

      expect(ebetlabCreds).toEqual(mockCredentials[0]);
      expect(pronetCreds).toEqual(mockCredentials[1]);
      expect(nonExistentCreds).toBeNull();
    });

    it('should check if panel has credentials', () => {
      setCredentialsCache(testCredentialsMap);

      expect(hasPanelCredentialsInCache('ebetlab')).toBe(true);
      expect(hasPanelCredentialsInCache('pronet')).toBe(true);
      expect(hasPanelCredentialsInCache('nonexistent')).toBe(false);
    });

    it('should get panel IDs with credentials', () => {
      setCredentialsCache(testCredentialsMap);

      const panelIds = getPanelIdsWithCredentials();

      expect(panelIds).toEqual(['ebetlab', 'pronet']);
    });

    it('should clear credentials cache', () => {
      setCredentialsCache(testCredentialsMap);
      expect(getCredentialsCache()).toEqual(testCredentialsMap);

      clearCredentialsCache();
      expect(getCredentialsCache()).toEqual({});
    });
  });

  describe('edge cases', () => {
    it('should handle empty credentials array', async () => {
      (httpClient.get as jest.Mock).mockResolvedValue({
        data: {
          success: true,
          data: {
            credentials: []
          }
        }
      });

      const result = await fetchPanelCredentials();

      expect(result).toEqual({});
    });

    it('should handle missing data property', async () => {
      (httpClient.get as jest.Mock).mockResolvedValue({
        data: {
          success: true
          // missing data property
        }
      });

      const result = await fetchPanelCredentials();

      expect(result).toEqual({});
    });

    it('should handle malformed response', async () => {
      (httpClient.get as jest.Mock).mockResolvedValue({
        data: null
      });

      const result = await fetchPanelCredentials();

      expect(result).toEqual({});
    });
  });
});

// Manual test scenarios for development
export const testScenarios = {
  // Test successful credentials fetch
  testSuccessfulFetch: async () => {
    console.log('🧪 Testing successful credentials fetch...');
    
    // Mock successful response
    (httpClient.get as jest.Mock).mockResolvedValue(mockApiResponse);

    try {
      const credentials = await fetchPanelCredentials();
      console.log('✅ Credentials fetched:', credentials);
      
      // Test cache functions
      setCredentialsCache(credentials);
      console.log('✅ Credentials cached');
      
      const ebetlabCreds = getPanelCredentialsFromCache('ebetlab');
      console.log('✅ EbetLab credentials:', ebetlabCreds);
      
      const hasEbetlab = hasPanelCredentialsInCache('ebetlab');
      const hasPronet = hasPanelCredentialsInCache('pronet');
      const hasNonExistent = hasPanelCredentialsInCache('nonexistent');
      
      console.log('✅ Credentials check:', { hasEbetlab, hasPronet, hasNonExistent });
      
      return true;
    } catch (error) {
      console.error('❌ Test failed:', error);
      return false;
    }
  },

  // Test error handling
  testErrorHandling: async () => {
    console.log('🧪 Testing error handling...');
    
    // Mock API error
    (httpClient.get as jest.Mock).mockResolvedValue({
      data: {
        success: false,
        message: 'Unauthorized'
      }
    });

    try {
      const credentials = await fetchPanelCredentials();
      console.log('✅ Error handled gracefully:', credentials);
      
      return Object.keys(credentials).length === 0;
    } catch (error) {
      console.error('❌ Test failed:', error);
      return false;
    }
  },

  // Run all tests
  runAllTests: async () => {
    console.log('🚀 Starting Credentials Service Tests...\n');
    
    const results = {
      successfulFetch: await testScenarios.testSuccessfulFetch(),
      errorHandling: await testScenarios.testErrorHandling()
    };
    
    console.log('\n📊 Test Results:');
    Object.entries(results).forEach(([test, passed]) => {
      console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
    });
    
    const allPassed = Object.values(results).every(Boolean);
    console.log(`\n${allPassed ? '🎉' : '💥'} Overall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
    
    return allPassed;
  }
};

export default testScenarios;
