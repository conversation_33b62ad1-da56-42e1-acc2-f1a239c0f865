import { authenticator } from 'otplib';

// Mock the credentialsService to avoid circular dependency issues
jest.mock('./credentialsService', () => ({
  getPanelCredentialsFromCache: jest.fn(),
  hasPanelCredentialsInCache: jest.fn(),
}));

// Mock fetch for testing authentication requests
global.fetch = jest.fn();

import { authenticateWithPanel, accessPanel, getAvailablePanels } from './panelAuth';

describe('Panel Authentication with OTP', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  describe('authenticateWithPanel', () => {
    it('should authenticate successfully without OTP for panels that do not require it', async () => {
      // Mock a panel without OTP requirement (we'll need to add one for testing)
      const mockCredentials = {
        login: 'testuser',
        password: 'testpass',
      };

      const { getPanelCredentialsFromCache } = require('./credentialsService');
      getPanelCredentialsFromCache.mockReturnValue(mockCredentials);

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          token: 'test-token',
          user: { id: '1', name: 'Test User' }
        })
      });

      // This test would need a panel without OTP - for now we'll test the OTP flow
    });

    it('should generate OTP and authenticate successfully for OTP-enabled panels', async () => {
      const mockCredentials = {
        login: 'testuser',
        password: 'testpass',
        otpSecret: 'JBSWY3DPEHPK3PXP' // Base32 encoded test secret
      };

      const { getPanelCredentialsFromCache } = require('./credentialsService');
      getPanelCredentialsFromCache.mockReturnValue(mockCredentials);

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          token: 'test-token',
          user: { id: '1', name: 'Test User' }
        })
      });

      const result = await authenticateWithPanel('ebetlab');

      expect(result.success).toBe(true);
      expect(result.token).toBe('test-token');
      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:3001/auth/login',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('"login":"testuser"')
        })
      );

      // Verify that OTP was included in the request
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      expect(requestBody.otp).toBeDefined();
      expect(requestBody.otp).toMatch(/^\d{6}$/); // Should be 6 digits
    });

    it('should fail when OTP secret is missing for OTP-enabled panel', async () => {
      const mockCredentials = {
        login: 'testuser',
        password: 'testpass',
        // otpSecret is missing
      };

      const { getPanelCredentialsFromCache } = require('./credentialsService');
      getPanelCredentialsFromCache.mockReturnValue(mockCredentials);

      const result = await authenticateWithPanel('ebetlab');

      expect(result.success).toBe(false);
      expect(result.error).toContain('OTP secret is required');
    });

    it('should handle OTP generation failure gracefully', async () => {
      const mockCredentials = {
        login: 'testuser',
        password: 'testpass',
        otpSecret: 'INVALID_SECRET' // This should cause OTP generation to fail
      };

      const { getPanelCredentialsFromCache } = require('./credentialsService');
      getPanelCredentialsFromCache.mockReturnValue(mockCredentials);

      const result = await authenticateWithPanel('ebetlab');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to generate OTP code');
    });

    it('should handle authentication failure from server', async () => {
      const mockCredentials = {
        login: 'testuser',
        password: 'wrongpass',
        otpSecret: 'JBSWY3DPEHPK3PXP'
      };

      const { getPanelCredentialsFromCache } = require('./credentialsService');
      getPanelCredentialsFromCache.mockReturnValue(mockCredentials);

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: async () => 'Invalid credentials'
      });

      const result = await authenticateWithPanel('ebetlab');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Authentication failed: 401 Unauthorized');
    });

    it('should handle network errors', async () => {
      const mockCredentials = {
        login: 'testuser',
        password: 'testpass',
        otpSecret: 'JBSWY3DPEHPK3PXP'
      };

      const { getPanelCredentialsFromCache } = require('./credentialsService');
      getPanelCredentialsFromCache.mockReturnValue(mockCredentials);

      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const result = await authenticateWithPanel('ebetlab');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network error');
    });
  });

  describe('getAvailablePanels', () => {
    it('should include hasOtp field in panel information', () => {
      const { hasPanelCredentialsInCache } = require('./credentialsService');
      hasPanelCredentialsInCache.mockReturnValue(true);

      const panels = getAvailablePanels();

      expect(panels).toHaveLength(2);
      expect(panels[0]).toHaveProperty('hasOtp', true);
      expect(panels[1]).toHaveProperty('hasOtp', true);
    });
  });

  describe('accessPanel', () => {
    it('should throw error when OTP secret is missing for OTP-enabled panel', async () => {
      const mockCredentials = {
        login: 'testuser',
        password: 'testpass',
        // otpSecret is missing
      };

      const { hasPanelCredentialsInCache, getPanelCredentialsFromCache } = require('./credentialsService');
      hasPanelCredentialsInCache.mockReturnValue(true);
      getPanelCredentialsFromCache.mockReturnValue(mockCredentials);

      await expect(accessPanel('ebetlab')).rejects.toThrow(
        'This panel requires OTP authentication. Please configure your OTP secret in the panel settings.'
      );
    });
  });
});

describe('OTP Code Generation', () => {
  it('should generate valid 6-digit TOTP codes', () => {
    const secret = 'JBSWY3DPEHPK3PXP';
    
    // Configure authenticator with the same settings as our implementation
    authenticator.options = {
      step: 30,
      window: 1,
      digits: 6,
    };
    
    const otpCode = authenticator.generate(secret);
    
    expect(otpCode).toMatch(/^\d{6}$/);
    expect(otpCode.length).toBe(6);
  });

  it('should generate different codes over time', async () => {
    const secret = 'JBSWY3DPEHPK3PXP';
    
    authenticator.options = {
      step: 30,
      window: 1,
      digits: 6,
    };
    
    const code1 = authenticator.generate(secret);
    
    // Wait a bit and generate another code
    // Note: In a real test, you might want to mock the time
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const code2 = authenticator.generate(secret);
    
    // Codes should be the same within the same 30-second window
    expect(code1).toBe(code2);
  });
});
