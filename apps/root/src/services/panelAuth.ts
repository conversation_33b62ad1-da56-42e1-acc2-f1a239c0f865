// Removed otplib import - sending OTP secret directly to server instead
import {
  getPanelCredentialsFromCache,
  hasPanelCredentialsInCache
} from './credentialsService';

interface PanelCredentials {
  login: string;
  password: string;
  otpSecret?: string;
}



interface AuthResponse {
  success: boolean;
  token?: string;
  user?: { id: string; name?: string; [key: string]: unknown };
  error?: string;
}

interface PanelConfig {
  name: string;
  authUrl: string;
  url: string;
  hasOtp: boolean;
}

// Panel configurations
const PANEL_CONFIGS: Record<string, PanelConfig> = {
  'ebetlab': {
    name: 'EbetLab Admin Panel',
    authUrl: `${import.meta.env.VITE_API_URL}/api/internal/panels/ebetlab`,
    url: "https://bo.makroz.org/ebetlab",
    hasOtp: true
  },
  'pronet': {
    name: 'ProNet Admin Panel',
    authUrl: `${import.meta.env.VITE_API_URL}/api/internal/panels/pgdagur`,
    url: "http://localhost:5173",
    hasOtp: true
  }
};

// No need for client-side OTP generation - sending secret directly to server

/**
 * Get credentials for a specific panel from the credentials cache
 */
export const getPanelCredentials = (panelId: string): PanelCredentials | null => {
  try {
    const fetchedCredential = getPanelCredentialsFromCache(panelId);

    if (fetchedCredential) {
      return {
        login: fetchedCredential.login,
        password: fetchedCredential.password,
        otpSecret: fetchedCredential.otpSecret
      };
    }

    return null;
  } catch (error) {
    console.error('Failed to get panel credentials:', error);
    return null;
  }
};

/**
 * Store credentials for a specific panel
 */
export const storePanelCredentials = (panelId: string, credentials: PanelCredentials): void => {
  try {
    localStorage.setItem(`panel_credentials_${panelId}`, JSON.stringify(credentials));
  } catch (error) {
    console.error('Failed to store panel credentials:', error);
  }
};

/**
 * Remove stored credentials for a specific panel
 */
export const removePanelCredentials = (panelId: string): void => {
  localStorage.removeItem(`panel_credentials_${panelId}`);
};

/**
 * Check if credentials are configured for a panel
 */
export const hasPanelCredentials = (panelId: string): boolean => {
  try {
    return hasPanelCredentialsInCache(panelId);
  } catch (error) {
    console.error('Failed to check panel credentials:', error);
    return false;
  }
};

/**
 * Authenticate with a specific panel and store the token
 */
export const authenticateWithPanel = async (panelId: string): Promise<AuthResponse> => {
  const config = PANEL_CONFIGS[panelId];
  if (!config) {
    return { success: false, error: 'Panel configuration not found' };
  }

  const credentials = getPanelCredentials(panelId);
  if (!credentials) {
    return { success: false, error: 'No credentials configured for this panel' };
  }

  try {
    // Prepare authentication payload
    const authPayload: { username: string; password: string; otp?: string; otpSecret?: string } = {
      username: credentials.login, // Read from credentials.login but send as username
      password: credentials.password
    };

    // Include OTP secret if panel requires it and secret is available
    if (config.hasOtp) {
      if (!credentials.otpSecret) {
        return {
          success: false,
          error: 'OTP secret is required for this panel but not configured'
        };
      }

      console.log('Panel requires OTP, sending OTP secret to server...');
      console.log('OTP secret available:', !!credentials.otpSecret);
      console.log('OTP secret length:', credentials.otpSecret?.length);

      // Send the OTP secret directly to the server - let the server handle OTP generation
      authPayload.otpSecret = credentials.otpSecret;
      console.log('OTP secret added to payload for server-side generation');
    }

    // Get the current user's token for authorization
    const authData = localStorage.getItem('root_panel_auth');
    if (!authData) {
      return {
        success: false,
        error: 'User not authenticated. Please log in first.'
      };
    }

    let userToken;
    try {
      const parsedAuthData = JSON.parse(authData);
      userToken = parsedAuthData.token;
    } catch (error) {
      return {
        success: false,
        error: 'Invalid authentication data. Please log in again.'
      };
    }

    // Make authentication request to the panel
    const response = await fetch(config.authUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`,
      },
      body: JSON.stringify(authPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        error: `Authentication failed: ${response.status} ${response.statusText}. ${errorText}`
      };
    }

    const authResult = await response.json();

    // Check if the response indicates success
    if (authResult.success === false) {
      return {
        success: false,
        error: authResult.error || authResult.message || 'Authentication failed'
      };
    }

    // Extract and set session cookies if available
    if (authResult.success && authResult.data?.session?.cookies) {
      try {
        console.log('Setting session cookies for panel authentication...');
        const cookies = authResult.data.session.cookies;

        // Iterate through all cookie key-value pairs
        Object.entries(cookies).forEach(([cookieName, cookieValue]) => {
          try {
            // Set each cookie with appropriate formatting
            // Using secure settings for production and allowing for cross-domain usage
            const cookieString = `${cookieName}=${cookieValue}; path=/; SameSite=Lax`;
            document.cookie = cookieString;
            console.log(`Set cookie: ${cookieName}=${cookieValue}`);
          } catch (cookieError) {
            console.error(`Failed to set cookie ${cookieName}:`, cookieError);
          }
        });

        console.log('All session cookies have been set successfully');
      } catch (error) {
        console.error('Failed to process session cookies:', error);
        // Don't fail the authentication if cookie setting fails, just log the error
      }
    }

    // Return successful authentication result
    return {
      success: true,
      token: authResult.token || authResult.accessToken || 'authenticated',
      user: authResult.user || authResult.data?.user || { id: 'authenticated' }
    };

  } catch (error) {
    console.error('Authentication request failed:', error);
    return {
      success: false,
      error: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

/**
 * Access a panel (authenticate and redirect)
 */
export const accessPanel = async (panelId: string): Promise<void> => {
  const config = PANEL_CONFIGS[panelId];
  if (!config) {
    throw new Error('Panel configuration not found');
  }

  // Check if credentials are configured
  if (!hasPanelCredentials(panelId)) {
    throw new Error('Please configure credentials for this panel first');
  }

  // Additional validation for OTP-enabled panels
  if (config.hasOtp) {
    const credentials = getPanelCredentials(panelId);
    if (!credentials?.otpSecret) {
      throw new Error('This panel requires OTP authentication. Please configure your OTP secret in the panel settings.');
    }
  }

  try {
    // Authenticate with the panel
    const authResult = await authenticateWithPanel(panelId);

    if (!authResult.success) {
      // Provide more specific error messages for common OTP issues
      if (authResult.error?.includes('OTP') || authResult.error?.includes('otp')) {
        throw new Error(`OTP Authentication failed: ${authResult.error}. Please check your OTP secret configuration.`);
      }
      throw new Error(authResult.error || 'Authentication failed');
    }

    // Redirect to the panel
    window.location.href = config.url;
  } catch (error) {
    // Re-throw with additional context if it's not already a user-friendly error
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(`Failed to access panel: ${error}`);
  }
};

/**
 * Get all available panels
 */
export const getAvailablePanels = () => {
  return Object.entries(PANEL_CONFIGS).map(([panelId, config]) => ({
    id: panelId,
    name: config.name,
    authUrl: config.authUrl,
    url: config.url,
    hasOtp: config.hasOtp,
    hasCredentials: hasPanelCredentials(panelId)
  }));
};

/**
 * Check if a panel token is still valid
 * Note: This will be updated to work with the new dynamic credentials system
 */
export const isPanelTokenValid = (panelId: string): boolean => {
  const config = PANEL_CONFIGS[panelId];
  if (!config) return false;

  // For now, return false as we'll implement proper token validation later
  return false;
};

/**
 * Logout from a specific panel
 * Note: This will be updated to work with the new dynamic credentials system
 */
export const logoutFromPanel = (panelId: string): void => {
  const config = PANEL_CONFIGS[panelId];
  if (!config) return;

  // For now, just remove the panel credentials
  localStorage.removeItem(`panel_credentials_${panelId}`);
};
