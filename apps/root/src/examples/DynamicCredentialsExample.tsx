/**
 * Dynamic Credentials System Example
 * 
 * This component demonstrates the complete flow of the dynamic credentials system
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getAvailablePanels } from '../services/panelAuth';
import { 
  getPanelCredentialsFromCache, 
  hasPanelCredentialsInCache,
  getPanelIdsWithCredentials 
} from '../services/credentialsService';

const DynamicCredentialsExample: React.FC = () => {
  const { 
    isAuthenticated, 
    panelCredentials, 
    isLoadingCredentials, 
    credentialsError,
    refreshCredentials 
  } = useAuth();

  const [panels, setPanels] = useState<any[]>([]);

  useEffect(() => {
    // Load panels when credentials change
    const availablePanels = getAvailablePanels();
    const panelsWithCredentials = availablePanels.map(panel => ({
      ...panel,
      hasCredentials: hasPanelCredentialsInCache(panel.id),
      credentials: getPanelCredentialsFromCache(panel.id)
    }));
    
    setPanels(panelsWithCredentials);
  }, [panelCredentials]);

  if (!isAuthenticated) {
    return (
      <div className="p-6 bg-gray-800 rounded-lg">
        <h2 className="text-xl font-bold text-white mb-4">Dynamic Credentials Example</h2>
        <p className="text-gray-400">Please log in to see the dynamic credentials system in action.</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-800 rounded-lg">
      <h2 className="text-xl font-bold text-white mb-6">Dynamic Credentials System Demo</h2>
      
      {/* Credentials Status */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-3">Credentials Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-700 p-4 rounded-lg">
            <div className="text-sm text-gray-400">Loading State</div>
            <div className={`text-lg font-bold ${isLoadingCredentials ? 'text-yellow-400' : 'text-green-400'}`}>
              {isLoadingCredentials ? 'Loading...' : 'Ready'}
            </div>
          </div>
          
          <div className="bg-gray-700 p-4 rounded-lg">
            <div className="text-sm text-gray-400">Error State</div>
            <div className={`text-lg font-bold ${credentialsError ? 'text-red-400' : 'text-green-400'}`}>
              {credentialsError ? 'Error' : 'No Errors'}
            </div>
          </div>
          
          <div className="bg-gray-700 p-4 rounded-lg">
            <div className="text-sm text-gray-400">Panels with Credentials</div>
            <div className="text-lg font-bold text-blue-400">
              {getPanelIdsWithCredentials().length} / {panels.length}
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {credentialsError && (
        <div className="mb-6 bg-red-900/20 border border-red-500/30 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-red-300">{credentialsError}</span>
            <button
              onClick={refreshCredentials}
              disabled={isLoadingCredentials}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white text-sm rounded"
            >
              {isLoadingCredentials ? 'Retrying...' : 'Retry'}
            </button>
          </div>
        </div>
      )}

      {/* Panels List */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-3">Available Panels</h3>
        <div className="space-y-3">
          {panels.map(panel => (
            <div key={panel.id} className="bg-gray-700 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-white font-medium">{panel.name}</h4>
                  <p className="text-sm text-gray-400">ID: {panel.id}</p>
                  <p className="text-sm text-gray-400">Auth URL: {panel.authUrl}</p>
                  <p className="text-sm text-gray-400">Panel URL: {panel.url}</p>
                </div>
                
                <div className="text-right">
                  <div className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                    panel.hasCredentials 
                      ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                      : 'bg-yellow-900/30 text-yellow-400 border border-yellow-500/30'
                  }`}>
                    {panel.hasCredentials ? 'Has Credentials' : 'Setup Required'}
                  </div>
                  
                  {panel.credentials && (
                    <div className="mt-2 text-xs text-gray-500">
                      <div>Login: {panel.credentials.login}</div>
                      <div>OTP: {panel.credentials.otpSecret ? 'Configured' : 'Not Set'}</div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Raw Data Display */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-3">Raw Credentials Data</h3>
        <div className="bg-gray-900 p-4 rounded-lg overflow-auto">
          <pre className="text-sm text-gray-300">
            {JSON.stringify(panelCredentials, null, 2)}
          </pre>
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-3">
        <button
          onClick={refreshCredentials}
          disabled={isLoadingCredentials}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white rounded transition-colors"
        >
          {isLoadingCredentials ? 'Refreshing...' : 'Refresh Credentials'}
        </button>
        
        <button
          onClick={() => {
            console.log('Current credentials cache:', panelCredentials);
            console.log('Panels with credentials:', getPanelIdsWithCredentials());
            console.log('Available panels:', getAvailablePanels());
          }}
          className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
        >
          Log Debug Info
        </button>
      </div>

      {/* Implementation Notes */}
      <div className="mt-8 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
        <h4 className="text-blue-300 font-medium mb-2">Implementation Notes</h4>
        <ul className="text-sm text-blue-200 space-y-1">
          <li>• Credentials are fetched automatically after login</li>
          <li>• Data is stored in memory (not localStorage) for security</li>
          <li>• UI updates automatically when credentials change</li>
          <li>• Error handling with retry functionality</li>
          <li>• Uses existing HTTP client with token refresh</li>
        </ul>
      </div>
    </div>
  );
};

export default DynamicCredentialsExample;
