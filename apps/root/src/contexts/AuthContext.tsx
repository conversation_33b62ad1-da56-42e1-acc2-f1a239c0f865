import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import httpClient from '../utils/httpClient';
import {
  fetchPanelCredentials,
  setCredentialsCache,
  clearCredentialsCache,
  PanelCredentialsMap
} from '../services/credentialsService';

interface User {
  id: string;
  username: string;
  email: string;
}

interface AuthData {
  token: string;
  refreshToken?: string;
  user: User;
}

interface AuthContextType {
  isAuthenticated: boolean;
  authData: AuthData | null;
  login: (email: string, password: string, otp: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  panelCredentials: PanelCredentialsMap;
  isLoadingCredentials: boolean;
  credentialsError: string | null;
  refreshCredentials: () => Promise<void>;
  retryCredentials: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authData, setAuthData] = useState<AuthData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [panelCredentials, setPanelCredentials] = useState<PanelCredentialsMap>({});
  const [isLoadingCredentials, setIsLoadingCredentials] = useState(false);
  const [credentialsError, setCredentialsError] = useState<string | null>(null);

  useEffect(() => {
    // Check for stored authentication data on app load
    const storedAuthData = localStorage.getItem('root_panel_auth');

    if (storedAuthData) {
      try {
        const parsedData = JSON.parse(storedAuthData);
        setAuthData(parsedData);
        setIsAuthenticated(true);

        // Fetch credentials on page load if user is already authenticated
        refreshCredentials();
      } catch (error) {
        console.error('Failed to parse stored auth data:', error);
        localStorage.removeItem('root_panel_auth');
      }
    }
    setIsLoading(false);

    // Listen for token updates from HTTP client
    const handleTokenUpdate = (event: CustomEvent) => {
      const updatedAuthData = event.detail as AuthData;
      setAuthData(updatedAuthData);
      setIsAuthenticated(true);
    };

    // Listen for auth failures from HTTP client
    const handleAuthFailure = () => {
      setAuthData(null);
      setIsAuthenticated(false);
    };

    window.addEventListener('auth-token-updated', handleTokenUpdate as EventListener);
    window.addEventListener('auth-failure', handleAuthFailure);

    return () => {
      window.removeEventListener('auth-token-updated', handleTokenUpdate as EventListener);
      window.removeEventListener('auth-failure', handleAuthFailure);
    };
  }, []);

  const refreshCredentials = async (): Promise<void> => {
    setIsLoadingCredentials(true);
    setCredentialsError(null);

    try {
      const credentials = await fetchPanelCredentials();
      setPanelCredentials(credentials);
      setCredentialsCache(credentials);
    } catch (error) {
      console.error('Failed to fetch credentials:', error);
      setCredentialsError('Failed to load panel credentials');
    } finally {
      setIsLoadingCredentials(false);
    }
  };

  const retryCredentials = async (): Promise<void> => {
    await refreshCredentials();
  };

  const login = async (email: string, password: string, otp: string): Promise<boolean> => {
    try {
      // Use the HTTP client for authentication (skip auth for login)
      const response = await httpClient.post('/api/internal/auth/logins', {
        email: email.trim(),
        password: password,
        otp: otp.trim()
      }, { skipAuth: true });

      if (response.data.success && response.data.data) {
        const newAuthData: AuthData = {
          token: response.data.data.tokens?.accessToken || 'root_panel_token_' + Date.now(),
          refreshToken: response.data.data.tokens?.refreshToken,
          user: {
            id: response.data.data.user?.id?.toString() || '1',
            username: response.data.data.user?.email?.split('@')[0] || 'user',
            email: response.data.data.user?.email || email
          }
        };

        setAuthData(newAuthData);
        setIsAuthenticated(true);
        localStorage.setItem('root_panel_auth', JSON.stringify(newAuthData));

        // Fetch panel credentials after successful login
        await refreshCredentials();

        return true;
      }

      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const logout = () => {
    setAuthData(null);
    setIsAuthenticated(false);
    localStorage.removeItem('root_panel_auth');

    // Clear credentials
    setPanelCredentials({});
    clearCredentialsCache();
    setCredentialsError(null);
  };

  const value: AuthContextType = {
    isAuthenticated,
    authData,
    login,
    logout,
    isLoading,
    panelCredentials,
    isLoadingCredentials,
    credentialsError,
    refreshCredentials,
    retryCredentials,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export type { User, AuthData };
