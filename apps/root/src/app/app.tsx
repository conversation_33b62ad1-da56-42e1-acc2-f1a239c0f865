import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { AuthProvider, useAuth } from '../contexts/AuthContext';
import Dashboard from '../pages/Dashboard';
import Settings from '../pages/Settings';
import PanelSettings from '../pages/PanelSettings';
import LoginModal from '../components/auth/LoginModal';
import Layout from '../components/layout/Layout';

const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <LoginModal isOpen={!isAuthenticated} />
      {isAuthenticated && (
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/panel/:panelId/settings" element={<PanelSettings />} />
          </Routes>
        </Layout>
      )}
    </>
  );
};

export function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
