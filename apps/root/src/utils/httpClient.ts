interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

interface AuthData {
  token: string;
  refreshToken?: string;
  user: {
    id: string;
    username: string;
    email: string;
  };
}

interface RefreshResponse {
  success: boolean;
  message?: string;
  data?: {
    tokens: AuthTokens;
  };
}

interface HttpClientOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: string;
  skipAuth?: boolean;
  timeout?: number;
}

interface HttpResponse<T = any> {
  ok: boolean;
  status: number;
  data: T;
  headers: Headers;
}

class HttpClient {
  private baseUrl: string;
  private isRefreshing = false;
  private refreshPromise: Promise<boolean> | null = null;
  private refreshAttempts = 0;
  private maxRefreshAttempts = 3;
  private pendingRequests: Array<{
    resolve: (value: any) => void;
    reject: (error: any) => void;
    url: string;
    options: HttpClientOptions;
  }> = [];

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000';
  }

  private getStoredAuth(): AuthData | null {
    try {
      const stored = localStorage.getItem('root_panel_auth');
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to parse stored auth data:', error);
      return null;
    }
  }

  private updateStoredAuth(authData: AuthData): void {
    try {
      localStorage.setItem('root_panel_auth', JSON.stringify(authData));
      
      // Dispatch custom event to notify AuthContext of token update
      window.dispatchEvent(new CustomEvent('auth-token-updated', { 
        detail: authData 
      }));
    } catch (error) {
      console.error('Failed to store auth data:', error);
    }
  }

  private async refreshToken(): Promise<boolean> {
    if (this.isRefreshing && this.refreshPromise) {
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  private async performTokenRefresh(): Promise<boolean> {
    try {
      // Check if we've exceeded max refresh attempts
      if (this.refreshAttempts >= this.maxRefreshAttempts) {
        console.error('Max refresh attempts exceeded');
        this.handleAuthFailure();
        return false;
      }

      this.refreshAttempts++;

      const authData = this.getStoredAuth();

      if (!authData?.refreshToken) {
        console.error('No refresh token available');
        this.handleAuthFailure();
        return false;
      }

      const response = await fetch(`${this.baseUrl}/api/internal/auth/refresh-token-exchanges`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refreshToken: authData.refreshToken
        })
      });

      if (!response.ok) {
        console.error('Token refresh HTTP error:', response.status, response.statusText);
        this.handleAuthFailure();
        return false;
      }

      const result: RefreshResponse = await response.json();

      if (result.success && result.data?.tokens) {
        // Reset refresh attempts on success
        this.refreshAttempts = 0;

        // Update stored auth data with new tokens
        const updatedAuthData: AuthData = {
          ...authData,
          token: result.data.tokens.accessToken,
          refreshToken: result.data.tokens.refreshToken
        };

        this.updateStoredAuth(updatedAuthData);

        // Process pending requests with new token
        this.processPendingRequests();

        return true;
      } else {
        console.error('Token refresh failed:', result.message);
        this.handleAuthFailure();
        return false;
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      this.handleAuthFailure();
      return false;
    }
  }

  private handleAuthFailure(): void {
    // Reset refresh attempts
    this.refreshAttempts = 0;
    this.isRefreshing = false;
    this.refreshPromise = null;

    // Clear stored auth data
    localStorage.removeItem('root_panel_auth');

    // Reject all pending requests
    this.pendingRequests.forEach(({ reject }) => {
      reject(new Error('Authentication failed'));
    });
    this.pendingRequests = [];

    // Dispatch event to notify AuthContext to logout
    window.dispatchEvent(new CustomEvent('auth-failure'));
  }

  private processPendingRequests(): void {
    const requests = [...this.pendingRequests];
    this.pendingRequests = [];

    requests.forEach(async ({ resolve, reject, url, options }) => {
      try {
        const response = await this.makeRequest(url, options);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  }

  private isTokenExpiredError(status: number, data: any): boolean {
    // Check for common token expiration indicators
    if (status === 401) {
      return true;
    }

    // Check for specific error messages that indicate token expiration
    if (data && typeof data === 'object') {
      const message = data.message || data.error || '';
      const lowerMessage = message.toLowerCase();

      return lowerMessage.includes('token expired') ||
             lowerMessage.includes('token invalid') ||
             lowerMessage.includes('unauthorized') ||
             lowerMessage.includes('jwt expired') ||
             lowerMessage.includes('access denied') ||
             lowerMessage.includes('authentication failed');
    }

    return false;
  }

  private async makeRequest(url: string, options: HttpClientOptions = {}): Promise<HttpResponse> {
    const authData = this.getStoredAuth();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add authorization header if not skipping auth and token exists
    if (!options.skipAuth && authData?.token) {
      headers.Authorization = `Bearer ${authData.token}`;
    }

    const fetchOptions: RequestInit = {
      method: options.method || 'GET',
      headers,
      ...(options.body && { body: options.body })
    };

    // Add timeout if specified
    if (options.timeout) {
      const controller = new AbortController();
      fetchOptions.signal = controller.signal;

      setTimeout(() => controller.abort(), options.timeout);
    }

    const response = await fetch(url, fetchOptions);
    let data: any;

    try {
      data = await response.json();
    } catch (error) {
      // Handle non-JSON responses
      data = await response.text();
    }

    return {
      ok: response.ok,
      status: response.status,
      data,
      headers: response.headers
    };
  }

  async request<T = any>(endpoint: string, options: HttpClientOptions = {}): Promise<HttpResponse<T>> {
    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;

    try {
      const response = await this.makeRequest(url, options);

      // Check if token is expired and we're not already refreshing
      if (!options.skipAuth && this.isTokenExpiredError(response.status, response.data)) {
        // If we're already refreshing, queue this request
        if (this.isRefreshing) {
          return new Promise((resolve, reject) => {
            this.pendingRequests.push({ resolve, reject, url, options });
          });
        }

        // Attempt to refresh token
        const refreshSuccess = await this.refreshToken();

        if (refreshSuccess) {
          // Retry the original request with new token
          return this.makeRequest(url, options);
        } else {
          throw new Error('Authentication failed');
        }
      }

      return response;
    } catch (error) {
      console.error('HTTP request failed:', error);
      throw error;
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, options: Omit<HttpClientOptions, 'method'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T = any>(endpoint: string, body?: any, options: Omit<HttpClientOptions, 'method' | 'body'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: body ? JSON.stringify(body) : undefined
    });
  }

  async put<T = any>(endpoint: string, body?: any, options: Omit<HttpClientOptions, 'method' | 'body'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: body ? JSON.stringify(body) : undefined
    });
  }

  async delete<T = any>(endpoint: string, options: Omit<HttpClientOptions, 'method'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  // Public method to reset client state (useful for testing or manual cleanup)
  public reset(): void {
    this.refreshAttempts = 0;
    this.isRefreshing = false;
    this.refreshPromise = null;
    this.pendingRequests.forEach(({ reject }) => {
      reject(new Error('Client reset'));
    });
    this.pendingRequests = [];
  }

  // Public method to check if client is currently refreshing
  public get isCurrentlyRefreshing(): boolean {
    return this.isRefreshing;
  }

  // Public method to get current refresh attempts
  public get currentRefreshAttempts(): number {
    return this.refreshAttempts;
  }
}

// Create and export a singleton instance
export const httpClient = new HttpClient();
export default httpClient;
