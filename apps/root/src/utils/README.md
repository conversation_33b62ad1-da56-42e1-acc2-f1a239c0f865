# HTTP Client with Automatic Token Refresh

This HTTP client provides automatic token refresh functionality for the root panel application. It handles expired authentication tokens seamlessly without requiring changes to individual API call sites.

## Features

- **Automatic Token Refresh**: Detects token expiration and automatically refreshes tokens
- **Request Queuing**: Queues requests during token refresh to prevent duplicate refresh calls
- **Error Handling**: Comprehensive error handling with retry limits
- **Event-Driven**: Integrates with AuthContext through custom events
- **Timeout Support**: Configurable request timeouts
- **Concurrent Request Handling**: Manages multiple simultaneous requests during token refresh

## Usage

### Basic Usage

```typescript
import httpClient from '../utils/httpClient';

// GET request
const response = await httpClient.get('/api/users');

// POST request
const response = await httpClient.post('/api/users', {
  name: '<PERSON>',
  email: '<EMAIL>'
});

// PUT request
const response = await httpClient.put('/api/users/1', {
  name: '<PERSON>'
});

// DELETE request
const response = await httpClient.delete('/api/users/1');
```

### Advanced Usage

```typescript
// Skip authentication for public endpoints
const response = await httpClient.get('/api/public/status', {
  skipAuth: true
});

// Add custom headers
const response = await httpClient.post('/api/data', data, {
  headers: {
    'X-Custom-Header': 'value'
  }
});

// Set request timeout
const response = await httpClient.get('/api/slow-endpoint', {
  timeout: 10000 // 10 seconds
});
```

## Token Refresh Flow

1. **Request Made**: API request is made with current access token
2. **Token Expired**: Server returns 401 or token expiration error
3. **Automatic Refresh**: HTTP client automatically calls refresh token endpoint
4. **Token Update**: New tokens are stored and AuthContext is notified
5. **Request Retry**: Original request is retried with new access token
6. **Queue Processing**: Any queued requests are processed with new token

## Error Handling

### Refresh Token Expired
When the refresh token is also expired:
- All pending requests are rejected
- User is automatically logged out
- AuthContext receives 'auth-failure' event

### Network Errors
- Requests are rejected with appropriate error messages
- Refresh attempts are limited to prevent infinite loops
- Client state is reset on critical failures

### Concurrent Requests
- Multiple requests during token refresh are queued
- Only one refresh operation occurs at a time
- All queued requests use the new token after refresh

## Integration with AuthContext

The HTTP client integrates with the AuthContext through custom events:

- `auth-token-updated`: Fired when tokens are refreshed
- `auth-failure`: Fired when authentication fails completely

## Configuration

### Environment Variables
- `VITE_API_URL`: Base URL for API requests (defaults to 'http://localhost:3000')

### Refresh Token Endpoint
- **Endpoint**: `POST /api/internal/auth/refresh-token-exchanges`
- **Request**: `{ "refreshToken": "<refresh_token>" }`
- **Response**: 
  ```json
  {
    "success": true,
    "data": {
      "tokens": {
        "accessToken": "new_access_token",
        "refreshToken": "new_refresh_token"
      }
    }
  }
  ```

## Token Storage

Tokens are stored in localStorage under the key `root_panel_auth`:

```json
{
  "token": "access_token",
  "refreshToken": "refresh_token",
  "user": {
    "id": "1",
    "username": "user",
    "email": "<EMAIL>"
  }
}
```

## Public Methods

### Request Methods
- `get<T>(endpoint, options)`: GET request
- `post<T>(endpoint, body, options)`: POST request
- `put<T>(endpoint, body, options)`: PUT request
- `delete<T>(endpoint, options)`: DELETE request
- `request<T>(endpoint, options)`: Generic request

### Utility Methods
- `reset()`: Reset client state (useful for testing)
- `isCurrentlyRefreshing`: Check if currently refreshing tokens
- `currentRefreshAttempts`: Get current refresh attempt count

## Error Prevention

### Infinite Loop Prevention
- Maximum of 3 refresh attempts before giving up
- Refresh attempts reset on successful refresh
- Client state is reset on authentication failure

### Request Timeout
- Configurable timeout for individual requests
- Prevents hanging requests
- Uses AbortController for clean cancellation

## Best Practices

1. **Use the singleton instance**: Import and use the default exported `httpClient`
2. **Handle errors appropriately**: Wrap API calls in try-catch blocks
3. **Use skipAuth for public endpoints**: Avoid unnecessary auth headers
4. **Set reasonable timeouts**: For slow endpoints, configure appropriate timeouts
5. **Monitor refresh attempts**: Use utility methods for debugging if needed

## Example Implementation

```typescript
// In a component or service
import httpClient from '../utils/httpClient';

const fetchUserData = async (userId: string) => {
  try {
    const response = await httpClient.get(`/api/users/${userId}`);
    
    if (response.ok) {
      return response.data;
    } else {
      throw new Error('Failed to fetch user data');
    }
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};
```

This HTTP client ensures that your application maintains seamless authentication without requiring manual token management in your components or services.
