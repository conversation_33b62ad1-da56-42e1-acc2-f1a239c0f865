/**
 * HTTP Client Integration Test
 * 
 * This file demonstrates how the HTTP client works and can be used
 * for manual testing of the token refresh functionality.
 */

import httpClient from './httpClient';

// Mock localStorage for testing
const mockLocalStorage = {
  store: {} as Record<string, string>,
  getItem: (key: string) => mockLocalStorage.store[key] || null,
  setItem: (key: string, value: string) => {
    mockLocalStorage.store[key] = value;
  },
  removeItem: (key: string) => {
    delete mockLocalStorage.store[key];
  },
  clear: () => {
    mockLocalStorage.store = {};
  }
};

// Replace global localStorage with mock for testing
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Mock fetch for testing
const originalFetch = global.fetch;

interface MockResponse {
  ok: boolean;
  status: number;
  json: () => Promise<any>;
  text: () => Promise<string>;
  headers: Headers;
}

const createMockResponse = (data: any, status = 200): MockResponse => ({
  ok: status >= 200 && status < 300,
  status,
  json: () => Promise.resolve(data),
  text: () => Promise.resolve(JSON.stringify(data)),
  headers: new Headers()
});

// Test scenarios
export const testScenarios = {
  // Test successful API call
  testSuccessfulRequest: async () => {
    console.log('Testing successful API request...');
    
    // Mock successful response
    global.fetch = jest.fn().mockResolvedValue(
      createMockResponse({ success: true, data: { message: 'Success' } })
    );

    try {
      const response = await httpClient.get('/api/test');
      console.log('✅ Successful request:', response.data);
      return true;
    } catch (error) {
      console.error('❌ Request failed:', error);
      return false;
    }
  },

  // Test token refresh flow
  testTokenRefresh: async () => {
    console.log('Testing token refresh flow...');
    
    // Set up initial auth data with expired token
    const initialAuthData = {
      token: 'expired_token',
      refreshToken: 'valid_refresh_token',
      user: { id: '1', username: 'test', email: '<EMAIL>' }
    };
    
    localStorage.setItem('root_panel_auth', JSON.stringify(initialAuthData));

    let callCount = 0;
    global.fetch = jest.fn().mockImplementation((url: string) => {
      callCount++;
      
      // First call - return 401 (token expired)
      if (callCount === 1) {
        return Promise.resolve(
          createMockResponse({ error: 'Token expired' }, 401)
        );
      }
      
      // Second call - refresh token endpoint
      if (url.includes('refresh-token-exchanges')) {
        return Promise.resolve(
          createMockResponse({
            success: true,
            data: {
              tokens: {
                accessToken: 'new_access_token',
                refreshToken: 'new_refresh_token'
              }
            }
          })
        );
      }
      
      // Third call - retry original request with new token
      return Promise.resolve(
        createMockResponse({ success: true, data: { message: 'Success with new token' } })
      );
    });

    try {
      const response = await httpClient.get('/api/protected');
      console.log('✅ Token refresh successful:', response.data);
      
      // Verify new tokens were stored
      const updatedAuth = JSON.parse(localStorage.getItem('root_panel_auth') || '{}');
      console.log('✅ New tokens stored:', {
        accessToken: updatedAuth.token,
        refreshToken: updatedAuth.refreshToken
      });
      
      return true;
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      return false;
    }
  },

  // Test auth failure handling
  testAuthFailure: async () => {
    console.log('Testing auth failure handling...');
    
    // Set up auth data with invalid refresh token
    const authData = {
      token: 'expired_token',
      refreshToken: 'invalid_refresh_token',
      user: { id: '1', username: 'test', email: '<EMAIL>' }
    };
    
    localStorage.setItem('root_panel_auth', JSON.stringify(authData));

    let callCount = 0;
    global.fetch = jest.fn().mockImplementation((url: string) => {
      callCount++;
      
      // First call - return 401 (token expired)
      if (callCount === 1) {
        return Promise.resolve(
          createMockResponse({ error: 'Token expired' }, 401)
        );
      }
      
      // Second call - refresh token fails
      if (url.includes('refresh-token-exchanges')) {
        return Promise.resolve(
          createMockResponse({ success: false, error: 'Refresh token expired' }, 401)
        );
      }
      
      return Promise.reject(new Error('Unexpected call'));
    });

    // Listen for auth failure event
    let authFailureTriggered = false;
    const handleAuthFailure = () => {
      authFailureTriggered = true;
      console.log('✅ Auth failure event triggered');
    };
    
    window.addEventListener('auth-failure', handleAuthFailure);

    try {
      await httpClient.get('/api/protected');
      console.error('❌ Expected auth failure but request succeeded');
      return false;
    } catch (error) {
      console.log('✅ Request failed as expected:', error.message);
      
      // Verify auth data was cleared
      const clearedAuth = localStorage.getItem('root_panel_auth');
      if (!clearedAuth) {
        console.log('✅ Auth data cleared from storage');
      }
      
      // Verify auth failure event was triggered
      if (authFailureTriggered) {
        console.log('✅ Auth failure handling complete');
        return true;
      } else {
        console.error('❌ Auth failure event not triggered');
        return false;
      }
    } finally {
      window.removeEventListener('auth-failure', handleAuthFailure);
    }
  },

  // Test concurrent requests during refresh
  testConcurrentRequests: async () => {
    console.log('Testing concurrent requests during token refresh...');
    
    // Set up auth data
    const authData = {
      token: 'expired_token',
      refreshToken: 'valid_refresh_token',
      user: { id: '1', username: 'test', email: '<EMAIL>' }
    };
    
    localStorage.setItem('root_panel_auth', JSON.stringify(authData));

    let refreshCalled = false;
    global.fetch = jest.fn().mockImplementation((url: string) => {
      // First few calls - return 401 (token expired)
      if (!refreshCalled && !url.includes('refresh-token-exchanges')) {
        return Promise.resolve(
          createMockResponse({ error: 'Token expired' }, 401)
        );
      }
      
      // Refresh token call
      if (url.includes('refresh-token-exchanges')) {
        refreshCalled = true;
        return Promise.resolve(
          createMockResponse({
            success: true,
            data: {
              tokens: {
                accessToken: 'new_access_token',
                refreshToken: 'new_refresh_token'
              }
            }
          })
        );
      }
      
      // Subsequent calls with new token
      return Promise.resolve(
        createMockResponse({ success: true, data: { message: 'Success' } })
      );
    });

    try {
      // Make multiple concurrent requests
      const promises = [
        httpClient.get('/api/data1'),
        httpClient.get('/api/data2'),
        httpClient.get('/api/data3')
      ];
      
      const responses = await Promise.all(promises);
      
      console.log('✅ All concurrent requests succeeded');
      console.log('✅ Responses:', responses.map(r => r.data));
      
      return true;
    } catch (error) {
      console.error('❌ Concurrent requests failed:', error);
      return false;
    }
  },

  // Run all tests
  runAllTests: async () => {
    console.log('🚀 Starting HTTP Client Tests...\n');
    
    const results = {
      successfulRequest: await testScenarios.testSuccessfulRequest(),
      tokenRefresh: await testScenarios.testTokenRefresh(),
      authFailure: await testScenarios.testAuthFailure(),
      concurrentRequests: await testScenarios.testConcurrentRequests()
    };
    
    console.log('\n📊 Test Results:');
    Object.entries(results).forEach(([test, passed]) => {
      console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
    });
    
    const allPassed = Object.values(results).every(Boolean);
    console.log(`\n${allPassed ? '🎉' : '💥'} Overall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
    
    // Restore original fetch
    global.fetch = originalFetch;
    
    return allPassed;
  }
};

// Export for manual testing
export default testScenarios;
