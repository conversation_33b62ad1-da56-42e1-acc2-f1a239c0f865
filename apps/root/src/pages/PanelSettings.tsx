import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Save, Eye, EyeOff, ArrowLeft, Shield } from 'lucide-react';
import { getAvailablePanels } from '../services/panelAuth';
import { useAuth } from '../contexts/AuthContext';
import { savePanelCredentials } from '../services/credentialsService';
import Toast from '../components/ui/Toast';
import { useToast } from '../hooks/useToast';

interface PanelCredentialsForm {
  login: string;
  password: string;
  otpSecret?: string;
}



interface Panel {
  id: string;
  name: string;
  hasOtp: boolean;
  hasCredentials: boolean;
}

const PanelSettings: React.FC = () => {
  const { panelId } = useParams<{ panelId: string }>();
  const navigate = useNavigate();
  const { panelCredentials, refreshCredentials } = useAuth();
  const [panel, setPanel] = useState<Panel | null>(null);
  const [credentials, setCredentials] = useState<PanelCredentialsForm>({
    login: '',
    password: '',
    otpSecret: ''
  });
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const [isSaving, setIsSaving] = useState(false);
  const { toast, showError, hideToast } = useToast();

  useEffect(() => {
    if (!panelId) {
      navigate('/');
      return;
    }

    // Find the panel
    const availablePanels = getAvailablePanels();
    const foundPanel = availablePanels.find(p => p.id === panelId);
    
    if (!foundPanel) {
      navigate('/');
      return;
    }

    setPanel(foundPanel);

    // Load existing credentials from fetched data
    const existingCredentials = panelCredentials[panelId];
    if (existingCredentials) {
      setCredentials({
        login: existingCredentials.login,
        password: existingCredentials.password,
        otpSecret: existingCredentials.otpSecret
      });
    }
  }, [panelId, navigate, panelCredentials]);

  const togglePasswordVisibility = (fieldId: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId]
    }));
  };

  const handleInputChange = (field: string, value: string) => {
    setCredentials((prev: PanelCredentialsForm) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    if (!panelId || !panel) return;

    // Validate required fields
    if (!credentials.login.trim()) {
      showError('Please enter a login/email');
      return;
    }

    if (!credentials.password.trim()) {
      showError('Please enter a password');
      return;
    }

    // Validate OTP secret if required for this panel
    if (panel.hasOtp && !credentials.otpSecret?.trim()) {
      showError('OTP secret is required for this panel');
      return;
    }

    setIsSaving(true);
    try {
      // Call the API to save credentials
      const response = await savePanelCredentials(
        panelId, // Use panelId consistently
        credentials.login,
        credentials.password,
        credentials.otpSecret
      );

      if (response.success) {
        // Refresh credentials cache to get the latest data
        await refreshCredentials();

        // Navigate with success message in state
        navigate('/', {
          state: {
            successMessage: `Successfully updated credentials for ${panel.name}`
          }
        });
      } else {
        showError(response.message || 'Failed to save credentials');
      }
    } catch (error: any) {
      console.error('Failed to save credentials:', error);

      // Handle different types of errors
      if (error.response?.data?.message) {
        showError(error.response.data.message);
      } else if (error.message) {
        showError(error.message);
      } else {
        showError('Failed to save credentials. Please try again.');
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    navigate('/');
  };

  if (!panel) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
      <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={handleBack}
          className="flex items-center text-gray-400 hover:text-white mb-4 transition-colors"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </button>
        
        <div className="flex items-center mb-4">
          <Shield className="mr-3 h-8 w-8 text-blue-500" />
          <div>
            <h1 className="text-3xl font-bold text-white">
              {panel.name} Settings
            </h1>
            <p className="text-gray-400">
              Configure your credentials for accessing this panel
            </p>
          </div>
        </div>
      </div>

      {/* Credentials Form */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-6">
          Authentication Credentials
        </h2>
        
        <div className="space-y-6">
          {/* Login/Email */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Login/Email
            </label>
            <input
              type="text"
              value={credentials.login}
              onChange={(e) => handleInputChange('login', e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your login or email"
            />
          </div>

          {/* Password */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Password
            </label>
            <div className="relative">
              <input
                type={showPasswords['password'] ? 'text' : 'password'}
                value={credentials.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your password"
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility('password')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              >
                {showPasswords['password'] ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
          </div>

          {/* OTP Secret (for panels that support it) */}
          {panel?.hasOtp && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                OTP Secret {panel.hasOtp ? '(Required)' : '(Optional)'}
              </label>
              <input
                type="text"
                value={credentials.otpSecret || ''}
                onChange={(e) => handleInputChange('otpSecret', e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter OTP secret key"
                required={panel.hasOtp}
              />
              <p className="mt-1 text-xs text-gray-500">
                {panel.hasOtp
                  ? 'This panel requires two-factor authentication'
                  : 'Leave empty if you don\'t use two-factor authentication'
                }
              </p>
            </div>
          )}
        </div>

        {/* Save Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            onClick={handleBack}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white font-medium rounded-md transition-colors"
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Credentials
              </>
            )}
          </button>
        </div>
      </div>


      </div>
    </>
  );
};

export default PanelSettings;
