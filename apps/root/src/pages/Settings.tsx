import React, { useState } from 'react';
import { Save, Eye, EyeOff, Shield } from 'lucide-react';
import Toast from '../components/ui/Toast';
import { useToast } from '../hooks/useToast';
import httpClient from '../utils/httpClient';

// API function for password change
const changePassword = async (currentPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }> => {
  try {
    // Use the HTTP client with automatic token refresh
    const response = await httpClient.post('/api/internal/auth/password-changes', {
      currentPassword,
      newPassword
    });

    if (response.data.success) {
      return { success: true };
    } else {
      return { success: false, error: response.data.message || 'Password change failed' };
    }
  } catch (error) {
    console.error('Password change error:', error);
    return { success: false, error: 'An unexpected error occurred. Please try again.' };
  }
};

const Settings: React.FC = () => {
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const [rootCredentials, setRootCredentials] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const { toast, showSuccess, showError, hideToast } = useToast();

  const togglePasswordVisibility = (fieldId: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId]
    }));
  };

  const handleRootCredentialsChange = (field: string, value: string) => {
    setRootCredentials(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveRootCredentials = async () => {
    // Frontend validation
    if (!rootCredentials.currentPassword.trim()) {
      showError('Current password is required');
      return;
    }

    if (!rootCredentials.newPassword.trim()) {
      showError('New password is required');
      return;
    }

    if (rootCredentials.newPassword !== rootCredentials.confirmPassword) {
      showError('New passwords do not match');
      return;
    }

    if (rootCredentials.newPassword.length < 8) {
      showError('New password must be at least 8 characters long');
      return;
    }

    if (rootCredentials.currentPassword === rootCredentials.newPassword) {
      showError('New password must be different from current password');
      return;
    }

    setIsChangingPassword(true);

    try {
      const result = await changePassword(rootCredentials.currentPassword, rootCredentials.newPassword);

      if (result.success) {
        showSuccess('Password updated successfully');
        setRootCredentials({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        showError(result.error || 'Failed to update password');
      }
    } catch (error) {
      console.error('Password change error:', error);
      showError('An unexpected error occurred. Please try again.');
    } finally {
      setIsChangingPassword(false);
    }
  };

  return (
    <>
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
      <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Shield className="mr-3 h-8 w-8 text-blue-500" />
          <div>
            <h1 className="text-3xl font-bold text-white">Root Panel Settings</h1>
            <p className="text-gray-400">
              Manage your root panel security and authentication
            </p>
          </div>
        </div>
      </div>

      {/* Root Panel Settings */}
      <div className="space-y-6">
        {/* Security Settings */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-6">
            Change Password
          </h2>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Current Password
              </label>
              <div className="relative">
                <input
                  type={showPasswords['current'] ? 'text' : 'password'}
                  value={rootCredentials.currentPassword}
                  onChange={(e) => handleRootCredentialsChange('currentPassword', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-800 disabled:cursor-not-allowed"
                  placeholder="Enter current password"
                  disabled={isChangingPassword}
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('current')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  {showPasswords['current'] ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                New Password
              </label>
              <div className="relative">
                <input
                  type={showPasswords['new'] ? 'text' : 'password'}
                  value={rootCredentials.newPassword}
                  onChange={(e) => handleRootCredentialsChange('newPassword', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-800 disabled:cursor-not-allowed"
                  placeholder="Enter new password"
                  disabled={isChangingPassword}
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('new')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  {showPasswords['new'] ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Confirm New Password
              </label>
              <div className="relative">
                <input
                  type={showPasswords['confirm'] ? 'text' : 'password'}
                  value={rootCredentials.confirmPassword}
                  onChange={(e) => handleRootCredentialsChange('confirmPassword', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-800 disabled:cursor-not-allowed"
                  placeholder="Confirm new password"
                  disabled={isChangingPassword}
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('confirm')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  {showPasswords['confirm'] ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            <div className="text-sm text-gray-400">
              <p>Password requirements:</p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>At least 8 characters long</li>
                <li>Must be different from current password</li>
              </ul>
            </div>

            <button
              onClick={handleSaveRootCredentials}
              disabled={isChangingPassword}
              className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white font-medium rounded-md transition-colors"
            >
              <Save className="mr-2 h-4 w-4" />
              {isChangingPassword ? 'Updating Password...' : 'Update Password'}
            </button>
          </div>
        </div>
      </div>
      </div>
    </>
  );
};

export default Settings;
