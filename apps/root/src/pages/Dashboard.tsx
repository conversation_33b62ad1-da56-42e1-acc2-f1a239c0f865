import React, { useState, useEffect } from 'react';
import { ExternalLink, Settings, Shield, AlertCircle } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { getAvailablePanels, accessPanel } from '../services/panelAuth';
import { useAuth } from '../contexts/AuthContext';
import Toast from '../components/ui/Toast';
import { useToast } from '../hooks/useToast';

interface Panel {
  id: string;
  name: string;
  authUrl: string;
  url: string;
  hasCredentials: boolean;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { panelCredentials, isLoadingCredentials, credentialsError, retryCredentials } = useAuth();
  const [panels, setPanels] = useState<Panel[]>([]);
  const [loading, setLoading] = useState<string | null>(null);
  const { toast, showError, showWarning, showSuccess, hideToast } = useToast();

  useEffect(() => {
    // Load available panels and check their credential status
    const loadPanels = () => {
      const availablePanels = getAvailablePanels();
      const panelsWithStatus: Panel[] = availablePanels.map(panel => ({
        id: panel.id,
        name: panel.name,
        authUrl: panel.authUrl,
        url: panel.url,
        hasCredentials: !!panelCredentials[panel.id]
      }));

      setPanels(panelsWithStatus);
    };

    loadPanels();
  }, [panelCredentials]); // Re-run when credentials change

  // Handle success message from navigation state
  useEffect(() => {
    const state = location.state as { successMessage?: string } | null;
    if (state?.successMessage) {
      showSuccess(state.successMessage);
      // Clear the state to prevent showing the message again on refresh
      navigate('/', { replace: true });
    }
  }, [location.state, showSuccess, navigate]);

  const handlePanelClick = async (panel: Panel) => {
    if (!panel.hasCredentials) {
      showWarning('Please configure credentials for this panel in Settings first.');
      return;
    }

    setLoading(panel.id);
    try {
      await accessPanel(panel.id);
    } catch (error) {
      console.error('Failed to access panel:', error);
      showError(error instanceof Error ? error.message : 'Failed to access panel. Please try again.');
    } finally {
      setLoading(null);
    }
  };

  const handleSettingsClick = (panel: Panel) => {
    // Navigate to panel-specific settings page
    navigate(`/panel/${panel.id}/settings`);
  };

  return (
    <>
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
      <div className="max-w-5xl mx-auto pt-8">

        {/* Credentials Loading State */}
        {isLoadingCredentials && (
          <div className="mb-6 bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-3"></div>
              <span className="text-blue-300">Loading panel credentials...</span>
            </div>
          </div>
        )}

        {/* Credentials Error State */}
        {credentialsError && (
          <div className="mb-6 bg-red-900/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
                <span className="text-red-300">{credentialsError}</span>
              </div>
              <button
                onClick={retryCredentials}
                disabled={isLoadingCredentials}
                className="px-3 py-1 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:cursor-not-allowed text-white text-sm rounded-md transition-colors"
              >
                {isLoadingCredentials ? 'Retrying...' : 'Retry'}
              </button>
            </div>
          </div>
        )}
      {/* Root Panel Statistics */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-white mb-4">Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Shield className="h-6 w-6 text-blue-500" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-400">Total Panels</p>
              <p className="text-xl font-bold text-white">{panels.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ExternalLink className="h-6 w-6 text-green-500" />
            </div>
            <div className="ml-3">
              <p className="text-xs font-medium text-gray-400">Active Panels</p>
              <p className="text-xl font-bold text-white">
                {panels.filter(p => p.hasCredentials).length}
              </p>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Admin Panels */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-white mb-4">Admin Panels</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {panels.map((panel) => (
          <div
            key={panel.id}
            className="bg-gray-800 border border-gray-700 rounded-lg p-6 hover:bg-gray-750 hover:border-gray-600 transition-all"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {panel.name}
                </h3>
              </div>
              <button
                onClick={() => handleSettingsClick(panel)}
                className="text-gray-400 hover:text-white transition-colors"
                title="Panel Settings"
              >
                <Settings className="h-5 w-5" />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  panel.hasCredentials ? 'bg-green-500' : 'bg-yellow-500'
                }`} />
                <span className="text-sm text-gray-400">
                  {panel.hasCredentials ? 'Active' : 'Setup Required'}
                </span>
              </div>
            </div>

            <button
              onClick={() => handlePanelClick(panel)}
              disabled={!panel.hasCredentials || loading === panel.id}
              className={`w-full mt-4 py-2 px-4 rounded-md font-medium transition-colors ${
                panel.hasCredentials && loading !== panel.id
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-gray-700 text-gray-400 cursor-not-allowed'
              }`}
            >
              {loading === panel.id ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Connecting...
                </div>
              ) : panel.hasCredentials ? (
                'Access Panel'
              ) : (
                'Credentials Setup Required'
              )}
            </button>
          </div>
        ))}
        </div>
      </div>

      {/* Empty state if no panels */}
      {panels.length === 0 && (
        <div className="text-center py-12">
          <Shield className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-300">No panels configured</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first admin panel.
          </p>
        </div>
      )}
      </div>
    </>
  );
};

export default Dashboard;
