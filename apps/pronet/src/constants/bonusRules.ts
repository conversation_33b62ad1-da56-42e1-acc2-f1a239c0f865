export interface RuleField {
  value: string;
  label: string;
  type: string;
  inputType?: string;
  options?: string[];
}

export interface RuleOperator {
  value: string;
  label: string;
}

// Standard rule fields used across all bonus modals
export const BONUS_RULE_FIELDS: RuleField[] = [
  { value: 'joinedAt', label: 'Register Date', type: 'date' },
  { value: 'lastDepositDate', label: 'Last Deposit Date', type: 'date' },
  { value: 'ipConflictCount', label: 'IP Conflict Count', type: 'number' },
  { value: 'totalDeposit', label: 'Total Deposit Amount', type: 'number' },
  { value: 'totalWithdraw', label: 'Total Withdraw Amount', type: 'number' },
  {
    value: 'kycVerification',
    label: 'KYC',
    type: 'string',
    inputType: 'binary-checkbox',
    options: ['email', 'phone', 'identity', 'address'],
  },
];

// Standard rule operators used across all bonus modals
export const BONUS_RULE_OPERATORS: RuleOperator[] = [
  { value: 'gt', label: 'Greater than (>)' },
  { value: 'gte', label: 'Greater than or equal (>=)' },
  { value: 'lt', label: 'Less than (<)' },
  { value: 'lte', label: 'Less than or equal (<=)' },
  { value: 'eq', label: 'Equal (=)' },
  { value: 'ne', label: 'Not equal (!=)' },
  { value: 'btw', label: 'Between' },
];
