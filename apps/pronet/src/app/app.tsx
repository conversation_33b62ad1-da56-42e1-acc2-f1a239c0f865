import { Routes, Route, Navigate, Outlet } from 'react-router-dom';
import Dashboard from '../pages/Dashboard';
import { MainLayout, NavSection } from '@panels/ui';
import {
  ApertureIcon,
  LayoutDashboard,
  LogOut,
  Users,
  FileText,
  Gift,
} from 'lucide-react';
import { AuthContextProvider } from '../features/auth/AuthContextProvider';
import { FreespinBonusListPage } from '../pages/FreespinBonus/FreespinBonusListPage';
import { ApiContextProvider } from '../features/api';
import { FreespinBonusTemplatesListPage } from '../pages/FreespinBonus/FreespinBonusTemplatesListPage';
import { FreespinBonusPromocodeListPage } from '../pages/FreespinBonus/FreespinBonusPromocodeListPage';
import { FreespinBonusClaimListPage } from '../pages/FreespinBonus/FreespinBonusClaimListPage';
import { FreespinBonusBulkAssignmentPage } from '../pages/FreespinBonus/FreespinBonusBulkAssignmentPage';
import { CustomersListPage } from '../pages/Customers/CustomersListPage';
import { CustomerDetailsPage } from '../pages/Customers/CustomerDetailsPage';
import { LossbackBonusListPage } from '../pages/LossbackBonus/LossbackBonusListPage';
import { LossbackBonusTemplatesListPage } from '../pages/LossbackBonus/LossbackBonusTemplatesListPage';
import { LossbackBonusPromocodeListPage } from '../pages/LossbackBonus/LossbackBonusPromocodeListPage';
import { LossbackBonusClaimListPage } from '../pages/LossbackBonus/LossbackBonusClaimListPage';
import { WeeklyLossbackBonusListPage } from '../pages/WeeklyLossbackBonus/WeeklyLossbackBonusListPage';
import { WeeklyLossbackBonusTemplatesListPage } from '../pages/WeeklyLossbackBonus/WeeklyLossbackBonusTemplatesListPage';
import { WeeklyLossbackBonusPromocodeListPage } from '../pages/WeeklyLossbackBonus/WeeklyLossbackBonusPromocodeListPage';
import { WeeklyLossbackBonusClaimListPage } from '../pages/WeeklyLossbackBonus/WeeklyLossbackBonusClaimListPage';
import { HappyHoursBonusListPage } from '../pages/HappyHoursBonus/HappyHoursBonusListPage';
import { HappyHoursBonusTemplatesListPage } from '../pages/HappyHoursBonus/HappyHoursBonusTemplatesListPage';
import { HappyHoursBonusPromocodeListPage } from '../pages/HappyHoursBonus/HappyHoursBonusPromocodeListPage';
import { HappyHoursBonusClaimListPage } from '../pages/HappyHoursBonus/HappyHoursBonusClaimListPage';
import { HappyHoursBonusBulkAssignmentPage } from '../pages/HappyHoursBonus/HappyHoursBonusBulkAssignmentPage';
import {
  TrialBonusListPage,
  TrialBonusTemplatesListPage,
  TrialBonusPromocodeListPage,
  TrialBonusClaimListPage,
  TrialBonusBulkAssignmentPage,
} from '../pages/TrialBonus';
import { RewardsPageConfigurationPage } from '../pages/StaticContent/RewardsPageConfigurationPage';
import { NotificationProvider } from '../contexts/NotificationContext';
import {
  CashBonusBulkAssignmentPage,
  CashBonusClaimListPage,
  CashBonusListPage,
  CashBonusPromocodeListPage,
  CashBonusTemplatesListPage,
} from '../pages/CashBonus';

const sideNavigation: NavSection[] = [
  {
    title: 'Main',
    items: [
      {
        to: '',
        icon: <LayoutDashboard size={20} />,
        label: 'Dashboard',
        end: true,
      },
    ],
  },
  {
    title: 'Management',
    items: [
      {
        to: '/customers',
        icon: <Users size={20} />,
        label: 'Customers',
        end: true,
      },
    ],
  },
  {
    title: 'Bonus',
    items: [
      {
        icon: <ApertureIcon size={20} />,
        type: 'dropdown',
        label: 'Freespin',
        children: [
          {
            to: '/bonuses/freespin',
            label: 'List',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/freespin/templates',
            label: 'Templates',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/freespin/promocodes',
            label: 'Promocodes',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/freespin/claims',
            label: 'Claims',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/freespin/bulk-assignments',
            label: 'Bulk assignment',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
        ],
      },
      {
        icon: <ApertureIcon size={20} />,
        type: 'dropdown',
        label: 'Lossback',
        children: [
          {
            to: '/bonuses/lossback',
            label: 'List',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/lossback/templates',
            label: 'Templates',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/lossback/promocodes',
            label: 'Promocodes',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/lossback/claims',
            label: 'Claims',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
        ],
      },
      {
        icon: <ApertureIcon size={20} />,
        type: 'dropdown',
        label: 'Weekly Lossback',
        children: [
          {
            to: '/bonuses/weekly-lossback',
            label: 'List',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/weekly-lossback/templates',
            label: 'Templates',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/weekly-lossback/promocodes',
            label: 'Promocodes',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/weekly-lossback/claims',
            label: 'Claims',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
        ],
      },
      {
        icon: <ApertureIcon size={20} />,
        type: 'dropdown',
        label: 'Cash',
        children: [
          {
            to: '/bonuses/cash',
            label: 'List',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/cash/templates',
            label: 'Templates',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/cash/promocodes',
            label: 'Promocodes',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/cash/claims',
            label: 'Claims',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/cash/bulk-assignments',
            label: 'Bulk assignment',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
        ],
      },
      {
        icon: <ApertureIcon size={20} />,
        type: 'dropdown',
        label: 'Happy Hours',
        children: [
          {
            to: '/bonuses/happy-hours',
            label: 'List',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/happy-hours/templates',
            label: 'Templates',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/happy-hours/promocodes',
            label: 'Promocodes',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/happy-hours/claims',
            label: 'Claims',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
          {
            to: '/bonuses/happy-hours/bulk-assignments',
            label: 'Bulk assignment',
            icon: <ApertureIcon size={16} />,
            end: true,
          },
        ],
      },
      {
        icon: <ApertureIcon size={20} />,
        type: 'dropdown',
        label: 'Trial',
        children: [
          {
            to: '/bonuses/trial',
            label: 'List',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/trial/templates',
            label: 'Templates',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/trial/promocodes',
            label: 'Promocodes',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/trial/claims',
            label: 'Claims',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
          {
            to: '/bonuses/trial/bulk-assignments',
            label: 'Bulk assignment',
            end: true,
            icon: <ApertureIcon size={16} />,
          },
        ],
      },
    ],
  },
  {
    title: 'Static Content',
    items: [
      {
        icon: <FileText size={20} />,
        type: 'dropdown',
        label: 'Content Management',
        children: [
          {
            to: '/static-content/rewards-page',
            label: 'Rewards Page',
            end: true,
            icon: <Gift size={16} />,
          },
        ],
      },
    ],
  },
];

export function App() {
  return (
    <AuthContextProvider>
      {({ session, logout }) => (
        <ApiContextProvider session={session}>
          <NotificationProvider>
            <Routes>
              <Route
                path="/"
                element={
                  <MainLayout
                    appName="Makrobet"
                    sidebarSections={sideNavigation}
                    userMenuActions={[
                      {
                        label: 'Sign Out',
                        icon: <LogOut size={16} />,
                        onClick: logout,
                        variant: 'danger',
                      },
                    ]}
                  >
                    <Outlet />
                  </MainLayout>
                }
              >
                <Route path="" element={<Dashboard />} />

                <Route path="customers">
                  <Route path="" element={<CustomersListPage />} />
                  <Route path=":id" element={<CustomerDetailsPage />} />
                </Route>

                <Route path="bonuses">
                  <Route path="freespin">
                    <Route path="" element={<FreespinBonusListPage />} />
                    <Route
                      path="templates"
                      element={<FreespinBonusTemplatesListPage />}
                    />
                    <Route
                      path="promocodes"
                      element={<FreespinBonusPromocodeListPage />}
                    />
                    <Route
                      path="claims"
                      element={<FreespinBonusClaimListPage />}
                    />
                    <Route
                      path="bulk-assignments"
                      element={<FreespinBonusBulkAssignmentPage />}
                    />
                  </Route>

                  <Route path="lossback">
                    <Route path="" element={<LossbackBonusListPage />} />
                    <Route
                      path="templates"
                      element={<LossbackBonusTemplatesListPage />}
                    />
                    <Route
                      path="promocodes"
                      element={<LossbackBonusPromocodeListPage />}
                    />
                    <Route
                      path="claims"
                      element={<LossbackBonusClaimListPage />}
                    />
                  </Route>

                  <Route path="weekly-lossback">
                    <Route path="" element={<WeeklyLossbackBonusListPage />} />
                    <Route
                      path="templates"
                      element={<WeeklyLossbackBonusTemplatesListPage />}
                    />
                    <Route
                      path="promocodes"
                      element={<WeeklyLossbackBonusPromocodeListPage />}
                    />
                    <Route
                      path="claims"
                      element={<WeeklyLossbackBonusClaimListPage />}
                    />
                  </Route>

                  <Route path="cash">
                    <Route path="" element={<CashBonusListPage />} />
                    <Route
                      path="templates"
                      element={<CashBonusTemplatesListPage />}
                    />
                    <Route
                      path="promocodes"
                      element={<CashBonusPromocodeListPage />}
                    />
                    <Route path="claims" element={<CashBonusClaimListPage />} />
                    <Route
                      path="bulk-assignments"
                      element={<CashBonusBulkAssignmentPage />}
                    />
                  </Route>

                  <Route path="trial">
                    <Route path="" element={<TrialBonusListPage />} />
                    <Route
                      path="templates"
                      element={<TrialBonusTemplatesListPage />}
                    />
                    <Route
                      path="promocodes"
                      element={<TrialBonusPromocodeListPage />}
                    />
                    <Route
                      path="claims"
                      element={<TrialBonusClaimListPage />}
                    />
                    <Route
                      path="bulk-assignments"
                      element={<TrialBonusBulkAssignmentPage />}
                    />
                  </Route>

                  <Route path="happy-hours">
                    <Route path="" element={<HappyHoursBonusListPage />} />
                    <Route
                      path="templates"
                      element={<HappyHoursBonusTemplatesListPage />}
                    />
                    <Route
                      path="promocodes"
                      element={<HappyHoursBonusPromocodeListPage />}
                    />
                    <Route
                      path="claims"
                      element={<HappyHoursBonusClaimListPage />}
                    />
                    <Route
                      path="bulk-assignments"
                      element={<HappyHoursBonusBulkAssignmentPage />}
                    />
                  </Route>
                </Route>

                <Route path="static-content">
                  <Route
                    path="rewards-page"
                    element={<RewardsPageConfigurationPage />}
                  />
                </Route>

                <Route path="static-content">
                  <Route
                    path="rewards-page"
                    element={<RewardsPageConfigurationPage />}
                  />
                </Route>

                <Route path="*" element={<Navigate to="/" />} />
              </Route>
            </Routes>
          </NotificationProvider>
        </ApiContextProvider>
      )}
    </AuthContextProvider>
  );
}

export default App;
