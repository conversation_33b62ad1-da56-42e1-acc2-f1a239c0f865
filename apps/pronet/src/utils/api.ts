import httpClient, { HttpResponse } from './httpClient';

// API Response interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Note: Authentication is handled by the root panel via cookies
// This panel does not handle its own authentication

// Customer balances API function
export const getCustomerBalances = async (): Promise<ApiResponse<any>> => {
  try {
    // Set endDate to 24 hours ago
    const endDate = new Date();
    endDate.setHours(endDate.getHours() - 24);
    const endDateISO = endDate.toISOString();

    const response: HttpResponse<ApiResponse> = await httpClient.get(
      `/api/pg-dagur/v1/customers/balances?endDate=${endDateISO}`
    );

    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to fetch customer balances'
      };
    }
  } catch (error) {
    console.error('Customer balances error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

// Dashboard API functions
export const getDashboardStats = async (): Promise<ApiResponse<any>> => {
  try {
    const response: HttpResponse<ApiResponse> = await httpClient.get('/api/internal/dashboard/stats');
    
    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to fetch dashboard stats'
      };
    }
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

// User management API functions
export const getCurrentUser = async (): Promise<ApiResponse<any>> => {
  try {
    const response: HttpResponse<ApiResponse> = await httpClient.get('/api/internal/user/profile');
    
    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to fetch user profile'
      };
    }
  } catch (error) {
    console.error('Get user profile error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

export const updateUserProfile = async (profileData: any): Promise<ApiResponse<any>> => {
  try {
    const response: HttpResponse<ApiResponse> = await httpClient.put('/api/internal/user/profile', profileData);
    
    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to update user profile'
      };
    }
  } catch (error) {
    console.error('Update user profile error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

export const changePassword = async (passwordData: {
  currentPassword: string;
  newPassword: string;
}): Promise<ApiResponse<any>> => {
  try {
    const response: HttpResponse<ApiResponse> = await httpClient.post('/api/internal/user/change-password', passwordData);
    
    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to change password'
      };
    }
  } catch (error) {
    console.error('Change password error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

// Settings API functions
export const getSettings = async (): Promise<ApiResponse<any>> => {
  try {
    const response: HttpResponse<ApiResponse> = await httpClient.get('/api/internal/settings');
    
    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to fetch settings'
      };
    }
  } catch (error) {
    console.error('Get settings error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

export const updateSettings = async (settingsData: any): Promise<ApiResponse<any>> => {
  try {
    const response: HttpResponse<ApiResponse> = await httpClient.put('/api/internal/settings', settingsData);
    
    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to update settings'
      };
    }
  } catch (error) {
    console.error('Update settings error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

// Freespin Bonuses API functions
export const getFreespinProviders = async (): Promise<ApiResponse<any>> => {
  try {
    const response: HttpResponse<ApiResponse> = await httpClient.get('/api/pg-ct/v1/internal/freespin-bonuses/providers');

    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to fetch freespin providers'
      };
    }
  } catch (error) {
    console.error('Freespin providers error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

export const getProviderGames = async (providerId: number, params: { page?: number; limit?: number; name?: string } = {}): Promise<ApiResponse<any>> => {
  try {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.name) queryParams.append('name', params.name);

    const queryString = queryParams.toString();
    const url = `/api/pg-ct/v1/internal/freespin-bonuses/providers/${providerId}/games${queryString ? `?${queryString}` : ''}`;

    const response: HttpResponse<ApiResponse> = await httpClient.get(url);

    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to fetch provider games'
      };
    }
  } catch (error) {
    console.error('Provider games error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

export const getBetAmounts = async (params: { vendorId: number; gameId: number[]; currencyId: number }): Promise<ApiResponse<any>> => {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append('vendorId', params.vendorId.toString());
    params.gameId.forEach(id => queryParams.append('gameId', id.toString()));
    queryParams.append('currencyId', params.currencyId.toString());

    const url = `/api/pg-ct/v1/internal/freespin-bonuses/providers/bet-amounts?${queryParams.toString()}`;

    const response: HttpResponse<ApiResponse> = await httpClient.get(url);

    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to fetch bet amounts'
      };
    }
  } catch (error) {
    console.error('Bet amounts error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

export const createFreespinBonus = async (bonusData: {
  providerId: number;
  customerId: number;
  nOfFreespins: number;
  betAmount: number;
  maxWin: number;
  gameIds: number[];
  expiresAt: string;
}): Promise<ApiResponse<any>> => {
  try {
    const response: HttpResponse<ApiResponse> = await httpClient.post('/api/pg-ct/v1/internal/freespin-bonuses', bonusData);

    if (response.ok && response.data) {
      return response.data;
    } else {
      return {
        success: false,
        error: response.data?.error || response.data?.message || 'Failed to create freespin bonus'
      };
    }
  } catch (error) {
    console.error('Create freespin bonus error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

// Export the http client for direct use if needed
export { httpClient };
