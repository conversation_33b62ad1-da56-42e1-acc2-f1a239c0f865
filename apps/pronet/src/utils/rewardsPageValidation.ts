import type { SiteClaimableBonus } from '@panels/api';

export interface ValidationResult {
  isValid: boolean;
  level: 'success' | 'warning' | 'error';
  message: string;
  details?: string;
}

export interface SlotValidation extends ValidationResult {
  slotName: string;
  slotId: number;
}

export interface RewardsPageValidation {
  overall: ValidationResult;
  slots: SlotValidation[];
  summary: {
    totalSlots: number;
    activeSlots: number;
    visibleSlots: number;
    claimableSlots: number;
    issueCount: number;
  };
}

/**
 * Validates a single bonus slot configuration
 */
export const validateSlot = (
  slot: SiteClaimableBonus,
  claimableBonuses: SiteClaimableBonus[]
): SlotValidation => {
  const isClaimable = claimableBonuses.some(bonus => bonus.id === slot.id);

  // Check if slot has no bonus assigned
  if (!slot.bonusId || !slot.bonus) {
    return {
      slotName: slot.slotName,
      slotId: slot.id,
      isValid: false,
      level: 'error',
      message: 'No bonus assigned',
      details: 'This slot needs a bonus to be assigned before it can be activated.',
    };
  }

  // Check if slot is active but bonus is inactive
  if (slot.isActive && !slot.bonus.isActive) {
    return {
      slotName: slot.slotName,
      slotId: slot.id,
      isValid: false,
      level: 'error',
      message: 'Assigned bonus is inactive',
      details: 'The bonus assigned to this slot is currently inactive and won\'t be available to users.',
    };
  }

  // Check if bonus has expired
  if (slot.bonus.expiresAt && new Date(slot.bonus.expiresAt) < new Date()) {
    return {
      slotName: slot.slotName,
      slotId: slot.id,
      isValid: false,
      level: 'error',
      message: 'Assigned bonus has expired',
      details: `The bonus expired on ${new Date(slot.bonus.expiresAt).toLocaleDateString()}.`,
    };
  }

  // Check if slot is active but not claimable
  if (slot.isActive && !isClaimable) {
    return {
      slotName: slot.slotName,
      slotId: slot.id,
      isValid: true,
      level: 'warning',
      message: 'Not currently claimable',
      details: 'The slot is active but the bonus is not currently claimable by users.',
    };
  }

  // Check if slot is inactive
  if (!slot.isActive) {
    return {
      slotName: slot.slotName,
      slotId: slot.id,
      isValid: true,
      level: 'warning',
      message: 'Slot is inactive',
      details: 'This slot is configured but not active. Users won\'t see this bonus.',
    };
  }

  // All good
  return {
    slotName: slot.slotName,
    slotId: slot.id,
    isValid: true,
    level: 'success',
    message: 'Ready for users',
    details: 'This slot is properly configured and available to users.',
  };
};

/**
 * Validates the entire rewards page configuration
 */
export const validateRewardsPage = (
  slots: SiteClaimableBonus[],
  claimableBonuses: SiteClaimableBonus[]
): RewardsPageValidation => {
  const slotValidations = slots.map(slot => validateSlot(slot, claimableBonuses));
  
  const totalSlots = slots.length;
  const activeSlots = slots.filter(slot => slot.isActive).length;
  const visibleSlots = slots.filter(slot => slot.isActive && slot.bonusId && slot.bonus).length;
  const claimableSlots = slots.filter(slot => 
    slot.isActive && 
    slot.bonusId && 
    slot.bonus && 
    claimableBonuses.some(bonus => bonus.id === slot.id)
  ).length;
  const issueCount = slotValidations.filter(v => v.level === 'error').length;

  // Determine overall validation status
  let overall: ValidationResult;
  
  if (issueCount > 0) {
    overall = {
      isValid: false,
      level: 'error',
      message: `${issueCount} configuration issue${issueCount > 1 ? 's' : ''} found`,
      details: 'Some slots have configuration issues that prevent them from working properly.',
    };
  } else if (visibleSlots === 0) {
    overall = {
      isValid: false,
      level: 'warning',
      message: 'No bonuses will be visible to users',
      details: 'Configure and activate at least one slot to show rewards to users.',
    };
  } else if (claimableSlots === 0) {
    overall = {
      isValid: true,
      level: 'warning',
      message: 'No bonuses are currently claimable',
      details: 'While slots are configured, no bonuses are currently available for users to claim.',
    };
  } else {
    overall = {
      isValid: true,
      level: 'success',
      message: `${claimableSlots} bonus${claimableSlots > 1 ? 'es' : ''} ready for users`,
      details: 'The rewards page is properly configured and ready for users.',
    };
  }

  return {
    overall,
    slots: slotValidations,
    summary: {
      totalSlots,
      activeSlots,
      visibleSlots,
      claimableSlots,
      issueCount,
    },
  };
};

/**
 * Gets validation recommendations for improving the configuration
 */
export const getValidationRecommendations = (
  validation: RewardsPageValidation
): string[] => {
  const recommendations: string[] = [];

  if (validation.summary.totalSlots === 0) {
    recommendations.push('Create bonus slots to start offering rewards to users.');
  }

  if (validation.summary.activeSlots === 0 && validation.summary.totalSlots > 0) {
    recommendations.push('Activate at least one slot to make bonuses visible to users.');
  }

  if (validation.summary.visibleSlots === 0 && validation.summary.activeSlots > 0) {
    recommendations.push('Assign bonuses to your active slots.');
  }

  if (validation.summary.claimableSlots === 0 && validation.summary.visibleSlots > 0) {
    recommendations.push('Ensure assigned bonuses are active and not expired.');
  }

  if (validation.summary.issueCount > 0) {
    recommendations.push('Fix configuration issues to ensure all slots work properly.');
  }

  // Performance recommendations
  if (validation.summary.visibleSlots > 6) {
    recommendations.push('Consider limiting visible slots to 6 or fewer for better user experience.');
  }

  return recommendations;
};
