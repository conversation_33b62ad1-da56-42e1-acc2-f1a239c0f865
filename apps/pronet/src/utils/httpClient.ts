// Note: This panel relies on cookies set by the root panel for authentication
// No token management is needed here

interface HttpClientOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: string;
  skipAuth?: boolean;
}

interface HttpResponse<T = any> {
  ok: boolean;
  status: number;
  data: T;
  headers: Headers;
}

class HttpClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000';
  }

  // Simplified HTTP client - authentication is handled via cookies

  private async makeRequest(url: string, options: HttpClientOptions = {}): Promise<HttpResponse> {
    const fetchOptions: RequestInit = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      // credentials: 'include', // Include cookies for authentication
    };

    if (options.body) {
      fetchOptions.body = options.body;
    }
console.log(fetchOptions);
    const response = await fetch(url, fetchOptions);
    let data: any;

    try {
      data = await response.json();
    } catch (error) {
      // Handle non-JSON responses
      data = await response.text();
    }

    return {
      ok: response.ok,
      status: response.status,
      data,
      headers: response.headers
    };
  }

  async request<T = any>(endpoint: string, options: HttpClientOptions = {}): Promise<HttpResponse<T>> {
    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;

    try {
      return await this.makeRequest(url, options);
    } catch (error) {
      console.error('HTTP request failed:', error);
      throw error;
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, options: Omit<HttpClientOptions, 'method'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T = any>(endpoint: string, body?: any, options: Omit<HttpClientOptions, 'method' | 'body'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: body ? JSON.stringify(body) : undefined
    });
  }

  async put<T = any>(endpoint: string, body?: any, options: Omit<HttpClientOptions, 'method' | 'body'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: body ? JSON.stringify(body) : undefined
    });
  }

  async delete<T = any>(endpoint: string, options: Omit<HttpClientOptions, 'method'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }
}

// Create and export a singleton instance
export const httpClient = new HttpClient();
export default httpClient;

// Export types for use in other modules
export type { HttpResponse, HttpClientOptions };
