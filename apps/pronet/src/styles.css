@tailwind base;
@tailwind components;
@tailwind utilities;
@source "../../../libs/ui/src/styles.css";
/* You can add global styles to this file, and also import other style files */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  body {
    @apply bg-dark-800 text-gray-100 font-sans;
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-dark-700;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-dark-600 rounded-full hover:bg-primary-500/50 transition-colors;
  }
}

@layer components {
  .sidebar-item {
    @apply flex items-center gap-3 px-4 py-3 text-gray-300 hover:bg-dark-700 hover:text-primary-500 rounded-md transition-all duration-200 cursor-pointer;
  }

  .sidebar-item.active {
    @apply bg-dark-700 text-primary-500;
  }

  .dropdown-content {
    @apply pl-4 mt-1 space-y-1;
  }

  /* Nested dropdown styling */
  .dropdown-content .dropdown-content {
    @apply pl-6 mt-1 space-y-1;
  }

  /* Smaller icons and text for nested items */
  .dropdown-content .dropdown-content .sidebar-item {
    @apply text-sm py-1.5 px-2;
  }

  /* Ensure nested items are visible and properly styled */
  .dropdown-content .sidebar-item {
    @apply py-2 px-3 text-sm;
  }

  .admin-card {
    @apply bg-dark-700 rounded-lg p-5 border border-dark-600 shadow-md;
  }

  .stats-card {
    @apply admin-card relative overflow-hidden;
  }

  .stats-card::after {
    @apply content-[''] absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-primary-500/10 to-primary-600/10 rounded-bl-full;
  }

  /* Unified Table Styles */
  .data-table {
    @apply w-full text-sm text-left;
  }

  .data-table th {
    @apply px-6 py-3 bg-dark-800 font-medium text-gray-400 uppercase tracking-wider text-xs;
  }

  .data-table td {
    @apply px-6 py-4 border-t border-dark-600;
  }

  .data-table tr:hover {
    @apply bg-dark-700/50 transition-colors;
  }

  /* Legacy table support - maintains old styling for gradual migration */
  .data-table-legacy th {
    @apply px-4 py-3 bg-dark-700 font-medium text-gray-300;
  }

  .data-table-legacy td {
    @apply px-4 py-3 border-t border-dark-700;
  }

  /* Enhanced table container */
  .table-container {
    @apply bg-dark-700 rounded-lg border border-dark-600 overflow-hidden;
  }

  .table-header {
    @apply px-6 py-4 border-b border-dark-600 bg-dark-800;
  }

  .table-content {
    @apply overflow-x-auto;
  }

  /* Sortable column styles */
  .sortable-column {
    @apply cursor-pointer hover:bg-dark-600 transition-colors;
  }

  /* Action column styles */
  .actions-column {
    @apply sticky right-0 bg-dark-800 z-10;
  }

  .actions-column td {
    @apply sticky right-0 bg-dark-700 z-10;
  }

  .btn {
    @apply px-4 py-2 rounded-md transition-all duration-200 font-medium;
  }

  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-dark-800;
  }

  .btn-secondary {
    @apply bg-dark-600 hover:bg-dark-500 text-gray-300;
  }

  .btn-outline {
    @apply border border-gray-600 hover:border-primary-500 hover:text-primary-500;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white;
  }
}
