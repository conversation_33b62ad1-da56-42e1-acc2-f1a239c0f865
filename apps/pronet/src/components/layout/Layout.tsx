import React, { ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();

  const navigation = [
    { name: 'Dashboard', href: '/pronet', current: location.pathname === '/' },
    {
      name: 'Settings',
      href: '/pronet/settings',
      current: location.pathname === '/settings',
    },
    {
      name: 'Bonuses',
      href: '/pronet/bonuses',
      current: location.pathname === '/bonuses',
    },
  ];

  const handleLogout = () => {
    // Redirect back to root panel
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen bg-dark-900">
      {/* Navigation */}
      <nav className="bg-dark-800 shadow-sm border-b border-dark-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold text-gray-300">
                  ProNet Admin
                </h1>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`${
                      item.current
                        ? 'border-primary-500 text-gray-300'
                        : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                    } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-sm text-gray-400 mr-4">
                  Welcome, User
                </span>
                <button
                  onClick={handleLogout}
                  className="bg-primary-500 hover:bg-primary-600 text-dark-800 px-4 py-2 rounded-md text-sm font-medium"
                >
                  Back to Root
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">{children}</div>
      </main>
    </div>
  );
};

export default Layout;
