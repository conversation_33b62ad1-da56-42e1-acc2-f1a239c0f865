import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Off,
  Plus,
  Edit3,
  <PERSON>ader2,
  CheckCircle,
  XCircle,
  Trash2
} from 'lucide-react';
import Button from './ui/Button';
import type { SiteClaimableBonus } from '@panels/api';

interface BonusSlotCardProps {
  slot: SiteClaimableBonus;
  isPreviewMode?: boolean;
  isLoading?: boolean;
  onSelect: () => void;
  onToggle: (isActive: boolean) => void;
  onDelete?: () => void;
}

export const BonusSlotCard: React.FC<BonusSlotCardProps> = ({
  slot,
  isPreviewMode = false,
  isLoading = false,
  onSelect,
  onToggle,
  onDelete,
}) => {
  const hasBonus = slot.bonusId && slot.bonus;
  const isActive = slot.isActive;
  const isVisible = isActive && hasBonus;

  // Format slot name for display
  const formatSlotName = (slotName: string) => {
    return slotName
      .split(/[-_]/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get bonus type display
  const getBonusTypeDisplay = (type: string) => {
    switch (type.toLowerCase()) {
      case 'freespin':
        return 'Free Spins';
      case 'freebet':
        return 'Free Bet';
      case 'cash':
        return 'Cash Bonus';
      case 'lossback':
        return 'Loss Back';
      default:
        return type;
    }
  };

  const cardClasses = `
    relative bg-dark-700 rounded-lg border transition-all duration-200
    ${isPreviewMode 
      ? (isVisible ? 'border-green-500/50 bg-green-500/5' : 'border-gray-600 opacity-50')
      : (isActive 
          ? (hasBonus ? 'border-green-500/50 hover:border-green-500/70' : 'border-yellow-500/50 hover:border-yellow-500/70')
          : 'border-gray-600 hover:border-gray-500'
        )
    }
  `;

  return (
    <div className={cardClasses}>
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-dark-800/80 rounded-lg flex items-center justify-center z-10">
          <Loader2 className="h-6 w-6 animate-spin text-primary-500" />
        </div>
      )}

      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${
              isVisible 
                ? 'bg-green-500/20 text-green-400' 
                : hasBonus 
                  ? 'bg-yellow-500/20 text-yellow-400'
                  : 'bg-gray-500/20 text-gray-400'
            }`}>
              <Gift className="h-5 w-5" />
            </div>
            <div>
              <h3 className="font-medium text-gray-300">
                {formatSlotName(slot.slotName)}
              </h3>
              <p className="text-sm text-gray-500">Position {slot.position}</p>
            </div>
          </div>

          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            {isVisible ? (
              <CheckCircle className="h-5 w-5 text-green-400" />
            ) : isActive ? (
              <Eye className="h-5 w-5 text-yellow-400" />
            ) : (
              <EyeOff className="h-5 w-5 text-gray-400" />
            )}
          </div>
        </div>

        {/* Bonus Information */}
        <div className="mb-4">
          {hasBonus ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Assigned Bonus:</span>
                <span className={`text-sm px-2 py-1 rounded ${
                  slot.bonus?.isActive 
                    ? 'bg-green-500/20 text-green-400' 
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {slot.bonus?.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div className="bg-dark-600 rounded-lg p-3">
                <p className="font-medium text-gray-300 mb-1">
                  {slot.bonus?.name}
                </p>
                <p className="text-sm text-gray-400">
                  Type: {getBonusTypeDisplay(slot.bonus?.type || '')}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  ID: {slot.bonusId}
                </p>
              </div>
            </div>
          ) : (
            <div className="bg-dark-600 rounded-lg p-4 text-center">
              <Plus className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-400 text-sm">No bonus assigned</p>
              <p className="text-gray-500 text-xs">Click to assign a bonus</p>
            </div>
          )}
        </div>

        {/* Status Information */}
        <div className="mb-4 space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-400">Slot Status:</span>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              isActive 
                ? 'bg-green-500/20 text-green-400' 
                : 'bg-gray-500/20 text-gray-400'
            }`}>
              {isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-400">Visibility:</span>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              isVisible 
                ? 'bg-green-500/20 text-green-400' 
                : 'bg-red-500/20 text-red-400'
            }`}>
              {isVisible ? 'Visible to Users' : 'Hidden from Users'}
            </span>
          </div>
        </div>

        {/* Actions */}
        {!isPreviewMode && (
          <div className="flex gap-2">
            <Button
              onClick={onSelect}
              variant="secondary"
              size="sm"
              className="flex-1 flex items-center justify-center gap-2"
              title={hasBonus ? "Select a different bonus for this slot" : "Assign a bonus to this slot"}
            >
              {hasBonus ? (
                <>
                  <Edit3 className="h-4 w-4" />
                  Change Bonus
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  Assign Bonus
                </>
              )}
            </Button>
            <Button
              onClick={() => onToggle(!isActive)}
              variant={isActive ? 'danger' : 'primary'}
              size="sm"
              className="flex items-center justify-center gap-2"
              title={isActive
                ? "Disable this slot to hide it from users"
                : "Enable this slot to make it visible to users"
              }
            >
              {isActive ? (
                <>
                  <EyeOff className="h-4 w-4" />
                  Disable
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4" />
                  Enable
                </>
              )}
            </Button>
            {onDelete && slot.slotName.startsWith('happy_') && (
              <Button
                onClick={onDelete}
                variant="danger"
                size="sm"
                className="flex items-center justify-center gap-2"
                title="Delete this happy bonus slot"
              >
                <Trash2 className="h-4 w-4" />
                Delete
              </Button>
            )}
          </div>
        )}

        {/* Preview Mode Info */}
        {isPreviewMode && (
          <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <p className="text-blue-400 text-sm font-medium mb-1">Preview Mode</p>
            <p className="text-blue-300 text-xs">
              {isVisible 
                ? 'This slot will be visible to users on the rewards page'
                : 'This slot is hidden from users (inactive or no bonus assigned)'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
