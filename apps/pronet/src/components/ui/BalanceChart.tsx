import React from 'react';

interface BalanceChartProps {
  title: string;
  balances: {
    TRY: number;
    GBP: number;
    USD: number;
    EUR: number;
    KZT: number;
    AZN: number;
  };
  icon: React.ReactNode;
  color: string;
}

const formatCurrency = (amount: number, currency: string): string => {
  if (amount === 0) return '0';

  // Format large numbers with K, M, B suffixes
  if (amount >= 1000000000) {
    return `${(amount / 1000000000).toFixed(1)}B ${currency}`;
  } else if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M ${currency}`;
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}K ${currency}`;
  } else {
    return `${amount.toFixed(2)} ${currency}`;
  }
};

const getTotalBalance = (balances: BalanceChartProps['balances']): number => {
  return Object.values(balances).reduce((sum, amount) => sum + amount, 0);
};

const getAllCurrencies = (
  balances: BalanceChartProps['balances']
): Array<{ currency: string; amount: number; percentage: number }> => {
  const total = getTotalBalance(balances);

  return Object.entries(balances)
    .map(([currency, amount]) => ({
      currency,
      amount,
      percentage: total > 0 ? (amount / total) * 100 : 0,
    }))
    .sort((a, b) => b.amount - a.amount); // Show all currencies, sorted by amount
};

export const BalanceChart: React.FC<BalanceChartProps> = ({
  title,
  balances,
  icon,
  color,
}) => {
  const totalBalance = getTotalBalance(balances);
  const allCurrencies = getAllCurrencies(balances);

  return (
    <div className="bg-dark-800 overflow-hidden shadow rounded-lg border border-dark-600">
      <div className="p-5">
        {/* Header */}
        <div className="flex items-center mb-4">
          <div
            className={`flex-shrink-0 w-8 h-8 ${color} rounded-md flex items-center justify-center`}
          >
            {icon}
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-gray-300">{title}</h3>
          </div>
        </div>

        {/* Total Balance */}
        <div className="mb-4">
          <div className="text-2xl font-bold text-gray-300">
            {totalBalance === 0 ? '0' : formatCurrency(totalBalance, 'Total')}
          </div>
        </div>

        {/* Currency Breakdown */}
        <div className="space-y-2">
          {allCurrencies.map(({ currency, amount, percentage }) => (
            <div key={currency} className="flex items-center justify-between">
              <div className="flex items-center">
                <div
                  className={`w-2 h-2 rounded-full mr-2`}
                  style={{ backgroundColor: getCurrencyColor(currency) }}
                />
                <span className="text-xs text-gray-400">{currency}</span>
              </div>
              <div
                className={`text-xs ${
                  amount === 0 ? 'text-gray-500' : 'text-gray-300'
                }`}
              >
                {amount === 0 ? '0' : formatCurrency(amount, currency)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Helper function to get consistent colors for currencies
const getCurrencyColor = (currency: string): string => {
  const colors: Record<string, string> = {
    TRY: '#ef4444', // red-500
    USD: '#22c55e', // green-500
    EUR: '#3b82f6', // blue-500
    GBP: '#a855f7', // purple-500
    KZT: '#f59e0b', // amber-500
    AZN: '#06b6d4', // cyan-500
  };
  return colors[currency] || '#6b7280'; // gray-500 as fallback
};
