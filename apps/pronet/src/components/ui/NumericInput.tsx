import React, { forwardRef, InputHTMLAttributes, useState, useEffect } from 'react';

interface NumericInputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type' | 'value' | 'onChange'> {
  label?: string;
  error?: string;
  helperText?: string;
  value: number | string;
  onChange: (value: number) => void;
  allowDecimals?: boolean;
  min?: number;
  max?: number;
  step?: number;
}

const NumericInput = forwardRef<HTMLInputElement, NumericInputProps>(
  (
    {
      className = '',
      label,
      error,
      helperText,
      value,
      onChange,
      allowDecimals = true,
      min,
      max,
      step,
      ...props
    },
    ref
  ) => {
    const [displayValue, setDisplayValue] = useState('');

    // Initialize and sync display value with prop value
    useEffect(() => {
      if (value === 0 || value === '') {
        setDisplayValue('');
      } else {
        setDisplayValue(String(value));
      }
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      
      // Allow empty string
      if (inputValue === '') {
        setDisplayValue('');
        onChange(0);
        return;
      }

      // Create regex pattern based on whether decimals are allowed
      const pattern = allowDecimals 
        ? /^-?\d*\.?\d*$/ // Allow decimals
        : /^-?\d*$/;      // Only integers

      // Check if the input matches the pattern
      if (pattern.test(inputValue)) {
        setDisplayValue(inputValue);
        
        // Only call onChange if we have a complete valid number
        // This allows typing "12." without immediately converting to 12
        const numValue = parseFloat(inputValue);
        if (!isNaN(numValue)) {
          // Apply min/max constraints if specified
          let constrainedValue = numValue;
          if (min !== undefined && constrainedValue < min) {
            constrainedValue = min;
          }
          if (max !== undefined && constrainedValue > max) {
            constrainedValue = max;
          }
          
          onChange(constrainedValue);
        }
      }
      // If input doesn't match pattern, don't update anything
    };

    const handleBlur = () => {
      // On blur, clean up the display value
      if (displayValue === '' || displayValue === '.') {
        setDisplayValue('');
        onChange(0);
      } else {
        const numValue = parseFloat(displayValue);
        if (!isNaN(numValue)) {
          // Apply constraints and format the display value
          let constrainedValue = numValue;
          if (min !== undefined && constrainedValue < min) {
            constrainedValue = min;
          }
          if (max !== undefined && constrainedValue > max) {
            constrainedValue = max;
          }
          
          setDisplayValue(String(constrainedValue));
          onChange(constrainedValue);
        }
      }
    };

    return (
      <div className="space-y-2">
        {label && (
          <label className="block text-sm font-medium text-gray-300">
            {label}
          </label>
        )}
        <input
          type="text"
          inputMode="decimal"
          value={displayValue}
          onChange={handleChange}
          onBlur={handleBlur}
          className={`w-full px-3 py-2 border border-dark-600 bg-dark-700 text-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 placeholder-gray-500 ${
            error
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
              : ''
          } ${className}`}
          ref={ref}
          {...props}
        />
        {error && <p className="text-sm text-red-400">{error}</p>}
        {helperText && !error && (
          <p className="text-sm text-gray-400">{helperText}</p>
        )}
      </div>
    );
  }
);

NumericInput.displayName = 'NumericInput';

export default NumericInput;
