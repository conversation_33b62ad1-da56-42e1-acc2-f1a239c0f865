import React, { useState, useEffect, useMemo } from 'react';
import { Search, X, Gift, Calendar, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import Modal from './ui/Modal';
import Button from './ui/Button';
import { useApi } from '../features/api/useApi';
import { BonusSearchRequest, type Bonus } from '@panels/api';

interface BonusSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (bonusId: number | null) => void;
  currentBonusId: number | null;
  isLoading?: boolean;
}

export const BonusSelectionModal: React.FC<BonusSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  currentBonusId,
  isLoading = false,
}) => {
  const { pronet } = useApi();
  
  const [bonuses, setBonuses] = useState<Bonus[]>([]);
  const [isFetching, setIsFetching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  // Load bonuses when modal opens
  useEffect(() => {
    if (isOpen) {
      loadBonuses();
    }
  }, [isOpen]);

  const loadBonuses = async () => {
    setIsFetching(true);
    setError(null);

    try {
      const request = new BonusSearchRequest({
        limit: 100, // Get a reasonable number of bonuses
      });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setBonuses(response.data.items);
      } else {
        setError(response.error || 'Failed to load bonuses');
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : 'An unexpected error occurred'
      );
    } finally {
      setIsFetching(false);
    }
  };

  // Filter bonuses based on search and filters
  const filteredBonuses = useMemo(() => {
    return bonuses.filter((bonus) => {
      // Search filter
      const matchesSearch = searchTerm === '' || 
        bonus.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        bonus.id.toString().includes(searchTerm);

      // Type filter
      const matchesType = selectedType === 'all' || bonus.type === selectedType;

      // Status filter
      const matchesStatus = selectedStatus === 'all' || 
        (selectedStatus === 'active' && bonus.isActive) ||
        (selectedStatus === 'inactive' && !bonus.isActive);

      return matchesSearch && matchesType && matchesStatus;
    });
  }, [bonuses, searchTerm, selectedType, selectedStatus]);

  // Get unique bonus types for filter
  const bonusTypes = useMemo(() => {
    const types = [...new Set(bonuses.map(bonus => bonus.type))];
    return types.sort();
  }, [bonuses]);

  const handleSelect = (bonusId: number) => {
    onSelect(bonusId);
  };

  const handleRemove = () => {
    onSelect(null);
  };

  const formatBonusType = (type: string) => {
    switch (type.toLowerCase()) {
      case 'freespin':
        return 'Free Spins';
      case 'freebet':
        return 'Free Bet';
      case 'cash':
        return 'Cash Bonus';
      case 'lossback':
        return 'Loss Back';
      default:
        return type;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Select Bonus"
      size="lg"
    >
      <div className="space-y-4">
        {/* Search and Filters */}
        <div className="space-y-3">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search bonuses by name or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-dark-600 border border-dark-500 rounded-lg text-gray-300 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          {/* Filters */}
          <div className="flex gap-3">
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 bg-dark-600 border border-dark-500 rounded-lg text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Types</option>
              {bonusTypes.map((type) => (
                <option key={type} value={type}>
                  {formatBonusType(type)}
                </option>
              ))}
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 bg-dark-600 border border-dark-500 rounded-lg text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>

        {/* Current Selection */}
        {currentBonusId && (
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300 font-medium">Currently Assigned</p>
                <p className="text-blue-400 text-sm">
                  Bonus ID: {currentBonusId}
                </p>
                <p className="text-blue-300 text-xs mt-1">
                  To remove this assignment, deactivate the slot instead
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isFetching && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary-500 mr-2" />
            <span className="text-gray-400">Loading bonuses...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
            <p className="text-red-400">{error}</p>
            <Button
              onClick={loadBonuses}
              variant="secondary"
              size="sm"
              className="mt-2"
            >
              Retry
            </Button>
          </div>
        )}

        {/* Bonus List */}
        {!isFetching && !error && (
          <div className="max-h-96 overflow-y-auto space-y-2">
            {filteredBonuses.length === 0 ? (
              <div className="text-center py-8">
                <Gift className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-400">No bonuses found</p>
                <p className="text-gray-500 text-sm">Try adjusting your search or filters</p>
              </div>
            ) : (
              filteredBonuses.map((bonus) => (
                <div
                  key={bonus.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                    bonus.id === currentBonusId
                      ? 'border-blue-500 bg-blue-500/10'
                      : 'border-dark-600 bg-dark-700 hover:border-primary-500/50 hover:bg-dark-600'
                  }`}
                  onClick={() => handleSelect(bonus.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium text-gray-300">{bonus.name}</h3>
                        {bonus.isActive ? (
                          <CheckCircle className="h-4 w-4 text-green-400" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-400" />
                        )}
                      </div>
                      
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-4">
                          <span className="text-gray-400">ID:</span>
                          <span className="text-gray-300">{bonus.id}</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="text-gray-400">Type:</span>
                          <span className="text-gray-300">{formatBonusType(bonus.type)}</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="text-gray-400">Status:</span>
                          <span className={`px-2 py-1 rounded text-xs ${
                            bonus.isActive 
                              ? 'bg-green-500/20 text-green-400' 
                              : 'bg-red-500/20 text-red-400'
                          }`}>
                            {bonus.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        {bonus.expiresAt && (
                          <div className="flex items-center gap-4">
                            <span className="text-gray-400">Expires:</span>
                            <span className="text-gray-300">{formatDate(bonus.expiresAt.toISOString())}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {bonus.id === currentBonusId && (
                      <div className="ml-4">
                        <span className="text-blue-400 text-sm font-medium">Current</span>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
          <Button
            onClick={onClose}
            variant="secondary"
            disabled={isLoading}
          >
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
};
