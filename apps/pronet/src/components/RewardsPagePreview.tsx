import React from 'react';
import { Gift, Star, Clock, CheckCircle, AlertTriangle, Coins } from 'lucide-react';
import type { SiteClaimableBonus } from '@panels/api';

interface RewardsPagePreviewProps {
  slots: SiteClaimableBonus[];
  claimableBonuses: SiteClaimableBonus[];
  className?: string;
}

export const RewardsPagePreview: React.FC<RewardsPagePreviewProps> = ({
  slots,
  claimableBonuses,
  className = '',
}) => {
  // Filter to only show active slots with assigned bonuses
  const activeSlots = slots
    .filter(slot => slot.isActive && slot.bonusId && slot.bonus);

  // Separate slots by type based on naming convention
  const timeSlots = activeSlots
    .filter(slot => slot.slotName.startsWith('time_'))
    .sort((a, b) => a.position - b.position);

  const happySlots = activeSlots
    .filter(slot => slot.slotName.startsWith('happy_'))
    .sort((a, b) => a.position - b.position);

  const welcomeSlots = activeSlots
    .filter(slot => slot.slotName === 'welcome')
    .sort((a, b) => a.position - b.position);

  // Combine for legacy compatibility (used by validation functions)
  const visibleSlots = [...timeSlots, ...happySlots, ...welcomeSlots];

  const formatBonusType = (type: string) => {
    switch (type.toLowerCase()) {
      case 'freespin':
        return 'Free Spins';
      case 'freebet':
        return 'Free Bet';
      case 'cash':
        return 'Cash Bonus';
      case 'lossback':
        return 'Loss Back';
      default:
        return type;
    }
  };

  const getBonusIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'freespin':
        return <Star className="h-8 w-8" />;
      case 'freebet':
        return <Coins className="h-8 w-8" />;
      case 'cash':
        return <Coins className="h-8 w-8" />;
      case 'lossback':
        return <Coins className="h-8 w-8" />;
      default:
        return <Coins className="h-8 w-8" />;
    }
  };

  // Component for individual bonus cards
  const BonusCard: React.FC<{
    bonus: SiteClaimableBonus;
    index: number;
    isFullWidth?: boolean;
  }> = ({ bonus, index, isFullWidth = false }) => {
    const validation = getValidationStatus(bonus);
    const claimable = isClaimable(bonus.id);

    // Determine card content based on slot type
    const isTimeSlot = bonus.slotName.startsWith('time_');
    const isHappySlot = bonus.slotName.startsWith('happy_');
    const isWelcomeSlot = bonus.slotName === 'welcome';

    const getCardContent = () => {
      if (isTimeSlot) {
        return {
          title: bonus.slotName === 'time_1' ? 'INSTANT REWARD' : 'WEEKLY REWARD',
          description: bonus.slotName === 'time_1'
            ? 'Claim your instant bonus now! Get rewarded immediately for your activity.'
            : 'Weekly bonus for loyal players! Claim your weekly reward and boost your balance.',
          amount: bonus.slotName === 'time_1' ? '50.00' : '150.00'
        };
      } else if (isHappySlot) {
        const happyNumber = bonus.slotName.replace('happy_', '');
        return {
          title: `HAPPY BONUS ${happyNumber}`,
          description: 'Special happy bonus! Claim your reward and spread the joy.',
          amount: '75.00'
        };
      } else if (isWelcomeSlot) {
        return {
          title: 'WELCOME BONUS',
          description: 'New players get an instant bonus! Start your winning journey today.',
          amount: '250'
        };
      } else {
        return {
          title: bonus.bonus?.name || 'BONUS',
          description: 'Claim your bonus and enjoy the rewards!',
          amount: '100.00'
        };
      }
    };

    const cardContent = getCardContent();

    return (
      <div className={`relative bg-gradient-to-br from-indigo-900/80 to-purple-900/80 rounded-xl p-6 border border-yellow-400/30 ${
        isFullWidth ? 'w-full' : ''
      }`}>
        {/* Card Header */}
        <div className="text-center mb-6">
          <h3 className="text-xl font-bold text-yellow-400 mb-2 uppercase tracking-wide">
            {cardContent.title}
          </h3>
          <p className="text-gray-300 text-sm">
            {cardContent.description}
          </p>
        </div>

        {/* Coin Icon */}
        <div className="flex justify-center mb-6">
          <div className="relative">
            {/* Multiple coin effect */}
            <div className="absolute -top-2 -left-2 w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full opacity-80"></div>
            <div className="absolute -top-1 -left-1 w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full opacity-90"></div>
            <div className="relative w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center">
              <span className="text-yellow-900 font-bold text-lg">₺</span>
            </div>
          </div>
        </div>

        {/* Amount */}
        <div className="text-center mb-6">
          <span className="text-3xl font-bold text-yellow-400">
            ₺ {cardContent.amount}
            {isWelcomeSlot && ' TL'}
          </span>
        </div>

        {/* Claim Button */}
        <div className="text-center">
          <button className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-gray-900 font-bold py-3 px-6 rounded-lg hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200">
            {isWelcomeSlot ? 'CLAIM YOUR BONUS' : 'Claim Reward'}
          </button>
        </div>
      </div>
    );
  };

  const isClaimable = (slotId: number) => {
    return claimableBonuses.some(bonus => bonus.id === slotId);
  };

  const getValidationStatus = (slot: SiteClaimableBonus) => {
    if (!slot.bonus) {
      return { status: 'error', message: 'No bonus assigned' };
    }
    
    if (!slot.bonus.isActive) {
      return { status: 'warning', message: 'Bonus is inactive' };
    }

    if (slot.bonus.expiresAt && new Date(slot.bonus.expiresAt) < new Date()) {
      return { status: 'error', message: 'Bonus has expired' };
    }

    if (!isClaimable(slot.id)) {
      return { status: 'warning', message: 'Not currently claimable' };
    }

    return { status: 'success', message: 'Ready for users' };
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Preview Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-300 mb-2">Rewards Page Preview</h2>
        <p className="text-gray-400">How this will appear to your users</p>
      </div>

      {/* Validation Summary */}
      <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
        <h3 className="text-lg font-medium text-gray-300 mb-3">Configuration Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-3">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <div>
              <p className="text-sm text-gray-400">Visible Slots</p>
              <p className="font-medium text-gray-300">{visibleSlots.length}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <CheckCircle className="h-5 w-5 text-blue-400" />
            <div>
              <p className="text-sm text-gray-400">Claimable Now</p>
              <p className="font-medium text-gray-300">
                {visibleSlots.filter(slot => isClaimable(slot.id)).length}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-400" />
            <div>
              <p className="text-sm text-gray-400">Issues</p>
              <p className="font-medium text-gray-300">
                {visibleSlots.filter(slot => getValidationStatus(slot).status !== 'success').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Rewards Page Layout */}
      <div className="bg-gradient-to-br from-gray-900 via-indigo-900/20 to-purple-900/20 rounded-lg p-8 border border-gray-700">
        {/* REWARDS Header */}
        <div className="mb-8">
          <h3 className="text-3xl font-bold text-yellow-400 tracking-wider">REWARDS</h3>
          <div className="w-16 h-1 bg-yellow-400 mt-2"></div>
        </div>

        {timeSlots.length === 0 && happySlots.length === 0 && welcomeSlots.length === 0 ? (
          <div className="text-center py-12">
            <Gift className="w-16 h-16 text-gray-500 mx-auto mb-4" />
            <p className="text-gray-400 text-lg">No active rewards available</p>
          </div>
        ) : (
          <div className="space-y-8">
            {/* First Row: Time-based slots (max 2 per row, 50% width each) */}
            {timeSlots.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {timeSlots.map((slot, index) => (
                  <BonusCard key={slot.id} bonus={slot} index={index} />
                ))}
              </div>
            )}

            {/* Second Row: Happy bonuses (33.33% width each) */}
            {happySlots.length > 0 && (
              <div className="mt-10 mb-10 p-6 bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-xl border border-purple-500/30">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {happySlots.map((slot, index) => (
                    <BonusCard key={slot.id} bonus={slot} index={timeSlots.length + index} />
                  ))}
                </div>
              </div>
            )}

            {/* Third Row: Welcome slot (full-width) */}
            {welcomeSlots.length > 0 && (
              <div className="w-full">
                {welcomeSlots.map((slot, index) => (
                  <BonusCard key={slot.id} bonus={slot} index={timeSlots.length + happySlots.length + index} isFullWidth />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
