import { useState } from 'react';
import { BonusCreateModal } from '@panels/ui';
import { Step1 } from './Step1';
import { Step2 } from './Step2';
import { Step3 } from './Step3';
import { useApi } from '../../../features/api/useApi';
import { useWeeklyLossbackBonusTemplates } from '../../../hooks/useWeeklyLossbackBonusTemplates';
import {
  WeeklyLossbackBonusCreateRequest,
  type WeeklyLossbackBonusTemplate,
} from '@panels/api';
import type {
  FormData,
  FormErrors,
  WeeklyLossbackBonusCreateModalProps,
} from './types';
import { STEPS } from './types';

export const WeeklyLossbackBonusCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: WeeklyLossbackBonusCreateModalProps) => {
  const { pronet } = useApi();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Templates data
  const {
    data: templates = [],
    isLoading: isLoadingTemplates,
    error: templatesError,
  } = useWeeklyLossbackBonusTemplates({
    limit: 100,
  });

  // Form data
  const [formData, setFormData] = useState<FormData>({
    templateId: null,
    name: '',
    maxBalance: 0,
    lossbackPercentage: 0,
    requestWindowStartSeconds: 432000, // 5 days in seconds
    requestWindowEndSeconds: 604800, // 7 days in seconds
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split('T')[0],
    rules: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const handleClose = () => {
    setCurrentStep(1);
    setFormData({
      templateId: null,
      name: '',
      maxBalance: 0,
      lossbackPercentage: 0,
      requestWindowStartSeconds: 432000,
      requestWindowEndSeconds: 604800,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split('T')[0],
      rules: [],
    });
    setErrors({});
    onClose();
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const handleTemplateSelect = (template: WeeklyLossbackBonusTemplate) => {
    setFormData((prev) => ({
      ...prev,
      templateId: template.id,
      name: template.bonusTemplate.name,
      maxBalance: template.maxBalance,
      lossbackPercentage: template.lossbackPercentage,
      requestWindowStartSeconds: template.requestWindowStartSeconds,
      requestWindowEndSeconds: template.requestWindowEndSeconds,
      rules: template.bonusTemplate.rules.map((rule) => ({
        criterium: rule.criterium,
        operator: rule.operator,
        firstOperand: rule.firstOperand,
        secondOperand: rule.secondOperand,
        startsAt: rule.startsAt,
        endsAt: rule.endsAt,
      })),
    }));
  };

  const handleEmptyTemplateSelect = () => {
    setFormData((prev) => ({
      ...prev,
      templateId: 'empty',
      name: '',
      maxBalance: 0,
      lossbackPercentage: 0,
      requestWindowStartSeconds: 432000,
      requestWindowEndSeconds: 604800,
      rules: [],
    }));
  };

  const validateStep = (step: number): FormErrors => {
    const newErrors: FormErrors = {};

    switch (step) {
      case 1:
        if (!formData.templateId) {
          newErrors.templateId =
            'Please select a template or choose empty template';
        }
        break;

      case 2:
        if (!formData.name.trim()) {
          newErrors.name = 'Bonus name is required';
        }
        if (formData.maxBalance <= 0) {
          newErrors.maxBalance = 'Max balance must be greater than 0';
        }
        if (
          formData.lossbackPercentage <= 0 ||
          formData.lossbackPercentage > 100
        ) {
          newErrors.lossbackPercentage =
            'Lossback percentage must be between 0 and 100';
        }
        if (formData.happyHoursBoostPercentage < 0) {
          newErrors.happyHoursBoostPercentage =
            'Happy hours boost percentage cannot be negative';
        }
        if (formData.requestWindowStartSeconds < 0) {
          newErrors.requestWindowStartSeconds =
            'Request window start cannot be negative';
        }
        if (
          formData.requestWindowEndSeconds <= formData.requestWindowStartSeconds
        ) {
          newErrors.requestWindowEndSeconds =
            'Request window end must be after start';
        }
        if (!formData.expiresAt) {
          newErrors.expiresAt = 'Expiration date is required';
        }
        break;

      case 3:
        // Rules validation is optional
        break;
    }

    return newErrors;
  };

  const handleAddRule = () => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: 'ipConflictCount',
          operator: 'lte',
          firstOperand: '2',
          secondOperand: null,
          startsAt: null,
          endsAt: null,
        },
      ],
    }));
  };

  const handleRemoveRule = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  };

  const handleUpdateRule = (
    index: number,
    field: keyof FormData['rules'][0],
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.map((rule, i) =>
        i === index ? { ...rule, [field]: value } : rule
      ),
    }));
  };

  const handleSubmit = async () => {
    const errors = validateStep(currentStep);
    if (Object.keys(errors).length > 0) {
      setErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      const request = new WeeklyLossbackBonusCreateRequest({
        name: formData.name,
        maxBalance: formData.maxBalance,
        lossbackPercentage: formData.lossbackPercentage,
        happyHoursStart: formData.happyHoursStart,
        happyHoursEnd: formData.happyHoursEnd,
        happyHoursBoostPercentage: formData.happyHoursBoostPercentage,
        requestWindowStartSeconds: formData.requestWindowStartSeconds,
        requestWindowEndSeconds: formData.requestWindowEndSeconds,
        expiresAt: new Date(formData.expiresAt),
        rules: formData.rules,
      });

      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        handleClose();
      } else {
        setErrors({
          submit: response.error || 'Failed to create weekly lossback bonus',
        });
      }
    } catch (error) {
      setErrors({
        submit:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
  };

  const canGoNext = (step: number): boolean => {
    return Object.keys(validateStep(step)).length === 0;
  };

  const canGoPrevious = (currentStep: number): boolean => {
    return currentStep > 1;
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Step1
            formData={formData}
            errors={errors}
            templates={templates || []}
            isLoadingTemplates={isLoadingTemplates}
            templatesError={templatesError}
            onTemplateSelect={handleTemplateSelect}
            onEmptyTemplateSelect={handleEmptyTemplateSelect}
            onInputChange={handleInputChange}
          />
        );
      case 2:
        return (
          <Step2
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
          />
        );
      case 3:
        return (
          <Step3
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
            onAddRule={handleAddRule}
            onRemoveRule={handleRemoveRule}
            onUpdateRule={handleUpdateRule}
          />
        );
      default:
        return null;
    }
  };

  return (
    <BonusCreateModal
      isOpen={isOpen}
      onClose={handleClose}
      steps={STEPS}
      currentStep={currentStep}
      onStepChange={handleStepChange}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Weekly Lossback Bonus"
      canGoNext={canGoNext}
      canGoPrevious={canGoPrevious}
      bonusType="weekly lossback bonus"
    >
      {() => renderCurrentStep()}
    </BonusCreateModal>
  );
};
