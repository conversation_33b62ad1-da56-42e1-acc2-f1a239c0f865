import { Calendar, DollarSign, Percent, Timer } from 'lucide-react';
import Input from '../../ui/Input';
import NumericInput from '../../ui/NumericInput';
import { Step2Props } from './types';

export const Step2 = ({ formData, errors, onInputChange }: Step2Props) => {
  const handleChange = (field: keyof typeof formData, value: any) => {
    onInputChange(field, value);
  };

  const formatSecondsToHours = (seconds: number) => {
    return Math.floor(seconds / 3600);
  };

  const formatHoursToSeconds = (hours: number) => {
    return hours * 3600;
  };

  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
          <DollarSign className="w-5 h-5 mr-2" />
          Basic Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <Input
              label="Bonus Name *"
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder="Enter weekly lossback bonus name..."
              error={errors.name}
              helperText="A descriptive name for this weekly lossback bonus"
            />
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <span className="text-gray-500">₺</span>
              </div>
              <NumericInput
                label="Max Balance *"
                min={0}
                step={0.01}
                value={formData.maxBalance || 0}
                onChange={(value) => handleChange('maxBalance', value)}
                placeholder="0.00"
                className="pl-8"
                error={errors.maxBalance}
                helperText="Maximum balance that can be earned from this bonus"
                allowDecimals={true}
              />
            </div>
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10">
                <Percent className="w-4 h-4 text-gray-500" />
              </div>
              <NumericInput
                label="Lossback Percentage *"
                min={0}
                max={100}
                step={0.01}
                value={formData.lossbackPercentage || 0}
                onChange={(value) => handleChange('lossbackPercentage', value)}
                placeholder="0.00"
                className="pr-8"
                error={errors.lossbackPercentage}
                helperText="Percentage of weekly losses to return as bonus (0-100%)"
                allowDecimals={true}
              />
            </div>
          </div>
        </div>
      </div>



      {/* Request Window */}
      <div>
        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
          <Timer className="w-5 h-5 mr-2" />
          Request Window
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <NumericInput
              label="Window Start (hours) *"
              min={0}
              step={1}
              value={formatSecondsToHours(formData.requestWindowStartSeconds)}
              onChange={(value) => handleChange('requestWindowStartSeconds', formatHoursToSeconds(value))}
              placeholder="120"
              error={errors.requestWindowStartSeconds}
              helperText="Hours after week end when customers can start requesting bonus"
              allowDecimals={false}
            />
          </div>

          <div>
            <NumericInput
              label="Window End (hours) *"
              min={0}
              step={1}
              value={formatSecondsToHours(formData.requestWindowEndSeconds)}
              onChange={(value) => handleChange('requestWindowEndSeconds', formatHoursToSeconds(value))}
              placeholder="168"
              error={errors.requestWindowEndSeconds}
              helperText="Hours after week end when request window closes"
              allowDecimals={false}
            />
          </div>
        </div>
        <div className="mt-4 p-4 bg-blue-900/20 border border-blue-700 rounded-md">
          <p className="text-sm text-blue-300">
            <strong>Request Window:</strong> The time period after a week ends during which 
            customers can request their weekly lossback bonus. Specified in hours from the end of the week.
          </p>
        </div>
      </div>

      {/* Expiration */}
      <div>
        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
          <Calendar className="w-5 h-5 mr-2" />
          Expiration
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Input
              label="Expires At *"
              type="date"
              value={formData.expiresAt}
              onChange={(e) => handleChange('expiresAt', e.target.value)}
              required
              error={errors.expiresAt}
              helperText="When this weekly lossback bonus will expire and become unavailable"
            />
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
        <h4 className="text-md font-medium text-gray-300 mb-3">
          Configuration Summary
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Bonus Name:</span>
              <span className="text-gray-300">
                {formData.name || 'Not set'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Max Balance:</span>
              <span className="text-gray-300">
                ₺{formData.maxBalance?.toLocaleString() || '0'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Lossback %:</span>
              <span className="text-gray-300">
                {formData.lossbackPercentage || 0}%
              </span>
            </div>

          </div>
          <div className="space-y-2">

            <div className="flex justify-between">
              <span className="text-gray-400">Request Window:</span>
              <span className="text-gray-300">
                {formatSecondsToHours(formData.requestWindowStartSeconds)}h - {formatSecondsToHours(formData.requestWindowEndSeconds)}h
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Expires:</span>
              <span className="text-gray-300">
                {formData.expiresAt
                  ? new Date(formData.expiresAt).toLocaleDateString()
                  : 'Not set'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
