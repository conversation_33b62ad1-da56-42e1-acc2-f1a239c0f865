import {
  type WeeklyLossbackBonusRuleCreateOptions,
  type WeeklyLossbackBonusTemplate,
} from '@panels/api';

export interface FormData {
  // Step 1: Template Selection
  templateId: number | 'empty' | null;

  // Step 2: Basic Information
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  requestWindowStartSeconds: number;
  requestWindowEndSeconds: number;
  expiresAt: string;

  // Step 3: Rules
  rules: WeeklyLossbackBonusRuleCreateOptions[];
}

export interface FormErrors {
  [key: string]: string;
}

export interface WeeklyLossbackBonusCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface StepProps {
  formData: FormData;
  errors: FormErrors;
  onInputChange: (field: keyof FormData, value: any) => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

export interface Step1Props extends StepProps {
  templates: WeeklyLossbackBonusTemplate[];
  isLoadingTemplates: boolean;
  templatesError: string | null;
  onTemplateSelect: (template: WeeklyLossbackBonusTemplate) => void;
  onEmptyTemplateSelect: () => void;
}

export interface Step2Props extends StepProps {}

export interface Step3Props extends StepProps {
  onAddRule: () => void;
  onRemoveRule: (index: number) => void;
  onUpdateRule: (
    index: number,
    field: keyof WeeklyLossbackBonusRuleCreateOptions,
    value: any
  ) => void;
}

export const STEPS = [
  { id: 1, title: 'Template', description: 'Select bonus template' },
  { id: 2, title: 'Configuration', description: 'Basic info and settings' },
  { id: 3, title: 'Rules', description: 'Define bonus rules' },
];

// Import shared rule constants
export {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
  type RuleField,
} from '../../../constants/bonusRules';
