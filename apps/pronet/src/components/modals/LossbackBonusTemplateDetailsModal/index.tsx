import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Calendar, Clock, Percent, Users, Target, X } from 'lucide-react';
import type { LossbackBonusTemplate } from '@panels/api';
import Button from '../../ui/Button';

interface LossbackBonusTemplateDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  template: LossbackBonusTemplate | null;
}

export const LossbackBonusTemplateDetailsModal = ({
  isOpen,
  onClose,
  template,
}: LossbackBonusTemplateDetailsModalProps) => {
  if (!template) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-lg bg-dark-800 border border-dark-600 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-300 flex items-center"
                  >
                    <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                      <Percent className="w-4 h-4 text-white" />
                    </div>
                    Lossback Bonus Template Details
                  </Dialog.Title>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="p-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Content */}
                <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
                          <Target className="w-5 h-5 mr-2" />
                          Basic Information
                        </h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between py-2 border-b border-dark-600">
                            <span className="text-gray-400">Template Type</span>
                            <span className="text-gray-300 font-medium">
                              {template.bonusTemplate.type}
                            </span>
                          </div>
                          <div className="flex items-center justify-between py-2 border-b border-dark-600">
                            <span className="text-gray-400">Max Balance</span>
                            <span className="text-gray-300 font-medium">
                              ₺{template.maxBalance.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex items-center justify-between py-2 border-b border-dark-600">
                            <span className="text-gray-400">
                              Lossback Percentage
                            </span>
                            <span className="text-gray-300 font-medium">
                              {template.lossbackPercentage}%
                            </span>
                          </div>
                          <div className="flex items-center justify-between py-2 border-b border-dark-600">
                            <span className="text-gray-400">Happy Hours</span>
                            <span className="text-gray-300 font-medium">
                              {template.happyHoursStart} -{' '}
                              {template.happyHoursEnd}
                            </span>
                          </div>
                          <div className="flex items-center justify-between py-2 border-b border-dark-600">
                            <span className="text-gray-400">Happy Hours Boost</span>
                            <span className="text-gray-300 font-medium">
                              {template.happyHoursBoostPercentage}%
                            </span>
                          </div>
                          <div className="flex items-center justify-between py-2 border-b border-dark-600">
                            <span className="text-gray-400">
                              Deposit/Withdraw Threshold
                            </span>
                            <span className="text-gray-300 font-medium">
                              ₺{template.depositWithDrawDifferenceThreshold.toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Timestamps */}
                      <div>
                        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
                          <Calendar className="w-5 h-5 mr-2" />
                          Timestamps
                        </h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between py-2 border-b border-dark-600">
                            <span className="text-gray-400">Created</span>
                            <span className="text-gray-300 text-sm">
                              {formatDate(template.createdAt)}
                            </span>
                          </div>
                          <div className="flex items-center justify-between py-2 border-b border-dark-600">
                            <span className="text-gray-400">Last Updated</span>
                            <span className="text-gray-300 text-sm">
                              {formatDate(template.updatedAt)}
                            </span>
                          </div>

                          {template.bonusTemplate.deletedAt && (
                            <div className="flex items-center justify-between py-2 border-b border-dark-600">
                              <span className="text-gray-400">Deleted</span>
                              <span className="text-red-300 text-sm">
                                {formatDate(template.bonusTemplate.deletedAt)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Rules */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
                        <Users className="w-5 h-5 mr-2" />
                        Eligibility Rules ({template.bonusTemplate.rules.length}
                        )
                      </h3>
                      {template.bonusTemplate.rules.length === 0 ? (
                        <div className="text-center py-8">
                          <Users className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                          <p className="text-gray-400">
                            No eligibility rules defined
                          </p>
                          <p className="text-sm text-gray-500 mt-1">
                            This template is available to all customers
                          </p>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {template.bonusTemplate.rules.map((rule, index) => (
                            <div
                              key={rule.id}
                              className="bg-dark-700 rounded-lg p-4 border border-dark-600"
                            >
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-xs font-medium text-gray-400">
                                  Rule #{index + 1}
                                </span>
                                <span className="text-xs text-gray-500">
                                  ID: {rule.id}
                                </span>
                              </div>
                              <div className="space-y-2">
                                <div className="flex items-center space-x-2 text-sm">
                                  <span className="text-gray-400">Field:</span>
                                  <span className="text-gray-300 font-medium">
                                    {rule.criterium}
                                  </span>
                                </div>
                                <div className="flex items-center space-x-2 text-sm">
                                  <span className="text-gray-400">
                                    Operator:
                                  </span>
                                  <span className="text-gray-300 font-medium">
                                    {rule.operator}
                                  </span>
                                </div>
                                <div className="flex items-center space-x-2 text-sm">
                                  <span className="text-gray-400">Value:</span>
                                  <span className="text-gray-300 font-medium">
                                    {rule.firstOperand}
                                  </span>
                                </div>
                                {rule.secondOperand && (
                                  <div className="flex items-center space-x-2 text-sm">
                                    <span className="text-gray-400">
                                      Second Value:
                                    </span>
                                    <span className="text-gray-300 font-medium">
                                      {rule.secondOperand}
                                    </span>
                                  </div>
                                )}
                                {(rule.startsAt || rule.endsAt) && (
                                  <div className="flex items-center space-x-2 text-sm">
                                    <Clock className="w-4 h-4 text-gray-400" />
                                    <span className="text-gray-400">
                                      {rule.startsAt && rule.endsAt
                                        ? `${formatDate(
                                            rule.startsAt
                                          )} - ${formatDate(rule.endsAt)}`
                                        : rule.startsAt
                                        ? `From ${formatDate(rule.startsAt)}`
                                        : `Until ${formatDate(rule.endsAt!)}`}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
