import { Fragment, useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, Plus, AlertCircle } from 'lucide-react';
import Button from '../ui/Button';
import { useApi } from '../../features/api/useApi';
import {
  BonusPromocodeCreateRequest,
  type BonusPromocodeCreateRequestOptions,
  BonusSearchRequest,
  type Bonus,
} from '@panels/api';

interface BonusPromocodeCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  bonusType?: string;
}

interface FormData {
  bonusId: number | null;
  code: string;
  maxActivations: number | null;
}

interface FormErrors {
  bonusId?: string;
  code?: string;
  maxActivations?: string;
}

export const BonusPromocodeCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
  bonusType,
}: BonusPromocodeCreateModalProps) => {
  const { pronet } = useApi();

  // Form state
  const [formData, setFormData] = useState<FormData>({
    bonusId: null,
    code: '',
    maxActivations: null,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Bonus options state
  const [bonuses, setBonuses] = useState<Bonus[]>([]);
  const [isLoadingBonuses, setIsLoadingBonuses] = useState(false);
  const [bonusError, setBonusError] = useState<string | null>(null);

  // Load bonuses when modal opens
  useEffect(() => {
    if (isOpen) {
      loadBonuses();
    }
  }, [isOpen]);

  const loadBonuses = async () => {
    setIsLoadingBonuses(true);
    setBonusError(null);

    try {
      const request = new BonusSearchRequest({
        isActive: true,
        limit: 100, // Get a reasonable number of active bonuses
        ...(bonusType && { type: bonusType }),
      });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setBonuses(response.data.items);
      } else {
        setBonusError(response.error || 'Failed to load bonuses');
      }
    } catch (error) {
      setBonusError(
        error instanceof Error ? error.message : 'An unexpected error occurred'
      );
    } finally {
      setIsLoadingBonuses(false);
    }
  };

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({
        bonusId: null,
        code: '',
        maxActivations: null,
      });
      setErrors({});
      setIsSubmitting(false);
    }
  }, [isOpen]);

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.bonusId) {
      newErrors.bonusId = 'Please select a bonus';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Promocode is required';
    } else if (formData.code.trim().length < 3) {
      newErrors.code = 'Promocode must be at least 3 characters';
    } else if (formData.code.trim().length > 50) {
      newErrors.code = 'Promocode must be less than 50 characters';
    }

    if (formData.maxActivations !== null && formData.maxActivations < 1) {
      newErrors.maxActivations = 'Max activations must be at least 1';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const requestOptions: BonusPromocodeCreateRequestOptions = {
        bonusId: formData.bonusId!,
        code: formData.code.trim(),
        maxActivations: formData.maxActivations,
      };

      const request = new BonusPromocodeCreateRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        // Handle API errors
        if (response.error?.includes('already exists')) {
          setErrors({ code: 'This promocode already exists' });
        } else {
          setErrors({ code: response.error || 'Failed to create promocode' });
        }
      }
    } catch (error) {
      setErrors({
        code:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-lg bg-dark-800 border border-dark-600 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-300"
                  >
                    Create Promocode
                  </Dialog.Title>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="p-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Bonus Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Bonus *
                    </label>
                    {isLoadingBonuses ? (
                      <div className="text-sm text-gray-400">
                        Loading bonuses...
                      </div>
                    ) : bonusError ? (
                      <div className="text-sm text-red-400 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        {bonusError}
                      </div>
                    ) : (
                      <select
                        value={formData.bonusId || ''}
                        onChange={(e) =>
                          handleInputChange(
                            'bonusId',
                            e.target.value ? parseInt(e.target.value) : null
                          )
                        }
                        className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors.bonusId ? 'border-red-500' : 'border-dark-600'
                        }`}
                      >
                        <option value="">Select a bonus...</option>
                        {bonuses.map((bonus) => (
                          <option key={bonus.id} value={bonus.id}>
                            {bonus.name} ({bonus.type})
                          </option>
                        ))}
                      </select>
                    )}
                    {errors.bonusId && (
                      <p className="mt-1 text-sm text-red-400">
                        {errors.bonusId}
                      </p>
                    )}
                  </div>

                  {/* Promocode */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Promocode *
                    </label>
                    <input
                      type="text"
                      value={formData.code}
                      onChange={(e) =>
                        handleInputChange('code', e.target.value)
                      }
                      placeholder="Enter promocode..."
                      className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.code ? 'border-red-500' : 'border-dark-600'
                      }`}
                    />
                    {errors.code && (
                      <p className="mt-1 text-sm text-red-400">{errors.code}</p>
                    )}
                  </div>

                  {/* Max Activations */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Max Activations
                    </label>
                    <input
                      type="number"
                      value={formData.maxActivations || ''}
                      onChange={(e) =>
                        handleInputChange(
                          'maxActivations',
                          e.target.value ? parseInt(e.target.value) : null
                        )
                      }
                      placeholder="Leave empty for unlimited..."
                      min="1"
                      className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.maxActivations
                          ? 'border-red-500'
                          : 'border-dark-600'
                      }`}
                    />
                    {errors.maxActivations && (
                      <p className="mt-1 text-sm text-red-400">
                        {errors.maxActivations}
                      </p>
                    )}
                    <p className="mt-1 text-xs text-gray-400">
                      Leave empty for unlimited activations
                    </p>
                  </div>

                  {/* Footer */}
                  <div className="flex justify-end space-x-3 pt-4">
                    <Button
                      variant="outline"
                      onClick={onClose}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={isSubmitting || isLoadingBonuses}
                      className="flex items-center gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          Creating...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4" />
                          Create Promocode
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
