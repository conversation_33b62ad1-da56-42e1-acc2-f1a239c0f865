import { BulkAssignmentDetailsModal } from '@panels/ui';
import {
  Calendar,
  Hash,
  Tag,
  Users,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
} from 'lucide-react';
import type { BonusBulkAssignmentJob } from '@panels/api';

interface TrialBonusBulkAssignmentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: BonusBulkAssignmentJob | null;
}

export const TrialBonusBulkAssignmentDetailsModal = ({
  isOpen,
  onClose,
  job,
}: TrialBonusBulkAssignmentDetailsModalProps) => {
  if (!job) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Format target status
  const formatTargetStatus = (status: string) => {
    const statusConfig = {
      pending: {
        color: 'text-yellow-400',
        bgColor: 'bg-yellow-900/20',
        borderColor: 'border-yellow-700',
        label: 'Pending',
      },
      processing: {
        color: 'text-blue-400',
        bgColor: 'bg-blue-900/20',
        borderColor: 'border-blue-700',
        label: 'Processing',
      },
      completed: {
        color: 'text-green-400',
        bgColor: 'bg-green-900/20',
        borderColor: 'border-green-700',
        label: 'Completed',
      },
      processed: {
        color: 'text-green-400',
        bgColor: 'bg-green-900/20',
        borderColor: 'border-green-700',
        label: 'Processed',
      },
      failed: {
        color: 'text-red-400',
        bgColor: 'bg-red-900/20',
        borderColor: 'border-red-700',
        label: 'Failed',
      },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${config.bgColor} ${config.borderColor} ${config.color}`}
      >
        {config.label}
      </span>
    );
  };

  const getStatusInfo = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return {
          icon: Clock,
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-900/20',
          borderColor: 'border-yellow-700',
          label: 'Pending',
        };
      case 'processing':
        return {
          icon: BarChart3,
          color: 'text-blue-400',
          bgColor: 'bg-blue-900/20',
          borderColor: 'border-blue-700',
          label: 'Processing',
        };
      case 'completed':
        return {
          icon: CheckCircle,
          color: 'text-green-400',
          bgColor: 'bg-green-900/20',
          borderColor: 'border-green-700',
          label: 'Completed',
        };
      case 'failed':
        return {
          icon: XCircle,
          color: 'text-red-400',
          bgColor: 'bg-red-900/20',
          borderColor: 'border-red-700',
          label: 'Failed',
        };
      default:
        return {
          icon: AlertCircle,
          color: 'text-gray-400',
          bgColor: 'bg-gray-900/20',
          borderColor: 'border-gray-700',
          label: status,
        };
    }
  };

  const statusInfo = getStatusInfo(job.status);
  const StatusIcon = statusInfo.icon;

  // Calculate target statistics
  const totalTargets = job.targets.length;
  const completedTargets = job.targets.filter(
    (t) => t.status === 'completed'
  ).length;
  const failedTargets = job.targets.filter((t) => t.status === 'failed').length;
  const processingTargets = job.targets.filter(
    (t) => t.status === 'processing'
  ).length;
  const pendingTargets = job.targets.filter(
    (t) => t.status === 'pending'
  ).length;

  const completionPercentage =
    totalTargets > 0 ? Math.round((completedTargets / totalTargets) * 100) : 0;

  return (
    <BulkAssignmentDetailsModal isOpen={isOpen} onClose={onClose} job={job}>
      <div className="space-y-6">
        {/* Header Info */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-300">
              Trial Bonus Bulk Assignment #{job.id}
            </h3>
            <p className="text-gray-400 mt-1">
              {job.bonus?.name || 'Trial Bonus Assignment'}
            </p>
          </div>
          <div
            className={`flex items-center gap-2 px-3 py-1 rounded-full border ${statusInfo.bgColor} ${statusInfo.borderColor}`}
          >
            <StatusIcon className={`h-4 w-4 ${statusInfo.color}`} />
            <span className={`text-sm font-medium ${statusInfo.color}`}>
              {statusInfo.label}
            </span>
          </div>
        </div>

        {/* Job Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Job Information
            </h4>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Hash className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Job ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">
                    #{job.id}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Tag className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Bonus ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">
                    #{job.bonusId}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Total Targets:</span>
                  <span className="ml-2 text-gray-300 font-semibold">
                    {totalTargets.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Progress Statistics
            </h4>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-400" />
                <div>
                  <span className="text-gray-400">Completed:</span>
                  <span className="ml-2 text-green-300 font-semibold">
                    {completedTargets.toLocaleString()}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <XCircle className="h-5 w-5 text-red-400" />
                <div>
                  <span className="text-gray-400">Failed:</span>
                  <span className="ml-2 text-red-300 font-semibold">
                    {failedTargets.toLocaleString()}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <BarChart3 className="h-5 w-5 text-blue-400" />
                <div>
                  <span className="text-gray-400">Processing:</span>
                  <span className="ml-2 text-blue-300 font-semibold">
                    {processingTargets.toLocaleString()}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-yellow-400" />
                <div>
                  <span className="text-gray-400">Pending:</span>
                  <span className="ml-2 text-yellow-300 font-semibold">
                    {pendingTargets.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
            Completion Progress
          </h4>

          <div>
            <div className="flex justify-between text-sm text-gray-400 mb-2">
              <span>Overall Progress</span>
              <span>{completionPercentage}% Complete</span>
            </div>
            <div className="w-full bg-dark-600 rounded-full h-3">
              <div
                className="bg-green-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${completionPercentage}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>{completedTargets} completed</span>
              <span>{totalTargets} total</span>
            </div>
          </div>
        </div>

        {/* Bonus Information */}
        {job.bonus && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Associated Bonus Details
            </h4>

            <div className="p-4 bg-dark-700 border border-dark-600 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Bonus Name:</span>
                  <span className="ml-2 text-gray-300 font-medium">
                    {job.bonus.name}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Bonus Type:</span>
                  <span className="ml-2 text-gray-300">{job.bonus.type}</span>
                </div>
                <div>
                  <span className="text-gray-400">Bonus Active:</span>
                  <span className="ml-2 text-gray-300">
                    {job.bonus.isActive ? 'Yes' : 'No'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Values Version:</span>
                  <span className="ml-2 text-gray-300 font-mono">
                    v{job.bonusValuesVersion}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Timing Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
            Timing Information
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-gray-400" />
              <div>
                <span className="text-gray-400">Created:</span>
                <span className="ml-2 text-gray-300">
                  {formatDate(job.createdAt)}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-gray-400" />
              <div>
                <span className="text-gray-400">Updated:</span>
                <span className="ml-2 text-gray-300">
                  {formatDate(job.updatedAt)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Assignment Targets */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
            Assignment Targets ({totalTargets})
          </h4>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-dark-600">
              <thead className="bg-dark-700">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Processed At
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Error
                  </th>
                </tr>
              </thead>
              <tbody className="bg-dark-800 divide-y divide-dark-600">
                {job.targets.map((target) => (
                  <tr key={target.id}>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        {target.customer?.username || 'Unknown'}
                      </div>
                      <div className="text-xs text-gray-400">
                        ID: {target.externalCustomerId}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      {formatTargetStatus(target.status)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-400">
                      {target.processedAt
                        ? formatDate(target.processedAt)
                        : '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-red-400 max-w-xs truncate">
                      {target.errorMessage || '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </BulkAssignmentDetailsModal>
  );
};
