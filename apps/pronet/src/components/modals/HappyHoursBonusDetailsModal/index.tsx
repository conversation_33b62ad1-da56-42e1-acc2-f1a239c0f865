import { DetailsModal } from '@panels/ui';
import { Calendar, Hash, Tag, DollarSign, Gift } from 'lucide-react';
import type { HappyHoursBonus } from '@panels/api';

interface HappyHoursBonusDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  bonus: HappyHoursBonus | null;
}

export const HappyHoursBonusDetailsModal = ({
  isOpen,
  onClose,
  bonus,
}: HappyHoursBonusDetailsModalProps) => {
  if (!bonus) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatStatus = () => {
    const now = new Date();
    const isExpired = bonus.bonus.expiresAt && bonus.bonus.expiresAt < now;
    const isActive = bonus.bonus.isActive;
    const isDeleted = bonus.bonus.deletedAt !== null;

    if (isDeleted) {
      return { status: 'Deleted', colorClass: 'bg-red-900 text-red-300 border-red-700' };
    } else if (isExpired) {
      return { status: 'Expired', colorClass: 'bg-red-900 text-red-300 border-red-700' };
    } else if (isActive) {
      return { status: 'Active', colorClass: 'bg-green-900 text-green-300 border-green-700' };
    } else {
      return { status: 'Inactive', colorClass: 'bg-gray-900 text-gray-300 border-gray-700' };
    }
  };

  const statusInfo = formatStatus();

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Happy Hours Bonus Details"
      size="xl"
    >
      <div className="space-y-6">
        {/* Header Info */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-300">
              {bonus.bonus.name}
            </h3>
            <p className="text-gray-400 mt-1">
              Happy Hours Bonus #{bonus.id}
            </p>
          </div>
          <span
            className={`px-3 py-1 rounded-full text-sm font-medium border ${statusInfo.colorClass}`}
          >
            {statusInfo.status}
          </span>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Bonus Information
            </h4>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Hash className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Bonus ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">#{bonus.id}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Tag className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">External Name:</span>
                  <span className="ml-2 text-gray-300 font-medium">
                    {bonus.externalBonusName}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Hash className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">External ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">
                    #{bonus.externalBonusId}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Amount:</span>
                  <span className="ml-2 text-gray-300 font-medium">
                    ₺{bonus.amount.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Timestamps
            </h4>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Created:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(bonus.createdAt)}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Updated:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(bonus.updatedAt)}
                  </span>
                </div>
              </div>

              {bonus.bonus.expiresAt && (
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <span className="text-gray-400">Expires:</span>
                    <span className="ml-2 text-gray-300">
                      {formatDate(bonus.bonus.expiresAt)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bonus Details */}
        <div>
          <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2 mb-4">
            Associated Bonus Details
          </h4>

          <div className="p-4 bg-dark-700 border border-dark-600 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Bonus Name:</span>
                <span className="ml-2 text-gray-300 font-medium">{bonus.bonus.name}</span>
              </div>
              <div>
                <span className="text-gray-400">Bonus Type:</span>
                <span className="ml-2 text-gray-300">{bonus.bonus.type}</span>
              </div>
              <div>
                <span className="text-gray-400">Bonus Active:</span>
                <span className="ml-2 text-gray-300">
                  {bonus.bonus.isActive ? 'Yes' : 'No'}
                </span>
              </div>
              {bonus.bonus.expiresAt && (
                <div>
                  <span className="text-gray-400">Bonus Expires:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(bonus.bonus.expiresAt)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Rules */}
        {bonus.bonus.rules && bonus.bonus.rules.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2 mb-4">
              Bonus Rules
            </h4>
            <div className="space-y-2">
              {bonus.bonus.rules.map((rule, index) => (
                <div
                  key={rule.id}
                  className="bg-dark-700 rounded-lg p-3 border border-dark-600"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">
                      Rule #{index + 1}: {rule.criterium} {rule.operator} {rule.firstOperand}
                      {rule.secondOperand && ` - ${rule.secondOperand}`}
                    </span>
                    {(rule.startsAt || rule.endsAt) && (
                      <span className="text-xs text-gray-400">
                        {rule.startsAt && formatDate(rule.startsAt)}
                        {rule.startsAt && rule.endsAt && ' - '}
                        {rule.endsAt && formatDate(rule.endsAt)}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </DetailsModal>
  );
};
