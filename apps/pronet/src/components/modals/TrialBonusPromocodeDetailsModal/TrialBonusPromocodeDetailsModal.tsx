import { DetailsModal } from '@panels/ui';
import { Calendar, Hash, Tag, Copy, BarChart3, Gift, DollarSign, User } from 'lucide-react';
import type { BonusPromocode } from '@panels/api';

interface TrialBonusPromocodeDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  promocode: BonusPromocode | null;
}

export const TrialBonusPromocodeDetailsModal = ({
  isOpen,
  onClose,
  promocode,
}: TrialBonusPromocodeDetailsModalProps) => {
  if (!promocode) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatStatus = () => {
    const isDeleted = promocode.deletedAt !== null;
    const isActive = promocode.isActive;

    if (isDeleted) {
      return { status: 'Deleted', colorClass: 'bg-red-900 text-red-300 border-red-700' };
    } else if (isActive) {
      return { status: 'Active', colorClass: 'bg-green-900 text-green-300 border-green-700' };
    } else {
      return { status: 'Inactive', colorClass: 'bg-gray-900 text-gray-300 border-gray-700' };
    }
  };

  const statusInfo = formatStatus();

  // Copy to clipboard handler
  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(promocode.code);
      // TODO: Show success toast
      console.log('Copied code:', promocode.code);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Trial Bonus Promocode Details"
      size="xl"
    >
      <div className="space-y-6">
        {/* Header Info */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-300">
              {promocode.code}
            </h3>
            <p className="text-gray-400 mt-1">
              {promocode.bonus?.name || 'Trial Bonus Promocode'}
            </p>
          </div>
          <span
            className={`px-3 py-1 rounded-full text-sm font-medium border ${statusInfo.colorClass}`}
          >
            {statusInfo.status}
          </span>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Promocode Information
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Hash className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Promocode ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">#{promocode.id}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Tag className="h-5 w-5 text-gray-400" />
                <div className="flex items-center">
                  <span className="text-gray-400">Code:</span>
                  <span className="ml-2 text-gray-300 font-mono bg-dark-600 px-2 py-1 rounded border">
                    {promocode.code}
                  </span>
                  <button
                    onClick={handleCopyCode}
                    className="ml-2 p-1 text-gray-400 hover:text-gray-300 transition-colors"
                    title="Copy code"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Gift className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Bonus ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">#{promocode.bonusId}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Active:</span>
                  <span className="ml-2 text-gray-300">
                    {promocode.isActive ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Usage Statistics
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <BarChart3 className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Activations:</span>
                  <span className="ml-2 text-gray-300 font-semibold">
                    {promocode.activations.toLocaleString()}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <BarChart3 className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Max Activations:</span>
                  <span className="ml-2 text-gray-300">
                    {promocode.maxActivations ? promocode.maxActivations.toLocaleString() : 'Unlimited'}
                  </span>
                </div>
              </div>

              {promocode.maxActivations && (
                <div className="flex items-center gap-3">
                  <BarChart3 className="h-5 w-5 text-gray-400" />
                  <div>
                    <span className="text-gray-400">Remaining:</span>
                    <span className="ml-2 text-gray-300 font-semibold">
                      {(promocode.maxActivations - promocode.activations).toLocaleString()}
                    </span>
                  </div>
                </div>
              )}

              {promocode.maxActivations && (
                <div className="mt-3">
                  <div className="flex justify-between text-sm text-gray-400 mb-1">
                    <span>Usage Progress</span>
                    <span>
                      {Math.round((promocode.activations / promocode.maxActivations) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-dark-600 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min((promocode.activations / promocode.maxActivations) * 100, 100)}%`,
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bonus Information */}
        {promocode.bonus && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Associated Bonus Details
            </h4>
            
            <div className="p-4 bg-dark-700 border border-dark-600 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Bonus Name:</span>
                  <span className="ml-2 text-gray-300 font-medium">{promocode.bonus.name}</span>
                </div>
                <div>
                  <span className="text-gray-400">Bonus Type:</span>
                  <span className="ml-2 text-gray-300">{promocode.bonus.type}</span>
                </div>
                <div>
                  <span className="text-gray-400">Bonus Active:</span>
                  <span className="ml-2 text-gray-300">
                    {promocode.bonus.isActive ? 'Yes' : 'No'}
                  </span>
                </div>
                {promocode.bonus.expiresAt && (
                  <div>
                    <span className="text-gray-400">Bonus Expires:</span>
                    <span className="ml-2 text-gray-300">
                      {formatDate(promocode.bonus.expiresAt)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Timing Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
            Timing Information
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-gray-400" />
              <div>
                <span className="text-gray-400">Created:</span>
                <span className="ml-2 text-gray-300">
                  {formatDate(promocode.createdAt)}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-gray-400" />
              <div>
                <span className="text-gray-400">Updated:</span>
                <span className="ml-2 text-gray-300">
                  {formatDate(promocode.updatedAt)}
                </span>
              </div>
            </div>

            {promocode.deletedAt && (
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Deleted:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(promocode.deletedAt)}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DetailsModal>
  );
};
