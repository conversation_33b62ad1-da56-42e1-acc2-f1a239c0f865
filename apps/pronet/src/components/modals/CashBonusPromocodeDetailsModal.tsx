import React from 'react';
import { DetailsModal } from '@panels/ui';
import { type BonusPromocode } from '@panels/api';
import { Copy, DollarSign, Calendar, Users, Activity } from 'lucide-react';

interface CashBonusPromocodeDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  promocode: BonusPromocode | null;
}

export const CashBonusPromocodeDetailsModal: React.FC<CashBonusPromocodeDetailsModalProps> = ({
  isOpen,
  onClose,
  promocode,
}) => {
  if (!promocode) {
    return null;
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const sections = [
    {
      title: 'Promocode Information',
      icon: <Copy className="h-5 w-5" />,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-dark-700 rounded-lg border border-dark-600">
            <div>
              <div className="text-sm text-gray-400">Promocode</div>
              <div className="text-xl font-mono font-bold text-gray-300">{promocode.code}</div>
            </div>
            <button
              onClick={() => copyToClipboard(promocode.code)}
              className="p-2 text-blue-400 hover:text-blue-300 hover:bg-dark-600 rounded-lg transition-colors"
              title="Copy to clipboard"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-gray-400">Status</div>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${
                promocode.isActive
                  ? 'bg-green-900 text-green-300 border-green-700'
                  : 'bg-gray-900 text-gray-300 border-gray-700'
              }`}>
                {promocode.isActive ? 'Active' : 'Inactive'}
              </div>
            </div>
            
            <div>
              <div className="text-sm text-gray-400">ID</div>
              <div className="text-gray-300 font-mono">#{promocode.id}</div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Associated Bonus',
      icon: <DollarSign className="h-5 w-5" />,
      content: promocode.bonus ? (
        <div className="space-y-3">
          <div>
            <div className="text-sm text-gray-400">Bonus Name</div>
            <div className="text-gray-300 font-medium">{promocode.bonus.name}</div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-gray-400">Bonus ID</div>
              <div className="text-gray-300 font-mono">#{promocode.bonus.id}</div>
            </div>
            
            <div>
              <div className="text-sm text-gray-400">Bonus Type</div>
              <div className="text-gray-300 capitalize">{promocode.bonus.type}</div>
            </div>
          </div>
          
          <div>
            <div className="text-sm text-gray-400">Bonus Status</div>
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${
              promocode.bonus.isActive
                ? 'bg-green-900 text-green-300 border-green-700'
                : 'bg-gray-900 text-gray-300 border-gray-700'
            }`}>
              {promocode.bonus.isActive ? 'Active' : 'Inactive'}
            </div>
          </div>
        </div>
      ) : (
        <div className="text-gray-400">No bonus information available</div>
      ),
    },
    {
      title: 'Usage Statistics',
      icon: <Users className="h-5 w-5" />,
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-dark-700 rounded-lg border border-dark-600">
              <div className="text-sm text-gray-400">Total Activations</div>
              <div className="text-2xl font-bold text-blue-400">{promocode.activations.toLocaleString()}</div>
            </div>
            
            <div className="p-4 bg-dark-700 rounded-lg border border-dark-600">
              <div className="text-sm text-gray-400">Max Activations</div>
              <div className="text-2xl font-bold text-gray-300">
                {promocode.maxActivations ? promocode.maxActivations.toLocaleString() : 'Unlimited'}
              </div>
            </div>
          </div>
          
          {promocode.maxActivations && (
            <div>
              <div className="flex justify-between text-sm text-gray-400 mb-2">
                <span>Usage Progress</span>
                <span>{Math.round((promocode.activations / promocode.maxActivations) * 100)}%</span>
              </div>
              <div className="w-full bg-dark-600 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${Math.min((promocode.activations / promocode.maxActivations) * 100, 100)}%`,
                  }}
                />
              </div>
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Timestamps',
      icon: <Calendar className="h-5 w-5" />,
      content: (
        <div className="space-y-3">
          <div>
            <div className="text-sm text-gray-400">Created</div>
            <div className="text-gray-300">{formatDate(promocode.createdAt)}</div>
          </div>
          
          <div>
            <div className="text-sm text-gray-400">Last Updated</div>
            <div className="text-gray-300">{formatDate(promocode.updatedAt)}</div>
          </div>
          
          {promocode.deletedAt && (
            <div>
              <div className="text-sm text-gray-400">Deleted</div>
              <div className="text-red-400">{formatDate(promocode.deletedAt)}</div>
            </div>
          )}
        </div>
      ),
    },
  ];

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Cash Bonus Promocode Details"
      subtitle={`Promocode: ${promocode.code}`}
      sections={sections}
    />
  );
};
