import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, Gift, Calendar, Tag, Copy, BarChart3 } from 'lucide-react';
import Button from '../ui/Button';
import type { BonusPromocode } from '@panels/api';

interface BonusPromocodeDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  promocode: BonusPromocode | null;
}

export const BonusPromocodeDetailsModal = ({
  isOpen,
  onClose,
  promocode,
}: BonusPromocodeDetailsModalProps) => {
  if (!promocode) return null;

  // Format date helper
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short',
    }).format(date);
  };

  // Format status badge
  const formatStatus = (promocode: BonusPromocode) => {
    const isActive = promocode.isActive;
    const isDeleted = promocode.deletedAt !== null;

    let status: string;
    let colorClass: string;

    if (isDeleted) {
      status = 'deleted';
      colorClass = 'bg-red-900 text-red-300 border-red-700';
    } else if (isActive) {
      status = 'active';
      colorClass = 'bg-green-900 text-green-300 border-green-700';
    } else {
      status = 'inactive';
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
    }

    return (
      <span
        className={`px-3 py-1 rounded-full text-sm font-medium border ${colorClass}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  // Copy to clipboard handler
  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(promocode.code);
      // TODO: Show success toast
      console.log('Copied code:', promocode.code);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-lg bg-dark-800 border border-dark-600 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-300"
                  >
                    Promocode Details
                  </Dialog.Title>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="p-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Content */}
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                          <Tag className="h-4 w-4 mr-2" />
                          Promocode Information
                        </h4>
                        <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">ID:</span>
                            <span className="text-sm font-mono text-gray-300">
                              #{promocode.id}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-400">Code:</span>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-mono text-gray-300 bg-dark-600 px-2 py-1 rounded border">
                                {promocode.code}
                              </span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleCopyCode}
                                className="p-1 text-gray-400 hover:text-gray-300"
                                title="Copy Code"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">
                              Status:
                            </span>
                            <div>{formatStatus(promocode)}</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                          <BarChart3 className="h-4 w-4 mr-2" />
                          Usage Statistics
                        </h4>
                        <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">
                              Activations:
                            </span>
                            <span className="text-sm font-medium text-gray-300">
                              {promocode.activations.toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Bonus Information */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                      <Gift className="h-4 w-4 mr-2" />
                      Associated Bonus
                    </h4>
                    <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Bonus ID:</span>
                        <span className="text-sm font-mono text-gray-300">
                          #{promocode.bonusId}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Name:</span>
                        <span className="text-sm font-medium text-gray-300">
                          {promocode.bonus?.name}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Type:</span>
                        <span className="text-sm text-gray-300 capitalize">
                          {promocode.bonus?.type}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Timestamps */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      Timeline
                    </h4>
                    <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">
                          Created At:
                        </span>
                        <span className="text-sm text-gray-300">
                          {formatDate(promocode.createdAt)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">
                          Last Updated:
                        </span>
                        <span className="text-sm text-gray-300">
                          {formatDate(promocode.updatedAt)}
                        </span>
                      </div>
                      {promocode.deletedAt && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-400">
                            Deleted At:
                          </span>
                          <span className="text-sm text-red-400">
                            {formatDate(promocode.deletedAt)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="mt-6 flex justify-end">
                  <Button variant="outline" onClick={onClose}>
                    Close
                  </Button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
