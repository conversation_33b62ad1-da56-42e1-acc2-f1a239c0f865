import { useState } from 'react';
import { BonusCreateModal } from '@panels/ui';
import { Step1 } from './Step1';
import { Step2 } from './Step2';
import { Step3 } from './Step3';
import { useApi } from '../../../features/api/useApi';
import { useLossbackBonusTemplates } from '../../../hooks/useLossbackBonusTemplates';
import {
  LossbackBonusCreateRequest,
  type LossbackBonusTemplate,
} from '@panels/api';
import type {
  FormData,
  FormErrors,
  LossbackBonusCreateModalProps,
} from './types';
import { STEPS } from './types';

export const LossbackBonusCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: LossbackBonusCreateModalProps) => {
  const { pronet } = useApi();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Templates data
  const {
    data: templates = [],
    isLoading: isLoadingTemplates,
    error: templatesError,
  } = useLossbackBonusTemplates({
    limit: 100,
  });

  // Form data
  const [formData, setFormData] = useState<FormData>({
    templateId: null,
    name: '',
    maxBalance: 0,
    lossbackPercentage: 0,
    happyHoursStart: '18:00',
    happyHoursEnd: '22:00',
    happyHoursBoostPercentage: 0,
    depositWithDrawDifferenceThreshold: 0,
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split('T')[0],
    rules: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const handleClose = () => {
    setCurrentStep(1);
    setFormData({
      templateId: null,
      name: '',
      maxBalance: 0,
      lossbackPercentage: 0,
      happyHoursStart: '18:00',
      happyHoursEnd: '22:00',
      happyHoursBoostPercentage: 0,
      depositWithDrawDifferenceThreshold: 0,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split('T')[0],
      rules: [],
    });
    setErrors({});
    onClose();
  };

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const handleTemplateSelect = (template: LossbackBonusTemplate) => {
    setFormData((prev) => ({
      ...prev,
      templateId: template.id,
      name: template.bonusTemplate.name,
      maxBalance: template.maxBalance,
      lossbackPercentage: template.lossbackPercentage,
      happyHoursStart: template.happyHoursStart,
      happyHoursEnd: template.happyHoursEnd,
      happyHoursBoostPercentage: template.happyHoursBoostPercentage,
      depositWithDrawDifferenceThreshold: template.depositWithDrawDifferenceThreshold,
      rules: template.bonusTemplate.rules.map((rule) => ({
        criterium: rule.criterium,
        operator: rule.operator,
        firstOperand: rule.firstOperand,
        secondOperand: rule.secondOperand,
        startsAt: rule.startsAt,
        endsAt: rule.endsAt,
      })),
    }));
  };

  const handleEmptyTemplateSelect = () => {
    setFormData((prev) => ({
      ...prev,
      templateId: 'empty',
      name: '',
      maxBalance: 0,
      lossbackPercentage: 0,
      happyHoursStart: '18:00',
      happyHoursEnd: '22:00',
      happyHoursBoostPercentage: 0,
      depositWithDrawDifferenceThreshold: 0,
      rules: [],
    }));
  };

  // Rule handlers
  const handleAddRule = () => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: '',
          operator: '',
          firstOperand: '',
          secondOperand: null,
          startsAt: null,
          endsAt: null,
        },
      ],
    }));
  };

  const handleRemoveRule = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  };

  const handleUpdateRule = (
    index: number,
    field: string | number | symbol,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.map((rule, i) =>
        i === index ? { ...rule, [field]: value } : rule
      ),
    }));
  };

  // Navigation validation
  const canGoNext = () => {
    return Object.keys(validateCurrentStep()).length === 0;
  };

  const canGoPrevious = () => {
    return currentStep > 1;
  };

  // Validation
  const validateCurrentStep = (): FormErrors => {
    const newErrors: FormErrors = {};

    if (currentStep === 1) {
      if (!formData.templateId) {
        newErrors.templateId = 'Please select a template';
      }
    } else if (currentStep === 2) {
      if (!formData.name.trim()) {
        newErrors.name = 'Bonus name is required';
      }
      if (formData.maxBalance <= 0) {
        newErrors.maxBalance = 'Max balance must be greater than 0';
      }
      if (
        formData.lossbackPercentage <= 0 ||
        formData.lossbackPercentage > 100
      ) {
        newErrors.lossbackPercentage =
          'Lossback percentage must be between 0 and 100';
      }
      if (!formData.expiresAt) {
        newErrors.expiresAt = 'Expiration date is required';
      } else if (new Date(formData.expiresAt) <= new Date()) {
        newErrors.expiresAt = 'Expiration date must be in the future';
      }
    }

    return newErrors;
  };

  const handleSubmit = async () => {
    const errors = validateCurrentStep();
    if (Object.keys(errors).length > 0) {
      setErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      const request = new LossbackBonusCreateRequest({
        name: formData.name,
        maxBalance: formData.maxBalance,
        lossbackPercentage: formData.lossbackPercentage,
        happyHoursStart: formData.happyHoursStart,
        happyHoursEnd: formData.happyHoursEnd,
        expiresAt: new Date(formData.expiresAt),
        rules: formData.rules,
      });

      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        handleClose();
      } else {
        setErrors({
          submit: response.error || 'Failed to create lossback bonus',
        });
      }
    } catch (error) {
      setErrors({
        submit:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Step1
            formData={formData}
            errors={errors}
            templates={templates || []}
            isLoadingTemplates={isLoadingTemplates}
            templatesError={templatesError}
            onTemplateSelect={handleTemplateSelect}
            onEmptyTemplateSelect={handleEmptyTemplateSelect}
            onInputChange={handleInputChange}
          />
        );
      case 2:
        return (
          <Step2
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
          />
        );
      case 3:
        return (
          <Step3
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
            onAddRule={handleAddRule}
            onRemoveRule={handleRemoveRule}
            onUpdateRule={handleUpdateRule}
          />
        );
      default:
        return null;
    }
  };

  return (
    <BonusCreateModal
      isOpen={isOpen}
      onClose={handleClose}
      steps={STEPS}
      currentStep={currentStep}
      onStepChange={handleStepChange}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Lossback Bonus"
      canGoNext={canGoNext}
      canGoPrevious={canGoPrevious}
      bonusType="lossback bonus"
    >
      {() => renderCurrentStep()}
    </BonusCreateModal>
  );
};
