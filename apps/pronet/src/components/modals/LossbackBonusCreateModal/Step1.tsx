import { AlertCircle } from 'lucide-react';
import { Step1Props } from './types';

export const Step1 = ({
  formData,
  errors,
  templates,
  isLoadingTemplates,
  templatesError,
  onTemplateSelect,
  onEmptyTemplateSelect,
}: Step1Props) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-300 mb-4">
        Select Bonus Template
      </h3>

      {/* Empty Template Option */}
      <div
        onClick={onEmptyTemplateSelect}
        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
          formData.templateId === 'empty'
            ? 'border-blue-500 bg-blue-900/20'
            : 'border-dark-600 hover:border-dark-500'
        }`}
      >
        <div className="font-medium text-gray-300">Empty Template</div>
        <div className="text-sm text-gray-400">
          Start with empty values and configure manually
        </div>
      </div>

      {isLoadingTemplates ? (
        <div className="text-sm text-gray-400">Loading templates...</div>
      ) : templatesError ? (
        <div className="text-sm text-red-400 flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          {templatesError}
        </div>
      ) : (
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {templates.map((template) => (
            <div
              key={template.id}
              onClick={() => onTemplateSelect(template)}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                formData.templateId === template.id
                  ? 'border-blue-500 bg-blue-900/20'
                  : 'border-dark-600 hover:border-dark-500'
              }`}
            >
              <div className="font-medium text-gray-300">
                {template.bonusTemplate.name}
              </div>
              <div className="text-sm text-gray-400">
                Max Balance: ₺{template.maxBalance.toLocaleString()} • Lossback:{' '}
                {template.lossbackPercentage}%
                {template.bonusTemplate.rules.length > 0 && (
                  <span className="text-blue-400">
                    {' '}
                    • {template.bonusTemplate.rules.length} rules
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      {errors.templateId && (
        <p className="text-sm text-red-400">{errors.templateId}</p>
      )}
    </div>
  );
};
