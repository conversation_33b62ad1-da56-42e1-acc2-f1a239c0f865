import { DetailsModal } from '@panels/ui';
import { Calendar, Users, Target, Timer } from 'lucide-react';
import type { WeeklyLossbackBonus } from '@panels/api';

interface WeeklyLossbackBonusDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  bonus: WeeklyLossbackBonus | null;
}

export const WeeklyLossbackBonusDetailsModal = ({
  isOpen,
  onClose,
  bonus,
}: WeeklyLossbackBonusDetailsModalProps) => {
  if (!bonus) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatBonusStatus = (bonus: WeeklyLossbackBonus) => {
    if (!bonus.bonus) return null;

    const isActive = bonus.bonus.isActive;
    const isDeleted = bonus.bonus.deletedAt !== null;
    const isExpired =
      bonus.bonus.expiresAt && new Date(bonus.bonus.expiresAt) < new Date();

    let status: string;
    let colorClass: string;

    if (isDeleted) {
      status = 'deleted';
      colorClass = 'bg-red-900 text-red-300 border-red-700';
    } else if (isExpired) {
      status = 'expired';
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
    } else if (isActive) {
      status = 'active';
      colorClass = 'bg-green-900 text-green-300 border-green-700';
    } else {
      status = 'inactive';
      colorClass = 'bg-yellow-900 text-yellow-300 border-yellow-700';
    }

    return (
      <span
        className={`px-3 py-1 rounded-full text-sm font-medium border ${colorClass}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const formatRequestWindow = () => {
    const startHours = Math.floor(bonus.requestWindowStartSeconds / 3600);
    const endHours = Math.floor(bonus.requestWindowEndSeconds / 3600);
    return `${startHours}h - ${endHours}h`;
  };

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Weekly Lossback Bonus Details"
      size="xl"
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Basic Information
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Bonus Name</span>
                <span className="text-gray-300 font-medium">
                  {bonus.bonus?.name || 'Unknown'}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Status</span>
                {formatBonusStatus(bonus)}
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Max Balance</span>
                <span className="text-gray-300 font-medium">
                  ₺{bonus.maxBalance.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Lossback Percentage</span>
                <span className="text-gray-300 font-medium">
                  {bonus.lossbackPercentage}%
                </span>
              </div>

              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">
                  Deposit/Withdraw Threshold
                </span>
                <span className="text-gray-300 font-medium">
                  ₺{bonus.depositWithDrawDifferenceThreshold.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Request Window</span>
                <span className="text-gray-300 font-medium">
                  {formatRequestWindow()}
                </span>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div>
            <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Timestamps
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Created</span>
                <span className="text-gray-300 text-sm">
                  {formatDate(bonus.createdAt)}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Last Updated</span>
                <span className="text-gray-300 text-sm">
                  {formatDate(bonus.updatedAt)}
                </span>
              </div>
              {bonus.bonus?.expiresAt && (
                <div className="flex items-center justify-between py-2 border-b border-dark-600">
                  <span className="text-gray-400">Expires</span>
                  <span className="text-gray-300 text-sm">
                    {formatDate(bonus.bonus.expiresAt)}
                  </span>
                </div>
              )}
              {bonus.bonus?.deletedAt && (
                <div className="flex items-center justify-between py-2 border-b border-dark-600">
                  <span className="text-gray-400">Deleted</span>
                  <span className="text-red-300 text-sm">
                    {formatDate(bonus.bonus.deletedAt)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Rules */}
        <div>
          <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
            <Users className="w-5 h-5 mr-2" />
            Bonus Rules ({bonus.bonus?.rules.length || 0})
          </h3>
          {!bonus.bonus?.rules || bonus.bonus.rules.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-500 mx-auto mb-3" />
              <p className="text-gray-400">No bonus rules defined</p>
              <p className="text-sm text-gray-500 mt-1">
                This bonus has no eligibility restrictions
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {bonus.bonus.rules.map((rule, index) => (
                <div
                  key={rule.id}
                  className="bg-dark-700 rounded-lg p-4 border border-dark-600"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-gray-400">
                      Rule #{index + 1}
                    </span>
                    <span className="text-xs text-gray-500">ID: {rule.id}</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-400">Field:</span>
                      <span className="text-gray-300 font-medium">
                        {rule.criterium}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-400">Operator:</span>
                      <span className="text-gray-300 font-medium">
                        {rule.operator}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-400">Value:</span>
                      <span className="text-gray-300 font-medium">
                        {rule.firstOperand}
                      </span>
                    </div>
                    {rule.secondOperand && (
                      <div className="flex items-center space-x-2 text-sm">
                        <span className="text-gray-400">Second Value:</span>
                        <span className="text-gray-300 font-medium">
                          {rule.secondOperand}
                        </span>
                      </div>
                    )}
                    {(rule.startsAt || rule.endsAt) && (
                      <div className="flex items-center space-x-2 text-sm">
                        <Timer className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-400">
                          {rule.startsAt && rule.endsAt
                            ? `${formatDate(rule.startsAt)} - ${formatDate(
                                rule.endsAt
                              )}`
                            : rule.startsAt
                            ? `From ${formatDate(rule.startsAt)}`
                            : `Until ${formatDate(rule.endsAt!)}`}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </DetailsModal>
  );
};
