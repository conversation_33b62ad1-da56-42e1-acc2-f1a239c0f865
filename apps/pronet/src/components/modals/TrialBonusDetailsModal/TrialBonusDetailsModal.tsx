import { DetailsModal } from '@panels/ui';
import { Calendar, DollarSign, Hash, User, Clock, Tag, Shield } from 'lucide-react';
import type { TrialBonus } from '@panels/api';

interface TrialBonusDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  bonus: TrialBonus | null;
}

export const TrialBonusDetailsModal = ({
  isOpen,
  onClose,
  bonus,
}: TrialBonusDetailsModalProps) => {
  if (!bonus) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatBonusStatus = () => {
    const now = new Date();
    const isExpired = bonus.bonus.expiresAt && bonus.bonus.expiresAt < now;
    const isActive = bonus.bonus.isActive;

    if (isExpired) {
      return { status: 'Expired', colorClass: 'bg-red-900 text-red-300 border-red-700' };
    } else if (isActive) {
      return { status: 'Active', colorClass: 'bg-green-900 text-green-300 border-green-700' };
    } else {
      return { status: 'Inactive', colorClass: 'bg-gray-900 text-gray-300 border-gray-700' };
    }
  };

  const statusInfo = formatBonusStatus();

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Trial Bonus Details"
      size="xl"
    >
      <div className="space-y-6">
        {/* Header Info */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-300">
              {bonus.bonus.name}
            </h3>
            <p className="text-gray-400 mt-1">
              External: {bonus.externalBonusName}
            </p>
          </div>
          <span
            className={`px-3 py-1 rounded-full text-sm font-medium border ${statusInfo.colorClass}`}
          >
            {statusInfo.status}
          </span>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Basic Information
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Hash className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Bonus ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">#{bonus.id}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Tag className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">External ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">#{bonus.externalBonusId}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Amount:</span>
                  <span className="ml-2 text-gray-300 font-semibold">
                    ₺{bonus.amount.toFixed(2)}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Active:</span>
                  <span className="ml-2 text-gray-300">
                    {bonus.bonus.isActive ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Timing Information
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Created:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(bonus.bonus.createdAt)}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Updated:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(bonus.bonus.updatedAt)}
                  </span>
                </div>
              </div>

              {bonus.bonus.expiresAt && (
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <span className="text-gray-400">Expires:</span>
                    <span className="ml-2 text-gray-300">
                      {formatDate(bonus.bonus.expiresAt)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Rules Section */}
        {bonus.bonus.rules && bonus.bonus.rules.length > 0 && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Bonus Rules ({bonus.bonus.rules.length})
            </h4>
            
            <div className="space-y-3">
              {bonus.bonus.rules.map((rule, index) => (
                <div
                  key={index}
                  className="p-4 bg-dark-700 border border-dark-600 rounded-lg"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-xs bg-blue-900 text-blue-300 px-2 py-1 rounded">
                        Rule {index + 1}
                      </span>
                      <span className="text-gray-300 font-medium">
                        {rule.criterium}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mt-2 text-sm text-gray-400">
                    <span className="font-medium">{rule.operator}</span>
                    <span className="ml-2">{rule.firstOperand}</span>
                    {rule.secondOperand && (
                      <span className="ml-1">- {rule.secondOperand}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* External Bonus Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
            External Bonus Details
          </h4>
          
          <div className="p-4 bg-dark-700 border border-dark-600 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">External Name:</span>
                <span className="ml-2 text-gray-300">{bonus.externalBonusName}</span>
              </div>
              <div>
                <span className="text-gray-400">External ID:</span>
                <span className="ml-2 text-gray-300 font-mono">#{bonus.externalBonusId}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DetailsModal>
  );
};
