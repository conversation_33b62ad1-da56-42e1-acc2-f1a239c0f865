import { DetailsModal } from '@panels/ui';
import { Calendar, Users, Target, Timer } from 'lucide-react';
import type { WeeklyLossbackBonusTemplate } from '@panels/api';

interface WeeklyLossbackBonusTemplateDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  template: WeeklyLossbackBonusTemplate | null;
}

export const WeeklyLossbackBonusTemplateDetailsModal = ({
  isOpen,
  onClose,
  template,
}: WeeklyLossbackBonusTemplateDetailsModalProps) => {
  if (!template) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatTemplateStatus = (template: WeeklyLossbackBonusTemplate) => {
    const isDeleted = template.bonusTemplate.deletedAt !== null;

    let status: string;
    let colorClass: string;

    if (isDeleted) {
      status = 'deleted';
      colorClass = 'bg-red-900 text-red-300 border-red-700';
    } else {
      status = 'active';
      colorClass = 'bg-green-900 text-green-300 border-green-700';
    }

    return (
      <span
        className={`px-3 py-1 rounded-full text-sm font-medium border ${colorClass}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const formatRequestWindow = () => {
    const startHours = Math.floor(template.requestWindowStartSeconds / 3600);
    const endHours = Math.floor(template.requestWindowEndSeconds / 3600);
    return `${startHours}h - ${endHours}h`;
  };

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Weekly Lossback Bonus Template Details"
      size="xl"
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Basic Information
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Template Name</span>
                <span className="text-gray-300 font-medium">
                  {template.bonusTemplate.name}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Template Type</span>
                <span className="text-gray-300 font-medium">
                  {template.bonusTemplate.type}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Status</span>
                {formatTemplateStatus(template)}
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Max Balance</span>
                <span className="text-gray-300 font-medium">
                  ₺{template.maxBalance.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Lossback Percentage</span>
                <span className="text-gray-300 font-medium">
                  {template.lossbackPercentage}%
                </span>
              </div>

              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">
                  Deposit/Withdraw Threshold
                </span>
                <span className="text-gray-300 font-medium">
                  ₺{template.depositWithDrawDifferenceThreshold.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Request Window</span>
                <span className="text-gray-300 font-medium">
                  {formatRequestWindow()}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Valid For</span>
                <span className="text-gray-300 font-medium">
                  {template.validForDays} days
                </span>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div>
            <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Timestamps
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Created</span>
                <span className="text-gray-300 text-sm">
                  {formatDate(template.createdAt)}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Last Updated</span>
                <span className="text-gray-300 text-sm">
                  {formatDate(template.updatedAt)}
                </span>
              </div>
              {template.bonusTemplate.deletedAt && (
                <div className="flex items-center justify-between py-2 border-b border-dark-600">
                  <span className="text-gray-400">Deleted</span>
                  <span className="text-red-300 text-sm">
                    {formatDate(template.bonusTemplate.deletedAt)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Rules */}
        <div>
          <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
            <Users className="w-5 h-5 mr-2" />
            Template Rules ({template.bonusTemplate.rules.length})
          </h3>
          {template.bonusTemplate.rules.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-500 mx-auto mb-3" />
              <p className="text-gray-400">No template rules defined</p>
              <p className="text-sm text-gray-500 mt-1">
                This template has no eligibility restrictions
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {template.bonusTemplate.rules.map((rule, index) => (
                <div
                  key={rule.id}
                  className="bg-dark-700 rounded-lg p-4 border border-dark-600"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-gray-400">
                      Rule #{index + 1}
                    </span>
                    <span className="text-xs text-gray-500">ID: {rule.id}</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-400">Field:</span>
                      <span className="text-gray-300 font-medium">
                        {rule.criterium}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-400">Operator:</span>
                      <span className="text-gray-300 font-medium">
                        {rule.operator}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-400">Value:</span>
                      <span className="text-gray-300 font-medium">
                        {rule.firstOperand}
                      </span>
                    </div>
                    {rule.secondOperand && (
                      <div className="flex items-center space-x-2 text-sm">
                        <span className="text-gray-400">Second Value:</span>
                        <span className="text-gray-300 font-medium">
                          {rule.secondOperand}
                        </span>
                      </div>
                    )}
                    {(rule.startsAt || rule.endsAt) && (
                      <div className="flex items-center space-x-2 text-sm">
                        <Timer className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-400">
                          {rule.startsAt && rule.endsAt
                            ? `${formatDate(rule.startsAt)} - ${formatDate(
                                rule.endsAt
                              )}`
                            : rule.startsAt
                            ? `From ${formatDate(rule.startsAt)}`
                            : `Until ${formatDate(rule.endsAt!)}`}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </DetailsModal>
  );
};
