import { DetailsModal } from '@panels/ui';
import { Calendar, Hash, Tag, DollarSign } from 'lucide-react';
import type { HappyHoursBonusTemplate } from '@panels/api';

interface HappyHoursBonusTemplateDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  template: HappyHoursBonusTemplate | null;
}

export const HappyHoursBonusTemplateDetailsModal = ({
  isOpen,
  onClose,
  template,
}: HappyHoursBonusTemplateDetailsModalProps) => {
  if (!template) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatTemplateStatus = (template: HappyHoursBonusTemplate) => {
    const isDeleted = template.bonusTemplate.deletedAt !== null;

    let status: string;
    let colorClass: string;

    if (isDeleted) {
      status = 'deleted';
      colorClass = 'bg-red-900 text-red-300 border-red-700';
    } else {
      status = 'active';
      colorClass = 'bg-green-900 text-green-300 border-green-700';
    }

    return (
      <span
        className={`px-3 py-1 rounded-full text-sm font-medium border ${colorClass}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };



  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Happy Hours Bonus Template Details"
      size="xl"
    >
      <div className="space-y-6">
        {/* Header Info */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-300">
              {template.bonusTemplate.name}
            </h3>
            <p className="text-gray-400 mt-1">
              Happy Hours Bonus Template #{template.id}
            </p>
          </div>
          {formatTemplateStatus(template)}
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Template Information
            </h4>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Hash className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Template ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">#{template.id}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Tag className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">External Name:</span>
                  <span className="ml-2 text-gray-300 font-medium">
                    {template.externalBonusName}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Hash className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">External ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">
                    #{template.externalBonusId}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Amount:</span>
                  <span className="ml-2 text-gray-300 font-medium">
                    ₺{template.amount.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Timestamps
            </h4>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Created:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(template.createdAt)}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Updated:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(template.updatedAt)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Template Details */}
        <div>
          <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2 mb-4">
            Associated Template Details
          </h4>

          <div className="p-4 bg-dark-700 border border-dark-600 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Template Name:</span>
                <span className="ml-2 text-gray-300 font-medium">{template.bonusTemplate.name}</span>
              </div>
              <div>
                <span className="text-gray-400">Template Type:</span>
                <span className="ml-2 text-gray-300">{template.bonusTemplate.type}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Rules */}
        {template.bonusTemplate.rules && template.bonusTemplate.rules.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2 mb-4">
              Template Rules
            </h4>
            <div className="space-y-2">
              {template.bonusTemplate.rules.map((rule, index) => (
                <div
                  key={rule.id}
                  className="bg-dark-700 rounded-lg p-3 border border-dark-600"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">
                      Rule #{index + 1}: {rule.criterium} {rule.operator} {rule.firstOperand}
                      {rule.secondOperand && ` - ${rule.secondOperand}`}
                    </span>
                    {(rule.startsInSeconds || rule.endsInSeconds) && (
                      <span className="text-xs text-gray-400">
                        {rule.startsInSeconds && `${rule.startsInSeconds}s`}
                        {rule.startsInSeconds && rule.endsInSeconds && ' - '}
                        {rule.endsInSeconds && `${rule.endsInSeconds}s`}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </DetailsModal>
  );
};
