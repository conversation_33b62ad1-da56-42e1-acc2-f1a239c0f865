import type {
  TrialBonusTemplate,
  TrialBonusExternal,
  TrialBonusRuleCreateOptions,
} from '@panels/api';

export interface FormData {
  templateId: number | 'empty';
  name: string;
  externalBonusId: number | null;
  externalBonusName: string;
  amount: number;
  expiresAt: string;
  rules: TrialBonusRuleCreateOptions[];
}

export interface FormErrors {
  templateId?: string;
  name?: string;
  externalBonusId?: string;
  externalBonusName?: string;
  amount?: string;
  expiresAt?: string;
  rules?: string;
  submit?: string;
}

export interface TrialBonusCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface StepProps {
  formData: FormData;
  errors: FormErrors;
  onInputChange: (field: keyof FormData, value: any) => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

export interface Step1Props extends StepProps {
  templates: TrialBonusTemplate[];
  isLoadingTemplates: boolean;
  templatesError: string | null;
  onTemplateSelect: (template: TrialBonusTemplate) => void;
  onEmptyTemplateSelect: () => void;
}

export interface Step2Props extends StepProps {
  externals: TrialBonusExternal[];
  isLoadingExternals: boolean;
  externalsError: string | null;
}

export interface Step3Props extends StepProps {
  onAddRule: () => void;
  onRemoveRule: (index: number) => void;
  onUpdateRule: (index: number, field: string, value: any) => void;
}

// Import shared rule constants
export {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
} from '../../../constants/bonusRules';
