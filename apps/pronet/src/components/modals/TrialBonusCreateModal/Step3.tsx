import { RulesStep } from '@panels/ui';
import { Step3Props, RULE_FIELDS, RULE_OPERATORS } from './types';

export const Step3 = ({
  formData,
  errors,
  onAddRule,
  onRemoveRule,
  onUpdateRule,
}: Step3Props) => {
  return (
    <RulesStep
      rules={formData.rules}
      errors={errors}
      ruleFields={RULE_FIELDS}
      ruleOperators={RULE_OPERATORS}
      onAddRule={onAddRule}
      onRemoveRule={onRemoveRule}
      onUpdateRule={onUpdateRule}
      title="Trial Bonus Rules"
      description="Define who can receive this trial bonus"
      emptyStateTitle="No rules defined"
      emptyStateDescription="Add rules to control who can receive this bonus, or leave empty to allow all users."
      variant="full"
    />
  );
};
