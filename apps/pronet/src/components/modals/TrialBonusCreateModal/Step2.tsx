import { AlertCircle } from 'lucide-react';
import Input from '../../ui/Input';
import NumericInput from '../../ui/NumericInput';
import { Step2Props } from './types';

export const Step2 = ({
  formData,
  errors,
  onInputChange,
  externals,
  isLoadingExternals,
  externalsError,
}: Step2Props) => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-300 mb-4">
        Configure Trial Bonus Details
      </h3>

      {/* Bonus Name */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Bonus Name *
        </label>
        <Input
          type="text"
          value={formData.name}
          onChange={(e) => onInputChange('name', e.target.value)}
          placeholder="Enter bonus name..."
          error={errors.name}
        />
      </div>

      {/* External Bonus Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          External Bonus *
        </label>

        {isLoadingExternals ? (
          <div className="text-sm text-gray-400">
            Loading external bonuses...
          </div>
        ) : externalsError ? (
          <div className="text-sm text-red-400 flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            {externalsError}
          </div>
        ) : (
          <select
            value={formData.externalBonusId || ''}
            onChange={(e) => {
              const selectedId = e.target.value
                ? parseInt(e.target.value)
                : null;
              const selectedExternal = externals.find(
                (ext) => ext.id === selectedId
              );
              onInputChange('externalBonusId', selectedId);
              onInputChange('externalBonusName', selectedExternal?.name || '');
            }}
            className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select an external bonus...</option>
            {externals.map((external) => (
              <option key={external.id} value={external.id}>
                {external.name} (ID: {external.id})
              </option>
            ))}
          </select>
        )}
        {errors.externalBonusId && (
          <p className="mt-1 text-sm text-red-400">{errors.externalBonusId}</p>
        )}
      </div>

      {/* Amount */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Amount *
        </label>
        <NumericInput
          value={formData.amount}
          onChange={(value) => onInputChange('amount', value)}
          placeholder="0.00"
          min={0}
          step={0.01}
          error={errors.amount}
        />
      </div>

      {/* Expires At */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Expires At *
        </label>
        <Input
          type="datetime-local"
          value={formData.expiresAt}
          onChange={(e) => onInputChange('expiresAt', e.target.value)}
          error={errors.expiresAt}
        />
      </div>
    </div>
  );
};
