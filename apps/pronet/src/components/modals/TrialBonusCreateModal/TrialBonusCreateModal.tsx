import { useState, useCallback, useEffect } from 'react';
import { BonusCreateModal } from '@panels/ui';
import { useApi } from '../../../features/api/useApi';
import { useTrialBonusTemplates } from '../../../hooks/useTrialBonusTemplates';
import { useTrialBonusExternals } from '../../../hooks/useTrialBonusExternals';
import {
  TrialBonusCreateRequest,
  type TrialBonusCreateRequestOptions,
} from '@panels/api';
import type { TrialBonusTemplate } from '@panels/api';
import { Step1 } from './Step1';
import { Step2 } from './Step2';
import { Step3 } from './Step3';
import type { FormData, FormErrors, TrialBonusCreateModalProps } from './types';

const STEPS = [
  {
    id: 1,
    title: 'Template',
    description: 'Select template or start from scratch',
  },
  {
    id: 2,
    title: 'Details',
    description: 'Configure bonus information',
  },
  {
    id: 3,
    title: 'Rules',
    description: 'Set up bonus rules',
  },
];

export const TrialBonusCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: TrialBonusCreateModalProps) => {
  const { pronet } = useApi();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form data
  const [formData, setFormData] = useState<FormData>({
    templateId: 'empty',
    name: '',
    externalBonusId: null,
    externalBonusName: '',
    amount: 0,
    expiresAt: '',
    rules: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Load templates
  const {
    data: templates,
    isLoading: isLoadingTemplates,
    error: templatesError,
  } = useTrialBonusTemplates({
    page: 1,
    limit: 100,
    // filters: {},
    sortField: 'createdAt',
    sortDirection: 'desc',
  });

  // Load externals
  const {
    data: externals,
    isLoading: isLoadingExternals,
    error: externalsError,
  } = useTrialBonusExternals({
    page: 1,
    limit: 100,
    // filters: {},
    sortField: 'name',
    sortDirection: 'asc',
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
      setFormData({
        templateId: 'empty',
        name: '',
        externalBonusId: null,
        externalBonusName: '',
        amount: 0,
        expiresAt: '',
        rules: [],
      });
      setErrors({});
    }
  }, [isOpen]);

  // Handle input changes
  const handleInputChange = useCallback(
    (field: keyof FormData, value: any) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: undefined }));
      }
    },
    [errors]
  );

  // Template selection handlers
  const handleTemplateSelect = useCallback(
    (template: TrialBonusTemplate) => {
      setFormData((prev) => ({
        ...prev,
        templateId: template.id,
        name: template.bonusTemplate.name,
        externalBonusId: template.externalBonusId,
        externalBonusName: template.externalBonusName,
        amount: template.amount,
        rules: template.bonusTemplate.rules || [],
      }));
    },
    [externals]
  );

  const handleEmptyTemplateSelect = useCallback(() => {
    setFormData((prev) => ({
      ...prev,
      templateId: 'empty',
      name: '',
      externalBonusId: null,
      externalBonusName: '',
      amount: 0,
      rules: [],
    }));
  }, []);

  // Rule handlers
  const handleAddRule = useCallback(() => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: '',
          operator: '',
          firstOperand: '',
          secondOperand: null,
          startsAt: null,
          endsAt: null,
        },
      ],
    }));
  }, []);

  const handleRemoveRule = useCallback((index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  }, []);

  const handleUpdateRule = useCallback(
    (index: number, field: string, value: any) => {
      setFormData((prev) => ({
        ...prev,
        rules: prev.rules.map((rule, i) =>
          i === index ? { ...rule, [field]: value } : rule
        ),
      }));
    },
    []
  );

  // Validation
  const canGoNext = useCallback((step: number, data: FormData) => {
    switch (step) {
      case 1:
        return true; // Template selection is optional
      case 2:
        return !!(
          data.name.trim() &&
          data.externalBonusId &&
          data.amount > 0 &&
          data.expiresAt
        );
      case 3:
        return true; // Rules are optional
      default:
        return false;
    }
  }, []);

  // Submit handler
  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true);
    setErrors({});

    try {
      const requestOptions: TrialBonusCreateRequestOptions = {
        name: formData.name,
        externalBonusId: formData.externalBonusId!,
        externalBonusName: formData.externalBonusName,
        amount: formData.amount,
        expiresAt: new Date(formData.expiresAt),
        rules: formData.rules,
      };

      const request = new TrialBonusCreateRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        setErrors({ submit: response.error || 'Failed to create trial bonus' });
      }
    } catch (error) {
      setErrors({
        submit:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, pronet, onSuccess, onClose]);

  // Render step content
  const renderStepContent = useCallback(
    (stepProps: any) => {
      switch (currentStep) {
        case 1:
          return (
            <Step1
              {...stepProps}
              templates={templates}
              isLoadingTemplates={isLoadingTemplates}
              templatesError={templatesError}
              onTemplateSelect={handleTemplateSelect}
              onEmptyTemplateSelect={handleEmptyTemplateSelect}
            />
          );
        case 2:
          return (
            <Step2
              {...stepProps}
              externals={externals}
              isLoadingExternals={isLoadingExternals}
              externalsError={externalsError}
            />
          );
        case 3:
          return (
            <Step3
              {...stepProps}
              onAddRule={handleAddRule}
              onRemoveRule={handleRemoveRule}
              onUpdateRule={handleUpdateRule}
            />
          );
        default:
          return null;
      }
    },
    [
      currentStep,
      templates,
      isLoadingTemplates,
      templatesError,
      externals,
      isLoadingExternals,
      externalsError,
      handleTemplateSelect,
      handleEmptyTemplateSelect,
    ]
  );

  return (
    <BonusCreateModal
      isOpen={isOpen}
      onClose={onClose}
      steps={STEPS}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Trial Bonus"
      canGoNext={canGoNext}
      bonusType="trial bonus"
    >
      {renderStepContent}
    </BonusCreateModal>
  );
};
