import { useState, useCallback, useEffect } from 'react';
import { PromocodeCreateModal } from '@panels/ui';
import { AlertCircle } from 'lucide-react';
import { useApi } from '../../../features/api/useApi';
import { useTrialBonuses } from '../../../hooks/useTrialBonuses';
import {
  BonusPromocodeCreateRequest,
  type BonusPromocodeCreateRequestOptions,
} from '@panels/api';
import Input from '../../ui/Input';
import NumericInput from '../../ui/NumericInput';
import type {
  FormData,
  FormErrors,
  TrialBonusPromocodeCreateModalProps,
} from './types';

export const TrialBonusPromocodeCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: TrialBonusPromocodeCreateModalProps) => {
  const { pronet } = useApi();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form data
  const [formData, setFormData] = useState<FormData>({
    bonusId: null,
    code: '',
    maxActivations: null,
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Load trial bonuses
  const {
    data: bonuses,
    isLoading: isLoadingBonuses,
    error: bonusesError,
  } = useTrialBonuses({
    page: 1,
    limit: 100,
    // filters: {},
    sortField: 'name',
    sortDirection: 'asc',
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        bonusId: null,
        code: '',
        maxActivations: null,
      });
      setErrors({});
    }
  }, [isOpen]);

  // Handle input changes
  const handleInputChange = useCallback(
    (field: string | number | symbol, value: any) => {
      const fieldKey = field as keyof FormData;
      setFormData((prev) => ({ ...prev, [fieldKey]: value }));
      // Clear error when user starts typing
      if (errors[fieldKey]) {
        setErrors((prev) => ({ ...prev, [fieldKey]: undefined }));
      }
    },
    [errors]
  );

  // Validation
  const canSubmit = useCallback((data: FormData) => {
    return !!(data.bonusId && data.code.trim());
  }, []);

  // Submit handler
  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true);
    setErrors({});

    try {
      const requestOptions: BonusPromocodeCreateRequestOptions = {
        bonusId: formData.bonusId!,
        code: formData.code.trim(),
        maxActivations: formData.maxActivations,
      };

      const request = new BonusPromocodeCreateRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        // Handle API errors
        if (response.error?.includes('already exists')) {
          setErrors({ code: 'This promocode already exists' });
        } else {
          setErrors({ submit: response.error || 'Failed to create promocode' });
        }
      }
    } catch (error) {
      setErrors({
        submit:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, pronet, onSuccess, onClose]);

  return (
    <PromocodeCreateModal
      isOpen={isOpen}
      onClose={onClose}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Trial Bonus Promocode"
      canSubmit={canSubmit}
    >
      {({ formData, errors, onInputChange }) => (
        <div className="space-y-6">
          {/* Trial Bonus Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Trial Bonus *
            </label>

            {isLoadingBonuses ? (
              <div className="text-sm text-gray-400">
                Loading trial bonuses...
              </div>
            ) : bonusesError ? (
              <div className="text-sm text-red-400 flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                {bonusesError}
              </div>
            ) : (
              <select
                value={formData.bonusId || ''}
                onChange={(e) =>
                  onInputChange(
                    'bonusId',
                    e.target.value ? parseInt(e.target.value) : null
                  )
                }
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a trial bonus...</option>
                {bonuses.map((bonus) => (
                  <option key={bonus.id} value={bonus.bonus.id}>
                    {bonus.bonus.name} - ₺{bonus.amount.toFixed(2)} (
                    {bonus.externalBonusName})
                  </option>
                ))}
              </select>
            )}
            {errors.bonusId && (
              <p className="mt-1 text-sm text-red-400">{errors.bonusId}</p>
            )}
          </div>

          {/* Promocode */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Promocode *
            </label>
            <Input
              type="text"
              value={formData.code}
              onChange={(e) => onInputChange('code', e.target.value)}
              placeholder="Enter promocode..."
              error={errors.code}
            />
          </div>

          {/* Max Activations */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Max Activations
            </label>
            <NumericInput
              value={formData.maxActivations || ''}
              onChange={(value) =>
                onInputChange('maxActivations', value || null)
              }
              placeholder="Unlimited"
              min={1}
              step={1}
              error={errors.maxActivations}
            />
            <p className="mt-1 text-sm text-gray-400">
              Leave empty for unlimited activations
            </p>
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="text-sm text-red-400 flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              {errors.submit}
            </div>
          )}
        </div>
      )}
    </PromocodeCreateModal>
  );
};
