import { DetailsModal } from '@panels/ui';
import { Calendar, Clock, Users, Target } from 'lucide-react';
import type { LossbackBonus } from '@panels/api';

interface LossbackBonusDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  bonus: LossbackBonus | null;
}

export const LossbackBonusDetailsModal = ({
  isOpen,
  onClose,
  bonus,
}: LossbackBonusDetailsModalProps) => {
  if (!bonus) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Lossback Bonus Details"
      size="xl"
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Basic Information
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Bonus Type</span>
                <span className="text-gray-300 font-medium">
                  {bonus.bonus?.type || 'N/A'}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Max Balance</span>
                <span className="text-gray-300 font-medium">
                  ₺{bonus.maxBalance.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Lossback Percentage</span>
                <span className="text-gray-300 font-medium">
                  {bonus.lossbackPercentage}%
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Happy Hours</span>
                <span className="text-gray-300 font-medium">
                  {bonus.happyHoursStart} - {bonus.happyHoursEnd}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Happy Hours Boost</span>
                <span className="text-gray-300 font-medium">
                  {bonus.happyHoursBoostPercentage}%
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">
                  Deposit/Withdraw Threshold
                </span>
                <span className="text-gray-300 font-medium">
                  ₺{bonus.depositWithDrawDifferenceThreshold.toLocaleString()}
                </span>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div>
            <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Timestamps
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Created</span>
                <span className="text-gray-300 text-sm">
                  {formatDate(bonus.createdAt)}
                </span>
              </div>
              <div className="flex items-center justify-between py-2 border-b border-dark-600">
                <span className="text-gray-400">Last Updated</span>
                <span className="text-gray-300 text-sm">
                  {formatDate(bonus.updatedAt)}
                </span>
              </div>
              {bonus.bonus?.expiresAt && (
                <div className="flex items-center justify-between py-2 border-b border-dark-600">
                  <span className="text-gray-400">Expires</span>
                  <span className="text-gray-300 text-sm">
                    {formatDate(bonus.bonus.expiresAt)}
                  </span>
                </div>
              )}
              {bonus.bonus?.deletedAt && (
                <div className="flex items-center justify-between py-2 border-b border-dark-600">
                  <span className="text-gray-400">Deleted</span>
                  <span className="text-red-300 text-sm">
                    {formatDate(bonus.bonus.deletedAt)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Rules */}
        <div>
          <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
            <Users className="w-5 h-5 mr-2" />
            Eligibility Rules ({bonus.bonus?.rules?.length || 0})
          </h3>
          {!bonus.bonus?.rules || bonus.bonus.rules.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-500 mx-auto mb-3" />
              <p className="text-gray-400">No eligibility rules defined</p>
              <p className="text-sm text-gray-500 mt-1">
                This bonus is available to all customers
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {bonus.bonus.rules.map((rule, index) => (
                <div
                  key={rule.id}
                  className="bg-dark-700 rounded-lg p-4 border border-dark-600"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-gray-400">
                      Rule #{index + 1}
                    </span>
                    <span className="text-xs text-gray-500">ID: {rule.id}</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-400">Field:</span>
                      <span className="text-gray-300 font-medium">
                        {rule.criterium}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-400">Operator:</span>
                      <span className="text-gray-300 font-medium">
                        {rule.operator}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-400">Value:</span>
                      <span className="text-gray-300 font-medium">
                        {rule.firstOperand}
                      </span>
                    </div>
                    {rule.secondOperand && (
                      <div className="flex items-center space-x-2 text-sm">
                        <span className="text-gray-400">Second Value:</span>
                        <span className="text-gray-300 font-medium">
                          {rule.secondOperand}
                        </span>
                      </div>
                    )}
                    {(rule.startsAt || rule.endsAt) && (
                      <div className="flex items-center space-x-2 text-sm">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-400">
                          {rule.startsAt && rule.endsAt
                            ? `${formatDate(rule.startsAt)} - ${formatDate(
                                rule.endsAt
                              )}`
                            : rule.startsAt
                            ? `From ${formatDate(rule.startsAt)}`
                            : `Until ${formatDate(rule.endsAt!)}`}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </DetailsModal>
  );
};
