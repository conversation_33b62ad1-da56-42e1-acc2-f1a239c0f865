import React from 'react';
import { DetailsModal } from '@panels/ui';
import {
  Gift,
  Calendar,
  Settings,
  ExternalLink,
  Gamepad2,
  Shield,
} from 'lucide-react';
import type { FreespinBonusTemplate } from '@panels/api';

interface FreespinBonusTemplateDetailsModalNewProps {
  isOpen: boolean;
  onClose: () => void;
  template: FreespinBonusTemplate | null;
}

export const FreespinBonusTemplateDetailsModalNew: React.FC<
  FreespinBonusTemplateDetailsModalNewProps
> = ({ isOpen, onClose, template }) => {
  if (!template) return null;

  // Format date helper
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short',
    }).format(date);
  };

  // Format template values
  const formatTemplateValues = (template: FreespinBonusTemplate) => {
    const values = template.values;
    const freespins = values.nOfFreespins as number;
    const betAmount = values.betAmount as number;
    const maxWin = values.maxWin as number;

    return {
      freespins: freespins || 0,
      betAmount: betAmount || 0,
      maxWin: maxWin || 0,
    };
  };

  const values = formatTemplateValues(template);

  const detailsContent = (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
              <Gift className="h-4 w-4 mr-2" />
              Template Information
            </h4>
            <div className="bg-dark-700 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">ID:</span>
                <span className="text-sm font-mono text-gray-300">
                  #{template.id}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Name:</span>
                <span className="text-sm font-medium text-gray-300">
                  {template.bonusTemplate.name}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Provider:</span>
                <span className="text-sm text-gray-300">
                  {template.vendorName}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Games:</span>
                <span className="text-sm text-gray-300">
                  {template.gameIds.length} selected
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Configuration
            </h4>
            <div className="bg-dark-700 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Freespins:</span>
                <span className="text-sm font-medium text-gray-300">
                  {values.freespins}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Bet Amount:</span>
                <span className="text-sm text-gray-300">
                  ₺{values.betAmount.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Max Win:</span>
                <span className="text-sm text-gray-300">
                  ₺{values.maxWin.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Template Details */}
      <div>
        <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
          <Shield className="h-4 w-4 mr-2" />
          Template Details
        </h4>
        <div className="bg-dark-700 rounded-lg p-4 space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Template ID:</span>
            <span className="text-sm font-mono text-gray-300">
              #{template.bonusTemplate.id}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Active:</span>
            <span className="text-sm text-gray-300">
              {template.bonusTemplate.deletedAt === null ? 'Yes' : 'No'}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Provider ID:</span>
            <span className="text-sm font-mono text-gray-300">
              #{template.vendorId}
            </span>
          </div>
        </div>
      </div>

      {/* Timestamps */}
      <div>
        <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
          <Calendar className="h-4 w-4 mr-2" />
          Timestamps
        </h4>
        <div className="bg-dark-700 rounded-lg p-4 space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Created:</span>
            <span className="text-sm text-gray-300">
              {formatDate(new Date(template.bonusTemplate.createdAt))}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Updated:</span>
            <span className="text-sm text-gray-300">
              {formatDate(new Date(template.bonusTemplate.updatedAt))}
            </span>
          </div>
        </div>
      </div>

      {/* Rules */}
      {template.bonusTemplate.rules &&
        template.bonusTemplate.rules.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Rules ({template.bonusTemplate.rules.length})
            </h4>
            <div className="bg-dark-700 rounded-lg p-4">
              <div className="space-y-3">
                {template.bonusTemplate.rules.map(
                  (rule: any, index: number) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-dark-600 rounded-md"
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-xs font-mono bg-dark-500 px-2 py-1 rounded text-gray-400">
                          {index + 1}
                        </span>
                        <span className="text-sm text-gray-300">
                          {rule.field}
                        </span>
                        <span className="text-sm text-gray-400">
                          {rule.operator}
                        </span>
                        <span className="text-sm font-medium text-gray-300">
                          {rule.value}
                        </span>
                      </div>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        )}

      {/* Games List */}
      {template.gameIds && template.gameIds.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
            <Gamepad2 className="h-4 w-4 mr-2" />
            Games ({template.gameIds.length})
          </h4>
          <div className="bg-dark-700 rounded-lg p-4">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {template.gameIds.map((gameId: number) => (
                <div
                  key={gameId}
                  className="text-xs font-mono bg-dark-600 px-2 py-1 rounded text-gray-400 text-center"
                >
                  Game #{gameId}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Additional Values */}
      {template.values && Object.keys(template.values).length > 3 && (
        <div>
          <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
            <ExternalLink className="h-4 w-4 mr-2" />
            Additional Configuration
          </h4>
          <div className="bg-dark-700 rounded-lg p-4">
            <div className="space-y-2">
              {Object.entries(template.values)
                .filter(
                  ([key]) =>
                    !['nOfFreespins', 'betAmount', 'maxWin'].includes(key)
                )
                .map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm text-gray-400 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}:
                    </span>
                    <span className="text-sm text-gray-300">
                      {typeof value === 'object'
                        ? JSON.stringify(value)
                        : String(value)}
                    </span>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Template: ${template.bonusTemplate.name}`}
      size="lg"
    >
      {detailsContent}
    </DetailsModal>
  );
};
