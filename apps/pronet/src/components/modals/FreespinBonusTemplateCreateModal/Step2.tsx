import React from 'react';
import Input from '../../ui/Input';
import NumericInput from '../../ui/NumericInput';
import { Step2Props } from './types';

export const Step2 = ({
  formData,
  errors,
  onInputChange,
  providers,
  selectedProviderSchema,
  getSchemaFields,
  renderSchemaField,
}: Step2Props) => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-300 mb-4">
        Template Configuration
      </h3>

      {/* Selected Provider and Games Summary */}
      <div className="bg-dark-700 border border-dark-600 rounded-md p-4">
        <h4 className="text-sm font-medium text-gray-300 mb-2">
          Selected Configuration
        </h4>
        <div className="text-sm text-gray-400 space-y-1">
          <p>
            Provider:{' '}
            {providers.find((p) => p.providerId === formData.providerId)
              ?.providerName || 'Unknown'}
          </p>
          <p>Games: {formData.gameIds.length} selected</p>
        </div>
      </div>

      {/* Required Template Fields */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-300">Required Fields</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Template Name */}
          <div>
            <Input
              label="Template Name *"
              type="text"
              value={formData.name || ''}
              onChange={(e) => onInputChange('name', e.target.value)}
              placeholder="Enter template name..."
              error={errors.name}
            />
          </div>

          {/* Valid For Days */}
          <div>
            <NumericInput
              label="Valid For Days *"
              min={1}
              value={formData.validForDays || 0}
              onChange={(value) => onInputChange('validForDays', value)}
              placeholder="Enter number of days..."
              error={errors.validForDays}
              allowDecimals={false}
            />
          </div>
        </div>
      </div>

      {/* Dynamic Schema Fields */}
      {selectedProviderSchema && (
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-300">
            Provider Configuration
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {getSchemaFields()
              .map((field) => renderSchemaField(field))
              .filter(Boolean)}
          </div>
        </div>
      )}
    </div>
  );
};
