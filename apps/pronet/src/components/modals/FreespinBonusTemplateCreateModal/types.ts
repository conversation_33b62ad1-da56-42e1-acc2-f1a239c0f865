import React from 'react';
import {
  type FreespinBonusTemplateRuleCreateOptions,
  type FreespinBonusProvider,
  type FreespinBonusGame,
  type FreespinBonusBetAmount,
  type Currency,
} from '@panels/api';

export interface FormData {
  // Step 1: Provider & Games
  providerId: number | null;
  gameIds: number[];
  currencyId: number | null;

  // Step 2: Basic Information & Schema Fields
  name: string;
  validForDays: number;
  [key: string]: any; // Dynamic fields from JSON schema

  // Step 3: Rules
  rules: FreespinBonusTemplateRuleCreateOptions[];
}

export interface FormErrors {
  [key: string]: string;
}

export interface FreespinBonusTemplateCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface StepProps {
  formData: FormData;
  errors: FormErrors;
  onInputChange: (field: keyof FormData, value: any) => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

export interface Step1Props extends StepProps {
  providers: FreespinBonusProvider[];
  isLoadingProviders: boolean;
  providersError: string | null;
  games: FreespinBonusGame[];
  isLoadingGames: boolean;
  gamesError: string | null;
  gameSearchTerm: string;
  onGameSearchChange: (term: string) => void;
  onProviderSelect: (provider: FreespinBonusProvider) => void;
}

export interface Step2Props extends StepProps {
  providers: FreespinBonusProvider[];
  currencies: Currency[];
  isLoadingCurrencies: boolean;
  currenciesError: string | null;
  betAmounts: FreespinBonusBetAmount[];
  isLoadingBetAmounts: boolean;
  selectedProviderSchema: Record<string, any> | null;
  getSchemaFields: () => any[];
  renderSchemaField: (field: any) => React.ReactNode;
}

export interface Step3Props extends StepProps {
  onAddRule: () => void;
  onRemoveRule: (index: number) => void;
  onUpdateRule: (
    index: number,
    field: keyof FreespinBonusTemplateRuleCreateOptions,
    value: any
  ) => void;
}

export const STEPS = [
  { id: 1, title: 'Provider & Games', description: 'Pick provider and games' },
  { id: 2, title: 'Configuration', description: 'Template info and settings' },
  { id: 3, title: 'Rules', description: 'Define template rules' },
];

// Import shared rule constants
export {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
  type RuleField,
} from '../../../constants/bonusRules';
