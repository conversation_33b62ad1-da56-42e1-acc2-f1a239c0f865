import { RulesStep } from '@panels/ui';
import { Step3Props, RULE_FIELDS, RULE_OPERATORS } from './types';

export const Step3 = ({
  formData,
  onAddRule,
  onRemoveRule,
  onUpdateRule,
}: Step3Props) => {
  return (
    <RulesStep
      rules={formData.rules}
      ruleFields={RULE_FIELDS}
      ruleOperators={RULE_OPERATORS}
      onAddRule={onAddRule}
      onRemoveRule={onRemoveRule}
      onUpdateRule={onUpdateRule}
      title="Template Rules"
      emptyStateTitle="No rules defined"
      emptyStateDescription="The template will have no restrictions."
      variant="compact"
    />
  );
};
