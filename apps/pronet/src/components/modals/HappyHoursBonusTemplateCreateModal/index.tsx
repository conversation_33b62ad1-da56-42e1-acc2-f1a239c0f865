import { useState } from 'react';
import { BonusTemplateCreateModal } from '@panels/ui';
import { Step1 } from './Step1';
import { Step2 } from './Step2';
import { useApi } from '../../../features/api/useApi';
import { HappyHoursBonusTemplateCreateRequest } from '@panels/api';
import type {
  FormData,
  FormErrors,
  HappyHoursBonusTemplateCreateModalProps,
} from './types';
import { STEPS } from './types';

export const HappyHoursBonusTemplateCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: HappyHoursBonusTemplateCreateModalProps) => {
  const { pronet } = useApi();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form data
  const [formData, setFormData] = useState<FormData>({
    name: '',
    externalBonusName: '',
    externalBonusId: 0,
    amount: 0,
    rules: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const handleClose = () => {
    setCurrentStep(1);
    setFormData({
      name: '',
      externalBonusName: '',
      externalBonusId: 0,
      amount: 0,
      rules: [],
    });
    setErrors({});
    onClose();
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  // Rule handlers
  const handleAddRule = () => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: 'ipConflictCount',
          operator: 'lte',
          firstOperand: '2',
          secondOperand: null,
          startsInSeconds: null,
          endsInSeconds: null,
        },
      ],
    }));
  };

  const handleRemoveRule = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  };

  const handleUpdateRule = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.map((rule, i) =>
        i === index ? { ...rule, [field]: value } : rule
      ),
    }));
  };

  // Validation
  const validateStep = (step: number): FormErrors => {
    const newErrors: FormErrors = {};

    if (step === 1) {
      if (!formData.name.trim()) {
        newErrors.name = 'Template name is required';
      }
      if (!formData.externalBonusName.trim()) {
        newErrors.externalBonusName = 'External bonus name is required';
      }
      if (formData.externalBonusId <= 0) {
        newErrors.externalBonusId = 'External bonus ID must be greater than 0';
      }
      if (formData.amount <= 0) {
        newErrors.amount = 'Amount must be greater than 0';
      }
    }

    return newErrors;
  };



  const handleSubmit = async () => {
    // Validate all steps
    const allErrors = validateStep(1);
    if (Object.keys(allErrors).length > 0) {
      setErrors(allErrors);
      setCurrentStep(1);
      return;
    }

    setIsSubmitting(true);

    try {
      const request = new HappyHoursBonusTemplateCreateRequest({
        name: formData.name.trim(),
        externalBonusName: formData.externalBonusName.trim(),
        externalBonusId: formData.externalBonusId,
        amount: formData.amount,
        rules: formData.rules,
      });

      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        handleClose();
      } else {
        setErrors({ name: response.error || 'Failed to create template' });
      }
    } catch (error) {
      setErrors({
        name: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Step1
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
          />
        );
      case 2:
        return (
          <Step2
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
            onAddRule={handleAddRule}
            onRemoveRule={handleRemoveRule}
            onUpdateRule={handleUpdateRule}
          />
        );
      default:
        return null;
    }
  };

  return (
    <BonusTemplateCreateModal
      isOpen={isOpen}
      onClose={handleClose}
      steps={STEPS}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Happy Hours Bonus Template"
      canGoNext={(step, data) => {
        if (step >= STEPS.length) return false;
        const stepErrors = validateStep(step);
        return Object.keys(stepErrors).length === 0;
      }}
      canGoPrevious={(step) => step > 1}
      bonusType="Happy Hours Bonus Template"
    >
      {() => renderStep()}
    </BonusTemplateCreateModal>
  );
};
