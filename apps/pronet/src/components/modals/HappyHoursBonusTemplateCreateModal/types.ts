import { type HappyHoursBonusTemplateRuleCreateOptions } from '@panels/api';

export interface FormData {
  // Step 1: Basic Information
  name: string;
  externalBonusName: string;
  externalBonusId: number;
  amount: number;

  // Step 2: Rules
  rules: HappyHoursBonusTemplateRuleCreateOptions[];
}

export interface FormErrors {
  [key: string]: string;
}

export interface HappyHoursBonusTemplateCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface StepProps {
  formData: FormData;
  errors: FormErrors;
  onInputChange: (field: keyof FormData, value: any) => void;
}

export interface Step2Props extends StepProps {
  onAddRule: () => void;
  onRemoveRule: (index: number) => void;
  onUpdateRule: (index: number, field: string, value: any) => void;
}

export const STEPS = [
  {
    id: 1,
    title: 'Basic Information',
    description: 'Configure template details',
  },
  {
    id: 2,
    title: 'Rules',
    description: 'Set up template rules and conditions',
  },
];

// Import shared rule constants
export {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
} from '../../../constants/bonusRules';
