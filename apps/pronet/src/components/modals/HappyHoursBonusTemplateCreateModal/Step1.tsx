import { DollarSign, Hash, Tag } from 'lucide-react';
import Input from '../../ui/Input';
import NumericInput from '../../ui/NumericInput';
import { StepProps } from './types';

export const Step1 = ({ formData, errors, onInputChange }: StepProps) => {
  const handleChange = (field: keyof typeof formData, value: any) => {
    onInputChange(field, value);
  };

  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
          <DollarSign className="w-5 h-5 mr-2" />
          Basic Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <Input
              label="Template Name *"
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder="Enter happy hours bonus template name..."
              error={errors.name}
              helperText="A descriptive name for this happy hours bonus template"
            />
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <Tag className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                label="External Bonus Name *"
                type="text"
                value={formData.externalBonusName}
                onChange={(e) => handleChange('externalBonusName', e.target.value)}
                placeholder="HAPPY_HOURS_TEMPLATE"
                className="pl-10"
                error={errors.externalBonusName}
                helperText="External system identifier name"
              />
            </div>
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <Hash className="w-4 h-4 text-gray-500" />
              </div>
              <NumericInput
                label="External Bonus ID *"
                min={1}
                step={1}
                value={formData.externalBonusId || 0}
                onChange={(value) => handleChange('externalBonusId', value)}
                placeholder="54321"
                className="pl-10"
                error={errors.externalBonusId}
                helperText="External system identifier number"
                allowDecimals={false}
              />
            </div>
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <span className="text-gray-500">₺</span>
              </div>
              <NumericInput
                label="Amount *"
                min={0}
                step={0.01}
                value={formData.amount || 0}
                onChange={(value) => handleChange('amount', value)}
                placeholder="0.00"
                className="pl-8"
                error={errors.amount}
                helperText="Template bonus amount value"
                allowDecimals={true}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
