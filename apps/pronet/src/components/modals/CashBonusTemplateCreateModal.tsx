import React, { useCallback, useState } from 'react';
import { BonusTemplateCreateModal, RulesStep } from '@panels/ui';
import { useApi } from '../../features/api/useApi';
import {
  CashBonusTemplateCreateRequest,
  type CashBonusTemplateCreateRequestOptions,
} from '@panels/api';
import {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
} from '../../constants/bonusRules';

// Define the rule creation options type
type BonusRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsAt: Date | null;
  endsAt: Date | null;
};

interface CashBonusTemplateCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface CashBonusTemplateFormData {
  name: string;
  cashAmount: number;
  expiresAt?: Date;
  isActive: boolean;
  rules: BonusRuleCreateOptions[];
}

interface CashBonusTemplateFormErrors {
  name?: string;
  cashAmount?: string;
  expiresAt?: string;
  rules?: string;
  submit?: string;
}

export const CashBonusTemplateCreateModal: React.FC<
  CashBonusTemplateCreateModalProps
> = ({ isOpen, onClose, onSuccess }) => {
  const { pronet } = useApi();

  // Form validation
  const validateForm = useCallback(
    (data: CashBonusTemplateFormData): CashBonusTemplateFormErrors => {
      const errors: CashBonusTemplateFormErrors = {};

      if (!data.name.trim()) {
        errors.name = 'Template name is required';
      }

      if (!data.cashAmount || data.cashAmount <= 0) {
        errors.cashAmount = 'Cash amount must be greater than 0';
      }

      if (data.expiresAt && data.expiresAt <= new Date()) {
        errors.expiresAt = 'Expiry date must be in the future';
      }

      if (!data.rules || data.rules.length === 0) {
        errors.rules = 'At least one rule is required';
      }

      return errors;
    },
    []
  );

  // Submit handler
  const handleSubmit = useCallback(
    async (data: CashBonusTemplateFormData): Promise<void> => {
      const requestOptions: CashBonusTemplateCreateRequestOptions = {
        name: data.name,
        cashAmount: data.cashAmount,
        rules: data.rules,
        expiresAt: data.expiresAt,
        isActive: data.isActive,
      };

      const request = new CashBonusTemplateCreateRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (!response.success) {
        throw new Error(
          response.error || 'Failed to create cash bonus template'
        );
      }

      onSuccess();
    },
    [pronet, onSuccess]
  );

  // Rule handlers
  const handleAddRule = useCallback(() => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: '',
          operator: '',
          firstOperand: '',
          secondOperand: null,
          startsAt: null,
          endsAt: null,
        },
      ],
    }));
  }, []);

  const handleRemoveRule = useCallback((index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  }, []);

  const handleUpdateRule = useCallback(
    (index: number, field: string, value: any) => {
      setFormData((prev) => ({
        ...prev,
        rules: prev.rules.map((rule, i) =>
          i === index ? { ...rule, [field]: value } : rule
        ),
      }));
    },
    []
  );

  const steps = [
    {
      id: 1,
      title: 'Template Details',
      description: 'Configure cash bonus template settings',
    },
    {
      id: 2,
      title: 'Rules & Conditions',
      description: 'Set up default bonus rules and eligibility',
    },
  ];

  // State for the modal
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<CashBonusTemplateFormData>({
    name: '',
    cashAmount: 0,
    isActive: true,
    rules: [],
  });
  const [errors, setErrors] = useState<CashBonusTemplateFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Input change handler
  const handleInputChange = useCallback(
    (field: keyof CashBonusTemplateFormData, value: any) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
      // Clear error when user starts typing
      if (errors[field as keyof CashBonusTemplateFormErrors]) {
        setErrors((prev) => ({
          ...prev,
          [field as keyof CashBonusTemplateFormErrors]: undefined,
        }));
      }
    },
    [errors]
  );

  // Submit handler
  const handleSubmitForm = useCallback(async () => {
    setIsSubmitting(true);
    try {
      await handleSubmit(formData);
      onClose();
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        submit:
          error instanceof Error
            ? error.message
            : 'Failed to create cash bonus template',
      }));
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, handleSubmit, onClose]);

  // Render current step content
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-300 mb-4">
                Template Details
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Template Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter template name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-400">{errors.name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Default Cash Amount
                  </label>
                  <input
                    type="number"
                    value={formData.cashAmount}
                    onChange={(e) =>
                      handleInputChange(
                        'cashAmount',
                        parseFloat(e.target.value) || 0
                      )
                    }
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                  />
                  {errors.cashAmount && (
                    <p className="mt-1 text-sm text-red-400">
                      {errors.cashAmount}
                    </p>
                  )}
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) =>
                      handleInputChange('isActive', e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-dark-600 rounded bg-dark-700"
                  />
                  <label
                    htmlFor="isActive"
                    className="ml-2 block text-sm text-gray-300"
                  >
                    Active template
                  </label>
                </div>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <RulesStep
            rules={formData.rules}
            errors={errors}
            ruleFields={RULE_FIELDS}
            ruleOperators={RULE_OPERATORS}
            onAddRule={handleAddRule}
            onRemoveRule={handleRemoveRule}
            onUpdateRule={handleUpdateRule}
            title="Cash Bonus Template Rules"
            description="Define default eligibility rules for bonuses created from this template"
            emptyStateTitle="No rules defined"
            emptyStateDescription="Add rules to control who can use this template, or leave empty to allow all users."
            variant="full"
          />
        );

      default:
        return <div>Invalid step</div>;
    }
  };

  return (
    <BonusTemplateCreateModal
      isOpen={isOpen}
      onClose={onClose}
      steps={steps}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmitForm}
      submitButtonText="Create Cash Bonus Template"
      bonusType="cash bonus template"
    >
      {() => renderCurrentStep()}
    </BonusTemplateCreateModal>
  );
};
