import React from 'react';
import { DetailsModal } from '@panels/ui';
import {
  Gift,
  Calendar,
  User,
  ExternalLink,
  Settings,
  Clock,
} from 'lucide-react';
import type { FreespinBonus } from '@panels/api';

interface FreespinBonusDetailsModalNewProps {
  isOpen: boolean;
  onClose: () => void;
  bonus: FreespinBonus | null;
}

export const FreespinBonusDetailsModalNew: React.FC<FreespinBonusDetailsModalNewProps> = ({
  isOpen,
  onClose,
  bonus,
}) => {
  if (!bonus) return null;

  // Format date helper
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short',
    }).format(date);
  };

  // Format bonus values
  const formatBonusValues = (bonus: FreespinBonus) => {
    const values = bonus.values;
    const freespins = values.nOfFreespins as number;
    const betAmount = values.betAmount as number;
    const maxWin = values.maxWin as number;

    return {
      freespins: freespins || 0,
      betAmount: betAmount || 0,
      maxWin: maxWin || 0,
    };
  };

  const values = formatBonusValues(bonus);

  const detailsContent = (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
              <Gift className="h-4 w-4 mr-2" />
              Bonus Information
            </h4>
            <div className="bg-dark-700 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">ID:</span>
                <span className="text-sm font-mono text-gray-300">
                  #{bonus.id}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">
                  Provider:
                </span>
                <span className="text-sm font-medium text-gray-300">
                  {bonus.vendorName}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Games:</span>
                <span className="text-sm text-gray-300">
                  {bonus.gameIds.length} selected
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Configuration
            </h4>
            <div className="bg-dark-700 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">
                  Freespins:
                </span>
                <span className="text-sm font-medium text-gray-300">
                  {values.freespins}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">
                  Bet Amount:
                </span>
                <span className="text-sm text-gray-300">
                  ₺{values.betAmount.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">
                  Max Win:
                </span>
                <span className="text-sm text-gray-300">
                  ₺{values.maxWin.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bonus Details */}
      <div>
        <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
          <User className="h-4 w-4 mr-2" />
          Bonus Details
        </h4>
        <div className="bg-dark-700 rounded-lg p-4 space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Bonus ID:</span>
            <span className="text-sm font-mono text-gray-300">
              #{bonus.bonusId}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Active:</span>
            <span className="text-sm text-gray-300">
              {bonus.bonus.isActive ? 'Yes' : 'No'}
            </span>
          </div>
          {bonus.bonus.expiresAt && (
            <div className="flex justify-between">
              <span className="text-sm text-gray-400">
                Expires At:
              </span>
              <span className="text-sm text-gray-300">
                {formatDate(new Date(bonus.bonus.expiresAt))}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Timestamps */}
      <div>
        <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
          <Clock className="h-4 w-4 mr-2" />
          Timestamps
        </h4>
        <div className="bg-dark-700 rounded-lg p-4 space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Created:</span>
            <span className="text-sm text-gray-300">
              {formatDate(new Date(bonus.bonus.createdAt))}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Updated:</span>
            <span className="text-sm text-gray-300">
              {formatDate(new Date(bonus.bonus.updatedAt))}
            </span>
          </div>
        </div>
      </div>

      {/* Rules */}
      {bonus.bonus.rules && bonus.bonus.rules.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
            <Settings className="h-4 w-4 mr-2" />
            Rules ({bonus.bonus.rules.length})
          </h4>
          <div className="bg-dark-700 rounded-lg p-4">
            <div className="space-y-3">
              {bonus.bonus.rules.map((rule: any, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-dark-600 rounded-md"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-xs font-mono bg-dark-500 px-2 py-1 rounded text-gray-400">
                      {index + 1}
                    </span>
                    <span className="text-sm text-gray-300">
                      {rule.field}
                    </span>
                    <span className="text-sm text-gray-400">
                      {rule.operator}
                    </span>
                    <span className="text-sm font-medium text-gray-300">
                      {rule.value}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Games List */}
      {bonus.gameIds && bonus.gameIds.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
            <ExternalLink className="h-4 w-4 mr-2" />
            Games ({bonus.gameIds.length})
          </h4>
          <div className="bg-dark-700 rounded-lg p-4">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {bonus.gameIds.map((gameId: number) => (
                <div
                  key={gameId}
                  className="text-xs font-mono bg-dark-600 px-2 py-1 rounded text-gray-400 text-center"
                >
                  Game #{gameId}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Freespin Bonus Details - ${bonus.bonus.name || 'Unnamed'}`}
      size="lg"
    >
      {detailsContent}
    </DetailsModal>
  );
};
