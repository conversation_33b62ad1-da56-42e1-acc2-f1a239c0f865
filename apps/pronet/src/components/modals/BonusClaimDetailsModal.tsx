import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, User, Gift, Calendar, Tag, ExternalLink } from 'lucide-react';
import Button from '../ui/Button';
import type { BonusClaim } from '@panels/api';

interface BonusClaimDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  claim: BonusClaim | null;
}

export const BonusClaimDetailsModal = ({
  isOpen,
  onClose,
  claim,
}: BonusClaimDetailsModalProps) => {
  if (!claim) return null;

  // Format date helper
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short',
    }).format(date);
  };

  // Format source badge
  const formatSource = (source: string) => {
    const sourceColors: Record<string, string> = {
      manual: 'bg-blue-900 text-blue-300 border-blue-700',
      automatic: 'bg-green-900 text-green-300 border-green-700',
      promocode: 'bg-purple-900 text-purple-300 border-purple-700',
      admin: 'bg-orange-900 text-orange-300 border-orange-700',
    };

    const colorClass =
      sourceColors[source.toLowerCase()] ||
      'bg-gray-900 text-gray-300 border-gray-700';

    return (
      <span
        className={`px-3 py-1 rounded-full text-sm font-medium border ${colorClass}`}
      >
        {source.charAt(0).toUpperCase() + source.slice(1)}
      </span>
    );
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-lg bg-dark-800 border border-dark-600 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-300"
                  >
                    Bonus Claim Details
                  </Dialog.Title>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="p-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Content */}
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                          <Tag className="h-4 w-4 mr-2" />
                          Claim Information
                        </h4>
                        <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">ID:</span>
                            <span className="text-sm font-mono text-gray-300">
                              #{claim.id}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">
                              Source:
                            </span>
                            <div>{formatSource(claim.source)}</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                          <User className="h-4 w-4 mr-2" />
                          Customer Information
                        </h4>
                        <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">
                              Username:
                            </span>
                            <span className="text-sm font-medium text-gray-300">
                              {claim.customer.username}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">
                              External ID:
                            </span>
                            <span className="text-sm font-mono text-gray-300">
                              {claim.customer.externalId}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Bonus Information */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                      <Gift className="h-4 w-4 mr-2" />
                      Bonus Information
                    </h4>
                    <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Bonus ID:</span>
                        <span className="text-sm font-mono text-gray-300">
                          #{claim.bonusId}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Name:</span>
                        <span className="text-sm font-medium text-gray-300">
                          {claim.bonus?.name}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">Type:</span>
                        <span className="text-sm text-gray-300 capitalize">
                          {claim.bonus?.type}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Timestamps */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      Timeline
                    </h4>
                    <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">
                          Claimed At:
                        </span>
                        <span className="text-sm text-gray-300">
                          {formatDate(claim.createdAt)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-400">
                          Last Updated:
                        </span>
                        <span className="text-sm text-gray-300">
                          {formatDate(claim.updatedAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="mt-6 flex justify-end space-x-3">
                  <Button variant="outline" onClick={onClose}>
                    Close
                  </Button>
                  <Button
                    variant="primary"
                    onClick={() => {
                      // TODO: Navigate to full details page if needed
                      console.log('Navigate to full details:', claim.id);
                    }}
                    className="flex items-center gap-2"
                  >
                    <ExternalLink className="h-4 w-4" />
                    View Full Details
                  </Button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
