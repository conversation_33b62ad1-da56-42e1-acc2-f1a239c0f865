import { AlertCircle, Search } from 'lucide-react';
import Input from '../../ui/Input';
import { Step2Props } from './types';

export const Step2 = ({
  formData,
  errors,
  onInputChange,
  providers,
  isLoadingProviders,
  providersError,
  games,
  isLoadingGames,
  gamesError,
  gameSearchTerm,
  onGameSearchChange,
  onProviderSelect,
}: Step2Props) => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-300 mb-4">
        Select Provider and Games
      </h3>

      {/* Provider Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Game Provider *
        </label>
        {isLoadingProviders ? (
          <div className="text-sm text-gray-400">Loading providers...</div>
        ) : providersError ? (
          <div className="text-sm text-red-400 flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            {providersError}
          </div>
        ) : (
          <select
            value={formData.providerId || ''}
            onChange={(e) => {
              const providerId = parseInt(e.target.value);
              const provider = providers.find(
                (p) => p.providerId === providerId
              );
              if (provider) {
                onProviderSelect(provider);
              }
            }}
            className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.providerId ? 'border-red-500' : 'border-dark-600'
            }`}
          >
            <option value="">Select a provider...</option>
            {providers.map((provider) => (
              <option key={provider.providerId} value={provider.providerId}>
                {provider.providerName}
              </option>
            ))}
          </select>
        )}
        {errors.providerId && (
          <p className="mt-1 text-sm text-red-400">{errors.providerId}</p>
        )}
      </div>

      {/* Game Selection */}
      {formData.providerId && (
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Games * ({formData.gameIds.length} selected)
          </label>

          {/* Game Search */}
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10" />
            <Input
              type="text"
              value={gameSearchTerm}
              onChange={(e) => onGameSearchChange(e.target.value)}
              placeholder="Search games..."
              className="pl-10"
            />
          </div>

          {isLoadingGames ? (
            <div className="text-sm text-gray-400">Loading games...</div>
          ) : gamesError ? (
            <div className="text-sm text-red-400 flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              {gamesError}
            </div>
          ) : games.length === 0 ? (
            <div className="text-center py-8 text-gray-400 border border-dark-600 rounded-md">
              <p>No games found for this provider.</p>
            </div>
          ) : (
            <div className="h-64 overflow-y-auto border border-dark-600 rounded-md bg-dark-700">
              {games.map((game) => (
                <label
                  key={game.id}
                  className="flex items-center p-3 hover:bg-dark-600 cursor-pointer border-b border-dark-600 last:border-b-0"
                >
                  <input
                    type="checkbox"
                    checked={formData.gameIds.includes(game.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        onInputChange('gameIds', [
                          ...formData.gameIds,
                          game.id,
                        ]);
                      } else {
                        onInputChange(
                          'gameIds',
                          formData.gameIds.filter((id) => id !== game.id)
                        );
                      }
                    }}
                    className="rounded border-dark-500 bg-dark-600 text-blue-600 focus:ring-blue-500 mr-3"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-300">
                      {game.name}
                    </div>
                    <div className="text-xs text-gray-400">
                      {game.providerName}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          )}
          {errors.gameIds && (
            <p className="mt-1 text-sm text-red-400">{errors.gameIds}</p>
          )}
        </div>
      )}
    </div>
  );
};
