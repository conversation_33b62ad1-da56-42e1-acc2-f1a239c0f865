import React, { useState, useCallback, useEffect } from 'react';
import { BonusCreateModal } from '@panels/ui';
import { useApi } from '../../../features/api/useApi';
import {
  FreespinBonusCreateRequest,
  type FreespinBonusCreateRequestOptions,
  type FreespinBonusRuleCreateOptions,
  FreespinBonusTemplateSearchRequest,
  type FreespinBonusTemplate,
  FreespinBonusProviderSearchRequest,
  type FreespinBonusProvider,
  FreespinBonusGameSearchRequest,
  type FreespinBonusGame,
  FreespinBonusBetAmountSearchRequest,
  type FreespinBonusBetAmount,
  FreespinBonusCurrencySearchRequest,
  type Currency,
} from '@panels/api';
import { Step1 } from './Step1';
import { Step2 } from './Step2';
import { Step3 } from './Step3';
import { Step4 } from './Step4';
import NumericInput from '../../ui/NumericInput';
import {
  type FormData,
  type FormErrors,
  type FreespinBonusCreateModalProps,
  STEPS,
} from './types';

export const FreespinBonusCreateModal: React.FC<
  FreespinBonusCreateModalProps
> = ({ isOpen, onClose, onSuccess }) => {
  const { pronet } = useApi();

  // Modal state
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState<FormData>({
    templateId: null,
    providerId: null,
    gameIds: [],
    currencyId: null,
    name: '',
    expiresAt: '',
    rules: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Data loading states
  const [templates, setTemplates] = useState<FreespinBonusTemplate[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const [templatesError, setTemplatesError] = useState<string | null>(null);

  const [providers, setProviders] = useState<FreespinBonusProvider[]>([]);
  const [isLoadingProviders, setIsLoadingProviders] = useState(false);
  const [providersError, setProvidersError] = useState<string | null>(null);

  const [games, setGames] = useState<FreespinBonusGame[]>([]);
  const [isLoadingGames, setIsLoadingGames] = useState(false);
  const [gamesError, setGamesError] = useState<string | null>(null);
  const [gameSearchTerm, setGameSearchTerm] = useState('');

  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [isLoadingCurrencies, setIsLoadingCurrencies] = useState(false);
  const [currenciesError, setCurrenciesError] = useState<string | null>(null);

  const [betAmounts, setBetAmounts] = useState<FreespinBonusBetAmount[]>([]);
  const [isLoadingBetAmounts, setIsLoadingBetAmounts] = useState(false);

  const [selectedProviderSchema, setSelectedProviderSchema] = useState<Record<
    string,
    any
  > | null>(null);

  // Reset form when modal closes
  const handleClose = useCallback(() => {
    setCurrentStep(1);
    setIsSubmitting(false);
    setFormData({
      templateId: null,
      providerId: null,
      gameIds: [],
      currencyId: null,
      name: '',
      expiresAt: '',
      rules: [],
    });
    setErrors({});
    setGameSearchTerm('');
    onClose();
  }, [onClose]);

  // Handle form input changes
  const handleInputChange = useCallback(
    (field: keyof FormData, value: any) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));

      // Clear error for this field when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({
          ...prev,
          [field]: '',
        }));
      }
    },
    [errors]
  );

  // Load templates when modal opens
  useEffect(() => {
    if (isOpen && currentStep === 1) {
      setIsLoadingTemplates(true);
      setTemplatesError(null);

      const loadTemplates = async () => {
        try {
          const request = new FreespinBonusTemplateSearchRequest({
            limit: 100,
          });
          const response = await pronet.makeRequest(request);

          if (response.success) {
            setTemplates(response.data.items);
          } else {
            setTemplatesError(response.error || 'Failed to load templates');
          }
        } catch (error) {
          setTemplatesError('Failed to load templates');
        } finally {
          setIsLoadingTemplates(false);
        }
      };

      loadTemplates();
    }
  }, [isOpen, currentStep, pronet]);

  // Load providers when moving to step 2
  useEffect(() => {
    if (isOpen && currentStep === 2) {
      setIsLoadingProviders(true);
      setProvidersError(null);

      const loadProviders = async () => {
        try {
          const request = new FreespinBonusProviderSearchRequest();
          const response = await pronet.makeRequest(request);

          if (response.success) {
            setProviders(response.data);

            const selectedProvider = response.data.find(
              (p) => p.providerId === formData.providerId
            );
            if (selectedProvider) {
              setSelectedProviderSchema(
                selectedProvider.steps[0]?.schema || null
              );
            }
          } else {
            setProvidersError(response.error || 'Failed to load providers');
          }
        } catch (error) {
          setProvidersError('Failed to load providers');
        } finally {
          setIsLoadingProviders(false);
        }
      };

      loadProviders();
    }
  }, [isOpen, currentStep, pronet, formData.providerId]);

  // Load games when provider is selected
  useEffect(() => {
    if (formData.providerId) {
      setIsLoadingGames(true);
      setGamesError(null);

      const loadGames = async () => {
        try {
          const request = new FreespinBonusGameSearchRequest({
            providerId: formData.providerId!,
            name: gameSearchTerm,
            limit: 100,
          });
          const response = await pronet.makeRequest(request);

          if (response.success) {
            setGames(response.data);
          } else {
            setGamesError(response.error || 'Failed to load games');
          }
        } catch (error) {
          setGamesError('Failed to load games');
        } finally {
          setIsLoadingGames(false);
        }
      };

      loadGames();
    }
  }, [formData.providerId, gameSearchTerm, pronet]);

  // Load currencies and bet amounts when moving to step 3
  useEffect(() => {
    if (isOpen && currentStep === 3) {
      setIsLoadingCurrencies(true);
      setCurrenciesError(null);

      const loadCurrencies = async () => {
        try {
          const request = new FreespinBonusCurrencySearchRequest({
            providerId: formData.providerId!,
          });
          const response = await pronet.makeRequest(request);

          if (response.success) {
            setCurrencies(response.data);
          } else {
            setCurrenciesError(response.error || 'Failed to load currencies');
          }
        } catch (error) {
          setCurrenciesError('Failed to load currencies');
        } finally {
          setIsLoadingCurrencies(false);
        }
      };

      if (formData.providerId) {
        loadCurrencies();

        setIsLoadingBetAmounts(true);
        const loadBetAmounts = async () => {
          try {
            const request = new FreespinBonusBetAmountSearchRequest({
              providerId: formData.providerId!,
              gameIds: formData.gameIds,
              currencyId: formData.currencyId!,
            });
            const response = await pronet.makeRequest(request);

            if (response.success) {
              setBetAmounts(response.data);
            }
          } catch (error) {
            console.error('Failed to load bet amounts:', error);
          } finally {
            setIsLoadingBetAmounts(false);
          }
        };

        if (formData.currencyId) {
          loadBetAmounts();
        } else {
          setIsLoadingBetAmounts(false);
        }
      }
    }
  }, [
    isOpen,
    currentStep,
    formData.providerId,
    formData.gameIds,
    formData.currencyId,
    pronet,
  ]);

  // Initialize form data with schema defaults
  const initializeFormDataWithSchema = useCallback(
    (provider: FreespinBonusProvider, preserveTemplateValues = false) => {
      const schema = provider.steps[0]?.schema;
      if (!schema) return;

      const newFormData: any = {
        ...formData,
        providerId: provider.providerId,
        // Only reset gameIds if not preserving template values
        gameIds: preserveTemplateValues ? formData.gameIds : [],
      };

      // Initialize form fields based on schema
      Object.entries(schema).forEach(
        ([fieldName, fieldConfig]: [string, any]) => {
          // If preserving template values and field already has a value, keep it
          if (
            preserveTemplateValues &&
            formData[fieldName] !== undefined &&
            formData[fieldName] !== null
          ) {
            return; // Keep existing value
          }

          // Only set default values if explicitly provided in schema
          if (fieldConfig.inputOptions?.defaultValue !== undefined) {
            newFormData[fieldName] = fieldConfig.inputOptions.defaultValue;
          } else {
            // For fields without default values, initialize as empty/null
            // Don't set default values for non-required fields
            newFormData[fieldName] = fieldConfig.required ? '' : null;
          }
        }
      );

      setFormData(newFormData);
    },
    [formData]
  );

  // Template selection handlers
  const handleTemplateSelect = useCallback(
    (template: FreespinBonusTemplate) => {
      // First populate form data with template values
      setFormData((prev) => ({
        ...prev,
        templateId: template.id,
        providerId: template.vendorId,
        gameIds: template.gameIds,
        name: template.bonusTemplate.name,
        rules: template.bonusTemplate.rules || [],
        // Populate schema-based values from template.values
        ...template.values,
      }));
    },
    [providers, initializeFormDataWithSchema]
  );

  const handleEmptyTemplateSelect = useCallback(() => {
    // Clear provider schema
    setSelectedProviderSchema(null);

    // Reset form data to initial state
    setFormData({
      templateId: 'empty',
      providerId: null,
      gameIds: [],
      currencyId: null,
      name: '',
      expiresAt: '',
      rules: [],
    });
  }, []);

  // Provider selection handler
  const handleProviderSelect = useCallback(
    (provider: FreespinBonusProvider) => {
      setSelectedProviderSchema(provider.steps[0]?.schema || null); // Use schema from step 1
      setGameSearchTerm(''); // Clear search term
      initializeFormDataWithSchema(provider); // Initialize form data with defaults
    },
    [initializeFormDataWithSchema]
  );

  // Game search handler
  const handleGameSearchChange = useCallback((term: string) => {
    setGameSearchTerm(term);
  }, []);

  // Schema field helpers
  const getSchemaFields = useCallback(() => {
    if (!selectedProviderSchema) return [];

    const fields: any[] = [];
    Object.entries(selectedProviderSchema).forEach(
      ([fieldName, fieldConfig]: [string, any]) => {
        // Always include all fields - handle loading states in renderSchemaField
        fields.push({
          name: fieldName,
          config: fieldConfig,
        });
      }
    );

    // Sort fields by order if available
    return fields.sort((a, b) => {
      const orderA = a.config.inputOptions?.order || 999;
      const orderB = b.config.inputOptions?.order || 999;
      return orderA - orderB;
    });
  }, [selectedProviderSchema]);

  const renderSchemaField = useCallback(
    (field: any) => {
      const { name, config } = field;
      const label = config.inputOptions?.name || name;
      const isRequired = config.required;

      // Get the current value, don't set defaults for non-required fields
      let value = formData[name];
      if (value === undefined || value === null) {
        if (config.inputOptions?.defaultValue !== undefined) {
          value = config.inputOptions.defaultValue;
        } else {
          value = ''; // Always use empty string for display
        }
      }

      const error = errors[name];

      // Handle currencyId field specifically
      if (name === 'currencyId') {
        return (
          <div key={name}>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {label}
              {isRequired ? ' *' : ''}
            </label>
            {isLoadingCurrencies ? (
              <div className="text-sm text-gray-400">Loading currencies...</div>
            ) : currenciesError ? (
              <div className="text-sm text-red-400 flex items-center">
                {currenciesError}
              </div>
            ) : (
              <select
                value={value?.toString() || ''}
                onChange={(e) => {
                  const currencyId = e.target.value
                    ? parseInt(e.target.value)
                    : null;
                  handleInputChange(name, currencyId);
                }}
                className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  error ? 'border-red-500' : 'border-dark-600'
                }`}
              >
                <option value="">Select currency...</option>
                {currencies.map((currency) => (
                  <option key={currency.id} value={currency.id}>
                    {currency.name}
                  </option>
                ))}
              </select>
            )}
            {error && <p className="mt-1 text-sm text-red-400">{error}</p>}
          </div>
        );
      }

      if (config.inputType === 'select') {
        // For select fields, use bet amounts
        return (
          <div key={name}>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {label}
              {isRequired ? ' *' : ''}
            </label>
            {isLoadingBetAmounts ? (
              <div className="text-sm text-gray-400">
                Loading bet amounts...
              </div>
            ) : betAmounts.length === 0 ? (
              <div className="text-sm text-gray-400">
                {!formData.currencyId
                  ? 'Please select a currency first to load bet amounts'
                  : formData.gameIds.length === 0
                  ? 'Please select games first to load bet amounts'
                  : 'No bet amounts available'}
              </div>
            ) : (
              <select
                value={value.toString()}
                onChange={(e) =>
                  handleInputChange(name, parseFloat(e.target.value))
                }
                className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  error ? 'border-red-500' : 'border-dark-600'
                }`}
              >
                <option value="">Select {label.toLowerCase()}...</option>
                {betAmounts.map((amount) => (
                  <option key={amount} value={amount.toString()}>
                    {amount.toString()}
                  </option>
                ))}
              </select>
            )}
            {error && <p className="mt-1 text-sm text-red-400">{error}</p>}
          </div>
        );
      }

      // For numeric fields, use NumericInput component
      if (config.type === 'integer' || config.type === 'float') {
        return (
          <div key={name}>
            <NumericInput
              label={`${label}${isRequired ? ' *' : ''}`}
              value={typeof value === 'number' ? value : 0}
              onChange={(numValue) => handleInputChange(name, numValue)}
              placeholder={
                config.inputOptions?.placeholder ||
                `Enter ${label.toLowerCase()}...`
              }
              error={error}
              allowDecimals={config.type === 'float'}
              min={config.inputOptions?.min}
              max={config.inputOptions?.max}
              step={config.inputOptions?.step}
            />
          </div>
        );
      }

      // Default text input field
      return (
        <div key={name}>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {label}
            {isRequired ? ' *' : ''}
          </label>
          <input
            type="text"
            value={value.toString()}
            onChange={(e) => handleInputChange(name, e.target.value)}
            placeholder={
              config.inputOptions?.placeholder ||
              `Enter ${label.toLowerCase()}...`
            }
            className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? 'border-red-500' : 'border-dark-600'
            }`}
          />
          {error && <p className="mt-1 text-sm text-red-400">{error}</p>}
        </div>
      );
    },
    [
      formData,
      handleInputChange,
      currencies,
      isLoadingCurrencies,
      currenciesError,
      betAmounts,
      isLoadingBetAmounts,
      errors,
    ]
  );

  // Rule management handlers
  const handleAddRule = useCallback(() => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: '',
          firstOperand: '',
          secondOperand: '',
          startsAt: new Date(),
          endsAt: new Date(),
        } as FreespinBonusRuleCreateOptions,
      ],
    }));
  }, []);

  const handleRemoveRule = useCallback((index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  }, []);

  const handleUpdateRule = useCallback(
    (
      index: number,
      field: keyof FreespinBonusRuleCreateOptions,
      value: any
    ) => {
      setFormData((prev) => ({
        ...prev,
        rules: prev.rules.map((rule, i) =>
          i === index ? { ...rule, [field]: value } : rule
        ),
      }));
    },
    []
  );

  // Validation function
  const validateCurrentStep = useCallback(
    (step: number): FormErrors => {
      const newErrors: FormErrors = {};

      switch (step) {
        case 1:
          if (!formData.templateId) {
            newErrors.templateId = 'Please select a template';
          }
          break;
        case 2:
          if (!formData.providerId) {
            newErrors.providerId = 'Please select a provider';
          }
          if (!formData.gameIds || formData.gameIds.length === 0) {
            newErrors.gameIds = 'Please select at least one game';
          }
          break;
        case 3:
          if (!formData.name?.trim()) {
            newErrors.name = 'Bonus name is required';
          }
          if (!formData.expiresAt) {
            newErrors.expiresAt = 'Expiration date is required';
          }
          break;
      }

      return newErrors;
    },
    [formData]
  );

  // Check if user can proceed to next step
  const canGoNext = useCallback(
    (step: number): boolean => {
      return Object.keys(validateCurrentStep(step)).length === 0;
    },
    [validateCurrentStep]
  );

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    const errors = validateCurrentStep(currentStep);
    if (Object.keys(errors).length > 0) {
      setErrors(errors);
      return;
    }

    setIsSubmitting(true);
    try {
      // Get vendor name from providers
      const selectedProvider = providers.find(
        (p) => p.providerId === formData.providerId
      );

      // Prepare request data
      const requestData: FreespinBonusCreateRequestOptions = {
        name: formData.name,
        expiresAt: new Date(formData.expiresAt),
        providerId: formData.providerId!,
        vendorName: selectedProvider?.providerName || 'Unknown',
        gameIds: formData.gameIds,
        rules: formData.rules,
        values: {
          currencyId: formData.currencyId,
          ...Object.fromEntries(
            Object.entries(formData).filter(
              ([key]) =>
                ![
                  'templateId',
                  'providerId',
                  'gameIds',
                  'currencyId',
                  'name',
                  'expiresAt',
                  'rules',
                ].includes(key)
            )
          ),
        },
      };

      const request = new FreespinBonusCreateRequest(requestData);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        handleClose();
      } else {
        setErrors((prev) => ({
          ...prev,
          submit: response.error || 'Failed to create bonus',
        }));
      }
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        submit: error instanceof Error ? error.message : 'An error occurred',
      }));
    } finally {
      setIsSubmitting(false);
    }
  }, [
    currentStep,
    validateCurrentStep,
    formData,
    providers,
    pronet,
    onSuccess,
    handleClose,
  ]);

  // Render step content
  const renderStepContent = useCallback(() => {
    const commonProps = {
      formData,
      errors,
      onInputChange: handleInputChange,
    };

    switch (currentStep) {
      case 1:
        return (
          <Step1
            {...commonProps}
            templates={templates}
            isLoadingTemplates={isLoadingTemplates}
            templatesError={templatesError}
            onTemplateSelect={handleTemplateSelect}
            onEmptyTemplateSelect={handleEmptyTemplateSelect}
          />
        );

      case 2:
        return (
          <Step2
            {...commonProps}
            providers={providers}
            isLoadingProviders={isLoadingProviders}
            providersError={providersError}
            games={games}
            isLoadingGames={isLoadingGames}
            gamesError={gamesError}
            gameSearchTerm={gameSearchTerm}
            onGameSearchChange={handleGameSearchChange}
            onProviderSelect={handleProviderSelect}
          />
        );

      case 3:
        return (
          <Step3
            {...commonProps}
            providers={providers}
            currencies={currencies}
            isLoadingCurrencies={isLoadingCurrencies}
            currenciesError={currenciesError}
            betAmounts={betAmounts}
            isLoadingBetAmounts={isLoadingBetAmounts}
            selectedProviderSchema={selectedProviderSchema}
            getSchemaFields={getSchemaFields}
            renderSchemaField={renderSchemaField}
          />
        );

      case 4:
        return (
          <Step4
            {...commonProps}
            onAddRule={handleAddRule}
            onRemoveRule={handleRemoveRule}
            onUpdateRule={handleUpdateRule}
          />
        );

      default:
        return null;
    }
  }, [
    currentStep,
    formData,
    errors,
    handleInputChange,
    templates,
    isLoadingTemplates,
    templatesError,
    handleTemplateSelect,
    handleEmptyTemplateSelect,
    providers,
    isLoadingProviders,
    providersError,
    games,
    isLoadingGames,
    gamesError,
    gameSearchTerm,
    handleGameSearchChange,
    handleProviderSelect,
    currencies,
    isLoadingCurrencies,
    currenciesError,
    betAmounts,
    isLoadingBetAmounts,
    selectedProviderSchema,
    getSchemaFields,
    renderSchemaField,
    handleAddRule,
    handleRemoveRule,
    handleUpdateRule,
  ]);

  return (
    <BonusCreateModal
      isOpen={isOpen}
      onClose={handleClose}
      steps={STEPS}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Freespin Bonus"
      canGoNext={canGoNext}
      bonusType="freespin bonus"
    >
      {renderStepContent}
    </BonusCreateModal>
  );
};
