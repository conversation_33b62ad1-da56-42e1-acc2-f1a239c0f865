import { RulesStep } from '@panels/ui';
import { Step4Props, RULE_FIELDS, RULE_OPERATORS } from './types';

export const Step4 = ({
  formData,
  onAddRule,
  onRemoveRule,
  onUpdateRule,
}: Step4Props) => {
  return (
    <RulesStep
      rules={formData.rules}
      ruleFields={RULE_FIELDS}
      ruleOperators={RULE_OPERATORS}
      onAddRule={onAddRule}
      onRemoveRule={onRemoveRule}
      onUpdateRule={onUpdateRule}
      title="Bonus Rules"
      emptyStateTitle="No rules defined"
      emptyStateDescription="The bonus will have no restrictions."
      variant="compact"
    />
  );
};
