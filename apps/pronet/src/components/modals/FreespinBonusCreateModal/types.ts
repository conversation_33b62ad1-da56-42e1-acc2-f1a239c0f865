import React from 'react';
import {
  type FreespinBonusRuleCreateOptions,
  type FreespinBonusTemplate,
  type FreespinBonusProvider,
  type FreespinBonusGame,
  type FreespinBonusBetAmount,
  type Currency,
} from '@panels/api';

export interface FormData {
  // Step 1: Template Selection
  templateId: number | 'empty' | null;

  // Step 2: Provider, Games & Currency
  providerId: number | null;
  gameIds: number[];
  currencyId: number | null;

  // Step 3: Basic Information & Schema Fields
  expiresAt: string;
  [key: string]: any; // Dynamic fields from JSON schema

  // Step 4: Rules
  rules: FreespinBonusRuleCreateOptions[];
}

export interface FormErrors {
  [key: string]: string;
}

export interface FreespinBonusCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface StepProps {
  formData: FormData;
  errors: FormErrors;
  onInputChange: (field: keyof FormData, value: any) => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

export interface Step1Props extends StepProps {
  templates: FreespinBonusTemplate[];
  isLoadingTemplates: boolean;
  templatesError: string | null;
  onTemplateSelect: (template: FreespinBonusTemplate) => void;
  onEmptyTemplateSelect: () => void;
}

export interface Step2Props extends StepProps {
  providers: FreespinBonusProvider[];
  isLoadingProviders: boolean;
  providersError: string | null;
  games: FreespinBonusGame[];
  isLoadingGames: boolean;
  gamesError: string | null;
  gameSearchTerm: string;
  onGameSearchChange: (term: string) => void;
  onProviderSelect: (provider: FreespinBonusProvider) => void;
}

export interface Step3Props extends StepProps {
  providers: FreespinBonusProvider[];
  currencies: Currency[];
  isLoadingCurrencies: boolean;
  currenciesError: string | null;
  betAmounts: FreespinBonusBetAmount[];
  isLoadingBetAmounts: boolean;
  selectedProviderSchema: Record<string, any> | null;
  getSchemaFields: () => any[];
  renderSchemaField: (field: any) => React.ReactNode;
}

export interface Step4Props extends StepProps {
  onAddRule: () => void;
  onRemoveRule: (index: number) => void;
  onUpdateRule: (
    index: number,
    field: keyof FreespinBonusRuleCreateOptions,
    value: any
  ) => void;
}

export const STEPS = [
  { id: 1, title: 'Template', description: 'Select bonus template' },
  { id: 2, title: 'Provider & Games', description: 'Pick provider and games' },
  { id: 3, title: 'Configuration', description: 'Basic info and settings' },
  { id: 4, title: 'Rules', description: 'Define bonus rules' },
];

// Import shared rule constants
export {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
  type RuleField,
} from '../../../constants/bonusRules';
