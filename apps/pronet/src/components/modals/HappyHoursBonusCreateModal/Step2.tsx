import { AlertCircle, Calendar, DollarSign } from 'lucide-react';
import Input from '../../ui/Input';
import NumericInput from '../../ui/NumericInput';
import { Step2Props } from './types';

export const Step2 = ({
  formData,
  errors,
  onInputChange,
  externals,
  isLoadingExternals,
  externalsError,
}: Step2Props) => {
  const handleChange = (field: keyof typeof formData, value: any) => {
    onInputChange(field, value);
  };

  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
          <DollarSign className="w-5 h-5 mr-2" />
          Basic Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <Input
              label="Bonus Name *"
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder="Enter happy hours bonus name..."
              error={errors.name}
              helperText="A descriptive name for this happy hours bonus"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              External Bonus *
            </label>

            {isLoadingExternals ? (
              <div className="text-sm text-gray-400">
                Loading external bonuses...
              </div>
            ) : externalsError ? (
              <div className="text-sm text-red-400 flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                {externalsError}
              </div>
            ) : (
              <select
                value={formData.externalBonusId || ''}
                onChange={(e) => {
                  const selectedId = e.target.value
                    ? parseInt(e.target.value)
                    : null;
                  const selectedExternal = externals.find(
                    (ext) => ext.id === selectedId
                  );
                  handleChange('externalBonusId', selectedId);
                  handleChange(
                    'externalBonusName',
                    selectedExternal?.name || ''
                  );
                }}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select an external bonus...</option>
                {externals.map((external) => (
                  <option key={external.id} value={external.id}>
                    {external.name} (ID: {external.id})
                  </option>
                ))}
              </select>
            )}
            {errors.externalBonusId && (
              <p className="mt-1 text-sm text-red-400">
                {errors.externalBonusId}
              </p>
            )}
            <p className="mt-1 text-xs text-gray-400">
              Select the external bonus that this happy hours bonus will be
              linked to
            </p>
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <span className="text-gray-500">₺</span>
              </div>
              <NumericInput
                label="Amount *"
                min={0}
                step={0.01}
                value={formData.amount || 0}
                onChange={(value) => handleChange('amount', value)}
                placeholder="0.00"
                className="pl-8"
                error={errors.amount}
                helperText="Bonus amount value"
                allowDecimals={true}
              />
            </div>
          </div>

          <div>
            <Input
              label="Expiration Date *"
              type="date"
              value={formData.expiresAt}
              onChange={(e) => handleChange('expiresAt', e.target.value)}
              error={errors.expiresAt}
              helperText="When this bonus expires and becomes unavailable"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
