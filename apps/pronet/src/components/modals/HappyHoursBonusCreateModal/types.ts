import {
  type HappyHoursBonusRuleCreateOptions,
  type HappyHoursBonusTemplate,
  type TrialBonusExternal,
} from '@panels/api';

export interface FormData {
  templateId: number | 'empty';
  name: string;
  externalBonusId: number | null;
  externalBonusName: string;
  amount: number;
  expiresAt: string;
  rules: HappyHoursBonusRuleCreateOptions[];
}

export interface FormErrors {
  templateId?: string;
  name?: string;
  externalBonusId?: string;
  externalBonusName?: string;
  amount?: string;
  expiresAt?: string;
  rules?: string;
  submit?: string;
}

export interface HappyHoursBonusCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface StepProps {
  formData: FormData;
  errors: FormErrors;
  onInputChange: (field: keyof FormData, value: any) => void;
}

export interface Step1Props extends StepProps {
  templates: HappyHoursBonusTemplate[];
  isLoadingTemplates: boolean;
  templatesError: string | null;
  onTemplateSelect: (template: HappyHoursBonusTemplate) => void;
  onEmptyTemplateSelect: () => void;
}

export interface Step2Props extends StepProps {
  externals: TrialBonusExternal[];
  isLoadingExternals: boolean;
  externalsError: string | null;
}

export interface Step3Props extends StepProps {
  onAddRule: () => void;
  onRemoveRule: (index: number) => void;
  onUpdateRule: (index: number, field: string, value: any) => void;
}

export const STEPS = [
  {
    id: 1,
    title: 'Template Selection',
    description: 'Choose a template or start from scratch',
  },
  { id: 2, title: 'Basic Information', description: 'Configure bonus details' },
  { id: 3, title: 'Rules', description: 'Set up bonus rules and conditions' },
];

// Import shared rule constants
export {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
} from '../../../constants/bonusRules';
