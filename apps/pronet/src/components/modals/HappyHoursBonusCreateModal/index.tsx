import { useState } from 'react';
import { BonusCreateModal } from '@panels/ui';
import { Step1 } from './Step1';
import { Step2 } from './Step2';
import { Step3 } from './Step3';
import { useApi } from '../../../features/api/useApi';
import { useHappyHoursBonusTemplates } from '../../../hooks/useHappyHoursBonusTemplates';
import { useTrialBonusExternals } from '../../../hooks/useTrialBonusExternals';
import {
  HappyHoursBonusCreateRequest,
  type HappyHoursBonusTemplate,
} from '@panels/api';
import type {
  FormData,
  FormErrors,
  HappyHoursBonusCreateModalProps,
} from './types';
import { STEPS } from './types';

export const HappyHoursBonusCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: HappyHoursBonusCreateModalProps) => {
  const { pronet } = useApi();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Templates data
  const {
    data: templates = [],
    isLoading: isLoadingTemplates,
    error: templatesError,
  } = useHappyHoursBonusTemplates({
    page: 1,
    limit: 100,
    // filters: {}, // NEVER UNCOMMENT THIS
    sortField: null,
    sortDirection: 'asc',
  });

  // External bonuses data
  const {
    data: externals = [],
    isLoading: isLoadingExternals,
    error: externalsError,
  } = useTrialBonusExternals({
    page: 1,
    limit: 100,
    sortField: 'name',
    sortDirection: 'asc',
  });

  // Form data
  const [formData, setFormData] = useState<FormData>({
    templateId: 'empty',
    name: '',
    externalBonusId: null,
    externalBonusName: '',
    amount: 0,
    expiresAt: '',
    rules: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const handleClose = () => {
    setCurrentStep(1);
    setFormData({
      templateId: 'empty',
      name: '',
      externalBonusId: null,
      externalBonusName: '',
      amount: 0,
      expiresAt: '',
      rules: [],
    });
    setErrors({});
    onClose();
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const handleTemplateSelect = (template: HappyHoursBonusTemplate) => {
    setFormData((prev) => ({
      ...prev,
      templateId: template.id,
      name: template.bonusTemplate.name,
      externalBonusName: template.externalBonusName,
      externalBonusId: template.externalBonusId,
      amount: template.amount,
      rules: template.bonusTemplate.rules.map((rule) => ({
        criterium: rule.criterium,
        operator: rule.operator,
        firstOperand: rule.firstOperand,
        secondOperand: rule.secondOperand,
        startsAt: rule.startsAt,
        endsAt: rule.endsAt,
      })),
    }));
  };

  const handleEmptyTemplateSelect = () => {
    setFormData((prev) => ({
      ...prev,
      templateId: 'empty',
      name: '',
      externalBonusId: null,
      externalBonusName: '',
      amount: 0,
      rules: [],
    }));
  };

  // Rule handlers
  const handleAddRule = () => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: 'ipConflictCount',
          operator: 'lte',
          firstOperand: '2',
          secondOperand: null,
          startsAt: null,
          endsAt: null,
        },
      ],
    }));
  };

  const handleRemoveRule = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  };

  const handleUpdateRule = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.map((rule, i) =>
        i === index ? { ...rule, [field]: value } : rule
      ),
    }));
  };

  // Validation
  const validateStep = (step: number): FormErrors => {
    const newErrors: FormErrors = {};

    if (step === 2) {
      if (!formData.name.trim()) {
        newErrors.name = 'Bonus name is required';
      }
      if (!formData.externalBonusId || formData.externalBonusId <= 0) {
        newErrors.externalBonusId = 'Please select an external bonus';
      }
      if (formData.amount <= 0) {
        newErrors.amount = 'Amount must be greater than 0';
      }
      if (!formData.expiresAt) {
        newErrors.expiresAt = 'Expiration date is required';
      }
    }

    return newErrors;
  };

  const handleSubmit = async () => {
    // Validate all steps
    const allErrors = { ...validateStep(2) };
    if (Object.keys(allErrors).length > 0) {
      setErrors(allErrors);
      setCurrentStep(2);
      return;
    }

    setIsSubmitting(true);

    try {
      const request = new HappyHoursBonusCreateRequest({
        name: formData.name.trim(),
        externalBonusName: formData.externalBonusName.trim(),
        externalBonusId: formData.externalBonusId!,
        amount: formData.amount,
        expiresAt: new Date(formData.expiresAt),
        rules: formData.rules,
      });

      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        handleClose();
      } else {
        setErrors({ submit: response.error || 'Failed to create bonus' });
      }
    } catch (error) {
      setErrors({
        submit:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Step1
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
            templates={templates}
            isLoadingTemplates={isLoadingTemplates}
            templatesError={templatesError}
            onTemplateSelect={handleTemplateSelect}
            onEmptyTemplateSelect={handleEmptyTemplateSelect}
          />
        );
      case 2:
        return (
          <Step2
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
            externals={externals}
            isLoadingExternals={isLoadingExternals}
            externalsError={externalsError}
          />
        );
      case 3:
        return (
          <Step3
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
            onAddRule={handleAddRule}
            onRemoveRule={handleRemoveRule}
            onUpdateRule={handleUpdateRule}
          />
        );
      default:
        return null;
    }
  };

  return (
    <BonusCreateModal
      isOpen={isOpen}
      onClose={handleClose}
      steps={STEPS}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Happy Hours Bonus"
      canGoNext={(step) => {
        if (step >= STEPS.length) return false;
        const stepErrors = validateStep(step);
        return Object.keys(stepErrors).length === 0;
      }}
      canGoPrevious={(step) => step > 1}
      bonusType="Happy Hours Bonus"
    >
      {() => renderStep()}
    </BonusCreateModal>
  );
};
