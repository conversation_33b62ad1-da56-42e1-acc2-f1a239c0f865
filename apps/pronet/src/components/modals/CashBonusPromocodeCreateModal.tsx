import React, { useState, useCallback, useEffect } from 'react';
import { PromocodeCreateModal } from '@panels/ui';
import { useApi } from '../../features/api/useApi';
import {
  BonusPromocodeCreateRequest,
  type BonusPromocodeCreateRequestOptions,
  CashBonusSearchRequest,
  type CashBonus,
} from '@panels/api';

interface CashBonusPromocodeCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface PromocodeFormData {
  bonusId: number;
  code: string;
  maxActivations?: number;
}

interface PromocodeFormErrors {
  bonusId?: string;
  code?: string;
  maxActivations?: string;
  submit?: string;
}

export const CashBonusPromocodeCreateModal: React.FC<
  CashBonusPromocodeCreateModalProps
> = ({ isOpen, onClose, onSuccess }) => {
  const { pronet } = useApi();
  const [bonuses, setBonuses] = useState<CashBonus[]>([]);
  const [isLoadingBonuses, setIsLoadingBonuses] = useState(false);

  // Form state
  const [formData, setFormData] = useState<PromocodeFormData>({
    bonusId: 0,
    code: '',
    maxActivations: undefined,
  });
  const [errors, setErrors] = useState<PromocodeFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load cash bonuses
  const loadBonuses = useCallback(async () => {
    setIsLoadingBonuses(true);
    try {
      const request = new CashBonusSearchRequest({
        page: 1,
        limit: 100,
        isActive: true,
      });
      const response = await pronet.makeRequest(request);
      if (response.success) {
        setBonuses(response.data.items);
      }
    } catch (error) {
      console.error('Failed to load cash bonuses:', error);
    } finally {
      setIsLoadingBonuses(false);
    }
  }, [pronet]);

  useEffect(() => {
    if (isOpen) {
      loadBonuses();
      // Reset form when modal opens
      setFormData({
        bonusId: 0,
        code: '',
        maxActivations: undefined,
      });
      setErrors({});
    }
  }, [isOpen, loadBonuses]);

  // Input change handler
  const handleInputChange = useCallback(
    (field: string | number | symbol, value: any) => {
      const fieldKey = field as keyof PromocodeFormData;
      setFormData((prev) => ({ ...prev, [fieldKey]: value }));
      // Clear error when user starts typing
      if (errors[fieldKey as keyof PromocodeFormErrors]) {
        setErrors((prev) => ({
          ...prev,
          [fieldKey as keyof PromocodeFormErrors]: undefined,
        }));
      }
    },
    [errors]
  );

  // Submit handler
  const handleSubmit = useCallback(async (): Promise<void> => {
    setIsSubmitting(true);
    try {
      const requestOptions: BonusPromocodeCreateRequestOptions = {
        bonusId: formData.bonusId,
        code: formData.code,
        maxActivations: formData.maxActivations || null,
      };

      const request = new BonusPromocodeCreateRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (!response.success) {
        throw new Error(response.error || 'Failed to create promocode');
      }

      onSuccess();
      onClose();
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        submit:
          error instanceof Error ? error.message : 'Failed to create promocode',
      }));
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, pronet, onSuccess, onClose]);

  // Generate random code
  const generateCode = useCallback((): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }, []);

  // Check if form can be submitted
  const canSubmit = useCallback((data: PromocodeFormData): boolean => {
    return !!(data.bonusId && data.code.trim());
  }, []);

  return (
    <PromocodeCreateModal
      isOpen={isOpen}
      onClose={onClose}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Cash Bonus Promocode"
      canSubmit={canSubmit}
    >
      {({ formData, errors, onInputChange }) => (
        <div className="space-y-6">
          {/* Cash Bonus Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Cash Bonus *
            </label>
            {isLoadingBonuses ? (
              <div className="text-center py-4">
                <div className="text-gray-400">Loading bonuses...</div>
              </div>
            ) : (
              <select
                value={formData.bonusId || ''}
                onChange={(e) =>
                  onInputChange('bonusId', parseInt(e.target.value) || 0)
                }
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a cash bonus</option>
                {bonuses.map((bonus) => (
                  <option key={bonus.id} value={bonus.bonus.id}>
                    {bonus.bonus.name} - ₺{bonus.cashAmount}
                  </option>
                ))}
              </select>
            )}
            {errors.bonusId && (
              <p className="mt-1 text-sm text-red-400">{errors.bonusId}</p>
            )}
          </div>

          {/* Promocode */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Promocode *
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={formData.code}
                onChange={(e) =>
                  onInputChange('code', e.target.value.toUpperCase())
                }
                className="flex-1 px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                placeholder="Enter promocode"
              />
              <button
                type="button"
                onClick={() => onInputChange('code', generateCode())}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Generate
              </button>
            </div>
            {errors.code && (
              <p className="mt-1 text-sm text-red-400">{errors.code}</p>
            )}
          </div>

          {/* Max Activations */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Max Activations (Optional)
            </label>
            <input
              type="number"
              value={formData.maxActivations || ''}
              onChange={(e) =>
                onInputChange(
                  'maxActivations',
                  e.target.value ? parseInt(e.target.value) : undefined
                )
              }
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Unlimited"
              min="1"
            />
            {errors.maxActivations && (
              <p className="mt-1 text-sm text-red-400">
                {errors.maxActivations}
              </p>
            )}
          </div>

          {errors.submit && (
            <div className="p-3 bg-red-900/20 border border-red-700 rounded-lg">
              <p className="text-sm text-red-400">{errors.submit}</p>
            </div>
          )}
        </div>
      )}
    </PromocodeCreateModal>
  );
};
