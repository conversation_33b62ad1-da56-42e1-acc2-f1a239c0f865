import React from 'react';
import { ClaimDetailsModal } from '@panels/ui';
import { type BonusClaim } from '@panels/api';
import { User, Calendar, Activity } from 'lucide-react';

interface CashBonusClaimDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  claim: BonusClaim | null;
}

export const CashBonusClaimDetailsModal: React.FC<
  CashBonusClaimDetailsModalProps
> = ({ isOpen, onClose, claim }) => {
  if (!claim) {
    return null;
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <ClaimDetailsModal isOpen={isOpen} onClose={onClose} claim={claim}>
      <div className="space-y-6">
        {/* Claim Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Claim Information
          </h3>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-400">Claim ID</div>
                <div className="text-gray-300 font-mono">#{claim.id}</div>
              </div>

              <div>
                <div className="text-sm text-gray-400">Source</div>
                <div className="text-gray-300 capitalize">{claim.source}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center gap-2">
            <User className="h-5 w-5" />
            Customer Information
          </h3>
          <div className="space-y-3">
            <div>
              <div className="text-sm text-gray-400">Username</div>
              <div className="text-gray-300 font-medium">
                {claim.customer?.username || `Customer #${claim.customerId}`}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-400">External ID</div>
              <div className="text-gray-300 font-mono">
                {claim.customer?.externalId || 'N/A'}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-400">Customer ID</div>
              <div className="text-gray-300 font-mono">#{claim.customerId}</div>
            </div>
          </div>
        </div>

        {/* Associated Bonus */}
        {claim.bonus && (
          <div>
            <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Associated Bonus
            </h3>
            <div className="space-y-3">
              <div>
                <div className="text-sm text-gray-400">Bonus Name</div>
                <div className="text-gray-300 font-medium">
                  {claim.bonus.name}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-400">Bonus ID</div>
                  <div className="text-gray-300 font-mono">
                    #{claim.bonus.id}
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-400">Bonus Type</div>
                  <div className="text-gray-300 capitalize">
                    {claim.bonus.type}
                  </div>
                </div>
              </div>

              <div>
                <div className="text-sm text-gray-400">Bonus Status</div>
                <div
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${
                    claim.bonus.isActive
                      ? 'bg-green-900 text-green-300 border-green-700'
                      : 'bg-gray-900 text-gray-300 border-gray-700'
                  }`}
                >
                  {claim.bonus.isActive ? 'Active' : 'Inactive'}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div>
          <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Timestamps
          </h3>
          <div className="space-y-3">
            <div>
              <div className="text-sm text-gray-400">Created</div>
              <div className="text-gray-300">{formatDate(claim.createdAt)}</div>
            </div>

            <div>
              <div className="text-sm text-gray-400">Last Updated</div>
              <div className="text-gray-300">{formatDate(claim.updatedAt)}</div>
            </div>
          </div>
        </div>
      </div>
    </ClaimDetailsModal>
  );
};
