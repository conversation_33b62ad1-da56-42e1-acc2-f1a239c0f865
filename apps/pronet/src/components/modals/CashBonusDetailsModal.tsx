import React from 'react';
import { DetailsModal } from '@panels/ui';
import { type CashBonus } from '@panels/api';
import { User, Calendar, Activity, DollarSign, Shield } from 'lucide-react';

interface CashBonusDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  bonus: CashBonus | null;
}

export const CashBonusDetailsModal: React.FC<CashBonusDetailsModalProps> = ({
  isOpen,
  onClose,
  bonus,
}) => {
  if (!bonus) {
    return null;
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatBonusStatus = (bonus: CashBonus) => {
    const isActive = bonus.bonus.isActive;
    const isDeleted = bonus.bonus.deletedAt !== null;
    const isExpired =
      bonus.bonus.expiresAt && new Date(bonus.bonus.expiresAt) < new Date();

    let status: string;
    let colorClass: string;

    if (isDeleted) {
      status = 'deleted';
      colorClass = 'bg-red-900 text-red-300 border-red-700';
    } else if (isExpired) {
      status = 'expired';
      colorClass = 'bg-orange-900 text-orange-300 border-orange-700';
    } else if (isActive) {
      status = 'active';
      colorClass = 'bg-green-900 text-green-300 border-green-700';
    } else {
      status = 'inactive';
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
    }

    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Cash Bonus Details - ${bonus.bonus.name || 'Unnamed'}`}
      size="lg"
    >
      <div className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Bonus Information
              </h4>
              <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">ID:</span>
                  <span className="text-sm font-mono text-gray-300">
                    #{bonus.id}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Name:</span>
                  <span className="text-sm font-medium text-gray-300">
                    {bonus.bonus.name}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Status:</span>
                  <span className="text-sm text-gray-300">
                    {formatBonusStatus(bonus)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
                <Activity className="h-4 w-4 mr-2" />
                Configuration
              </h4>
              <div className="bg-dark-700 rounded-lg p-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Cash Amount:</span>
                  <span className="text-sm font-medium text-gray-300">
                    ₺{bonus.cashAmount}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Active:</span>
                  <span className="text-sm text-gray-300">
                    {bonus.bonus.isActive ? 'Yes' : 'No'}
                  </span>
                </div>
                {bonus.bonus.expiresAt && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Expires At:</span>
                    <span className="text-sm text-gray-300">
                      {formatDate(new Date(bonus.bonus.expiresAt))}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Bonus Rules */}
        {bonus.bonus.rules && bonus.bonus.rules.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
              <Shield className="h-4 w-4 mr-2" />
              Eligibility Rules
            </h4>
            <div className="bg-dark-700 rounded-lg p-4 space-y-3">
              {bonus.bonus.rules.map((rule, index) => (
                <div
                  key={rule.id || index}
                  className="border-b border-dark-600 last:border-b-0 pb-3 last:pb-0"
                >
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-gray-400">Criterium</div>
                      <div className="text-gray-300 capitalize">
                        {rule.criterium}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-400">Operator</div>
                      <div className="text-gray-300">{rule.operator}</div>
                    </div>
                    <div>
                      <div className="text-gray-400">Value</div>
                      <div className="text-gray-300">{rule.firstOperand}</div>
                    </div>
                  </div>
                  {(rule.startsAt || rule.endsAt) && (
                    <div className="mt-2 pt-2 border-t border-dark-600">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        {rule.startsAt && (
                          <div>
                            <div className="text-gray-400">Starts At</div>
                            <div className="text-gray-300">
                              {formatDate(rule.startsAt)}
                            </div>
                          </div>
                        )}
                        {rule.endsAt && (
                          <div>
                            <div className="text-gray-400">Ends At</div>
                            <div className="text-gray-300">
                              {formatDate(rule.endsAt)}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div>
          <h4 className="text-sm font-medium text-gray-400 mb-2 flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            Timestamps
          </h4>
          <div className="bg-dark-700 rounded-lg p-4 space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-400">Created:</span>
              <span className="text-sm text-gray-300">
                {formatDate(bonus.createdAt)}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm text-gray-400">Last Updated:</span>
              <span className="text-sm text-gray-300">
                {formatDate(bonus.updatedAt)}
              </span>
            </div>

            {bonus.bonus.expiresAt && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Expires At:</span>
                <span className="text-sm text-gray-300">
                  {formatDate(new Date(bonus.bonus.expiresAt))}
                </span>
              </div>
            )}

            {bonus.bonus.deletedAt && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Deleted At:</span>
                <span className="text-sm text-red-400">
                  {formatDate(new Date(bonus.bonus.deletedAt))}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </DetailsModal>
  );
};
