import { DetailsModal } from '@panels/ui';
import { Calendar, DollarSign, Hash, Clock, Tag, Shield, User } from 'lucide-react';
import type { TrialBonusTemplate } from '@panels/api';

interface TrialBonusTemplateDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  template: TrialBonusTemplate | null;
}

export const TrialBonusTemplateDetailsModal = ({
  isOpen,
  onClose,
  template,
}: TrialBonusTemplateDetailsModalProps) => {
  if (!template) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <DetailsModal
      isOpen={isOpen}
      onClose={onClose}
      title="Trial Bonus Template Details"
      size="xl"
    >
      <div className="space-y-6">
        {/* Header Info */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-300">
              {template.bonusTemplate.name}
            </h3>
            <p className="text-gray-400 mt-1">
              External: {template.externalBonusName}
            </p>
          </div>
          <span className="px-3 py-1 rounded-full text-sm font-medium border bg-blue-900 text-blue-300 border-blue-700">
            Template
          </span>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Template Information
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Hash className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Template ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">#{template.id}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Tag className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">External ID:</span>
                  <span className="ml-2 text-gray-300 font-mono">#{template.externalBonusId}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Amount:</span>
                  <span className="ml-2 text-gray-300 font-semibold">
                    ₺{template.amount.toFixed(2)}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Valid For:</span>
                  <span className="ml-2 text-gray-300">
                    {template.validForDays} days
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
              Timing Information
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Created:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(template.createdAt)}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Updated:</span>
                  <span className="ml-2 text-gray-300">
                    {formatDate(template.updatedAt)}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-400" />
                <div>
                  <span className="text-gray-400">Type:</span>
                  <span className="ml-2 text-gray-300">
                    {template.bonusTemplate.type}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Rules Section */}
        {template.bonusTemplate.rules && template.bonusTemplate.rules.length > 0 && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Template Rules ({template.bonusTemplate.rules.length})
            </h4>
            
            <div className="space-y-3">
              {template.bonusTemplate.rules.map((rule, index) => (
                <div
                  key={rule.id}
                  className="p-4 bg-dark-700 border border-dark-600 rounded-lg"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-xs bg-blue-900 text-blue-300 px-2 py-1 rounded">
                        Rule {index + 1}
                      </span>
                      <span className="text-gray-300 font-medium">
                        {rule.criterium}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mt-2 text-sm text-gray-400">
                    <span className="font-medium">{rule.operator}</span>
                    <span className="ml-2">{rule.firstOperand}</span>
                    {rule.secondOperand && (
                      <span className="ml-1">- {rule.secondOperand}</span>
                    )}
                  </div>

                  {(rule.startsAt || rule.endsAt) && (
                    <div className="mt-2 text-xs text-gray-500">
                      {rule.startsAt && (
                        <span>Starts: {formatDate(rule.startsAt)}</span>
                      )}
                      {rule.startsAt && rule.endsAt && <span className="mx-2">•</span>}
                      {rule.endsAt && (
                        <span>Ends: {formatDate(rule.endsAt)}</span>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* External Bonus Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-300 border-b border-dark-600 pb-2">
            External Bonus Details
          </h4>
          
          <div className="p-4 bg-dark-700 border border-dark-600 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">External Name:</span>
                <span className="ml-2 text-gray-300">{template.externalBonusName}</span>
              </div>
              <div>
                <span className="text-gray-400">External ID:</span>
                <span className="ml-2 text-gray-300 font-mono">#{template.externalBonusId}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DetailsModal>
  );
};
