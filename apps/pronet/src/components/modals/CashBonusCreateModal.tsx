import React, { useState, useCallback, useEffect } from 'react';
import { BonusCreateModal, RulesStep } from '@panels/ui';
import { useApi } from '../../features/api/useApi';
import {
  CashBonusCreateRequest,
  type CashBonusCreateRequestOptions,
  CashBonusTemplateSearchRequest,
  type CashBonusTemplate,
} from '@panels/api';
import {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
} from '../../constants/bonusRules';

// Define the rule creation options type
type BonusRuleCreateOptions = {
  criterium: string;
  operator: string;
  firstOperand: string;
  secondOperand: string | null;
  startsAt: Date | null;
  endsAt: Date | null;
};

interface CashBonusCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface CashBonusFormData {
  templateId?: number;
  name: string;
  cashAmount: number;
  expiresAt?: Date;
  isActive: boolean;
  rules: BonusRuleCreateOptions[];
}

interface CashBonusFormErrors {
  name?: string;
  cashAmount?: string;
  expiresAt?: string;
  rules?: string;
  submit?: string;
}

export const CashBonusCreateModal: React.FC<CashBonusCreateModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { pronet } = useApi();
  const [templates, setTemplates] = useState<CashBonusTemplate[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);

  // Load templates
  const loadTemplates = useCallback(async () => {
    setIsLoadingTemplates(true);
    try {
      const request = new CashBonusTemplateSearchRequest({
        page: 1,
        limit: 100,
        isActive: true,
      });
      const response = await pronet.makeRequest(request);
      if (response.success) {
        setTemplates(response.data.items);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
    } finally {
      setIsLoadingTemplates(false);
    }
  }, [pronet]);

  useEffect(() => {
    if (isOpen) {
      loadTemplates();
    }
  }, [isOpen, loadTemplates]);

  // Form validation
  const validateForm = useCallback(
    (data: CashBonusFormData): CashBonusFormErrors => {
      const errors: CashBonusFormErrors = {};

      if (!data.name.trim()) {
        errors.name = 'Name is required';
      }

      if (!data.cashAmount || data.cashAmount <= 0) {
        errors.cashAmount = 'Cash amount must be greater than 0';
      }

      if (data.expiresAt && data.expiresAt <= new Date()) {
        errors.expiresAt = 'Expiry date must be in the future';
      }

      if (!data.rules || data.rules.length === 0) {
        errors.rules = 'At least one rule is required';
      }

      return errors;
    },
    []
  );

  // Submit handler
  const handleSubmit = useCallback(
    async (data: CashBonusFormData): Promise<void> => {
      const requestOptions: CashBonusCreateRequestOptions = {
        name: data.name,
        cashAmount: data.cashAmount,
        rules: data.rules,
        expiresAt: data.expiresAt,
        isActive: data.isActive,
      };

      const request = new CashBonusCreateRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (!response.success) {
        throw new Error(response.error || 'Failed to create cash bonus');
      }

      onSuccess();
    },
    [pronet, onSuccess]
  );

  // Template selection handler
  const handleTemplateSelect = useCallback(
    (template: CashBonusTemplate): Partial<CashBonusFormData> => {
      return {
        name: template.bonusTemplate.name,
        cashAmount: template.cashAmount,
        expiresAt: undefined, // Templates don't have expiration dates
        isActive: true, // Default to active
        rules: template.bonusTemplate.rules.map((rule) => ({
          criterium: rule.criterium,
          operator: rule.operator,
          firstOperand: rule.firstOperand,
          secondOperand: rule.secondOperand,
          startsAt: rule.startsAt,
          endsAt: rule.endsAt,
        })),
      };
    },
    []
  );

  // Rule handlers
  const handleAddRule = useCallback(() => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: '',
          operator: '',
          firstOperand: '',
          secondOperand: null,
          startsAt: null,
          endsAt: null,
        },
      ],
    }));
  }, []);

  const handleRemoveRule = useCallback((index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  }, []);

  const handleUpdateRule = useCallback(
    (index: number, field: string, value: any) => {
      setFormData((prev) => ({
        ...prev,
        rules: prev.rules.map((rule, i) =>
          i === index ? { ...rule, [field]: value } : rule
        ),
      }));
    },
    []
  );

  const steps = [
    {
      id: 1,
      title: 'Select Template',
      description: 'Choose a template or start from scratch',
    },
    {
      id: 2,
      title: 'Bonus Details',
      description: 'Configure cash bonus settings',
    },
    {
      id: 3,
      title: 'Rules & Conditions',
      description: 'Set up bonus rules and eligibility',
    },
  ];

  // State for the modal
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<CashBonusFormData>({
    name: '',
    cashAmount: 0,
    isActive: true,
    rules: [],
  });
  const [errors, setErrors] = useState<CashBonusFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Input change handler
  const handleInputChange = useCallback(
    (field: keyof CashBonusFormData, value: any) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
      // Clear error when user starts typing
      if (errors[field as keyof CashBonusFormErrors]) {
        setErrors((prev) => ({
          ...prev,
          [field as keyof CashBonusFormErrors]: undefined,
        }));
      }
    },
    [errors]
  );

  // Submit handler
  const handleSubmitForm = useCallback(async () => {
    setIsSubmitting(true);
    try {
      await handleSubmit(formData);
      onClose();
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        submit:
          error instanceof Error
            ? error.message
            : 'Failed to create cash bonus',
      }));
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, handleSubmit, onClose]);

  // Template selection handler
  const handleTemplateSelectInStep = useCallback(
    (template: CashBonusTemplate) => {
      const templateData = handleTemplateSelect(template);
      setFormData((prev) => ({ ...prev, ...templateData }));
    },
    [handleTemplateSelect]
  );

  // Render current step content
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-300 mb-4">
                Select Template
              </h3>
              <p className="text-sm text-gray-400 mb-6">
                Choose a template to start with or create from scratch
              </p>

              {isLoadingTemplates ? (
                <div className="text-center py-8">
                  <div className="text-gray-400">Loading templates...</div>
                </div>
              ) : (
                <div className="space-y-3">
                  <button
                    type="button"
                    onClick={() => setCurrentStep(2)}
                    className="w-full p-4 border border-dark-600 rounded-md hover:border-gray-500 transition-colors text-left"
                  >
                    <div className="font-medium text-gray-300">
                      Start from scratch
                    </div>
                    <div className="text-sm text-gray-400">
                      Create a new cash bonus without a template
                    </div>
                  </button>

                  {templates.map((template) => (
                    <button
                      key={template.id}
                      type="button"
                      onClick={() => {
                        handleTemplateSelectInStep(template);
                        setCurrentStep(2);
                      }}
                      className="w-full p-4 border border-dark-600 rounded-md hover:border-gray-500 transition-colors text-left"
                    >
                      <div className="font-medium text-gray-300">
                        {template.bonusTemplate.name}
                      </div>
                      <div className="text-sm text-gray-400">
                        Cash Amount: ₺{template.cashAmount} •{' '}
                        {template.bonusTemplate.rules.length} rules
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-300 mb-4">
                Bonus Details
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Bonus Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter bonus name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-400">{errors.name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Cash Amount
                  </label>
                  <input
                    type="number"
                    value={formData.cashAmount}
                    onChange={(e) =>
                      handleInputChange(
                        'cashAmount',
                        parseFloat(e.target.value) || 0
                      )
                    }
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                  />
                  {errors.cashAmount && (
                    <p className="mt-1 text-sm text-red-400">
                      {errors.cashAmount}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Expires At (Optional)
                  </label>
                  <input
                    type="datetime-local"
                    value={
                      formData.expiresAt
                        ? formData.expiresAt.toISOString().slice(0, 16)
                        : ''
                    }
                    onChange={(e) =>
                      handleInputChange(
                        'expiresAt',
                        e.target.value ? new Date(e.target.value) : undefined
                      )
                    }
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.expiresAt && (
                    <p className="mt-1 text-sm text-red-400">
                      {errors.expiresAt}
                    </p>
                  )}
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) =>
                      handleInputChange('isActive', e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-dark-600 rounded bg-dark-700"
                  />
                  <label
                    htmlFor="isActive"
                    className="ml-2 block text-sm text-gray-300"
                  >
                    Active bonus
                  </label>
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <RulesStep
            rules={formData.rules}
            errors={errors}
            ruleFields={RULE_FIELDS}
            ruleOperators={RULE_OPERATORS}
            onAddRule={handleAddRule}
            onRemoveRule={handleRemoveRule}
            onUpdateRule={handleUpdateRule}
            title="Cash Bonus Rules"
            description="Define who can receive this cash bonus"
            emptyStateTitle="No rules defined"
            emptyStateDescription="Add rules to control who can receive this bonus, or leave empty to allow all users."
            variant="full"
          />
        );

      default:
        return <div>Invalid step</div>;
    }
  };

  return (
    <BonusCreateModal
      isOpen={isOpen}
      onClose={onClose}
      steps={steps}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmitForm}
      submitButtonText="Create Cash Bonus"
      bonusType="cash bonus"
    >
      {() => renderCurrentStep()}
    </BonusCreateModal>
  );
};
