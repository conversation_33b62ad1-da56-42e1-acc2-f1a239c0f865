import { useState, useCallback, useEffect } from 'react';
import { BonusTemplateCreateModal } from '@panels/ui';
import { useApi } from '../../../features/api/useApi';
import { useTrialBonusExternals } from '../../../hooks/useTrialBonusExternals';
import {
  TrialBonusTemplateCreateRequest,
  type TrialBonusTemplateCreateRequestOptions,
} from '@panels/api';
import type { TrialBonusExternal } from '@panels/api';
import { Step1 } from './Step1';
import { Step2 } from './Step2';
import type {
  FormData,
  FormErrors,
  TrialBonusTemplateCreateModalProps,
} from './types';

const STEPS = [
  {
    id: 1,
    title: 'Details',
    description: 'Configure template information',
  },
  {
    id: 2,
    title: 'Rules',
    description: 'Set up template rules',
  },
];

export const TrialBonusTemplateCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: TrialBonusTemplateCreateModalProps) => {
  const { pronet } = useApi();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedExternal, setSelectedExternal] =
    useState<TrialBonusExternal | null>(null);

  // Form data
  const [formData, setFormData] = useState<FormData>({
    name: '',
    externalBonusId: null,
    externalBonusName: '',
    amount: 0,
    validForDays: 30,
    rules: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Load externals
  const {
    data: externals,
    isLoading: isLoadingExternals,
    error: externalsError,
  } = useTrialBonusExternals({
    page: 1,
    limit: 100,
    // filters: {},
    sortField: 'name',
    sortDirection: 'asc',
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
      setFormData({
        name: '',
        externalBonusId: null,
        externalBonusName: '',
        amount: 0,
        validForDays: 30,
        rules: [],
      });
      setErrors({});
      setSelectedExternal(null);
    }
  }, [isOpen]);

  // Handle input changes
  const handleInputChange = useCallback(
    (field: keyof FormData, value: any) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: undefined }));
      }
    },
    [errors]
  );

  // Validation
  const canGoNext = useCallback((step: number, data: FormData) => {
    switch (step) {
      case 1:
        return !!(
          data.name.trim() &&
          data.externalBonusId &&
          data.amount > 0 &&
          data.validForDays > 0
        );
      case 2:
        return true; // Rules are optional
      default:
        return false;
    }
  }, []);

  // Submit handler
  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true);
    setErrors({});

    try {
      const requestOptions: TrialBonusTemplateCreateRequestOptions = {
        name: formData.name,
        externalBonusName: formData.externalBonusName,
        externalBonusId: formData.externalBonusId!,
        amount: formData.amount,
        validForDays: formData.validForDays,
        rules: formData.rules,
      };

      const request = new TrialBonusTemplateCreateRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        setErrors({
          submit: response.error || 'Failed to create trial bonus template',
        });
      }
    } catch (error) {
      setErrors({
        submit:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, pronet, onSuccess, onClose]);

  // Rule management handlers
  const handleAddRule = useCallback(() => {
    const newRule = {
      criterium: '',
      operator: '',
      firstOperand: '',
      secondOperand: null,
      startsAt: null,
      endsAt: null,
    };
    setFormData((prev) => ({
      ...prev,
      rules: [...prev.rules, newRule],
    }));
  }, []);

  const handleRemoveRule = useCallback((index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  }, []);

  const handleUpdateRule = useCallback(
    (index: number, field: string, value: any) => {
      setFormData((prev) => ({
        ...prev,
        rules: prev.rules.map((rule, i) =>
          i === index ? { ...rule, [field]: value } : rule
        ),
      }));
    },
    []
  );

  // Render step content
  const renderStepContent = useCallback(
    (stepProps: any) => {
      switch (currentStep) {
        case 1:
          return (
            <Step1
              {...stepProps}
              externals={externals}
              isLoadingExternals={isLoadingExternals}
              externalsError={externalsError}
              selectedExternal={selectedExternal}
              setSelectedExternal={setSelectedExternal}
            />
          );
        case 2:
          return (
            <Step2
              {...stepProps}
              onAddRule={handleAddRule}
              onRemoveRule={handleRemoveRule}
              onUpdateRule={handleUpdateRule}
            />
          );
        default:
          return null;
      }
    },
    [
      currentStep,
      externals,
      isLoadingExternals,
      externalsError,
      selectedExternal,
      handleAddRule,
      handleRemoveRule,
      handleUpdateRule,
    ]
  );

  return (
    <BonusTemplateCreateModal
      isOpen={isOpen}
      onClose={onClose}
      steps={STEPS}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Trial Bonus Template"
      canGoNext={canGoNext}
      bonusType="trial bonus template"
    >
      {renderStepContent}
    </BonusTemplateCreateModal>
  );
};
