import { useState, useEffect } from 'react';
import { AlertCircle, Search } from 'lucide-react';
import Input from '../../ui/Input';
import NumericInput from '../../ui/NumericInput';
import { Step1Props } from './types';

export const Step1 = ({
  formData,
  errors,
  onInputChange,
  externals,
  isLoadingExternals,
  externalsError,
  selectedExternal,
  setSelectedExternal,
}: Step1Props) => {
  const [externalSearchTerm, setExternalSearchTerm] = useState('');

  // Filter externals based on search term
  const filteredExternals = externals.filter(
    (external) =>
      external.name.toLowerCase().includes(externalSearchTerm.toLowerCase()) ||
      external.id.toString().includes(externalSearchTerm)
  );

  // Update form data when external is selected
  useEffect(() => {
    if (selectedExternal) {
      onInputChange('externalBonusId', selectedExternal.id);
      onInputChange('externalBonusName', selectedExternal.name);
    }
  }, [selectedExternal, onInputChange]);

  const handleExternalSelect = (external: any) => {
    setSelectedExternal(external);
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-300 mb-4">
        Configure Trial Bonus Template
      </h3>

      {/* Template Name */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Template Name *
        </label>
        <Input
          type="text"
          value={formData.name}
          onChange={(e) => onInputChange('name', e.target.value)}
          placeholder="Enter template name..."
          error={errors.name}
        />
      </div>

      {/* External Bonus Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          External Bonus *
        </label>
        
        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            value={externalSearchTerm}
            onChange={(e) => setExternalSearchTerm(e.target.value)}
            placeholder="Search external bonuses..."
            className="w-full pl-10 pr-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {isLoadingExternals ? (
          <div className="text-sm text-gray-400">Loading external bonuses...</div>
        ) : externalsError ? (
          <div className="text-sm text-red-400 flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            {externalsError}
          </div>
        ) : (
          <div className="h-48 overflow-y-auto border border-dark-600 rounded-md bg-dark-700">
            {filteredExternals.length === 0 ? (
              <div className="p-4 text-center text-gray-400">
                No external bonuses found
              </div>
            ) : (
              filteredExternals.map((external) => (
                <div
                  key={external.id}
                  onClick={() => handleExternalSelect(external)}
                  className={`p-3 border-b border-dark-600 last:border-b-0 cursor-pointer hover:bg-dark-600 ${
                    selectedExternal?.id === external.id ? 'bg-blue-900/20' : ''
                  }`}
                >
                  <div className="font-medium text-gray-300">
                    {external.name}
                  </div>
                  <div className="text-sm text-gray-400">
                    ID: {external.id}
                  </div>
                </div>
              ))
            )}
          </div>
        )}
        {errors.externalBonusId && (
          <p className="mt-1 text-sm text-red-400">{errors.externalBonusId}</p>
        )}
      </div>

      {/* Amount */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Amount *
        </label>
        <NumericInput
          value={formData.amount}
          onChange={(value) => onInputChange('amount', value)}
          placeholder="0.00"
          min={0}
          step={0.01}
          error={errors.amount}
        />
      </div>

      {/* Valid For Days */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Valid For Days *
        </label>
        <NumericInput
          value={formData.validForDays}
          onChange={(value) => onInputChange('validForDays', value)}
          placeholder="30"
          min={1}
          step={1}
          error={errors.validForDays}
        />
        <p className="mt-1 text-sm text-gray-400">
          Number of days the bonus will be valid after assignment
        </p>
      </div>
    </div>
  );
};
