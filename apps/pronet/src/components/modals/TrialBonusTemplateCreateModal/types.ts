import type {
  TrialBonusTemplateRuleCreateOptions,
  TrialBonusExternal,
} from '@panels/api';

export interface FormData {
  // Step 1: Basic Information
  name: string;
  externalBonusId: number | null;
  externalBonusName: string;
  amount: number;
  validForDays: number;

  // Step 2: Rules
  rules: TrialBonusTemplateRuleCreateOptions[];
}

export interface FormErrors {
  name?: string;
  externalBonusId?: string;
  externalBonusName?: string;
  amount?: string;
  validForDays?: string;
  rules?: string;
  submit?: string;
}

export interface TrialBonusTemplateCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface StepProps {
  formData: FormData;
  errors: FormErrors;
  onInputChange: (field: keyof FormData, value: any) => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

export interface Step1Props extends StepProps {
  externals: TrialBonusExternal[];
  isLoadingExternals: boolean;
  externalsError: string | null;
  selectedExternal: TrialBonusExternal | null;
  setSelectedExternal: (external: TrialBonusExternal | null) => void;
}

export interface Step2Props extends StepProps {
  onAddRule: () => void;
  onRemoveRule: (index: number) => void;
  onUpdateRule: (
    index: number,
    field: keyof TrialBonusTemplateRuleCreateOptions,
    value: any
  ) => void;
}

// Import shared rule constants
export {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
  type RuleField,
} from '../../../constants/bonusRules';
