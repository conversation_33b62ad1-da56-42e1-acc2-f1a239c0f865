import { RulesStep } from '@panels/ui';
import { Step2Props, RULE_FIELDS, RULE_OPERATORS } from './types';

export const Step2 = ({
  formData,
  errors,
  onAddRule,
  onRemoveRule,
  onUpdateRule,
}: Step2Props) => {
  return (
    <RulesStep
      rules={formData.rules}
      errors={errors}
      ruleFields={RULE_FIELDS}
      ruleOperators={RULE_OPERATORS}
      onAddRule={onAddRule}
      onRemoveRule={onRemoveRule}
      onUpdateRule={onUpdateRule}
      title="Template Rules"
      description="Define conditions that customers must meet to be eligible for this template"
      emptyStateTitle="No rules defined"
      emptyStateDescription="Add rules to control who can use this template, or leave empty to allow all users."
      useSecondsForDates={false}
      variant="full"
    />
  );
};
