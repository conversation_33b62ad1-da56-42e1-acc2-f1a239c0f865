import { Calendar, Clock, DollarSign, Percent } from 'lucide-react';
import Input from '../../ui/Input';
import NumericInput from '../../ui/NumericInput';
import { Step1Props } from './types';

export const Step1 = ({ formData, errors, onInputChange }: Step1Props) => {
  const handleChange = (field: keyof typeof formData, value: any) => {
    onInputChange(field, value);
  };

  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
          <DollarSign className="w-5 h-5 mr-2" />
          Basic Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <Input
              label="Template Name *"
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder="Enter template name..."
              error={errors.name}
              helperText="A descriptive name for this lossback bonus template"
            />
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <span className="text-gray-500">₺</span>
              </div>
              <NumericInput
                label="Max Balance *"
                min={0}
                step={0.01}
                value={formData.maxBalance || 0}
                onChange={(value) => handleChange('maxBalance', value)}
                placeholder="0.00"
                className="pl-8"
                error={errors.maxBalance}
                helperText="Maximum balance that can be earned from this bonus"
                allowDecimals={true}
              />
            </div>
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10">
                <Percent className="w-4 h-4 text-gray-500" />
              </div>
              <NumericInput
                label="Lossback Percentage *"
                min={0}
                max={100}
                step={0.01}
                value={formData.lossbackPercentage || 0}
                onChange={(value) => handleChange('lossbackPercentage', value)}
                placeholder="0.00"
                className="pr-8"
                error={errors.lossbackPercentage}
                helperText="Percentage of losses to return as bonus (0-100%)"
                allowDecimals={true}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Happy Hours */}
      <div>
        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Happy Hours
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <Input
              label="Start Time *"
              type="time"
              value={formData.happyHoursStart}
              onChange={(e) => handleChange('happyHoursStart', e.target.value)}
              helperText="When the happy hours period begins"
            />
          </div>

          <div>
            <Input
              label="End Time *"
              type="time"
              value={formData.happyHoursEnd}
              onChange={(e) => handleChange('happyHoursEnd', e.target.value)}
              helperText="When the happy hours period ends"
            />
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10">
                <Percent className="w-4 h-4 text-gray-500" />
              </div>
              <NumericInput
                label="Happy Hours Boost *"
                min={0}
                step={0.01}
                value={formData.happyHoursBoostPercentage || 0}
                onChange={(value) => handleChange('happyHoursBoostPercentage', value)}
                placeholder="0.00"
                className="pr-8"
                helperText="Additional percentage boost during happy hours"
                allowDecimals={true}
              />
            </div>
          </div>

          <div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <span className="text-gray-500">₺</span>
              </div>
              <NumericInput
                label="Deposit/Withdraw Threshold *"
                min={0}
                step={0.01}
                value={formData.depositWithDrawDifferenceThreshold || 0}
                onChange={(value) => handleChange('depositWithDrawDifferenceThreshold', value)}
                placeholder="0.00"
                className="pl-8"
                helperText="Minimum deposit/withdraw difference threshold"
                allowDecimals={true}
              />
            </div>
          </div>
        </div>
        <div className="mt-4 p-4 bg-blue-900/20 border border-blue-700 rounded-md">
          <p className="text-sm text-blue-300">
            <strong>Happy Hours:</strong> During this time period, the lossback
            bonus will have an additional boost percentage applied to the base lossback rate.
            The deposit/withdraw threshold determines the minimum difference required for eligibility.
          </p>
        </div>
      </div>

      {/* Validity Period */}
      <div>
        <h3 className="text-lg font-medium text-gray-300 mb-4 flex items-center">
          <Calendar className="w-5 h-5 mr-2" />
          Validity Period (Optional)
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              Valid For Days
            </label>
            <input
              type="number"
              min="1"
              value={formData.validForDays || ''}
              onChange={(e) =>
                handleChange('validForDays', parseInt(e.target.value) || null)
              }
              placeholder="30"
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              Number of days the bonus remains valid after creation (leave empty
              for no expiration)
            </p>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
        <h4 className="text-md font-medium text-gray-300 mb-3">
          Template Summary
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Template Name:</span>
              <span className="text-gray-300">
                {formData.name || 'Not set'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Max Balance:</span>
              <span className="text-gray-300">
                ₺{formData.maxBalance.toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Lossback %:</span>
              <span className="text-gray-300">
                {formData.lossbackPercentage}%
              </span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Happy Hours:</span>
              <span className="text-gray-300">
                {formData.happyHoursStart} - {formData.happyHoursEnd}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Valid For:</span>
              <span className="text-gray-300">
                {formData.validForDays
                  ? `${formData.validForDays} days`
                  : 'No expiration'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
