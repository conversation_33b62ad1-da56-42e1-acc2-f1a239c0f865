import { type LossbackBonusTemplateRuleCreateOptions } from '@panels/api';

export interface FormData {
  // Step 1: Basic Information
  name: string;
  maxBalance: number;
  lossbackPercentage: number;
  happyHoursStart: string;
  happyHoursEnd: string;
  happyHoursBoostPercentage: number;
  depositWithDrawDifferenceThreshold: number;
  validForDays: number | null;

  // Step 2: Rules
  rules: LossbackBonusTemplateRuleCreateOptions[];
}

export interface FormErrors {
  [key: string]: string;
}

export interface LossbackBonusTemplateCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export interface StepProps {
  formData: FormData;
  errors: FormErrors;
  onInputChange: (field: keyof FormData, value: any) => void;
}

export interface Step1Props extends StepProps {}

export interface Step2Props extends StepProps {
  onAddRule: () => void;
  onRemoveRule: (index: number) => void;
  onUpdateRule: (
    index: number,
    field: keyof LossbackBonusTemplateRuleCreateOptions,
    value: any
  ) => void;
}

// Import shared rule constants
export {
  BONUS_RULE_FIELDS as RULE_FIELDS,
  BONUS_RULE_OPERATORS as RULE_OPERATORS,
  type RuleField,
} from '../../../constants/bonusRules';

export const STEPS = [
  { id: 1, title: 'Configuration', description: 'Basic template settings' },
  { id: 2, title: 'Rules', description: 'Define template rules' },
];
