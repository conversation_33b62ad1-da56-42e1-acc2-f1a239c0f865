import { useState, Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { Step1 } from './Step1';
import { Step2 } from './Step2';
import { useApi } from '../../../features/api/useApi';
import Button from '../../ui/Button';
import { LossbackBonusTemplateCreateRequest } from '@panels/api';
import type {
  FormData,
  FormErrors,
  LossbackBonusTemplateCreateModalProps,
} from './types';
import { STEPS } from './types';

export const LossbackBonusTemplateCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: LossbackBonusTemplateCreateModalProps) => {
  const { pronet } = useApi();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Form data
  const [formData, setFormData] = useState<FormData>({
    name: '',
    maxBalance: 0,
    lossbackPercentage: 0,
    happyHoursStart: '18:00',
    happyHoursEnd: '22:00',
    happyHoursBoostPercentage: 0,
    depositWithDrawDifferenceThreshold: 0,
    validForDays: null,
    rules: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const handleClose = () => {
    setCurrentStep(1);
    setFormData({
      name: '',
      maxBalance: 0,
      lossbackPercentage: 0,
      happyHoursStart: '18:00',
      happyHoursEnd: '22:00',
      happyHoursBoostPercentage: 0,
      depositWithDrawDifferenceThreshold: 0,
      validForDays: null,
      rules: [],
    });
    setErrors({});
    setSubmitError(null);
    onClose();
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  // Rule handlers
  const handleAddRule = () => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: '',
          operator: '',
          firstOperand: '',
          secondOperand: null,
          startsInSeconds: null,
          endsInSeconds: null,
        },
      ],
    }));
  };

  const handleRemoveRule = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  };

  const handleUpdateRule = (index: number, field: any, value: any) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.map((rule, i) =>
        i === index ? { ...rule, [field]: value } : rule
      ),
    }));
  };

  // Navigation
  const handleNext = () => {
    if (validateCurrentStep()) {
      setCurrentStep((prev) => Math.min(prev + 1, 2));
    }
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  // Validation
  const validateCurrentStep = (): boolean => {
    const newErrors: FormErrors = {};

    if (currentStep === 1) {
      if (!formData.name.trim()) {
        newErrors.name = 'Template name is required';
      }
      if (formData.maxBalance <= 0) {
        newErrors.maxBalance = 'Max balance must be greater than 0';
      }
      if (
        formData.lossbackPercentage <= 0 ||
        formData.lossbackPercentage > 100
      ) {
        newErrors.lossbackPercentage =
          'Lossback percentage must be between 0 and 100';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateCurrentStep()) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const request = new LossbackBonusTemplateCreateRequest({
        name: formData.name,
        maxBalance: formData.maxBalance,
        lossbackPercentage: formData.lossbackPercentage,
        happyHoursStart: formData.happyHoursStart,
        happyHoursEnd: formData.happyHoursEnd,
        validForDays: formData.validForDays || undefined,
        rules: formData.rules,
      });

      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        handleClose();
      } else {
        setSubmitError(
          response.error || 'Failed to create lossback bonus template'
        );
      }
    } catch (error) {
      setSubmitError(
        error instanceof Error ? error.message : 'An unexpected error occurred'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Step1
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
          />
        );
      case 2:
        return (
          <Step2
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
            onAddRule={handleAddRule}
            onRemoveRule={handleRemoveRule}
            onUpdateRule={handleUpdateRule}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-lg bg-dark-800 border border-dark-600 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-300"
                  >
                    Create Lossback Bonus Template
                  </Dialog.Title>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="p-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Step Indicator */}
                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    {STEPS.map((step, index) => (
                      <div key={step.id} className="flex items-center">
                        <div
                          className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                            currentStep >= step.id
                              ? 'border-blue-500 bg-blue-500 text-white'
                              : 'border-dark-600 text-gray-400'
                          }`}
                        >
                          {step.id}
                        </div>
                        <div className="ml-2 hidden sm:block">
                          <div className="text-sm font-medium text-gray-300">
                            {step.title}
                          </div>
                          <div className="text-xs text-gray-400">
                            {step.description}
                          </div>
                        </div>
                        {index < STEPS.length - 1 && (
                          <div className="flex-1 h-px bg-dark-600 mx-4" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Step Content */}
                <div className="mb-6 min-h-[400px]">{renderCurrentStep()}</div>

                {/* Error Display */}
                {submitError && (
                  <div className="mb-4 p-3 bg-red-900/20 border border-red-700 rounded-md">
                    <p className="text-sm text-red-400">{submitError}</p>
                  </div>
                )}

                {/* Footer */}
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={currentStep === 1 ? onClose : handlePrevious}
                    disabled={isSubmitting}
                    className="flex items-center gap-2"
                  >
                    {currentStep === 1 ? (
                      'Cancel'
                    ) : (
                      <>
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </>
                    )}
                  </Button>

                  {currentStep < STEPS.length ? (
                    <Button
                      onClick={handleNext}
                      disabled={isSubmitting}
                      className="flex items-center gap-2"
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  ) : (
                    <Button
                      onClick={handleSubmit}
                      disabled={isSubmitting}
                      className="flex items-center gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          Creating...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4" />
                          Create Template
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
