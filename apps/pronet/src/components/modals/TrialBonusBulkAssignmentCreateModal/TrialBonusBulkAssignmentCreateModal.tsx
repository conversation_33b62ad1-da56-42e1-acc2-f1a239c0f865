import { useState, useCallback, useEffect } from 'react';
import { BulkAssignmentCreateModal } from '@panels/ui';
import { AlertCircle, Users, FileText } from 'lucide-react';
import { useApi } from '../../../features/api/useApi';
import { useTrialBonuses } from '../../../hooks/useTrialBonuses';
import {
  TrialBonusBulkAssignmentJobCreateRequest,
  type TrialBonusBulkAssignmentRequestCreateOptions,
} from '@panels/api';
import Input from '../../ui/Input';
import type {
  FormData,
  FormErrors,
  TrialBonusBulkAssignmentCreateModalProps,
} from './types';

export const TrialBonusBulkAssignmentCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: TrialBonusBulkAssignmentCreateModalProps) => {
  const { pronet } = useApi();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form data
  const [formData, setFormData] = useState<FormData>({
    bonusId: null,
    customerIds: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Load trial bonuses
  const {
    data: bonuses,
    isLoading: isLoadingBonuses,
    error: bonusesError,
  } = useTrialBonuses({
    page: 1,
    limit: 100,
    // filters: {},
    sortField: 'name',
    sortDirection: 'asc',
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        bonusId: null,
        customerIds: '',
      });
      setErrors({});
    }
  }, [isOpen]);

  // Handle input changes
  const handleInputChange = useCallback(
    (field: string | number | symbol, value: any) => {
      const fieldKey = field as keyof FormData;
      setFormData((prev) => ({ ...prev, [fieldKey]: value }));
      // Clear error when user starts typing
      if (errors[fieldKey]) {
        setErrors((prev) => ({ ...prev, [fieldKey]: undefined }));
      }
    },
    [errors]
  );

  // Validation
  const canSubmit = useCallback((data: FormData) => {
    return !!(data.bonusId && data.customerIds.trim());
  }, []);

  // Submit handler
  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true);
    setErrors({});

    try {
      // Parse customer IDs
      const customerIds = formData.customerIds
        .split(/[,\n]/)
        .map((id) => id.trim())
        .filter((id) => id)
        .map((id) => parseInt(id));

      // Validate customer IDs
      if (customerIds.length === 0) {
        setErrors({ customerIds: 'Please provide at least one customer ID' });
        return;
      }

      const invalidIds = customerIds.filter((id) => isNaN(id));
      if (invalidIds.length > 0) {
        setErrors({ customerIds: 'All customer IDs must be valid numbers' });
        return;
      }

      const requestOptions: TrialBonusBulkAssignmentRequestCreateOptions = {
        trialBonusId: formData.bonusId!,
        externalCustomerIds: customerIds,
      };

      const request = new TrialBonusBulkAssignmentJobCreateRequest(
        requestOptions
      );
      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        setErrors({
          submit: response.error || 'Failed to create bulk assignment job',
        });
      }
    } catch (error) {
      setErrors({
        submit:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, pronet, onSuccess, onClose]);

  return (
    <BulkAssignmentCreateModal
      isOpen={isOpen}
      onClose={onClose}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Bulk Assignment Job"
      canSubmit={canSubmit}
    >
      {({ formData, errors, onInputChange }) => (
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center gap-3 mb-6">
            <Users className="h-6 w-6 text-blue-400" />
            <div>
              <h3 className="text-lg font-medium text-gray-300">
                Bulk Trial Bonus Assignment
              </h3>
              <p className="text-sm text-gray-400">
                Assign a trial bonus to multiple customers at once
              </p>
            </div>
          </div>

          {/* Trial Bonus Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Trial Bonus *
            </label>

            {isLoadingBonuses ? (
              <div className="text-sm text-gray-400">
                Loading trial bonuses...
              </div>
            ) : bonusesError ? (
              <div className="text-sm text-red-400 flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                {bonusesError}
              </div>
            ) : (
              <select
                value={formData.bonusId || ''}
                onChange={(e) =>
                  onInputChange(
                    'bonusId',
                    e.target.value ? parseInt(e.target.value) : null
                  )
                }
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a trial bonus...</option>
                {bonuses.map((bonus) => (
                  <option key={bonus.id} value={bonus.id}>
                    {bonus.bonus.name} - ₺{bonus.amount.toFixed(2)} (
                    {bonus.externalBonusName})
                  </option>
                ))}
              </select>
            )}
            {errors.bonusId && (
              <p className="mt-1 text-sm text-red-400">{errors.bonusId}</p>
            )}
          </div>

          {/* Customer IDs */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Customer IDs *
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <textarea
                value={formData.customerIds}
                onChange={(e) => onInputChange('customerIds', e.target.value)}
                placeholder="Enter customer IDs separated by commas or new lines..."
                rows={6}
                className="w-full pl-10 pr-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
              />
            </div>
            <div className="mt-2 text-sm text-gray-400">
              <p>• Enter one customer ID per line or separate with commas</p>
              <p>• Example: 12345, 67890, 11111</p>
              <p>• Only numeric customer IDs are accepted</p>
            </div>
            {errors.customerIds && (
              <p className="mt-1 text-sm text-red-400">{errors.customerIds}</p>
            )}
          </div>

          {/* Customer Count Preview */}
          {formData.customerIds.trim() && (
            <div className="p-3 bg-blue-900/20 border border-blue-700 rounded-md">
              <div className="flex items-center gap-2 text-blue-300">
                <Users className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {
                    formData.customerIds
                      .split(/[,\n]/)
                      .map((id) => id.trim())
                      .filter((id) => id).length
                  }{' '}
                  customers will be processed
                </span>
              </div>
            </div>
          )}

          {/* Submit Error */}
          {errors.submit && (
            <div className="text-sm text-red-400 flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              {errors.submit}
            </div>
          )}
        </div>
      )}
    </BulkAssignmentCreateModal>
  );
};
