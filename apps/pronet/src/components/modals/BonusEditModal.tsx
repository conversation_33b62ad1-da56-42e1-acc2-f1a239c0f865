import React, { useState, useEffect } from 'react';
import { Edit, AlertCircle } from 'lucide-react';
import { Modal } from '@panels/ui';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { useApi } from '../../features/api/useApi';
import { BonusUpdateRequest, type Bonus } from '@panels/api';

interface BonusEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  bonus: Bonus | null;
  onSuccess: () => void;
}

interface FormData {
  description: string;
  reward: string;
}

interface FormErrors {
  description?: string;
  reward?: string;
  submit?: string;
}

export const BonusEditModal: React.FC<BonusEditModalProps> = ({
  isOpen,
  onClose,
  bonus,
  onSuccess,
}) => {
  const { pronet } = useApi();
  const [formData, setFormData] = useState<FormData>({
    description: '',
    reward: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  // Populate form when modal opens or bonus changes
  useEffect(() => {
    if (isOpen && bonus) {
      setFormData({
        description: bonus.description || '',
        reward: bonus.reward || '',
      });
      setErrors({});
    }
  }, [isOpen, bonus]);

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.trim().length < 3) {
      newErrors.description = 'Description must be at least 3 characters';
    } else if (formData.description.trim().length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    if (!formData.reward.trim()) {
      newErrors.reward = 'Reward is required';
    } else if (formData.reward.trim().length > 100) {
      newErrors.reward = 'Reward must be less than 100 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const handleSubmit = async () => {
    if (!bonus || !validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const request = new BonusUpdateRequest({
        id: bonus.id,
        description: formData.description.trim(),
        reward: formData.reward.trim(),
      });

      const response = await pronet.execute(request);

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        setErrors({
          submit: response.error || 'Failed to update bonus',
        });
      }
    } catch (error) {
      setErrors({
        submit: error instanceof Error ? error.message : 'Failed to update bonus',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
      setErrors({});
    }
  };

  if (!bonus) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Edit Bonus"
      size="md"
    >
      <div className="space-y-6">
        {/* Bonus Info */}
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-md p-4">
          <p className="text-sm text-blue-400">
            <strong>Editing bonus:</strong> {bonus.name} (ID: #{bonus.id})
          </p>
        </div>

        {/* Error Display */}
        {errors.submit && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-md p-4 flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
            <p className="text-sm text-red-400">{errors.submit}</p>
          </div>
        )}

        {/* Form Fields */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={3}
              placeholder="Enter bonus description..."
              disabled={isLoading}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-400">{errors.description}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Reward *
            </label>
            <Input
              type="text"
              value={formData.reward}
              onChange={(e) => handleInputChange('reward', e.target.value)}
              placeholder="e.g., 10%, $50, 100 Free Spins"
              disabled={isLoading}
              error={errors.reward}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t border-dark-600">
          <Button
            onClick={handleClose}
            variant="outline"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="primary"
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <Edit className="w-4 h-4" />
                Update Bonus
              </>
            )}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
