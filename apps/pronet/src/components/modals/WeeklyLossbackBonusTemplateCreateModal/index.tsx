import { useState } from 'react';
import { BonusTemplateCreateModal } from '@panels/ui';
import { Step1 } from './Step1';
import { Step2 } from './Step2';
import { useApi } from '../../../features/api/useApi';
import { WeeklyLossbackBonusTemplateCreateRequest } from '@panels/api';
import type {
  FormData,
  FormErrors,
  WeeklyLossbackBonusTemplateCreateModalProps,
} from './types';
import { STEPS } from './types';

export const WeeklyLossbackBonusTemplateCreateModal = ({
  isOpen,
  onClose,
  onSuccess,
}: WeeklyLossbackBonusTemplateCreateModalProps) => {
  const { pronet } = useApi();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form data
  const [formData, setFormData] = useState<FormData>({
    name: '',
    maxBalance: 0,
    lossbackPercentage: 0,
    requestWindowStartSeconds: 432000, // 5 days in seconds
    requestWindowEndSeconds: 604800, // 7 days in seconds
    validForDays: null,
    rules: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const handleClose = () => {
    setCurrentStep(1);
    setFormData({
      name: '',
      maxBalance: 0,
      lossbackPercentage: 0,
      requestWindowStartSeconds: 432000,
      requestWindowEndSeconds: 604800,
      validForDays: null,
      rules: [],
    });
    setErrors({});
    onClose();
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  // Rule handlers
  const handleAddRule = () => {
    setFormData((prev) => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
          criterium: 'ipConflictCount',
          operator: 'lte',
          firstOperand: '2',
          secondOperand: null,
          startsInSeconds: null,
          endsInSeconds: null,
        },
      ],
    }));
  };

  const handleRemoveRule = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index),
    }));
  };

  const handleUpdateRule = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      rules: prev.rules.map((rule, i) =>
        i === index ? { ...rule, [field]: value } : rule
      ),
    }));
  };

  // Validation
  const validateStep = (step: number): FormErrors => {
    const newErrors: FormErrors = {};

    if (step === 1) {
      if (!formData.name.trim()) {
        newErrors.name = 'Template name is required';
      }
      if (formData.maxBalance <= 0) {
        newErrors.maxBalance = 'Max balance must be greater than 0';
      }
      if (
        formData.lossbackPercentage <= 0 ||
        formData.lossbackPercentage > 100
      ) {
        newErrors.lossbackPercentage =
          'Lossback percentage must be between 0 and 100';
      }
      if (formData.happyHoursBoostPercentage < 0) {
        newErrors.happyHoursBoostPercentage =
          'Happy hours boost percentage cannot be negative';
      }
      if (formData.requestWindowStartSeconds < 0) {
        newErrors.requestWindowStartSeconds =
          'Request window start cannot be negative';
      }
      if (
        formData.requestWindowEndSeconds <= formData.requestWindowStartSeconds
      ) {
        newErrors.requestWindowEndSeconds =
          'Request window end must be after start';
      }
    }

    return newErrors;
  };

  // Submit
  const handleSubmit = async () => {
    const errors = validateStep(currentStep);
    if (Object.keys(errors).length > 0) {
      setErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      const request = new WeeklyLossbackBonusTemplateCreateRequest({
        name: formData.name,
        maxBalance: formData.maxBalance,
        lossbackPercentage: formData.lossbackPercentage,
        happyHoursStart: formData.happyHoursStart,
        happyHoursEnd: formData.happyHoursEnd,
        happyHoursBoostPercentage: formData.happyHoursBoostPercentage,
        requestWindowStartSeconds: formData.requestWindowStartSeconds,
        requestWindowEndSeconds: formData.requestWindowEndSeconds,
        validForDays: formData.validForDays || undefined,
        rules: formData.rules,
      });

      const response = await pronet.makeRequest(request);

      if (response.success) {
        onSuccess();
        handleClose();
      } else {
        setErrors({
          submit:
            response.error || 'Failed to create weekly lossback bonus template',
        });
      }
    } catch (error) {
      setErrors({
        submit:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
  };

  const canGoNext = (step: number): boolean => {
    return Object.keys(validateStep(step)).length === 0;
  };

  const canGoPrevious = (step: number): boolean => {
    return step > 1;
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Step1
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
          />
        );
      case 2:
        return (
          <Step2
            formData={formData}
            errors={errors}
            onInputChange={handleInputChange}
            onAddRule={handleAddRule}
            onRemoveRule={handleRemoveRule}
            onUpdateRule={handleUpdateRule}
          />
        );
      default:
        return null;
    }
  };

  return (
    <BonusTemplateCreateModal
      isOpen={isOpen}
      onClose={handleClose}
      steps={STEPS}
      currentStep={currentStep}
      onStepChange={handleStepChange}
      formData={formData}
      errors={errors}
      onInputChange={handleInputChange}
      isSubmitting={isSubmitting}
      onSubmit={handleSubmit}
      submitButtonText="Create Weekly Lossback Bonus Template"
      canGoNext={canGoNext}
      canGoPrevious={canGoPrevious}
      bonusType="weekly lossback bonus template"
    >
      {() => renderCurrentStep()}
    </BonusTemplateCreateModal>
  );
};
