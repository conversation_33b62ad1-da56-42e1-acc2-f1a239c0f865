import { useState, useEffect } from 'react';
import { useApi } from '../features/api/useApi';
import {
  CashBonusSearchRequest,
  type CashBonus,
  type CashBonusSearchRequestOptions,
} from '@panels/api';

interface UseCashBonusesOptions {
  page: number;
  limit: number;
  filters?: Record<string, any>;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
}

interface UseCashBonusesResult {
  data: CashBonus[];
  total: number;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useCashBonuses = ({
  page,
  limit,
  filters = {},
  sortField,
  sortDirection,
}: UseCashBonusesOptions): UseCashBonusesResult => {
  const { pronet } = useApi();
  const [data, setData] = useState<CashBonus[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const requestOptions: CashBonusSearchRequestOptions = {
        page,
        limit,
        ...filters,
      };

      const request = new CashBonusSearchRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setData(response.data.items);
        setTotal(response.data.total);
      } else {
        setError(response.error || 'Failed to fetch cash bonuses');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [page, limit, JSON.stringify(filters), sortField, sortDirection]);

  return {
    data,
    total,
    isLoading,
    error,
    refetch: fetchData,
  };
};
