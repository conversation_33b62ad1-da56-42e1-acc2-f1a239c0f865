import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  SiteClaimableBonusSearchRequest,
  SiteClaimableBonusCreateRequest,
  SiteClaimableBonusUpdateRequest,
  SiteClaimableBonusDeleteRequest,
  SiteClaimableBonusRemoveRequest,
  SiteClaimableBonusToggleRequest,
  SiteClaimableBonusClaimableRequest,
  SiteClaimableBonusCheckRequest,
  type SiteClaimableBonusCreateRequestOptions,
  type SiteClaimableBonusUpdateRequestOptions,
  type SiteClaimableBonusDeleteRequestOptions,
  type SiteClaimableBonusRemoveRequestOptions,
  type SiteClaimableBonusToggleRequestOptions,
  type SiteClaimableBonusCheckRequestOptions,
  type SiteClaimableBonus,
  type ClaimableBonusCheck,
} from '@panels/api';

interface UseSiteClaimableBonusesState {
  data: SiteClaimableBonus[];
  isLoading: boolean;
  error: string | null;
}

export const useSiteClaimableBonuses = () => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseSiteClaimableBonusesState>({
    data: [],
    isLoading: true,
    error: null,
  });

  const fetchBonuses = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const request = new SiteClaimableBonusSearchRequest();
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch site claimable bonuses',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [pronet]);

  const updateBonus = useCallback(async (options: { slotName: string; bonusId: number }) => {
    try {
      const updateRequest = new SiteClaimableBonusUpdateRequest({
        slotName: options.slotName,
        bonusId: options.bonusId,
      });
      const response = await pronet.makeRequest(updateRequest);

      if (response.success) {
        // Update the local state with the updated bonus
        setState((prev) => ({
          ...prev,
          data: prev.data.map((bonus) =>
            bonus.slotName === options.slotName ? response.data : bonus
          ),
        }));
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error || 'Failed to update bonus' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }, [pronet]);

  const toggleBonus = useCallback(async (options: SiteClaimableBonusToggleRequestOptions) => {
    try {
      const request = new SiteClaimableBonusToggleRequest(options);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        // Update the local state with the toggled bonus
        setState((prev) => ({
          ...prev,
          data: prev.data.map((bonus) =>
            bonus.slotName === options.slotName ? response.data : bonus
          ),
        }));
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error || 'Failed to toggle bonus' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }, [pronet]);

  const createBonus = useCallback(async (options: SiteClaimableBonusCreateRequestOptions) => {
    try {
      const request = new SiteClaimableBonusCreateRequest(options);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        // Add the new bonus to the local state
        setState((prev) => ({
          ...prev,
          data: [...prev.data, response.data],
        }));
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error || 'Failed to create bonus slot' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }, [pronet]);

  const deleteBonus = useCallback(async (options: SiteClaimableBonusDeleteRequestOptions) => {
    try {
      const request = new SiteClaimableBonusDeleteRequest(options);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        // Remove the bonus from the local state
        setState((prev) => ({
          ...prev,
          data: prev.data.filter((bonus) => bonus.slotName !== options.slotName),
        }));
        return { success: true, data: response.data };
      } else {
        return { success: false, error: response.error || 'Failed to delete bonus slot' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }, [pronet]);

  useEffect(() => {
    fetchBonuses();
  }, [fetchBonuses]);

  const refetch = useCallback(() => {
    fetchBonuses();
  }, [fetchBonuses]);

  return {
    ...state,
    refetch,
    updateBonus,
    toggleBonus,
    createBonus,
    deleteBonus,
  };
};
