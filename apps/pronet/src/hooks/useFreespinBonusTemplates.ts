import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  FreespinBonusTemplateSearchRequest,
  type FreespinBonusTemplateSearchRequestOptions,
} from '@panels/api';
import type { FreespinBonusTemplate } from '@panels/api';

interface UseFreespinBonusTemplatesState {
  data: FreespinBonusTemplate[];
  total: number;
  isLoading: boolean;
  error: string | null;
}

interface UseFreespinBonusTemplatesOptions {
  page: number;
  limit: number;
  filters?: Record<string, unknown>;
  sortField?: string | null;
  sortDirection?: 'asc' | 'desc';
}

export const useFreespinBonusTemplates = (
  options: UseFreespinBonusTemplatesOptions
) => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseFreespinBonusTemplatesState>({
    data: [],
    total: 0,
    isLoading: true,
    error: null,
  });

  const fetchTemplates = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert filters and options to the request format
      const requestOptions: FreespinBonusTemplateSearchRequestOptions = {
        page: options.page,
        limit: options.limit,
        // Add any additional filters here
        ...options.filters,
      };

      const request = new FreespinBonusTemplateSearchRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data.items,
          total: response.data.total,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch freespin bonus templates',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [
    pronet,
    options.page,
    options.limit,
    options.filters,
    options.sortField,
    options.sortDirection,
  ]);

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  const refetch = useCallback(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  return {
    ...state,
    refetch,
  };
};
