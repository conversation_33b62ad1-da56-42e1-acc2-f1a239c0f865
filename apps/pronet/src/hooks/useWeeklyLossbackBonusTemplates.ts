import { useState, useEffect } from 'react';
import { useApi } from '../features/api/useApi';
import {
  WeeklyLossbackBonusTemplateSearchRequest,
  type WeeklyLossbackBonusTemplate,
} from '@panels/api';

export interface UseWeeklyLossbackBonusTemplatesOptions {
  page?: number;
  limit?: number;
  filters?: Record<string, unknown>;
  sortField?: string | null;
  sortDirection?: 'asc' | 'desc';
}

export interface UseWeeklyLossbackBonusTemplatesResult {
  data: WeeklyLossbackBonusTemplate[] | null;
  total: number | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useWeeklyLossbackBonusTemplates = (
  options: UseWeeklyLossbackBonusTemplatesOptions = {}
): UseWeeklyLossbackBonusTemplatesResult => {
  const { pronet } = useApi();
  const [data, setData] = useState<WeeklyLossbackBonusTemplate[] | null>(null);
  const [total, setTotal] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const request = new WeeklyLossbackBonusTemplateSearchRequest({
        page: options.page || 1,
        limit: options.limit || 25,
        isActive: options.filters?.isActive as boolean | undefined,
      });

      const response = await pronet.makeRequest(request);

      if (response.success) {
        setData(response.data.items);
        setTotal(response.data.total);
      } else {
        setError(response.error || 'Failed to fetch weekly lossback bonus templates');
        setData(null);
        setTotal(null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setData(null);
      setTotal(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [
    options.page,
    options.limit,
    options.filters,
    options.sortField,
    options.sortDirection,
  ]);

  return {
    data,
    total,
    isLoading,
    error,
    refetch: fetchData,
  };
};
