import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  BonusPromocodeActivationsByCustomerSearchRequest,
  type BonusPromocodeActivationsByCustomerSearchRequestOptions,
} from '@panels/api';
import type { BonusPromocodeActivation } from '@panels/api';

interface UseBonusPromocodeActivationsByCustomerState {
  data: BonusPromocodeActivation[];
  total: number;
  isLoading: boolean;
  error: string | null;
}

interface UseBonusPromocodeActivationsByCustomerOptions {
  customerId: number;
  page: number;
  limit: number;
}

export const useBonusPromocodeActivationsByCustomer = (options: UseBonusPromocodeActivationsByCustomerOptions) => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseBonusPromocodeActivationsByCustomerState>({
    data: [],
    total: 0,
    isLoading: true,
    error: null,
  });

  const fetchPromocodeActivations = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const requestOptions: BonusPromocodeActivationsByCustomerSearchRequestOptions = {
        customerId: options.customerId,
        page: options.page,
        limit: options.limit,
      };

      const request = new BonusPromocodeActivationsByCustomerSearchRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data.items,
          total: response.data.total,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch promo code activations',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [
    pronet,
    options.customerId,
    options.page,
    options.limit,
  ]);

  useEffect(() => {
    fetchPromocodeActivations();
  }, [fetchPromocodeActivations]);

  const refetch = useCallback(() => {
    fetchPromocodeActivations();
  }, [fetchPromocodeActivations]);

  return {
    ...state,
    refetch,
  };
};
