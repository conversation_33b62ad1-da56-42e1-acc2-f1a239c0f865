import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  HappyHoursBonusSearchRequest,
  type HappyHoursBonusSearchRequestOptions,
} from '@panels/api';
import type { HappyHoursBonus } from '@panels/api';

interface UseHappyHoursBonusesState {
  data: HappyHoursBonus[];
  total: number;
  isLoading: boolean;
  error: string | null;
}

interface UseHappyHoursBonusesOptions {
  page: number;
  limit: number;
  filters?: Record<string, unknown>;
  sortField?: string | null;
  sortDirection?: 'asc' | 'desc';
}

export const useHappyHoursBonuses = (options: UseHappyHoursBonusesOptions) => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseHappyHoursBonusesState>({
    data: [],
    total: 0,
    isLoading: true,
    error: null,
  });

  const fetchBonuses = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert filters and options to the request format
      const requestOptions: HappyHoursBonusSearchRequestOptions = {
        page: options.page,
        limit: options.limit,
        // Add any additional filters here
        ...options.filters,
      };

      const request = new HappyHoursBonusSearchRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data.items,
          total: response.data.total,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch happy hours bonuses',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [
    pronet,
    options.page,
    options.limit,
    options.filters,
    options.sortField,
    options.sortDirection,
  ]);

  useEffect(() => {
    fetchBonuses();
  }, [fetchBonuses]);

  const refetch = useCallback(() => {
    fetchBonuses();
  }, [fetchBonuses]);

  return {
    ...state,
    refetch,
  };
};
