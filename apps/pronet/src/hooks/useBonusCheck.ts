import { useState, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  SiteClaimableBonusCheckRequest,
  type SiteClaimableBonusCheckRequestOptions,
  type ClaimableBonusCheck,
} from '@panels/api';

interface UseBonusCheckState {
  isLoading: boolean;
  error: string | null;
}

export const useBonusCheck = () => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseBonusCheckState>({
    isLoading: false,
    error: null,
  });

  const checkBonus = useCallback(async (options: SiteClaimableBonusCheckRequestOptions): Promise<{
    success: boolean;
    data?: ClaimableBonusCheck;
    error?: string;
  }> => {
    setState({ isLoading: true, error: null });

    try {
      const request = new SiteClaimableBonusCheckRequest(options);
      const response = await pronet.makeRequest(request);

      setState({ isLoading: false, error: null });

      if (response.success) {
        return { success: true, data: response.data };
      } else {
        const error = response.error || 'Failed to check bonus';
        setState({ isLoading: false, error });
        return { success: false, error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setState({ isLoading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }, [pronet]);

  return {
    ...state,
    checkBonus,
  };
};
