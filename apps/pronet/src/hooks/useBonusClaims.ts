import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  BonusClaimSearchRequest,
  type BonusClaimSearchRequestOptions,
} from '@panels/api';
import type { BonusClaim } from '@panels/api';

interface UseBonusClaimsState {
  data: BonusClaim[];
  total: number;
  isLoading: boolean;
  error: string | null;
}

interface UseBonusClaimsOptions {
  page: number;
  limit: number;
  filters?: Record<string, unknown>;
  sortField?: string | null;
  sortDirection?: 'asc' | 'desc';
}

export const useBonusClaims = (options: UseBonusClaimsOptions) => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseBonusClaimsState>({
    data: [],
    total: 0,
    isLoading: true,
    error: null,
  });

  const fetchClaims = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert filters and options to the request format
      const requestOptions: BonusClaimSearchRequestOptions = {
        page: options.page,
        limit: options.limit,
        // Add any additional filters here
        ...options.filters,
      };

      const request = new BonusClaimSearchRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data.items,
          total: response.data.total,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch bonus claims',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [
    pronet,
    options.page,
    options.limit,
    options.filters,
    options.sortField,
    options.sortDirection,
  ]);

  useEffect(() => {
    fetchClaims();
  }, [fetchClaims]);

  const refetch = useCallback(() => {
    fetchClaims();
  }, [fetchClaims]);

  return {
    ...state,
    refetch,
  };
};
