import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  BonusPromocodeSearchRequest,
  type BonusPromocodeSearchRequestOptions,
} from '@panels/api';
import type { BonusPromocode } from '@panels/api';

interface UseBonusPromocodesState {
  data: BonusPromocode[];
  total: number;
  isLoading: boolean;
  error: string | null;
}

interface UseBonusPromocodesOptions {
  page: number;
  limit: number;
  filters?: Record<string, unknown>;
  sortField?: string | null;
  sortDirection?: 'asc' | 'desc';
}

export const useBonusPromocodes = (options: UseBonusPromocodesOptions) => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseBonusPromocodesState>({
    data: [],
    total: 0,
    isLoading: true,
    error: null,
  });

  const fetchPromocodes = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert filters and options to the request format
      const requestOptions: BonusPromocodeSearchRequestOptions = {
        page: options.page,
        limit: options.limit,
        // Add any additional filters here
        ...options.filters,
      };

      const request = new BonusPromocodeSearchRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data.items,
          total: response.data.total,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch bonus promocodes',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [
    pronet,
    options.page,
    options.limit,
    options.filters,
    options.sortField,
    options.sortDirection,
  ]);

  useEffect(() => {
    fetchPromocodes();
  }, [fetchPromocodes]);

  const refetch = useCallback(() => {
    fetchPromocodes();
  }, [fetchPromocodes]);

  return {
    ...state,
    refetch,
  };
};
