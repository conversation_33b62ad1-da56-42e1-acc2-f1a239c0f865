import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  TrialBonusExternalListRequest,
  type TrialBonusExternalListRequestOptions,
} from '@panels/api';
import type { TrialBonusExternal } from '@panels/api';

interface UseTrialBonusExternalsState {
  data: TrialBonusExternal[];
  total: number;
  isLoading: boolean;
  error: string | null;
}

interface UseTrialBonusExternalsOptions {
  page: number;
  limit: number;
  filters?: Record<string, unknown>;
  sortField?: string | null;
  sortDirection?: 'asc' | 'desc';
}

export const useTrialBonusExternals = (
  options: UseTrialBonusExternalsOptions
) => {
  const { pronet } = useApi();

  const [state, setState] = useState<UseTrialBonusExternalsState>({
    data: [],
    total: 0,
    isLoading: true,
    error: null,
  });

  const fetchExternals = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const requestOptions: TrialBonusExternalListRequestOptions = {
        page: options.page,
        limit: options.limit,
        ...options.filters,
      };

      const request = new TrialBonusExternalListRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data.items,
          total: response.data.total,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch external trial bonuses',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [
    pronet,
    options.page,
    options.limit,
    options.filters,
    options.sortField,
    options.sortDirection,
  ]);

  useEffect(() => {
    fetchExternals();
  }, [fetchExternals]);

  const refetch = useCallback(() => {
    fetchExternals();
  }, [fetchExternals]);

  return {
    ...state,
    refetch,
  };
};
