import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  BonusClaimsByCustomerSearchRequest,
  type BonusClaimsByCustomerSearchRequestOptions,
} from '@panels/api';
import type { BonusClaim } from '@panels/api';

interface UseBonusClaimsByCustomerState {
  data: BonusClaim[];
  total: number;
  isLoading: boolean;
  error: string | null;
}

interface UseBonusClaimsByCustomerOptions {
  customerId: number;
  page: number;
  limit: number;
}

export const useBonusClaimsByCustomer = (options: UseBonusClaimsByCustomerOptions) => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseBonusClaimsByCustomerState>({
    data: [],
    total: 0,
    isLoading: true,
    error: null,
  });

  const fetchBonusClaims = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const requestOptions: BonusClaimsByCustomerSearchRequestOptions = {
        customerId: options.customerId,
        page: options.page,
        limit: options.limit,
      };

      const request = new BonusClaimsByCustomerSearchRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data.items,
          total: response.data.total,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch bonus claims',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [
    pronet,
    options.customerId,
    options.page,
    options.limit,
  ]);

  useEffect(() => {
    fetchBonusClaims();
  }, [fetchBonusClaims]);

  const refetch = useCallback(() => {
    fetchBonusClaims();
  }, [fetchBonusClaims]);

  return {
    ...state,
    refetch,
  };
};
