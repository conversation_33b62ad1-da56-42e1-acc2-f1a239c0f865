import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  SiteClaimableBonusClaimableRequest,
  type SiteClaimableBonus,
} from '@panels/api';

interface UseClaimableBonusesState {
  data: SiteClaimableBonus[];
  isLoading: boolean;
  error: string | null;
}

export const useClaimableBonuses = () => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseClaimableBonusesState>({
    data: [],
    isLoading: true,
    error: null,
  });

  const fetchClaimableBonuses = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const request = new SiteClaimableBonusClaimableRequest();
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch claimable bonuses',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [pronet]);

  useEffect(() => {
    fetchClaimableBonuses();
  }, [fetchClaimableBonuses]);

  const refetch = useCallback(() => {
    fetchClaimableBonuses();
  }, [fetchClaimableBonuses]);

  return {
    ...state,
    refetch,
  };
};
