import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  CustomerSearchRequest,
  type CustomerSearchRequestOptions,
} from '@panels/api';
import type { Customer } from '@panels/api';

interface UseCustomersState {
  data: Customer[];
  total: number;
  isLoading: boolean;
  error: string | null;
}

interface UseCustomersOptions {
  page: number;
  limit: number;
  filters?: Record<string, unknown>;
  sortField?: string | null;
  sortDirection?: 'asc' | 'desc';
}

export const useCustomers = (options: UseCustomersOptions) => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseCustomersState>({
    data: [],
    total: 0,
    isLoading: true,
    error: null,
  });

  const fetchCustomers = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Convert filters and options to the request format
      const requestOptions: CustomerSearchRequestOptions = {
        page: options.page,
        limit: options.limit,
        // Add any additional filters here
        ...options.filters,
      };

      const request = new CustomerSearchRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data.items,
          total: response.data.total,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch customers',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [
    pronet,
    options.page,
    options.limit,
    options.filters,
    options.sortField,
    options.sortDirection,
  ]);

  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  const refetch = useCallback(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  return {
    ...state,
    refetch,
  };
};
