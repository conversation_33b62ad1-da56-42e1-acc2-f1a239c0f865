import { useState, useEffect } from 'react';
import { useApi } from '../features/api/useApi';
import {
  CashBonusTemplateSearchRequest,
  type CashBonusTemplate,
  type CashBonusTemplateSearchRequestOptions,
} from '@panels/api';

interface UseCashBonusTemplatesOptions {
  page: number;
  limit: number;
  filters?: Record<string, any>;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
}

interface UseCashBonusTemplatesResult {
  data: CashBonusTemplate[];
  total: number;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useCashBonusTemplates = ({
  page,
  limit,
  filters = {},
  sortField,
  sortDirection,
}: UseCashBonusTemplatesOptions): UseCashBonusTemplatesResult => {
  const { pronet } = useApi();
  const [data, setData] = useState<CashBonusTemplate[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const requestOptions: CashBonusTemplateSearchRequestOptions = {
        page,
        limit,
        ...filters,
      };

      const request = new CashBonusTemplateSearchRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setData(response.data.items);
        setTotal(response.data.total);
      } else {
        setError(response.error || 'Failed to fetch cash bonus templates');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [page, limit, JSON.stringify(filters), sortField, sortDirection]);

  return {
    data,
    total,
    isLoading,
    error,
    refetch: fetchData,
  };
};
