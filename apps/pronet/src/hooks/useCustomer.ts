import { useState, useEffect, useCallback } from 'react';
import { useApi } from '../features/api/useApi';
import {
  CustomerGetRequest,
  type CustomerGetRequestOptions,
} from '@panels/api';
import type { Customer } from '@panels/api';

interface UseCustomerState {
  data: Customer | null;
  isLoading: boolean;
  error: string | null;
}

interface UseCustomerOptions {
  id: number;
}

export const useCustomer = (options: UseCustomerOptions) => {
  const { pronet } = useApi();
  const [state, setState] = useState<UseCustomerState>({
    data: null,
    isLoading: true,
    error: null,
  });

  const fetchCustomer = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const requestOptions: CustomerGetRequestOptions = {
        id: options.id,
      };

      const request = new CustomerGetRequest(requestOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setState({
          data: response.data,
          isLoading: false,
          error: null,
        });
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: response.error || 'Failed to fetch customer',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  }, [pronet, options.id]);

  useEffect(() => {
    fetchCustomer();
  }, [fetchCustomer]);

  const refetch = useCallback(() => {
    fetchCustomer();
  }, [fetchCustomer]);

  return {
    ...state,
    refetch,
  };
};
