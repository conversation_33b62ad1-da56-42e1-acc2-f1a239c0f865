import { FC, ReactNode, useMemo } from 'react';
import { ApiContext } from './ApiContext';
import { ApiClient, AuthorizedApiClient } from '@panels/api';

const apiUrl = import.meta.env.VITE_API_URL;

export const ApiContextProvider: FC<{
  session: string;
  children: ReactNode;
}> = ({ session, children }) => {
  if (!apiUrl) {
    throw new Error('Api url is not defined');
  }

  const clients = useMemo(() => {
    return {
      public: new ApiClient({ baseUrl: apiUrl }),
      internal: new AuthorizedApiClient({ baseUrl: apiUrl }, () =>
        Promise.resolve(session)
      ),
      pronet: new AuthorizedApiClient({ baseUrl: apiUrl }, () =>
        Promise.resolve(session)
      ),
    };
  }, [session]);

  return <ApiContext.Provider value={clients}>{children}</ApiContext.Provider>;
};
