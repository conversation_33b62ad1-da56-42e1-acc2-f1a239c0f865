import { FC, ReactNode, useEffect, useState } from 'react';
import { AuthContext, IAuthContext } from './AuthContext';

export const AuthContextProvider: FC<{
  children: ReactNode | ((ctx: IAuthContext) => ReactNode);
}> = ({ children }) => {
  const [session, setSession] = useState(
    localStorage.getItem('@makroz/pronet/session')
  );
  void setSession;

  const logout = () => {
    document.cookie =
      'environment=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    localStorage.removeItem('@makroz/pronet/session');
    window.location.reload();
  };

  useEffect(() => {
    if (session) {
      return;
    }

    // @todo get session from redirect from root panel
    const query = new URLSearchParams(window.location.search);
    const newSession = query.get('session');
    if (!newSession) {
      return logout();
    }

    localStorage.setItem('@makroz/pronet/session', newSession);
    setTimeout(() => {
      window.location.href = import.meta.env.VITE_ROOT_APP_URL || '/';
    }, 300);
  }, []);

  // @todo refresh session
  useEffect(() => {}, []);

  if (!session) {
    return null;
  }

  return (
    <AuthContext.Provider value={{ session, logout }}>
      {typeof children === 'function'
        ? children({ session, logout })
        : children}
    </AuthContext.Provider>
  );
};
