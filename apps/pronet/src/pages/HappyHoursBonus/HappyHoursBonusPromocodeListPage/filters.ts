import { FilterGroup } from '@panels/ui';

export const createHappyHoursBonusPromocodeFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'code',
        label: 'Promocode',
        type: 'text',
        placeholder: 'Search by promocode...',
      },
      {
        key: 'bonusName',
        label: 'Bonus Name',
        type: 'text',
        placeholder: 'Search by bonus name...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
    ],
  },
  {
    name: 'status',
    displayName: 'Status Filters',
    fields: [
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: '', label: 'All' },
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Inactive' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'maxActivationsMin',
        label: 'Min Max Activations',
        type: 'number',
        placeholder: 'Enter minimum...',
      },
      {
        key: 'maxActivationsMax',
        label: 'Max Max Activations',
        type: 'number',
        placeholder: 'Enter maximum...',
      },
      {
        key: 'activationCountMin',
        label: 'Min Activation Count',
        type: 'number',
        placeholder: 'Enter minimum...',
      },
      {
        key: 'activationCountMax',
        label: 'Max Activation Count',
        type: 'number',
        placeholder: 'Enter maximum...',
      },
    ],
  },
];
