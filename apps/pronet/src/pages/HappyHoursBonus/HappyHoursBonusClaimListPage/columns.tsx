import { Eye, User, Calendar, DollarSign, Clock } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusClaim } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
  }).format(amount);
};

// Helper function to format claim status
const formatClaimStatus = (claim: BonusClaim) => {
  const status = claim.status.toLowerCase();
  let colorClass: string;

  switch (status) {
    case 'pending':
      colorClass = 'bg-yellow-900 text-yellow-300 border-yellow-700';
      break;
    case 'approved':
      colorClass = 'bg-green-900 text-green-300 border-green-700';
      break;
    case 'rejected':
      colorClass = 'bg-red-900 text-red-300 border-red-700';
      break;
    case 'expired':
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
      break;
    default:
      colorClass = 'bg-blue-900 text-blue-300 border-blue-700';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

interface HappyHoursBonusClaimColumnsProps {
  onView: (claim: BonusClaim) => void;
}

export const createHappyHoursBonusClaimColumns = ({
  onView,
}: HappyHoursBonusClaimColumnsProps): TableColumn<BonusClaim>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (claim) => (
      <span className="font-mono text-sm text-gray-300">#{claim.id}</span>
    ),
  },
  {
    key: 'customer',
    label: 'Customer',
    width: '200px',
    sortable: true,
    render: (claim) => (
      <div className="flex items-center space-x-2">
        <User className="h-4 w-4 text-blue-400" />
        <div>
          <div className="text-gray-300 font-medium">
            {claim.customer?.username || `Customer #${claim.customerId}`}
          </div>
          <div className="text-xs text-gray-400">
            External ID: {claim.customer?.externalId || 'N/A'}
          </div>
        </div>
      </div>
    ),
  },
  {
    key: 'bonusName',
    label: 'Bonus',
    width: '200px',
    sortable: true,
    render: (claim) => (
      <span className="text-gray-300">{claim.bonus?.name}</span>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (claim) => formatClaimStatus(claim),
  },
  {
    key: 'amount',
    label: 'Amount',
    width: '120px',
    sortable: true,
    render: (claim) => (
      <div className="flex items-center space-x-1">
        <DollarSign className="h-3 w-3 text-green-400" />
        <span className="text-green-300">
          {claim.amount ? formatCurrency(claim.amount) : 'N/A'}
        </span>
      </div>
    ),
  },
  {
    key: 'claimedAt',
    label: 'Claimed At',
    width: '160px',
    sortable: true,
    render: (claim) => (
      <div className="flex items-center space-x-1">
        <Clock className="h-3 w-3 text-blue-400" />
        <span className="text-sm text-gray-400">
          {formatDate(claim.claimedAt)}
        </span>
      </div>
    ),
  },
  {
    key: 'processedAt',
    label: 'Processed At',
    width: '160px',
    sortable: true,
    render: (claim) => (
      <div className="flex items-center space-x-1">
        <Calendar className="h-3 w-3 text-purple-400" />
        <span className="text-sm text-gray-400">
          {claim.processedAt ? formatDate(claim.processedAt) : 'Not processed'}
        </span>
      </div>
    ),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (claim) => (
      <span className="text-sm text-gray-400">
        {formatDate(claim.createdAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '100px',
    sortable: false,
    sticky: 'right',
    render: (claim) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onView(claim)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];
