import { FilterGroup } from '@panels/ui';

export const createHappyHoursBonusClaimFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'customerName',
        label: 'Customer Name',
        type: 'text',
        placeholder: 'Search by customer name...',
      },
      {
        key: 'customerId',
        label: 'Customer ID',
        type: 'number',
        placeholder: 'Enter customer ID...',
      },
      {
        key: 'bonusName',
        label: 'Bonus Name',
        type: 'text',
        placeholder: 'Search by bonus name...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
    ],
  },
  {
    name: 'status',
    displayName: 'Status Filters',
    fields: [
      {
        key: 'status',
        label: 'Claim Status',
        type: 'select',
        options: [
          { value: '', label: 'All' },
          { value: 'pending', label: 'Pending' },
          { value: 'approved', label: 'Approved' },
          { value: 'rejected', label: 'Rejected' },
          { value: 'expired', label: 'Expired' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'amountMin',
        label: 'Min Amount',
        type: 'number',
        placeholder: 'Enter minimum amount...',
      },
      {
        key: 'amountMax',
        label: 'Max Amount',
        type: 'number',
        placeholder: 'Enter maximum amount...',
      },
      {
        key: 'claimedAtFrom',
        label: 'Claimed From',
        type: 'date',
        placeholder: 'Select start date...',
      },
      {
        key: 'claimedAtTo',
        label: 'Claimed To',
        type: 'date',
        placeholder: 'Select end date...',
      },
      {
        key: 'processedAtFrom',
        label: 'Processed From',
        type: 'date',
        placeholder: 'Select start date...',
      },
      {
        key: 'processedAtTo',
        label: 'Processed To',
        type: 'date',
        placeholder: 'Select end date...',
      },
    ],
  },
];
