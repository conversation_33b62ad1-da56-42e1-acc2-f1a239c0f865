import { Eye, RotateCcw, X, Play, Pause, Users, Clock, CheckCircle, XCircle } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusBulkAssignmentJob } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format job status
const formatJobStatus = (job: BonusBulkAssignmentJob) => {
  const status = job.status.toLowerCase();
  let colorClass: string;
  let icon: React.ReactNode;

  switch (status) {
    case 'pending':
      colorClass = 'bg-yellow-900 text-yellow-300 border-yellow-700';
      icon = <Clock className="h-3 w-3" />;
      break;
    case 'processing':
      colorClass = 'bg-blue-900 text-blue-300 border-blue-700';
      icon = <Play className="h-3 w-3" />;
      break;
    case 'completed':
      colorClass = 'bg-green-900 text-green-300 border-green-700';
      icon = <CheckCircle className="h-3 w-3" />;
      break;
    case 'failed':
      colorClass = 'bg-red-900 text-red-300 border-red-700';
      icon = <XCircle className="h-3 w-3" />;
      break;
    case 'cancelled':
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
      icon = <X className="h-3 w-3" />;
      break;
    default:
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
      icon = <Clock className="h-3 w-3" />;
  }

  return (
    <span
      className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {icon}
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

// Helper function to format progress
const formatProgress = (job: BonusBulkAssignmentJob) => {
  const processed = job.processedCount || 0;
  const total = job.totalCount || 0;
  const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

  return (
    <div className="flex items-center space-x-2">
      <div className="flex-1 bg-dark-700 rounded-full h-2">
        <div
          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${percentage}%` }}
        />
      </div>
      <span className="text-xs text-gray-400 min-w-[60px]">
        {processed}/{total} ({percentage}%)
      </span>
    </div>
  );
};

interface HappyHoursBonusBulkAssignmentColumnsProps {
  onView: (job: BonusBulkAssignmentJob) => void;
  onCancel: (job: BonusBulkAssignmentJob) => void;
  onRetry: (job: BonusBulkAssignmentJob) => void;
}

export const createHappyHoursBonusBulkAssignmentColumns = ({
  onView,
  onCancel,
  onRetry,
}: HappyHoursBonusBulkAssignmentColumnsProps): TableColumn<BonusBulkAssignmentJob>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (job) => (
      <span className="font-mono text-sm text-gray-300">#{job.id}</span>
    ),
  },
  {
    key: 'bonusName',
    label: 'Bonus',
    width: '200px',
    sortable: true,
    render: (job) => (
      <span className="text-gray-300">{job.bonus?.name}</span>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '140px',
    sortable: true,
    render: (job) => formatJobStatus(job),
  },
  {
    key: 'progress',
    label: 'Progress',
    width: '200px',
    sortable: false,
    render: (job) => formatProgress(job),
  },
  {
    key: 'totalCount',
    label: 'Total Customers',
    width: '120px',
    sortable: true,
    render: (job) => (
      <div className="flex items-center space-x-1">
        <Users className="h-3 w-3 text-blue-400" />
        <span className="text-blue-300">{job.totalCount?.toLocaleString()}</span>
      </div>
    ),
  },
  {
    key: 'successCount',
    label: 'Success',
    width: '100px',
    sortable: true,
    render: (job) => (
      <span className="text-green-300">{job.successCount?.toLocaleString() || 0}</span>
    ),
  },
  {
    key: 'failureCount',
    label: 'Failed',
    width: '100px',
    sortable: true,
    render: (job) => (
      <span className="text-red-300">{job.failureCount?.toLocaleString() || 0}</span>
    ),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (job) => (
      <span className="text-sm text-gray-400">
        {formatDate(job.createdAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '150px',
    sortable: false,
    sticky: 'right',
    render: (job) => {
      const canCancel = ['pending', 'processing'].includes(job.status);
      const canRetry = job.status === 'failed';

      return (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onView(job)}
            className="p-1"
            title="View Details"
          >
            <Eye className="h-4 w-4" />
          </Button>

          {canCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onCancel(job)}
              className="p-1 text-red-400 hover:text-red-300"
              title="Cancel Job"
            >
              <X className="h-4 w-4" />
            </Button>
          )}

          {canRetry && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onRetry(job)}
              className="p-1 text-blue-400 hover:text-blue-300"
              title="Retry Job"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          )}
        </div>
      );
    },
  },
];
