import { FilterGroup } from '@panels/ui';

export const createHappyHoursBonusBulkAssignmentFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'bonusName',
        label: 'Bonus Name',
        type: 'text',
        placeholder: 'Search by bonus name...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
    ],
  },
  {
    name: 'status',
    displayName: 'Status Filters',
    fields: [
      {
        key: 'status',
        label: 'Job Status',
        type: 'select',
        options: [
          { value: '', label: 'All' },
          { value: 'pending', label: 'Pending' },
          { value: 'processing', label: 'Processing' },
          { value: 'completed', label: 'Completed' },
          { value: 'failed', label: 'Failed' },
          { value: 'cancelled', label: 'Cancelled' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'totalCountMin',
        label: 'Min Total Count',
        type: 'number',
        placeholder: 'Enter minimum...',
      },
      {
        key: 'totalCountMax',
        label: 'Max Total Count',
        type: 'number',
        placeholder: 'Enter maximum...',
      },
      {
        key: 'createdAtFrom',
        label: 'Created From',
        type: 'date',
        placeholder: 'Select start date...',
      },
      {
        key: 'createdAtTo',
        label: 'Created To',
        type: 'date',
        placeholder: 'Select end date...',
      },
    ],
  },
];
