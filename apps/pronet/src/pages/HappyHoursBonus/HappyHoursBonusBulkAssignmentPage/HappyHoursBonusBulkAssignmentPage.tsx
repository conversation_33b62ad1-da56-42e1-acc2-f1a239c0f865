import { useState, useEffect, useRef } from 'react';
import { Plus, X } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { useApi } from '../../../features/api/useApi';
import {
  BonusBulkAssignmentJobSearchRequest,
  BonusBulkAssignmentJobGetRequest,
  BonusBulkAssignmentJobCancelRequest,
  BonusBulkAssignmentJobRetryRequest,
  HappyHoursBonusBulkAssignmentJobCreateRequest,
  HappyHoursBonusSearchRequest,
  type BonusBulkAssignmentJob,
  type HappyHoursBonus,
  type BonusBulkAssignmentJobSearchRequestOptions,
  type HappyHoursBonusBulkAssignmentRequestCreateOptions,
} from '@panels/api';
import { createHappyHoursBonusBulkAssignmentColumns } from './columns';
import { createHappyHoursBonusBulkAssignmentFilters } from './filters';

interface FormData {
  bonusId: number | null;
  customerIds: string;
}

interface FormErrors {
  [key: string]: string | undefined;
}

export const HappyHoursBonusBulkAssignmentPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  // Data state
  const [jobs, setJobs] = useState<BonusBulkAssignmentJob[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Long polling state
  const [isPolling, setIsPolling] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const POLLING_INTERVAL = 3000; // 3 seconds

  // Modal states
  const [isCreateFormOpen, setIsCreateFormOpen] = useState(false);
  const [isJobDetailsOpen, setIsJobDetailsOpen] = useState(false);
  const [jobDetails, setJobDetails] = useState<BonusBulkAssignmentJob | null>(
    null
  );
  const [jobDetailsError, setJobDetailsError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<FormData>({
    bonusId: null,
    customerIds: '',
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bonuses, setBonuses] = useState<HappyHoursBonus[]>([]);
  const [isLoadingBonuses, setIsLoadingBonuses] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Load jobs function
  const loadJobs = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const searchOptions: BonusBulkAssignmentJobSearchRequestOptions = {
        page: currentPage,
        limit: itemsPerPage,
        bonusType: 'happyHours',
        ...filters,
      };

      const request = new BonusBulkAssignmentJobSearchRequest(searchOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setJobs(response.data.items);
        setTotal(response.data.total);
      } else {
        setError(response.error || 'Failed to load jobs');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Polling functions
  const startPolling = () => {
    if (pollingIntervalRef.current) return;

    setIsPolling(true);
    pollingIntervalRef.current = setInterval(() => {
      loadJobs();
    }, POLLING_INTERVAL);
  };

  const stopPolling = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    setIsPolling(false);
  };

  // Check if any jobs are in progress
  const hasActiveJobs = jobs.some((job) =>
    ['pending', 'processing'].includes(job.status)
  );

  // Auto-start/stop polling based on active jobs
  useEffect(() => {
    if (hasActiveJobs && !isPolling) {
      startPolling();
    } else if (!hasActiveJobs && isPolling) {
      stopPolling();
    }
  }, [hasActiveJobs, isPolling]);

  // Load jobs on mount and when dependencies change
  useEffect(() => {
    loadJobs();
  }, [currentPage, itemsPerPage, sortState, filters]);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  // Load bonuses for form
  const loadBonuses = async () => {
    setIsLoadingBonuses(true);
    try {
      const request = new HappyHoursBonusSearchRequest({
        isActive: true,
        limit: 100,
      });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setBonuses(response.data.items);
      }
    } catch (error) {
      console.error('Failed to load bonuses:', error);
    } finally {
      setIsLoadingBonuses(false);
    }
  };

  // Load bonuses when form opens
  useEffect(() => {
    if (isCreateFormOpen) {
      loadBonuses();
    }
  }, [isCreateFormOpen]);

  // Form handlers
  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    if (!formData.bonusId) {
      errors.bonusId = 'Please select a bonus';
    }

    if (!formData.customerIds.trim()) {
      errors.customerIds = 'Please enter customer IDs';
    } else {
      // Validate customer IDs format (comma-separated numbers)
      const ids = formData.customerIds.split(',').map((id) => id.trim());
      const invalidIds = ids.filter((id) => !id || isNaN(Number(id)));
      if (invalidIds.length > 0) {
        errors.customerIds = 'Please enter valid comma-separated customer IDs';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const customerIds = formData.customerIds
        .split(',')
        .map((id) => parseInt(id.trim()))
        .filter((id) => !isNaN(id));

      const requestOptions: HappyHoursBonusBulkAssignmentRequestCreateOptions =
        {
          happyHoursBonusId: formData.bonusId!,
          externalCustomerIds: customerIds,
        };

      const request = new HappyHoursBonusBulkAssignmentJobCreateRequest(
        requestOptions
      );
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setIsCreateFormOpen(false);
        setFormData({ bonusId: null, customerIds: '' });
        setFormErrors({});
        loadJobs(); // Refresh the list
        // TODO: Show success toast
      } else {
        setFormErrors({
          customerIds: response.error || 'Failed to create bulk assignment job',
        });
      }
    } catch (error) {
      setFormErrors({
        customerIds:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Action handlers
  const handleViewJob = async (job: BonusBulkAssignmentJob) => {
    setJobDetailsError(null);
    setJobDetails(null);
    setIsJobDetailsOpen(true);

    try {
      const request = new BonusBulkAssignmentJobGetRequest({ id: job.id });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setJobDetails(response.data);
      } else {
        setJobDetailsError(response.error || 'Failed to load job details');
      }
    } catch (error) {
      setJobDetailsError(
        error instanceof Error ? error.message : 'An error occurred'
      );
    }
  };

  const handleCancelJob = (job: BonusBulkAssignmentJob) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Cancel Bulk Assignment Job',
      message: `Are you sure you want to cancel the bulk assignment job for "${job.bonus?.name}"? This action cannot be undone.`,
      variant: 'warning',
      confirmText: 'Cancel Job',
      action: async () => {
        const request = new BonusBulkAssignmentJobCancelRequest({ id: job.id });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          loadJobs(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to cancel job');
        }
      },
    });
  };

  const handleRetryJob = (job: BonusBulkAssignmentJob) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Retry Bulk Assignment Job',
      message: `Are you sure you want to retry the bulk assignment job for "${job.bonus?.name}"?`,
      variant: 'info',
      confirmText: 'Retry Job',
      action: async () => {
        const request = new BonusBulkAssignmentJobRetryRequest({ id: job.id });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          loadJobs(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to retry job');
        }
      },
    });
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with action handlers
  const columns = createHappyHoursBonusBulkAssignmentColumns({
    onView: handleViewJob,
    onCancel: handleCancelJob,
    onRetry: handleRetryJob,
  });

  // Get filter groups
  const filterGroups = createHappyHoursBonusBulkAssignmentFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No bulk assignment jobs found',
    description: 'Get started by creating your first bulk assignment job.',
    action: (
      <Button
        onClick={() => setIsCreateFormOpen(true)}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Bulk Assignment
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">
            Error loading bulk assignment jobs
          </div>
          <div className="text-gray-400 mb-4">{error}</div>
          <Button onClick={() => loadJobs()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Happy Hours Bonus Bulk Assignment
          </h1>
          <p className="text-gray-400 mt-1">
            Create and manage bulk assignment jobs for happy hours bonuses
            {isPolling && (
              <span className="ml-2 text-blue-400">
                • Auto-refreshing every {POLLING_INTERVAL / 1000}s
              </span>
            )}
          </p>
        </div>
        <Button
          onClick={() => setIsCreateFormOpen(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Bulk Assignment
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={jobs}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          title={`Bulk Assignment Jobs (${total.toLocaleString()})`}
        />
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />
    </div>
  );
};
