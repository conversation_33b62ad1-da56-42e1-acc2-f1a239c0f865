import { Eye, Trash2 } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type HappyHoursBonusTemplate } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format template status
const formatTemplateStatus = (template: HappyHoursBonusTemplate) => {
  const isDeleted = template.bonusTemplate.deletedAt !== null;

  let status: string;
  let colorClass: string;

  if (isDeleted) {
    status = 'deleted';
    colorClass = 'bg-red-900 text-red-300 border-red-700';
  } else {
    status = 'active';
    colorClass = 'bg-green-900 text-green-300 border-green-700';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

interface HappyHoursBonusTemplateColumnsProps {
  onView: (template: HappyHoursBonusTemplate) => void;
  onDelete: (template: HappyHoursBonusTemplate) => void;
}

export const createHappyHoursBonusTemplateColumns = ({
  onView,
  onDelete,
}: HappyHoursBonusTemplateColumnsProps): TableColumn<HappyHoursBonusTemplate>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (template) => (
      <span className="font-mono text-sm text-gray-300">#{template.id}</span>
    ),
  },
  {
    key: 'name',
    label: 'Name',
    width: '200px',
    sortable: true,
    render: (template) => (
      <span className="font-medium text-gray-300">
        {template.bonusTemplate.name}
      </span>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (template) => formatTemplateStatus(template),
  },
  {
    key: 'externalBonusName',
    label: 'External Name',
    width: '180px',
    sortable: true,
    render: (template) => (
      <span className="font-medium text-gray-300">
        {template.externalBonusName}
      </span>
    ),
  },
  {
    key: 'amount',
    label: 'Amount',
    width: '120px',
    sortable: true,
    render: (template) => (
      <span className="text-gray-300">₺{template.amount.toFixed(2)}</span>
    ),
  },
  {
    key: 'externalBonusId',
    label: 'External ID',
    width: '120px',
    sortable: true,
    render: (template) => (
      <span className="font-mono text-sm text-gray-300">
        #{template.externalBonusId}
      </span>
    ),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (template) => (
      <span className="text-sm text-gray-400">
        {formatDate(template.createdAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '120px',
    sortable: false,
    sticky: 'right',
    render: (template) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onView(template)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete(template)}
          className="p-1 text-red-400 hover:text-red-300"
          title="Delete Template"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];
