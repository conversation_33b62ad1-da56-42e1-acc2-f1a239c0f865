import { useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useHappyHoursBonusTemplates } from '../../../hooks/useHappyHoursBonusTemplates';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { HappyHoursBonusTemplateCreateModal } from '../../../components/modals/HappyHoursBonusTemplateCreateModal';
import { HappyHoursBonusTemplateDetailsModal } from '../../../components/modals/HappyHoursBonusTemplateDetailsModal';
import { useApi } from '../../../features/api/useApi';
import {
  BonusTemplateSoftDeleteRequest,
  type HappyHoursBonusTemplate,
} from '@panels/api';
import { createHappyHoursBonusTemplateColumns } from './columns';
import { createHappyHoursBonusTemplateFilters } from './filters';

export const HappyHoursBonusTemplatesListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(() => ({ ...filters }), [filters]);

  // Modal state
  const [selectedTemplate, setSelectedTemplate] = useState<HappyHoursBonusTemplate | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Fetch data
  const { data, total, isLoading, error, refetch } = useHappyHoursBonusTemplates({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleCreateTemplate = () => {
    setIsCreateModalOpen(true);
  };

  const handleViewTemplate = (template: HappyHoursBonusTemplate) => {
    setSelectedTemplate(template);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedTemplate(null);
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleCreateSuccess = () => {
    refetch(); // Refresh the list after successful creation
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      // Ensure the modal closes after successful action
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  // Delete template
  const handleDeleteTemplate = (template: HappyHoursBonusTemplate) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Happy Hours Bonus Template',
      message: `Are you sure you want to delete the template "${template.bonusTemplate.name}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        const request = new BonusTemplateSoftDeleteRequest({ 
          id: template.bonusTemplate.id 
        });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to delete template');
        }
      },
    });
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with action handlers
  const columns = createHappyHoursBonusTemplateColumns({
    onView: handleViewTemplate,
    onDelete: handleDeleteTemplate,
  });

  // Get filter groups
  const filterGroups = createHappyHoursBonusTemplateFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No happy hours bonus templates found',
    description: 'Get started by creating your first happy hours bonus template.',
    action: (
      <Button onClick={handleCreateTemplate} className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        Create Template
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">
            Error loading happy hours bonus templates
          </div>
          <div className="text-gray-400 mb-4">{error}</div>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">Happy Hours Bonus Templates</h1>
          <p className="text-gray-400 mt-1">
            Manage happy hours bonus templates and their configurations
          </p>
        </div>
        <Button onClick={handleCreateTemplate} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Template
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          title={`Happy Hours Bonus Templates (${(total || 0).toLocaleString()})`}
        />
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />

      {/* Create Modal */}
      <HappyHoursBonusTemplateCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {/* Details Modal */}
      <HappyHoursBonusTemplateDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        template={selectedTemplate}
      />
    </div>
  );
};
