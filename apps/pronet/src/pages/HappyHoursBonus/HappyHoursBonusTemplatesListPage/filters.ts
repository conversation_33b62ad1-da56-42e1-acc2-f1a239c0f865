import { FilterGroup } from '@panels/ui';

export const createHappyHoursBonusTemplateFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'templateName',
        label: 'Template Name',
        type: 'text',
        placeholder: 'Search by template name...',
      },
      {
        key: 'templateId',
        label: 'Template ID',
        type: 'number',
        placeholder: 'Enter template ID...',
      },
      {
        key: 'maxBalance',
        label: 'Max Balance',
        type: 'number',
        placeholder: 'Enter max balance...',
      },
    ],
  },
  {
    name: 'status',
    displayName: 'Status Filters',
    fields: [
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: '', label: 'All' },
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Deleted' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'lossbackPercentageMin',
        label: 'Min Lossback %',
        type: 'number',
        placeholder: 'Enter minimum percentage...',
      },
      {
        key: 'lossbackPercentageMax',
        label: 'Max Lossback %',
        type: 'number',
        placeholder: 'Enter maximum percentage...',
      },
      {
        key: 'happyHoursBoostMin',
        label: 'Min Happy Hours Boost %',
        type: 'number',
        placeholder: 'Enter minimum boost...',
      },
      {
        key: 'validForDaysMin',
        label: 'Min Valid Days',
        type: 'number',
        placeholder: 'Enter minimum days...',
      },
      {
        key: 'validForDaysMax',
        label: 'Max Valid Days',
        type: 'number',
        placeholder: 'Enter maximum days...',
      },
    ],
  },
];
