import { FilterGroup } from '@panels/ui';

export const createHappyHoursBonusFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'bonusName',
        label: 'Bonus Name',
        type: 'text',
        placeholder: 'Search by bonus name...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'maxBalance',
        label: 'Max Balance',
        type: 'number',
        placeholder: 'Enter max balance...',
      },
    ],
  },
  {
    name: 'status',
    displayName: 'Status Filters',
    fields: [
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: '', label: 'All' },
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Inactive' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'lossbackPercentageMin',
        label: 'Min Lossback %',
        type: 'number',
        placeholder: 'Enter minimum percentage...',
      },
      {
        key: 'lossbackPercentageMax',
        label: 'Max Lossback %',
        type: 'number',
        placeholder: 'Enter maximum percentage...',
      },
      {
        key: 'happyHoursBoostMin',
        label: 'Min Happy Hours Boost %',
        type: 'number',
        placeholder: 'Enter minimum boost...',
      },
    ],
  },
];
