import { useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState, PromocodeCreateModal } from '@panels/ui';
import { useBonusPromocodes } from '../../../hooks/useBonusPromocodes';
import Button from '../../../components/ui/Button';
import { BonusPromocodeDetailsModal } from '../../../components/modals/BonusPromocodeDetailsModal';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { useApi } from '../../../features/api/useApi';
import {
  BonusPromocodeToggleRequest,
  BonusPromocodeSoftDeleteRequest,
  BonusPromocodeCreateRequest,
  BonusSearchRequest,
  type BonusPromocode,
  type Bonus,
} from '@panels/api';
import { createFreespinBonusPromocodeColumns } from './columns';
import { createFreespinBonusPromocodeFilters } from './filters';

export const FreespinBonusPromocodeListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(
    () => ({ ...filters, bonusType: 'freespin' }),
    [filters]
  );

  // Modal state
  const [selectedPromocode, setSelectedPromocode] =
    useState<BonusPromocode | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });

  // Promocode create modal state
  const [createFormData, setCreateFormData] = useState({
    bonusId: null as number | null,
    code: '',
    maxActivations: null as number | null,
  });
  const [createErrors, setCreateErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bonuses, setBonuses] = useState<Bonus[]>([]);
  const [isLoadingBonuses, setIsLoadingBonuses] = useState(false);

  // Fetch data
  const { data, total, isLoading, error, refetch } = useBonusPromocodes({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleCreatePromocode = async () => {
    setIsCreateModalOpen(true);
    setIsLoadingBonuses(true);

    try {
      const request = new BonusSearchRequest({
        isActive: true,
        type: 'freespin',
        limit: 100,
      });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setBonuses(response.data.items || []);
      }
    } catch (error) {
      console.error('Failed to load bonuses:', error);
    } finally {
      setIsLoadingBonuses(false);
    }
  };

  const handleViewPromocode = (promocode: BonusPromocode) => {
    setSelectedPromocode(promocode);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPromocode(null);
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
    setCreateFormData({
      bonusId: null,
      code: '',
      maxActivations: null,
    });
    setCreateErrors({});
  };

  const handleCreateSuccess = () => {
    refetch();
    handleCloseCreateModal();
  };

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  const handlePausePromocode = (promocode: BonusPromocode) => {
    const newStatus = !promocode.isActive;
    const action = newStatus ? 'activate' : 'pause';

    setConfirmationDialog({
      isOpen: true,
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Promocode`,
      message: `Are you sure you want to ${action} the promocode "${
        promocode.code
      }"? This will ${newStatus ? 'enable' : 'disable'} it for customer use.`,
      variant: newStatus ? 'info' : 'warning',
      confirmText: action.charAt(0).toUpperCase() + action.slice(1),
      action: async () => {
        const request = new BonusPromocodeToggleRequest({
          id: promocode.id,
          isActive: newStatus,
        });
        const response = await pronet.makeRequest(request);
        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || `Failed to ${action} promocode`);
        }
      },
    });
  };

  const handleDeletePromocode = (promocode: BonusPromocode) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Promocode',
      message: `Are you sure you want to delete the promocode "${promocode.code}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        const request = new BonusPromocodeSoftDeleteRequest({
          id: promocode.id,
        });
        const response = await pronet.makeRequest(request);
        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to delete promocode');
        }
      },
    });
  };

  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    await confirmationDialog.action();
    handleConfirmationClose();
  };

  // Promocode create handlers
  const handleCreateInputChange = (
    field: string | number | symbol,
    value: any
  ) => {
    const fieldKey = field as keyof typeof createFormData;
    setCreateFormData((prev) => ({ ...prev, [fieldKey]: value }));
    if (createErrors[fieldKey]) {
      setCreateErrors((prev) => ({ ...prev, [fieldKey]: '' }));
    }
  };

  const handleCreateSubmit = async () => {
    // Validate form
    const errors: Record<string, string> = {};
    if (!createFormData.bonusId) {
      errors.bonusId = 'Please select a bonus';
    }
    if (!createFormData.code.trim()) {
      errors.code = 'Please enter a promocode';
    }

    if (Object.keys(errors).length > 0) {
      setCreateErrors(errors);
      return;
    }

    setIsSubmitting(true);
    try {
      const request = new BonusPromocodeCreateRequest({
        bonusId: createFormData.bonusId!,
        code: createFormData.code.trim(),
        maxActivations: createFormData.maxActivations,
      });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        handleCreateSuccess();
      } else {
        setCreateErrors({
          code: response.error || 'Failed to create promocode',
        });
      }
    } catch (error) {
      setCreateErrors({
        code:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with handlers
  const columns = createFreespinBonusPromocodeColumns({
    onViewPromocode: handleViewPromocode,
    onPausePromocode: handlePausePromocode,
    onDeletePromocode: handleDeletePromocode,
    onCopyCode: handleCopyCode,
  });

  // Create filter groups
  const filterGroups = createFreespinBonusPromocodeFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No freespin bonus promocodes found',
    description: 'Get started by creating your first freespin bonus promocode.',
    action: (
      <Button
        onClick={handleCreatePromocode}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Promocode
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            Error loading promocodes
          </h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <Button onClick={() => refetch()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Freespin Bonus Promocodes
          </h1>
          <p className="text-gray-400">
            Manage promocodes for freespin bonuses
          </p>
        </div>
        <Button
          onClick={handleCreatePromocode}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Promocode
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          sortState={sortState}
        />
      </div>

      {/* Details Modal */}
      <BonusPromocodeDetailsModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        promocode={selectedPromocode}
      />

      {/* Create Modal */}
      <PromocodeCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        formData={createFormData}
        errors={createErrors}
        onInputChange={handleCreateInputChange}
        isSubmitting={isSubmitting}
        onSubmit={handleCreateSubmit}
        submitButtonText="Create Promocode"
      >
        {({ formData, errors, onInputChange }) => (
          <div className="space-y-4">
            {/* Bonus Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bonus *
              </label>
              {isLoadingBonuses ? (
                <div className="text-sm text-gray-400">Loading bonuses...</div>
              ) : (
                <select
                  value={formData.bonusId || ''}
                  onChange={(e) =>
                    onInputChange(
                      'bonusId',
                      e.target.value ? parseInt(e.target.value) : null
                    )
                  }
                  className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.bonusId ? 'border-red-500' : 'border-dark-600'
                  }`}
                >
                  <option value="">Select a bonus...</option>
                  {bonuses.map((bonus) => (
                    <option key={bonus.id} value={bonus.id}>
                      {bonus.name}
                    </option>
                  ))}
                </select>
              )}
              {errors.bonusId && (
                <p className="mt-1 text-sm text-red-400">{errors.bonusId}</p>
              )}
            </div>

            {/* Promocode */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Promocode *
              </label>
              <input
                type="text"
                value={formData.code}
                onChange={(e) => onInputChange('code', e.target.value)}
                placeholder="Enter promocode..."
                className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.code ? 'border-red-500' : 'border-dark-600'
                }`}
              />
              {errors.code && (
                <p className="mt-1 text-sm text-red-400">{errors.code}</p>
              )}
            </div>

            {/* Max Activations */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Max Activations
              </label>
              <input
                type="number"
                value={formData.maxActivations || ''}
                onChange={(e) =>
                  onInputChange(
                    'maxActivations',
                    e.target.value ? parseInt(e.target.value) : null
                  )
                }
                placeholder="Leave empty for unlimited..."
                min="1"
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        )}
      </PromocodeCreateModal>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
      />
    </div>
  );
};
