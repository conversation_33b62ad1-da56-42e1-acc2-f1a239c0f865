import { FilterGroup } from '@panels/ui';

export const createFreespinBonusPromocodeFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'code',
        label: 'Promocode',
        type: 'text',
        placeholder: 'Search by promocode...',
      },
      {
        key: 'bonusName',
        label: 'Bonus Name',
        type: 'text',
        placeholder: 'Search by bonus name...',
      },
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Inactive' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'createdAfter',
        label: 'Created After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Created Before',
        type: 'date',
      },
      {
        key: 'minActivations',
        label: 'Min Activations',
        type: 'number',
        placeholder: 'Minimum activations...',
        min: 0,
      },
      {
        key: 'maxActivations',
        label: 'Max Activations',
        type: 'number',
        placeholder: 'Maximum activations...',
        min: 0,
      },
    ],
  },
];
