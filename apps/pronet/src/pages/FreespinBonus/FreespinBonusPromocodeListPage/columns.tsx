import { Eye, Pause, Trash2, Play, Copy } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusPromocode } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format promocode status
const formatPromocodeStatus = (promocode: BonusPromocode) => {
  const isDeleted = !!promocode.deletedAt;
  const isActive = promocode.isActive;

  let status: string;
  let colorClass: string;

  if (isDeleted) {
    status = 'deleted';
    colorClass = 'bg-red-900 text-red-300 border-red-700';
  } else if (isActive) {
    status = 'active';
    colorClass = 'bg-green-900 text-green-300 border-green-700';
  } else {
    status = 'inactive';
    colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

interface ColumnHandlers {
  onViewPromocode: (promocode: BonusPromocode) => void;
  onPausePromocode: (promocode: BonusPromocode) => void;
  onDeletePromocode: (promocode: BonusPromocode) => void;
  onCopyCode: (code: string) => void;
}

export const createFreespinBonusPromocodeColumns = (handlers: ColumnHandlers): TableColumn<BonusPromocode>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (promocode) => (
      <span className="font-mono text-sm text-gray-300">#{promocode.id}</span>
    ),
  },
  {
    key: 'bonus',
    label: 'Bonus',
    width: '200px',
    sortable: true,
    render: (promocode) => (
      <div className="space-y-1">
        <div className="font-medium text-gray-300 truncate">
          {promocode.bonus?.name}
        </div>
        <div className="text-xs text-gray-400 capitalize">
          {promocode.bonus?.type}
        </div>
      </div>
    ),
  },
  {
    key: 'code',
    label: 'Promocode',
    width: '200px',
    sortable: true,
    render: (promocode) => (
      <div className="flex items-center space-x-2">
        <span className="font-mono text-sm text-gray-300 bg-dark-700 px-2 py-1 rounded border">
          {promocode.code}
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onCopyCode(promocode.code)}
          className="p-1 text-gray-400 hover:text-gray-300"
          title="Copy Code"
        >
          <Copy className="h-3 w-3" />
        </Button>
      </div>
    ),
  },
  {
    key: 'activations',
    label: 'Activations',
    width: '140px',
    sortable: true,
    render: (promocode) => (
      <div className="space-y-1">
        <div className="text-gray-300">
          {promocode.activations.toLocaleString()}
          {promocode.maxActivations && (
            <span className="text-gray-400">
              {' / '}
              {promocode.maxActivations.toLocaleString()}
            </span>
          )}
        </div>
        {promocode.maxActivations && (
          <div className="w-full bg-dark-700 rounded-full h-1">
            <div
              className="h-1 rounded-full transition-all duration-300 bg-blue-500"
              style={{
                width: `${Math.min(
                  (promocode.activations / promocode.maxActivations) * 100,
                  100
                )}%`,
              }}
            />
          </div>
        )}
      </div>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (promocode) => formatPromocodeStatus(promocode),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (promocode) => (
      <span className="text-sm text-gray-400">
        {formatDate(promocode.createdAt)}
      </span>
    ),
  },
  {
    key: 'updatedAt',
    label: 'Updated',
    width: '160px',
    sortable: true,
    render: (promocode) => (
      <span className="text-sm text-gray-400">
        {formatDate(promocode.updatedAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '150px',
    sortable: false,
    sticky: 'right',
    render: (promocode) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onViewPromocode(promocode)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>

        {!promocode.deletedAt && (
          <>
            {promocode.isActive ? (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handlers.onPausePromocode(promocode)}
                className="p-1 text-yellow-400 hover:text-yellow-300"
                title="Pause Promocode"
              >
                <Pause className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handlers.onPausePromocode(promocode)}
                className="p-1 text-green-400 hover:text-green-300"
                title="Activate Promocode"
              >
                <Play className="h-4 w-4" />
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handlers.onDeletePromocode(promocode);
              }}
              className="p-1 text-red-400 hover:text-red-300"
              title="Delete Promocode"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </>
        )}
      </div>
    ),
  },
];
