import { FilterGroup } from '@panels/ui';

export const createFreespinBonusBulkAssignmentFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'status',
        label: 'Status',
        type: 'select',
        options: [
          { value: 'pending', label: 'Pending' },
          { value: 'processing', label: 'Processing' },
          { value: 'processed', label: 'Processed' },
          { value: 'cancelled', label: 'Cancelled' },
          { value: 'timeout', label: 'Timeout' },
          { value: 'failed', label: 'Failed' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'createdAfter',
        label: 'Created After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Created Before',
        type: 'date',
      },
    ],
  },
];
