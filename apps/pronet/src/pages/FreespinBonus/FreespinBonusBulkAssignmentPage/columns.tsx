import { Eye, X, RotateCcw } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusBulkAssignmentJob } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format status badge
const formatStatus = (status: string) => {
  let colorClass: string;

  switch (status.toLowerCase()) {
    case 'pending':
      colorClass = 'bg-yellow-900 text-yellow-300 border-yellow-700';
      break;
    case 'processing':
      colorClass = 'bg-blue-900 text-blue-300 border-blue-700';
      break;
    case 'processed':
      colorClass = 'bg-green-900 text-green-300 border-green-700';
      break;
    case 'cancelled':
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
      break;
    case 'timeout':
      colorClass = 'bg-orange-900 text-orange-300 border-orange-700';
      break;
    case 'failed':
      colorClass = 'bg-red-900 text-red-300 border-red-700';
      break;
    // Legacy status for backward compatibility
    case 'completed':
      colorClass = 'bg-green-900 text-green-300 border-green-700';
      break;
    default:
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

interface ColumnHandlers {
  onViewJob: (job: BonusBulkAssignmentJob) => void;
  onCancelJob: (job: BonusBulkAssignmentJob) => void;
  onRetryJob: (job: BonusBulkAssignmentJob) => void;
}

export const createFreespinBonusBulkAssignmentColumns = (handlers: ColumnHandlers): TableColumn<BonusBulkAssignmentJob>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (job) => (
      <span className="font-mono text-sm text-gray-300">#{job.id}</span>
    ),
  },
  {
    key: 'bonus',
    label: 'Bonus',
    width: '200px',
    sortable: true,
    render: (job) => (
      <div className="space-y-1">
        <div className="font-medium text-gray-300 truncate">
          {job.bonus.name}
        </div>
        <div className="text-xs text-gray-400 capitalize">
          {job.bonus.type}
        </div>
      </div>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (job) => formatStatus(job.status),
  },
  {
    key: 'targets',
    label: 'Targets',
    width: '100px',
    sortable: false,
    render: (job) => {
      const processed = job.targets.filter(
        (t) => t.status === 'processed' || t.status === 'completed' // Support both new and legacy status
      ).length;
      const total = job.targets.length;
      return (
        <div className="text-sm text-gray-300">
          <div>
            {processed}/{total}
          </div>
          <div className="text-xs text-gray-400">
            {total > 0 ? Math.round((processed / total) * 100) : 0}%
          </div>
        </div>
      );
    },
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (job) => (
      <span className="text-sm text-gray-400">
        {formatDate(job.createdAt)}
      </span>
    ),
  },
  {
    key: 'updatedAt',
    label: 'Updated',
    width: '160px',
    sortable: true,
    render: (job) => (
      <span className="text-sm text-gray-400">
        {formatDate(job.updatedAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '150px',
    sortable: false,
    sticky: 'right',
    render: (job) => {
      const canCancel = ['pending', 'processing'].includes(job.status);
      const canRetry = ['cancelled', 'timeout', 'failed'].includes(job.status);

      return (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlers.onViewJob(job)}
            className="p-1"
            title="View Details"
          >
            <Eye className="h-4 w-4" />
          </Button>

          {canCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handlers.onCancelJob(job)}
              className="p-1 text-red-400 hover:text-red-300"
              title="Cancel Job"
            >
              <X className="h-4 w-4" />
            </Button>
          )}

          {canRetry && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handlers.onRetryJob(job)}
              className="p-1 text-blue-400 hover:text-blue-300"
              title="Retry Job"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          )}
        </div>
      );
    },
  },
];

// Export helper functions for use in other components
export { formatDate, formatStatus };
