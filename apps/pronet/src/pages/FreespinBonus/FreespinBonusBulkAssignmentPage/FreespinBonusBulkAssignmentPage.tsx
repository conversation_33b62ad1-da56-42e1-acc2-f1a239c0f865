import { useState, useEffect, useRef } from 'react';
import { Plus, AlertCircle, X } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { useApi } from '../../../features/api/useApi';
import {
  BonusBulkAssignmentJobSearchRequest,
  BonusBulkAssignmentJobGetRequest,
  BonusBulkAssignmentJobCancelRequest,
  BonusBulkAssignmentJobRetryRequest,
  FreespinBonusBulkAssignmentJobCreateRequest,
  FreespinBonusSearchRequest,
  type BonusBulkAssignmentJob,
  type FreespinBonus,
  type BonusBulkAssignmentJobSearchRequestOptions,
  type FreespinBonusBulkAssignmentRequestCreateOptions,
} from '@panels/api';
import {
  createFreespinBonusBulkAssignmentColumns,
  formatDate,
  formatStatus,
} from './columns';
import { createFreespinBonusBulkAssignmentFilters } from './filters';

interface FormData {
  bonusId: number | null;
  customerIds: string;
}

interface FormErrors {
  [key: string]: string;
}

export const FreespinBonusBulkAssignmentPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  // Data state
  const [jobs, setJobs] = useState<BonusBulkAssignmentJob[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Long polling state
  const [isPolling, setIsPolling] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const POLLING_INTERVAL = 3000; // 3 seconds

  // Modal states
  const [isCreateFormOpen, setIsCreateFormOpen] = useState(false);
  const [isJobDetailsOpen, setIsJobDetailsOpen] = useState(false);
  const [jobDetails, setJobDetails] = useState<BonusBulkAssignmentJob | null>(
    null
  );
  const [jobDetailsError, setJobDetailsError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<FormData>({
    bonusId: null,
    customerIds: '',
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bonuses, setBonuses] = useState<FreespinBonus[]>([]);
  const [isLoadingBonuses, setIsLoadingBonuses] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Load jobs function
  const loadJobs = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const searchOptions: BonusBulkAssignmentJobSearchRequestOptions = {
        page: currentPage,
        limit: itemsPerPage,
        bonusType: 'freespin',
        ...filters,
      };

      const request = new BonusBulkAssignmentJobSearchRequest(searchOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setJobs(response.data.items);
        setTotal(response.data.total);
      } else {
        setError(response.error || 'Failed to load jobs');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Polling functions
  const startPolling = () => {
    if (pollingIntervalRef.current) return;

    setIsPolling(true);
    pollingIntervalRef.current = setInterval(() => {
      loadJobs();
    }, POLLING_INTERVAL);
  };

  const stopPolling = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    setIsPolling(false);
  };

  // Check if any jobs are in progress
  const hasActiveJobs = jobs.some((job) =>
    ['pending', 'processing'].includes(job.status)
  );

  // Auto-start/stop polling based on active jobs
  useEffect(() => {
    if (hasActiveJobs && !isPolling) {
      startPolling();
    } else if (!hasActiveJobs && isPolling) {
      stopPolling();
    }
  }, [hasActiveJobs, isPolling]);

  // Load jobs when dependencies change
  useEffect(() => {
    loadJobs();
  }, [currentPage, itemsPerPage, sortState, filters]);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  // Action handlers
  const handleViewJob = async (job: BonusBulkAssignmentJob) => {
    setIsJobDetailsOpen(true);
    setJobDetailsError(null);

    try {
      const request = new BonusBulkAssignmentJobGetRequest({ id: job.id });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setJobDetails(response.data);
      } else {
        setJobDetailsError(response.error || 'Failed to load job details');
      }
    } catch (error) {
      setJobDetailsError(
        error instanceof Error ? error.message : 'An error occurred'
      );
    }
  };

  const handleCloseJobDetails = () => {
    setIsJobDetailsOpen(false);
    setJobDetails(null);
    setJobDetailsError(null);
  };

  const handleCancelJob = async (job: BonusBulkAssignmentJob) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Cancel Bulk Assignment Job',
      message: `Are you sure you want to cancel the bulk assignment job for bonus "${job.bonus.name}"? This will stop processing any remaining assignments.`,
      variant: 'warning',
      confirmText: 'Cancel Job',
      action: async () => {
        const request = new BonusBulkAssignmentJobCancelRequest({
          id: job.id,
        });
        const response = await pronet.makeRequest(request);
        if (response.success) {
          loadJobs(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to cancel job');
        }
      },
    });
  };

  const handleRetryJob = async (job: BonusBulkAssignmentJob) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Retry Bulk Assignment Job',
      message: `Are you sure you want to retry the bulk assignment job for bonus "${job.bonus.name}"? This will restart the failed job.`,
      variant: 'info',
      confirmText: 'Retry Job',
      action: async () => {
        const request = new BonusBulkAssignmentJobRetryRequest({
          id: job.id,
        });
        const response = await pronet.makeRequest(request);
        if (response.success) {
          loadJobs(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to retry job');
        }
      },
    });
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  // Form handlers
  const handleCreateFormOpen = async () => {
    setIsCreateFormOpen(true);
    setIsLoadingBonuses(true);

    try {
      const request = new FreespinBonusSearchRequest({
        isActive: true,
        limit: 100,
      });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setBonuses(response.data.items);
      }
    } catch (error) {
      console.error('Failed to load bonuses:', error);
    } finally {
      setIsLoadingBonuses(false);
    }
  };

  const handleCreateFormClose = () => {
    setIsCreateFormOpen(false);
    setFormData({ bonusId: null, customerIds: '' });
    setFormErrors({});
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const handleFormSubmit = async () => {
    // Validate form
    const errors: FormErrors = {};

    if (!formData.bonusId) {
      errors.bonusId = 'Please select a bonus';
    }

    if (!formData.customerIds.trim()) {
      errors.customerIds = 'Please enter customer IDs';
    } else {
      // Validate customer IDs format
      const customerIds = formData.customerIds
        .split(/[,\n]/)
        .map((id) => id.trim())
        .filter((id) => id);

      if (customerIds.length === 0) {
        errors.customerIds = 'Please enter valid customer IDs';
      } else {
        // Check if all IDs are numbers
        const invalidIds = customerIds.filter((id) => !/^\d+$/.test(id));
        if (invalidIds.length > 0) {
          errors.customerIds = `Invalid customer IDs: ${invalidIds.join(', ')}`;
        }
      }
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);
    try {
      const customerIds = formData.customerIds
        .split(/[,\n]/)
        .map((id) => id.trim())
        .filter((id) => id)
        .map((id) => parseInt(id));

      const requestData: FreespinBonusBulkAssignmentRequestCreateOptions = {
        freespinBonusId: formData.bonusId!,
        externalCustomerIds: customerIds,
      };

      const request = new FreespinBonusBulkAssignmentJobCreateRequest(
        requestData
      );
      const response = await pronet.makeRequest(request);

      if (response.success) {
        handleCreateFormClose();
        loadJobs(); // Refresh the list
      } else {
        setFormErrors({
          submit: response.error || 'Failed to create bulk assignment job',
        });
      }
    } catch (error) {
      setFormErrors({
        submit: error instanceof Error ? error.message : 'An error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with handlers
  const columns = createFreespinBonusBulkAssignmentColumns({
    onViewJob: handleViewJob,
    onCancelJob: handleCancelJob,
    onRetryJob: handleRetryJob,
  });

  // Create filter groups
  const filterGroups = createFreespinBonusBulkAssignmentFilters();

  // Empty state configuration
  const emptyState = {
    icon: <AlertCircle className="h-12 w-12 text-gray-400" />,
    title: 'No bulk assignment jobs found',
    description: 'Get started by creating your first bulk assignment job.',
    action: (
      <Button
        onClick={handleCreateFormOpen}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Bulk Assignment
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            Error loading bulk assignment jobs
          </h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <Button onClick={loadJobs}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Freespin Bonus Bulk Assignment
          </h1>
          <p className="text-gray-400">
            Manage bulk assignment jobs for freespin bonuses
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {isPolling && (
            <div className="flex items-center text-sm text-blue-400">
              <div className="animate-pulse w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Auto-refreshing...
            </div>
          )}
          <Button
            onClick={handleCreateFormOpen}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create Bulk Assignment
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] flex flex-col">
        <div className="flex-1">
          <DataTable
            data={jobs}
            columns={columns}
            filterGroups={filterGroups}
            total={total}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            isLoading={isLoading}
            sortState={sortState}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={setItemsPerPage}
            onSort={handleSort}
            onFilterChange={setFilters}
            emptyState={emptyState}
          />
        </div>
      </div>

      {/* Create Form Modal */}
      {isCreateFormOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-dark-800 rounded-lg border border-dark-600 p-6 w-full max-w-2xl mx-4">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-300">
                Create Bulk Assignment Job
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCreateFormClose}
                className="p-2"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-4">
              {/* Bonus Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Bonus *
                </label>
                {isLoadingBonuses ? (
                  <div className="text-sm text-gray-400">
                    Loading bonuses...
                  </div>
                ) : (
                  <select
                    value={formData.bonusId || ''}
                    onChange={(e) =>
                      handleInputChange(
                        'bonusId',
                        e.target.value ? parseInt(e.target.value) : null
                      )
                    }
                    className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.bonusId ? 'border-red-500' : 'border-dark-600'
                    }`}
                  >
                    <option value="">Select a bonus...</option>
                    {bonuses.map((bonus) => (
                      <option key={bonus.id} value={bonus.id}>
                        {bonus.bonus.name}
                      </option>
                    ))}
                  </select>
                )}
                {formErrors.bonusId && (
                  <p className="mt-1 text-sm text-red-400">
                    {formErrors.bonusId}
                  </p>
                )}
              </div>

              {/* Customer IDs */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Customer IDs *
                </label>
                <textarea
                  value={formData.customerIds}
                  onChange={(e) =>
                    handleInputChange('customerIds', e.target.value)
                  }
                  placeholder="Enter customer IDs separated by commas or new lines..."
                  rows={6}
                  className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
                    formErrors.customerIds
                      ? 'border-red-500'
                      : 'border-dark-600'
                  }`}
                />
                {formErrors.customerIds && (
                  <p className="mt-1 text-sm text-red-400">
                    {formErrors.customerIds}
                  </p>
                )}
                <p className="mt-1 text-xs text-gray-400">
                  Enter customer IDs separated by commas or new lines. Example:
                  123, 456, 789
                </p>
              </div>

              {/* Submit Error */}
              {formErrors.submit && (
                <div className="p-3 bg-red-900/20 border border-red-700 rounded-md">
                  <p className="text-sm text-red-400">{formErrors.submit}</p>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="mt-6 flex justify-end space-x-3">
              <Button variant="outline" onClick={handleCreateFormClose}>
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleFormSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating...' : 'Create Job'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Job Details Modal */}
      {isJobDetailsOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-dark-800 rounded-lg border border-dark-600 p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-300">
                Bulk Assignment Job Details
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseJobDetails}
                className="p-2"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {jobDetailsError ? (
              <div className="p-4 bg-red-900/20 border border-red-700 rounded-md">
                <p className="text-red-400">{jobDetailsError}</p>
              </div>
            ) : jobDetails ? (
              <div className="space-y-6">
                {/* Job Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-400 mb-3">
                      Job Information
                    </h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Status:</span>
                        <span>{formatStatus(jobDetails.status)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Bonus:</span>
                        <span className="text-gray-300">
                          {jobDetails.bonus.name}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total Targets:</span>
                        <span className="text-gray-300">
                          {jobDetails.targets.length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Processed:</span>
                        <span className="text-gray-300">
                          {
                            jobDetails.targets.filter(
                              (t) =>
                                t.status === 'processed' ||
                                t.status === 'completed'
                            ).length
                          }
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Targets Table */}
                <div>
                  <h3 className="text-sm font-medium text-gray-400 mb-3">
                    Assignment Targets ({jobDetails.targets.length})
                  </h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-dark-600">
                      <thead className="bg-dark-700">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Customer
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Processed At
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Error
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-dark-800 divide-y divide-dark-600">
                        {jobDetails.targets.map((target) => (
                          <tr key={target.id}>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="text-sm text-gray-300">
                                {target.customer?.username || 'Unknown'}
                              </div>
                              <div className="text-xs text-gray-400">
                                ID: {target.externalCustomerId}
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              {formatStatus(target.status)}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-400">
                              {target.processedAt
                                ? formatDate(target.processedAt)
                                : '-'}
                            </td>
                            <td className="px-4 py-3 text-sm text-red-400 max-w-xs truncate">
                              {target.errorMessage || '-'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Timestamps */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-dark-600">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Created:</span>
                    <span className="text-gray-300">
                      {formatDate(jobDetails.createdAt)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Last Updated:</span>
                    <span className="text-gray-300">
                      {formatDate(jobDetails.updatedAt)}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center py-8">
                <div className="text-gray-400">Loading job details...</div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />
    </div>
  );
};
