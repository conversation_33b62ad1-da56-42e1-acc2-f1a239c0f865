import { FilterGroup } from '@panels/ui';

export const createFreespinBonusFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'vendorName',
        label: 'Provider',
        type: 'text',
        placeholder: 'Search by provider name...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Inactive' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'createdAfter',
        label: 'Created After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Created Before',
        type: 'date',
      },
      {
        key: 'minBetAmount',
        label: 'Min Bet Amount',
        type: 'number',
        placeholder: 'Minimum bet amount...',
        min: 0,
      },
      {
        key: 'maxBetAmount',
        label: 'Max Bet Amount',
        type: 'number',
        placeholder: 'Maximum bet amount...',
        min: 0,
      },
    ],
  },
];
