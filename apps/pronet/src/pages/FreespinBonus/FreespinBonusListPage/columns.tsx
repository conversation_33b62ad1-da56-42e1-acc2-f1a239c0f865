import { Eye, Pause, Play, Trash2, Edit } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type FreespinBonus } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format bonus status
const formatBonusStatus = (bonus: FreespinBonus) => {
  const isActive = bonus.bonus.isActive;
  const isExpired = bonus.bonus.expiresAt
    ? new Date(bonus.bonus.expiresAt) < new Date()
    : false;

  let status = 'Unknown';
  let colorClass = 'text-gray-400';

  if (isExpired) {
    status = 'Expired';
    colorClass = 'text-red-400';
  } else if (isActive) {
    status = 'Active';
    colorClass = 'text-green-400';
  } else {
    status = 'Inactive';
    colorClass = 'text-yellow-400';
  }

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass} bg-opacity-10`}>
      <span className={`w-2 h-2 rounded-full mr-2 ${colorClass.replace('text-', 'bg-')}`}></span>
      {status}
    </span>
  );
};

interface ColumnHandlers {
  onViewBonus: (bonus: FreespinBonus) => void;
  onEditBonus: (bonus: FreespinBonus) => void;
  onPauseBonus: (bonus: FreespinBonus) => void;
  onDeleteBonus: (bonus: FreespinBonus) => void;
}

export const createFreespinBonusColumns = (handlers: ColumnHandlers): TableColumn<FreespinBonus>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (bonus) => (
      <span className="font-mono text-sm text-gray-300">#{bonus.id}</span>
    ),
  },
  {
    key: 'name',
    label: 'Name',
    width: '200px',
    sortable: true,
    render: (bonus) => (
      <span className="font-medium text-gray-300">{bonus.bonus.name}</span>
    ),
  },
  {
    key: 'vendorName',
    label: 'Provider',
    width: '150px',
    sortable: true,
    render: (bonus) => (
      <span className="font-medium text-gray-300">{bonus.vendorName}</span>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (bonus) => formatBonusStatus(bonus),
  },
  {
    key: 'freespins',
    label: 'Freespins',
    width: '100px',
    sortable: false,
    render: (bonus) => {
      const freespins = bonus.values.nOfFreespins as number;
      return <span className="text-gray-300">{freespins || 'N/A'}</span>;
    },
  },
  {
    key: 'betAmount',
    label: 'Bet Amount',
    width: '120px',
    sortable: false,
    render: (bonus) => {
      const betAmount = bonus.values.betAmount as number;
      return (
        <span className="text-gray-300">
          {betAmount ? `₺${betAmount.toFixed(2)}` : 'N/A'}
        </span>
      );
    },
  },
  {
    key: 'maxWin',
    label: 'Max Win',
    width: '120px',
    sortable: false,
    render: (bonus) => {
      const maxWin = bonus.values.maxWin as number;
      return (
        <span className="text-gray-300">
          {maxWin ? `₺${maxWin.toFixed(2)}` : 'N/A'}
        </span>
      );
    },
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (bonus) => (
      <span className="text-sm text-gray-400">
        {formatDate(bonus.createdAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '180px',
    sortable: false,
    sticky: 'right',
    render: (bonus) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onViewBonus(bonus)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onEditBonus(bonus)}
          className="p-1 text-blue-400 hover:text-blue-300"
          title="Edit Bonus"
        >
          <Edit className="h-4 w-4" />
        </Button>

        {bonus.bonus.isActive ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlers.onPauseBonus(bonus)}
            className="p-1 text-yellow-400 hover:text-yellow-300"
            title="Pause Bonus"
          >
            <Pause className="h-4 w-4" />
          </Button>
        ) : (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlers.onPauseBonus(bonus)}
            className="p-1 text-green-400 hover:text-green-300"
            title="Activate Bonus"
          >
            <Play className="h-4 w-4" />
          </Button>
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onDeleteBonus(bonus)}
          className="p-1 text-red-400 hover:text-red-300"
          title="Delete Bonus"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];
