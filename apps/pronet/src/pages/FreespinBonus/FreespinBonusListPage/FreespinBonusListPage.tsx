import { useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useFreespinBonuses } from '../../../hooks/useFreespinBonuses';
import Button from '../../../components/ui/Button';
import { FreespinBonusDetailsModalNew } from '../../../components/modals/FreespinBonusDetailsModal';
import { FreespinBonusCreateModal } from '../../../components/modals/FreespinBonusCreateModal';
import { BonusEditModal } from '../../../components/modals/BonusEditModal';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { useApi } from '../../../features/api/useApi';
import {
  BonusToggleRequest,
  BonusSoftDeleteRequest,
  type FreespinBonus,
} from '@panels/api';
import { createFreespinBonusColumns } from './columns';
import { createFreespinBonusFilters } from './filters';

export const FreespinBonusListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  // Modal state
  const [selectedBonus, setSelectedBonus] = useState<FreespinBonus | null>(
    null
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });

  // Fetch data using the hook
  const { data, total, isLoading, error, refetch } = useFreespinBonuses({
    page: currentPage,
    limit: itemsPerPage,
    sortField: sortState.field,
    sortDirection: sortState.direction,
    filters,
  });

  // Action handlers
  const handleViewBonus = (bonus: FreespinBonus) => {
    setSelectedBonus(bonus);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedBonus(null);
  };

  const handleCreateBonus = () => {
    setIsCreateModalOpen(true);
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleCreateSuccess = () => {
    refetch();
    setIsCreateModalOpen(false);
  };

  const handleEditBonus = (bonus: FreespinBonus) => {
    setSelectedBonus(bonus);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setSelectedBonus(null);
  };

  const handleEditSuccess = () => {
    refetch();
    setIsEditModalOpen(false);
    setSelectedBonus(null);
  };

  const handlePauseBonus = (bonus: FreespinBonus) => {
    const isCurrentlyActive = bonus.bonus.isActive;
    const action = isCurrentlyActive ? 'pause' : 'activate';
    const actionText = isCurrentlyActive ? 'Pause' : 'Activate';

    setConfirmationDialog({
      isOpen: true,
      title: `${actionText} Freespin Bonus`,
      message: `Are you sure you want to ${action} the freespin bonus "${bonus.bonus.name}"?`,
      variant: isCurrentlyActive ? 'warning' : 'info',
      confirmText: actionText,
      action: async () => {
        try {
          const request = new BonusToggleRequest({
            id: bonus.bonus.id,
            isActive: !isCurrentlyActive,
          });
          await pronet.makeRequest(request);
          refetch();
        } catch (error) {
          console.error(`Failed to ${action} bonus:`, error);
        }
      },
    });
  };

  const handleDeleteBonus = (bonus: FreespinBonus) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Freespin Bonus',
      message: `Are you sure you want to delete the freespin bonus "${bonus.bonus.name}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        try {
          const request = new BonusSoftDeleteRequest({
            id: bonus.bonus.id,
          });
          await pronet.makeRequest(request);
          refetch();
        } catch (error) {
          console.error('Failed to delete bonus:', error);
        }
      },
    });
  };

  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    await confirmationDialog.action();
    handleConfirmationClose();
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with handlers
  const columns = createFreespinBonusColumns({
    onViewBonus: handleViewBonus,
    onEditBonus: handleEditBonus,
    onPauseBonus: handlePauseBonus,
    onDeleteBonus: handleDeleteBonus,
  });

  // Create filter groups
  const filterGroups = createFreespinBonusFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No freespin bonuses found',
    description: 'Get started by creating your first freespin bonus.',
    action: (
      <Button onClick={handleCreateBonus} className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        Create Freespin Bonus
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            Error loading freespin bonuses
          </h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <Button onClick={() => refetch()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">Freespin Bonuses</h1>
          <p className="text-gray-400">
            Manage and monitor your freespin bonus campaigns
          </p>
        </div>
        <Button onClick={handleCreateBonus} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Freespin Bonus
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          sortState={sortState}
        />
      </div>

      {/* Details Modal */}
      <FreespinBonusDetailsModalNew
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        bonus={selectedBonus}
      />

      {/* Create Modal */}
      <FreespinBonusCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {/* Edit Modal */}
      <BonusEditModal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        bonus={selectedBonus?.bonus || null}
        onSuccess={handleEditSuccess}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
      />
    </div>
  );
};
