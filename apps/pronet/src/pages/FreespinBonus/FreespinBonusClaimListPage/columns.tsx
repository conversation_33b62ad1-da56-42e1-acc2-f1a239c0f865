import { Eye } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusClaim } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format source badge with updated source types
const formatSource = (source: string) => {
  const sourceColors: Record<string, string> = {
    'bulk-assignment': 'bg-blue-900 text-blue-300 border-blue-700',
    manual: 'bg-green-900 text-green-300 border-green-700',
    promocode: 'bg-purple-900 text-purple-300 border-purple-700',
    'rewards-page': 'bg-orange-900 text-orange-300 border-orange-700',
    // Legacy fallbacks
    automatic: 'bg-green-900 text-green-300 border-green-700',
    admin: 'bg-orange-900 text-orange-300 border-orange-700',
  };

  const colorClass =
    sourceColors[source.toLowerCase()] ||
    'bg-gray-900 text-gray-300 border-gray-700';

  // Format display name
  const displayName = source
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {displayName}
    </span>
  );
};

interface ColumnHandlers {
  onViewClaim: (claim: BonusClaim) => void;
}

export const createFreespinBonusClaimColumns = (
  handlers: ColumnHandlers
): TableColumn<BonusClaim>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (claim) => (
      <span className="font-mono text-sm text-gray-300">#{claim.id}</span>
    ),
  },
  {
    key: 'customer',
    label: 'Customer',
    width: '200px',
    sortable: true,
    render: (claim) => (
      <div className="space-y-1">
        <div className="font-medium text-gray-300">
          {claim.customer.username}
        </div>
        <div className="text-xs text-gray-400">
          External ID: {claim.customer.externalId}
        </div>
      </div>
    ),
  },
  {
    key: 'bonus',
    label: 'Bonus',
    width: '200px',
    sortable: true,
    render: (claim) => (
      <div className="space-y-1">
        <div className="font-medium text-gray-300 truncate">
          {claim.bonus?.name}
        </div>
        <div className="text-xs text-gray-400 capitalize">
          {claim.bonus?.type}
        </div>
      </div>
    ),
  },
  {
    key: 'source',
    label: 'Source',
    width: '120px',
    sortable: true,
    render: (claim) => formatSource(claim.source),
  },
  {
    key: 'createdAt',
    label: 'Claimed At',
    width: '160px',
    sortable: true,
    render: (claim) => (
      <span className="text-sm text-gray-400">
        {formatDate(claim.createdAt)}
      </span>
    ),
  },
  {
    key: 'updatedAt',
    label: 'Updated At',
    width: '160px',
    sortable: true,
    render: (claim) => (
      <span className="text-sm text-gray-400">
        {formatDate(claim.updatedAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '100px',
    sortable: false,
    sticky: 'right',
    render: (claim) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onViewClaim(claim)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];
