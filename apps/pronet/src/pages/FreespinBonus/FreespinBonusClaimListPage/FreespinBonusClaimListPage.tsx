import { useMemo, useState } from 'react';
import { Database } from 'lucide-react';
import { DataTable, SortState, ClaimDetailsModal } from '@panels/ui';
import { useBonusClaims } from '../../../hooks/useBonusClaims';
import Button from '../../../components/ui/Button';
import { type BonusClaim } from '@panels/api';
import { createFreespinBonusClaimColumns } from './columns';
import { createFreespinBonusClaimFilters } from './filters';

export const FreespinBonusClaimListPage = () => {
  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(
    () => ({ ...filters, bonusType: 'freespin' }),
    [filters]
  );

  // Modal state
  const [selectedClaim, setSelectedClaim] = useState<BonusClaim | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch data
  const { data, total, isLoading, error, refetch } = useBonusClaims({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleViewClaim = (claim: BonusClaim) => {
    setSelectedClaim(claim);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedClaim(null);
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with handlers
  const columns = createFreespinBonusClaimColumns({
    onViewClaim: handleViewClaim,
  });

  // Create filter groups
  const filterGroups = createFreespinBonusClaimFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Database className="h-12 w-12 text-gray-400" />,
    title: 'No bonus claims found',
    description: 'No bonus claims match your current filters.',
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            Error loading bonus claims
          </h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <Button onClick={refetch}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Freespin Bonus Claims
          </h1>
          <p className="text-gray-400">View and manage freespin bonus claims</p>
        </div>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          sortState={sortState}
        />
      </div>

      {/* Details Modal */}
      <ClaimDetailsModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        claim={selectedClaim}
      >
        {selectedClaim && <ClaimDetailsContent claim={selectedClaim} />}
      </ClaimDetailsModal>
    </div>
  );
};

// Helper component for claim details content
const ClaimDetailsContent = ({ claim }: { claim: BonusClaim }) => {
  // Format date helper
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short',
    }).format(date);
  };

  // Format source badge
  const formatSource = (source: string) => {
    const sourceColors: Record<string, string> = {
      'bulk-assignment': 'bg-blue-900 text-blue-300 border-blue-700',
      manual: 'bg-green-900 text-green-300 border-green-700',
      promocode: 'bg-purple-900 text-purple-300 border-purple-700',
      'rewards-page': 'bg-orange-900 text-orange-300 border-orange-700',
    };

    const colorClass =
      sourceColors[source.toLowerCase()] ||
      'bg-gray-900 text-gray-300 border-gray-700';

    const displayName = source
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
      >
        {displayName}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2">
              Claim Information
            </h4>
            <div className="bg-dark-700 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">ID:</span>
                <span className="text-sm font-mono text-gray-300">
                  #{claim.id}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Source:</span>
                <div>{formatSource(claim.source)}</div>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2">
              Customer Information
            </h4>
            <div className="bg-dark-700 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">Username:</span>
                <span className="text-sm text-gray-300">
                  {claim.customer.username}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-400">External ID:</span>
                <span className="text-sm font-mono text-gray-300">
                  {claim.customer.externalId}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bonus Information */}
      {claim.bonus && (
        <div>
          <h4 className="text-sm font-medium text-gray-400 mb-2">
            Bonus Information
          </h4>
          <div className="bg-dark-700 rounded-lg p-4 space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-400">Name:</span>
              <span className="text-sm text-gray-300">{claim.bonus.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-400">Type:</span>
              <span className="text-sm text-gray-300 capitalize">
                {claim.bonus.type}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Timestamps */}
      <div>
        <h4 className="text-sm font-medium text-gray-400 mb-2">Timestamps</h4>
        <div className="bg-dark-700 rounded-lg p-4 space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Claimed At:</span>
            <span className="text-sm text-gray-300">
              {formatDate(claim.createdAt)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Updated At:</span>
            <span className="text-sm text-gray-300">
              {formatDate(claim.updatedAt)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
