import { FilterGroup } from '@panels/ui';

export const createFreespinBonusClaimFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'username',
        label: 'Customer Username',
        type: 'text',
        placeholder: 'Search by username...',
      },
      {
        key: 'customerId',
        label: 'Customer ID',
        type: 'number',
        placeholder: 'Enter customer ID...',
      },
      {
        key: 'source',
        label: 'Source',
        type: 'select',
        options: [
          { value: 'bulk-assignment', label: 'Bulk Assignment' },
          { value: 'manual', label: 'Manual' },
          { value: 'promocode', label: 'Promocode' },
          { value: 'rewards-page', label: 'Rewards Page' },
        ],
      },
      {
        key: 'bonusType',
        label: 'Bonus Type',
        type: 'select',
        options: [
          { value: 'freespin', label: 'Freespin' },
          { value: 'deposit', label: 'Deposit' },
          { value: 'cashback', label: 'Cashback' },
          { value: 'reload', label: 'Reload' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'externalCustomerId',
        label: 'External Customer ID',
        type: 'number',
        placeholder: 'Enter external customer ID...',
      },
      {
        key: 'createdAfter',
        label: 'Claimed After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Claimed Before',
        type: 'date',
      },
    ],
  },
];
