import { FilterGroup } from '@panels/ui';

export const createFreespinBonusTemplateFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'vendorName',
        label: 'Provider',
        type: 'text',
        placeholder: 'Search by provider name...',
      },
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Inactive' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'createdAfter',
        label: 'Created After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Created Before',
        type: 'date',
      },
      {
        key: 'minValidDays',
        label: 'Min Valid Days',
        type: 'number',
        placeholder: 'Minimum valid days...',
        min: 1,
      },
      {
        key: 'maxValidDays',
        label: 'Max Valid Days',
        type: 'number',
        placeholder: 'Maximum valid days...',
        min: 1,
      },
    ],
  },
];
