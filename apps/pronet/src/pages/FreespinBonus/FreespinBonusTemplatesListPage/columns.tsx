import { Eye, Trash2 } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type FreespinBonusTemplate } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format template values
const formatTemplateValues = (template: FreespinBonusTemplate) => {
  const values = template.values;
  const parts: string[] = [];

  // Add freespins
  if (values.nOfFreespins) {
    parts.push(`${values.nOfFreespins} FS`);
  }

  // Add bet amount
  if (values.betAmount) {
    parts.push(`₺${(values.betAmount as number).toFixed(2)} bet`);
  }

  // Add max win
  if (values.maxWin) {
    parts.push(`₺${(values.maxWin as number).toFixed(2)} max`);
  }

  return parts.length > 0 ? parts.join(' • ') : 'N/A';
};

interface ColumnHandlers {
  onViewTemplate: (template: FreespinBonusTemplate) => void;
  onDeleteTemplate: (template: FreespinBonusTemplate) => void;
}

export const createFreespinBonusTemplateColumns = (handlers: ColumnHandlers): TableColumn<FreespinBonusTemplate>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (template) => (
      <span className="font-mono text-sm text-gray-300">#{template.id}</span>
    ),
  },
  {
    key: 'nameAndProvider',
    label: 'Template / Provider',
    width: '250px',
    sortable: true,
    render: (template) => (
      <div className="space-y-1">
        <div className="font-medium text-gray-300">
          {template.bonusTemplate.name}
        </div>
        <div className="text-sm text-gray-400">{template.vendorName}</div>
      </div>
    ),
  },
  {
    key: 'values',
    label: 'Configuration',
    width: '250px',
    sortable: false,
    render: (template) => (
      <span className="text-sm text-gray-300">
        {formatTemplateValues(template)}
      </span>
    ),
  },
  {
    key: 'gameIds',
    label: 'Games',
    width: '100px',
    sortable: false,
    render: (template) => (
      <span className="text-gray-300">
        {template.gameIds.length} game
        {template.gameIds.length !== 1 ? 's' : ''}
      </span>
    ),
  },
  {
    key: 'validForDays',
    label: 'Valid For',
    width: '120px',
    sortable: true,
    render: (template) => (
      <span className="text-gray-300">
        {template.validForDays} day{template.validForDays !== 1 ? 's' : ''}
      </span>
    ),
  },
  {
    key: 'rules',
    label: 'Rules',
    width: '100px',
    sortable: false,
    render: (template) => (
      <span className="text-gray-300">
        {template.bonusTemplate.rules.length} rule
        {template.bonusTemplate.rules.length !== 1 ? 's' : ''}
      </span>
    ),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (template) => (
      <span className="text-sm text-gray-400">
        {formatDate(template.createdAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '120px',
    sortable: false,
    sticky: 'right',
    render: (template) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onViewTemplate(template)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onDeleteTemplate(template)}
          className="p-1 text-red-400 hover:text-red-300"
          title="Delete Template"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];
