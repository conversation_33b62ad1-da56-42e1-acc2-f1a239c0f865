import { useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useFreespinBonusTemplates } from '../../../hooks/useFreespinBonusTemplates';
import Button from '../../../components/ui/Button';
import { FreespinBonusTemplateDetailsModalNew } from '../../../components/modals/FreespinBonusTemplateDetailsModal';
import { FreespinBonusTemplateCreateModal } from '../../../components/modals/FreespinBonusTemplateCreateModal';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { useApi } from '../../../features/api/useApi';
import {
  BonusTemplateSoftDeleteRequest,
  type FreespinBonusTemplate,
} from '@panels/api';
import { createFreespinBonusTemplateColumns } from './columns';
import { createFreespinBonusTemplateFilters } from './filters';

export const FreespinBonusTemplatesListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(
    () => ({
      ...filters,
      isActive: true,
    }),
    [filters]
  );

  // Modal state
  const [selectedTemplate, setSelectedTemplate] =
    useState<FreespinBonusTemplate | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });

  // Fetch data
  const { data, total, isLoading, error, refetch } = useFreespinBonusTemplates({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleCreateTemplate = () => {
    setIsCreateModalOpen(true);
  };

  const handleViewTemplate = (template: FreespinBonusTemplate) => {
    setSelectedTemplate(template);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedTemplate(null);
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleCreateSuccess = () => {
    refetch();
    setIsCreateModalOpen(false);
  };

  const handleDeleteTemplate = (template: FreespinBonusTemplate) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Template',
      message: `Are you sure you want to delete the template "${template.bonusTemplate.name}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        try {
          const request = new BonusTemplateSoftDeleteRequest({
            id: template.bonusTemplate.id,
          });
          await pronet.makeRequest(request);
          refetch();
        } catch (error) {
          console.error('Failed to delete template:', error);
        }
      },
    });
  };

  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    await confirmationDialog.action();
    handleConfirmationClose();
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with handlers
  const columns = createFreespinBonusTemplateColumns({
    onViewTemplate: handleViewTemplate,
    onDeleteTemplate: handleDeleteTemplate,
  });

  // Create filter groups
  const filterGroups = createFreespinBonusTemplateFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No templates found',
    description: 'Get started by creating your first freespin bonus template.',
    action: (
      <Button
        onClick={handleCreateTemplate}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Template
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            Error loading templates
          </h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <Button onClick={() => refetch()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Freespin Bonus Templates
          </h1>
          <p className="text-gray-400">
            Manage reusable templates for freespin bonuses
          </p>
        </div>
        <Button
          onClick={handleCreateTemplate}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Template
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          sortState={sortState}
        />
      </div>

      {/* Details Modal */}
      <FreespinBonusTemplateDetailsModalNew
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        template={selectedTemplate}
      />

      {/* Create Modal */}
      <FreespinBonusTemplateCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
      />
    </div>
  );
};
