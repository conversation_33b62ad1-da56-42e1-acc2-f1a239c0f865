import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Eye, Users } from 'lucide-react';
import { DataTable, TableColumn, FilterGroup, SortState } from '@panels/ui';
import { useCustomers } from '../../hooks/useCustomers';
import Button from '../../components/ui/Button';
import type { Customer } from '@panels/api';

export const CustomersListPage = () => {
  const navigate = useNavigate();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  // Fetch data
  const { data, total, isLoading, error, refetch } = useCustomers({
    page: currentPage,
    limit: itemsPerPage,
    filters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleViewCustomer = (customer: Customer) => {
    navigate(`/customers/${customer.id}`);
  };

  const handleRowClick = (customer: Customer) => {
    navigate(`/customers/${customer.id}`);
  };

  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Format date helper
  const formatDate = (date: Date | undefined) => {
    if (!date) return 'N/A';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Format status helper
  const formatStatus = (status: string | undefined) => {
    if (!status) return 'Unknown';
    
    const statusLower = status.toLowerCase();
    let colorClass: string;

    switch (statusLower) {
      case 'active':
        colorClass = 'bg-green-900 text-green-300 border-green-700';
        break;
      case 'inactive':
        colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
        break;
      case 'suspended':
        colorClass = 'bg-red-900 text-red-300 border-red-700';
        break;
      default:
        colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
    }

    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  // Table columns configuration
  const columns: TableColumn<Customer>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (customer) => (
        <span className="font-mono text-sm text-gray-300">#{customer.id}</span>
      ),
    },
    {
      key: 'username',
      label: 'Username',
      width: '200px',
      sortable: true,
      render: (customer) => (
        <span className="font-medium text-gray-300">{customer.username}</span>
      ),
    },
    {
      key: 'code',
      label: 'Code',
      width: '120px',
      sortable: true,
      render: (customer) => (
        <span className="font-mono text-sm text-gray-300">{customer.code}</span>
      ),
    },
    {
      key: 'email',
      label: 'Email',
      width: '200px',
      sortable: true,
      render: (customer) => (
        <span className="text-gray-300">{customer.email || 'N/A'}</span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      width: '120px',
      sortable: true,
      render: (customer) => formatStatus(customer.status),
    },
    {
      key: 'registrationDate',
      label: 'Registration',
      width: '160px',
      sortable: true,
      render: (customer) => (
        <span className="text-sm text-gray-400">
          {formatDate(customer.registrationDate)}
        </span>
      ),
    },
    {
      key: 'lastLoginDate',
      label: 'Last Login',
      width: '160px',
      sortable: true,
      render: (customer) => (
        <span className="text-sm text-gray-400">
          {formatDate(customer.lastLoginDate)}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      sortable: false,
      sticky: 'right',
      render: (customer) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleViewCustomer(customer);
            }}
            className="p-1"
            title="View Details"
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter groups configuration
  const filterGroups: FilterGroup[] = [
    {
      name: 'basic',
      displayName: 'Basic Filters',
      fields: [
        {
          key: 'username',
          label: 'Username',
          type: 'text',
          placeholder: 'Search by username...',
        },
        {
          key: 'code',
          label: 'Customer Code',
          type: 'text',
          placeholder: 'Search by customer code...',
        },
      ],
    },
  ];

  // Empty state configuration
  const emptyState = {
    icon: <Users className="h-12 w-12 text-gray-400" />,
    title: 'No customers found',
    description: 'No customers match your current search criteria.',
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-400 mb-4">
            <svg
              className="h-12 w-12 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            Error loading customers
          </h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <Button onClick={refetch}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">Customers</h1>
          <p className="text-gray-400 mt-1">
            View and manage customer accounts
          </p>
        </div>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          onRowClick={handleRowClick}
          emptyState={emptyState}
          title={`Customers (${total.toLocaleString()})`}
        />
      </div>
    </div>
  );
};
