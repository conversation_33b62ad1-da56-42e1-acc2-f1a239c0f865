import { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { ArrowLeft, User, Gift, Tag } from 'lucide-react';
import { DataTable, TableColumn } from '@panels/ui';
import { useCustomer } from '../../hooks/useCustomer';
import { useBonusClaimsByCustomer } from '../../hooks/useBonusClaimsByCustomer';
import { useBonusPromocodeActivationsByCustomer } from '../../hooks/useBonusPromocodeActivationsByCustomer';
import Button from '../../components/ui/Button';
import type { Customer, BonusClaim, BonusPromocodeActivation } from '@panels/api';

// Tab components (we'll create these as simple placeholders for now)
const CustomerInfoTab = ({ customer }: { customer: Customer }) => (
  <div className="space-y-6">
    <div className="bg-dark-700 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-gray-300 mb-4">Customer Information</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">ID</label>
          <p className="text-gray-300">#{customer.id}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">External ID</label>
          <p className="text-gray-300">{customer.externalId}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Username</label>
          <p className="text-gray-300">{customer.username}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Code</label>
          <p className="text-gray-300">{customer.code}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Email</label>
          <p className="text-gray-300">{customer.email || 'N/A'}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
          <p className="text-gray-300">{customer.status || 'Unknown'}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Registration Date</label>
          <p className="text-gray-300">
            {customer.registrationDate 
              ? new Intl.DateTimeFormat('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                }).format(customer.registrationDate)
              : 'N/A'
            }
          </p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Last Login</label>
          <p className="text-gray-300">
            {customer.lastLoginDate 
              ? new Intl.DateTimeFormat('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                }).format(customer.lastLoginDate)
              : 'N/A'
            }
          </p>
        </div>
      </div>
    </div>

    {/* Financial Summary */}
    <div className="bg-dark-700 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-gray-300 mb-4">Financial Summary</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Total Deposits</label>
          <p className="text-gray-300">
            {customer.totalDeposits ? `₺${customer.totalDeposits.toLocaleString()}` : 'N/A'}
          </p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Total Withdrawals</label>
          <p className="text-gray-300">
            {customer.totalWithdrawals ? `₺${customer.totalWithdrawals.toLocaleString()}` : 'N/A'}
          </p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">Total Bonuses</label>
          <p className="text-gray-300">
            {customer.totalBonuses ? `₺${customer.totalBonuses.toLocaleString()}` : 'N/A'}
          </p>
        </div>
      </div>
    </div>
  </div>
);

const BonusesTab = ({ customer }: { customer: Customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const { data, total, isLoading, error, refetch } = useBonusClaimsByCustomer({
    customerId: customer.id,
    page: currentPage,
    limit: itemsPerPage,
  });

  // Format date helper
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Table columns configuration
  const columns: TableColumn<BonusClaim>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (claim) => (
        <span className="font-mono text-sm text-gray-300">#{claim.id}</span>
      ),
    },
    {
      key: 'bonusId',
      label: 'Bonus ID',
      width: '100px',
      sortable: true,
      render: (claim) => (
        <span className="font-mono text-sm text-gray-300">#{claim.bonusId}</span>
      ),
    },
    {
      key: 'bonus.name',
      label: 'Bonus Name',
      width: '200px',
      sortable: true,
      render: (claim) => (
        <span className="font-medium text-gray-300">
          {claim.bonus?.name || 'N/A'}
        </span>
      ),
    },
    {
      key: 'bonus.type',
      label: 'Type',
      width: '120px',
      sortable: true,
      render: (claim) => (
        <span className="text-gray-300">
          {claim.bonus?.type || 'N/A'}
        </span>
      ),
    },
    {
      key: 'source',
      label: 'Source',
      width: '120px',
      sortable: true,
      render: (claim) => (
        <span className="text-gray-300">{claim.source}</span>
      ),
    },
    {
      key: 'createdAt',
      label: 'Claimed At',
      width: '160px',
      sortable: true,
      render: (claim) => (
        <span className="text-sm text-gray-400">
          {formatDate(claim.createdAt)}
        </span>
      ),
    },
  ];

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-dark-700 rounded-lg p-6">
          <div className="text-center">
            <div className="text-red-400 mb-4">
              <svg
                className="h-8 w-8 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-300 mb-2">
              Error loading bonuses
            </h3>
            <p className="text-gray-400 mb-4">{error}</p>
            <Button onClick={refetch}>Try Again</Button>
          </div>
        </div>
      </div>
    );
  }

  // Empty state configuration
  const emptyState = {
    icon: <Gift className="h-12 w-12 text-gray-400" />,
    title: 'No bonuses found',
    description: `${customer.username} has not received any bonuses yet.`,
  };

  return (
    <div className="space-y-6">
      <div className="bg-dark-700 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-300 mb-4">
          Bonus Claims ({total.toLocaleString()})
        </h3>

        <div className="min-h-[400px]">
          <DataTable
            data={data}
            columns={columns}
            total={total}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            isLoading={isLoading}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={setItemsPerPage}
            emptyState={emptyState}
            showFilters={false}
            showSearch={false}
          />
        </div>
      </div>
    </div>
  );
};

const PromoCodesTab = ({ customer }: { customer: Customer }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const { data, total, isLoading, error, refetch } = useBonusPromocodeActivationsByCustomer({
    customerId: customer.id,
    page: currentPage,
    limit: itemsPerPage,
  });

  // Format date helper
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Table columns configuration
  const columns: TableColumn<BonusPromocodeActivation>[] = [
    {
      key: 'id',
      label: 'ID',
      width: '80px',
      sortable: true,
      render: (activation) => (
        <span className="font-mono text-sm text-gray-300">#{activation.id}</span>
      ),
    },
    {
      key: 'promocode.code',
      label: 'Promo Code',
      width: '150px',
      sortable: true,
      render: (activation) => (
        <span className="font-mono font-medium text-gray-300 bg-dark-600 px-2 py-1 rounded">
          {activation.promocode.code}
        </span>
      ),
    },
    {
      key: 'promocode.bonusId',
      label: 'Bonus ID',
      width: '100px',
      sortable: true,
      render: (activation) => (
        <span className="font-mono text-sm text-gray-300">#{activation.promocode.bonusId}</span>
      ),
    },
    {
      key: 'promocode.bonus.name',
      label: 'Bonus Name',
      width: '200px',
      sortable: true,
      render: (activation) => (
        <span className="font-medium text-gray-300">
          {activation.promocode.bonus?.name || 'N/A'}
        </span>
      ),
    },
    {
      key: 'promocode.bonus.type',
      label: 'Bonus Type',
      width: '120px',
      sortable: true,
      render: (activation) => (
        <span className="text-gray-300">
          {activation.promocode.bonus?.type || 'N/A'}
        </span>
      ),
    },
    {
      key: 'promocode.activations',
      label: 'Usage Count',
      width: '100px',
      sortable: true,
      render: (activation) => (
        <span className="text-gray-300">
          {activation.promocode.activations}
          {activation.promocode.maxActivations && (
            <span className="text-gray-500">/{activation.promocode.maxActivations}</span>
          )}
        </span>
      ),
    },
    {
      key: 'promocode.isActive',
      label: 'Status',
      width: '100px',
      sortable: true,
      render: (activation) => {
        const isActive = activation.promocode.isActive;
        return (
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium border ${
              isActive
                ? 'bg-green-900 text-green-300 border-green-700'
                : 'bg-gray-900 text-gray-300 border-gray-700'
            }`}
          >
            {isActive ? 'Active' : 'Inactive'}
          </span>
        );
      },
    },
    {
      key: 'createdAt',
      label: 'Activated At',
      width: '160px',
      sortable: true,
      render: (activation) => (
        <span className="text-sm text-gray-400">
          {formatDate(activation.createdAt)}
        </span>
      ),
    },
  ];

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-dark-700 rounded-lg p-6">
          <div className="text-center">
            <div className="text-red-400 mb-4">
              <svg
                className="h-8 w-8 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-300 mb-2">
              Error loading promo codes
            </h3>
            <p className="text-gray-400 mb-4">{error}</p>
            <Button onClick={refetch}>Try Again</Button>
          </div>
        </div>
      </div>
    );
  }

  // Empty state configuration
  const emptyState = {
    icon: <Tag className="h-12 w-12 text-gray-400" />,
    title: 'No promo codes found',
    description: `${customer.username} has not used any promo codes yet.`,
  };

  return (
    <div className="space-y-6">
      <div className="bg-dark-700 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-300 mb-4">
          Promo Code Activations ({total.toLocaleString()})
        </h3>

        <div className="min-h-[400px]">
          <DataTable
            data={data}
            columns={columns}
            total={total}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            isLoading={isLoading}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={setItemsPerPage}
            emptyState={emptyState}
            showFilters={false}
            showSearch={false}
          />
        </div>
      </div>
    </div>
  );
};

export const CustomerDetailsPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('info');

  const customerId = id ? parseInt(id, 10) : 0;
  const { data: customer, isLoading, error, refetch } = useCustomer({ id: customerId });

  const tabs = [
    { id: 'info', label: 'Customer Info', icon: User },
    { id: 'bonuses', label: 'Bonuses', icon: Gift },
    { id: 'promocodes', label: 'Promo Codes', icon: Tag },
  ];

  const handleBackClick = () => {
    navigate('/customers');
  };

  const renderTabContent = () => {
    if (!customer) return null;

    switch (activeTab) {
      case 'info':
        return <CustomerInfoTab customer={customer} />;
      case 'bonuses':
        return <BonusesTab customer={customer} />;
      case 'promocodes':
        return <PromoCodesTab customer={customer} />;
      default:
        return <CustomerInfoTab customer={customer} />;
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading customer details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-400 mb-4">
            <svg
              className="h-12 w-12 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            Error loading customer
          </h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <div className="space-x-4">
            <Button onClick={refetch}>Try Again</Button>
            <Button variant="outline" onClick={handleBackClick}>
              Back to Customers
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Customer not found
  if (!customer) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-gray-400 mb-4">
            <User className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            Customer not found
          </h3>
          <p className="text-gray-400 mb-4">
            The customer with ID {id} could not be found.
          </p>
          <Button onClick={handleBackClick}>Back to Customers</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={handleBackClick}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Customers</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-300">
              {customer.username}
            </h1>
            <p className="text-gray-400">Customer ID: #{customer.id}</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-dark-600">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {renderTabContent()}
      </div>
    </div>
  );
};
