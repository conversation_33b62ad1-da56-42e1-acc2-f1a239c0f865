import { FilterGroup } from '@panels/ui';

export const createLossbackBonusTemplateFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'templateId',
        label: 'Template ID',
        type: 'number',
        placeholder: 'Enter template ID...',
      },
      {
        key: 'name',
        label: 'Template Name',
        type: 'text',
        placeholder: 'Search by template name...',
      },
      {
        key: 'minMaxBalance',
        label: 'Min Max Balance',
        type: 'number',
        placeholder: 'Minimum max balance...',
        min: 0,
      },
      {
        key: 'maxMaxBalance',
        label: 'Max Max Balance',
        type: 'number',
        placeholder: 'Maximum max balance...',
        min: 0,
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'createdAfter',
        label: 'Created After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Created Before',
        type: 'date',
      },
      {
        key: 'minLossbackPercentage',
        label: 'Min Lossback %',
        type: 'number',
        placeholder: 'Minimum lossback percentage...',
        min: 0,
        max: 100,
      },
      {
        key: 'maxLossbackPercentage',
        label: 'Max Lossback %',
        type: 'number',
        placeholder: 'Maximum lossback percentage...',
        min: 0,
        max: 100,
      },
    ],
  },
];
