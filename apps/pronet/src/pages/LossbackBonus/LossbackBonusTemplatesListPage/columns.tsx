import { Eye, Trash2 } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type LossbackBonusTemplate } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format template status
const formatTemplateStatus = (template: LossbackBonusTemplate) => {
  const isDeleted = template.bonusTemplate.deletedAt !== null;

  let status: string;
  let colorClass: string;

  if (isDeleted) {
    status = 'deleted';
    colorClass = 'bg-red-900 text-red-300 border-red-700';
  } else {
    status = 'active';
    colorClass = 'bg-green-900 text-green-300 border-green-700';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

interface ColumnHandlers {
  onViewTemplate: (template: LossbackBonusTemplate) => void;
  onDeleteTemplate: (template: LossbackBonusTemplate) => void;
}

export const createLossbackBonusTemplateColumns = (handlers: ColumnHandlers): TableColumn<LossbackBonusTemplate>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (template) => (
      <span className="font-mono text-sm text-gray-300">#{template.id}</span>
    ),
  },
  {
    key: 'name',
    label: 'Name',
    width: '200px',
    sortable: true,
    render: (template) => (
      <span className="font-medium text-gray-300">
        {template.bonusTemplate.name}
      </span>
    ),
  },
  {
    key: 'maxBalance',
    label: 'Max Balance',
    width: '120px',
    sortable: true,
    render: (template) => (
      <span className="text-gray-300">
        ₺{template.maxBalance.toLocaleString()}
      </span>
    ),
  },
  {
    key: 'lossbackPercentage',
    label: 'Lossback %',
    width: '120px',
    sortable: true,
    render: (template) => (
      <span className="text-gray-300">{template.lossbackPercentage}%</span>
    ),
  },
  {
    key: 'happyHours',
    label: 'Happy Hours',
    width: '180px',
    sortable: false,
    render: (template) => (
      <div className="text-gray-300">
        <div className="text-sm">
          {template.happyHoursStart} - {template.happyHoursEnd}
        </div>
        <div className="text-xs text-gray-400">
          +{template.happyHoursBoostPercentage}% boost
        </div>
      </div>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (template) => formatTemplateStatus(template),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (template) => (
      <span className="text-sm text-gray-400">
        {formatDate(template.createdAt)}
      </span>
    ),
  },
  {
    key: 'updatedAt',
    label: 'Updated',
    width: '160px',
    sortable: true,
    render: (template) => (
      <span className="text-sm text-gray-400">
        {formatDate(template.updatedAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '120px',
    sortable: false,
    sticky: 'right',
    render: (template) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onViewTemplate(template)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onDeleteTemplate(template)}
          className="p-1 text-red-400 hover:text-red-300"
          title="Delete Template"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];

// Export helper functions for use in other components
export { formatDate, formatTemplateStatus };
