import { FilterGroup } from '@panels/ui';

export const createLossbackBonusClaimFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'claimId',
        label: 'Claim ID',
        type: 'number',
        placeholder: 'Enter claim ID...',
      },
      {
        key: 'customerId',
        label: 'Customer ID',
        type: 'number',
        placeholder: 'Enter customer ID...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'source',
        label: 'Claim Source',
        type: 'select',
        options: [
          { value: 'manual', label: 'Manual' },
          { value: 'automatic', label: 'Automatic' },
          { value: 'promocode', label: 'Promocode' },
          { value: 'bulk', label: 'Bulk Assignment' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'claimedAfter',
        label: 'Claimed After',
        type: 'date',
      },
      {
        key: 'claimedBefore',
        label: 'Claimed Before',
        type: 'date',
      },
      {
        key: 'customerUsername',
        label: 'Customer Username',
        type: 'text',
        placeholder: 'Enter username...',
      },
    ],
  },
];
