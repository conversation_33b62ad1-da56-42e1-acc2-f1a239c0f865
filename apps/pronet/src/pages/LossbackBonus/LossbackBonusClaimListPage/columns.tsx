import { Eye, User, Calendar } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusClaim } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format claim source
const formatClaimSource = (source: string) => {
  const sourceMap: Record<string, { label: string; color: string }> = {
    manual: {
      label: 'Manual',
      color: 'bg-blue-900 text-blue-300 border-blue-700',
    },
    automatic: {
      label: 'Automatic',
      color: 'bg-green-900 text-green-300 border-green-700',
    },
    promocode: {
      label: 'Promocode',
      color: 'bg-purple-900 text-purple-300 border-purple-700',
    },
    bulk: {
      label: 'Bulk Assignment',
      color: 'bg-orange-900 text-orange-300 border-orange-700',
    },
  };

  const sourceInfo = sourceMap[source] || {
    label: source,
    color: 'bg-gray-900 text-gray-300 border-gray-700',
  };

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${sourceInfo.color}`}
    >
      {sourceInfo.label}
    </span>
  );
};

interface ColumnHandlers {
  onViewClaim: (claim: BonusClaim) => void;
}

export const createLossbackBonusClaimColumns = (
  handlers: ColumnHandlers
): TableColumn<BonusClaim>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (claim) => (
      <span className="font-mono text-sm text-gray-300">#{claim.id}</span>
    ),
  },
  {
    key: 'customer',
    label: 'Customer',
    width: '200px',
    sortable: false,
    render: (claim) => (
      <div className="flex items-center space-x-2">
        <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
          <User className="w-4 h-4 text-white" />
        </div>
        <div>
          <div className="text-sm font-medium text-gray-300">
            {claim.customer.username}
          </div>
          <div className="text-xs text-gray-400">
            External ID: {claim.customer.externalId}
          </div>
        </div>
      </div>
    ),
  },
  {
    key: 'bonusName',
    label: 'Bonus',
    width: '200px',
    sortable: false,
    render: (claim) => (
      <span className="text-gray-300">
        {claim.bonus?.name || `Bonus #${claim.bonusId}`}
      </span>
    ),
  },
  {
    key: 'source',
    label: 'Source',
    width: '140px',
    sortable: true,
    render: (claim) => formatClaimSource(claim.source),
  },
  {
    key: 'createdAt',
    label: 'Claimed At',
    width: '160px',
    sortable: true,
    render: (claim) => (
      <div className="flex items-center space-x-2">
        <Calendar className="w-4 h-4 text-gray-400" />
        <span className="text-sm text-gray-300">
          {formatDate(claim.createdAt)}
        </span>
      </div>
    ),
  },
  {
    key: 'updatedAt',
    label: 'Updated',
    width: '160px',
    sortable: true,
    render: (claim) => (
      <span className="text-sm text-gray-400">
        {formatDate(claim.updatedAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '100px',
    sortable: false,
    sticky: 'right',
    render: (claim) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onViewClaim(claim)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];

// Export helper functions for use in other components
export { formatDate, formatClaimSource };
