import { FilterGroup } from '@panels/ui';

export const createLossbackBonusPromocodeFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'promocodeId',
        label: 'Promocode ID',
        type: 'number',
        placeholder: 'Enter promocode ID...',
      },
      {
        key: 'code',
        label: 'Promocode',
        type: 'text',
        placeholder: 'Enter promocode...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Inactive' },
        ],
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'createdAfter',
        label: 'Created After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Created Before',
        type: 'date',
      },
      {
        key: 'minActivations',
        label: 'Min Activations',
        type: 'number',
        placeholder: 'Minimum activations...',
        min: 0,
      },
      {
        key: 'maxActivations',
        label: 'Max Activations',
        type: 'number',
        placeholder: 'Maximum activations...',
        min: 0,
      },
    ],
  },
];
