import {
  Eye,
  X,
  <PERSON>otateCcw,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusBulkAssignmentJob } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to get status configuration
const getStatusConfig = (status: string) => {
  switch (status.toLowerCase()) {
    case 'pending':
      return {
        icon: Clock,
        color: 'text-yellow-400',
        bgColor: 'bg-yellow-900/20',
        borderColor: 'border-yellow-700',
        label: 'Pending',
      };
    case 'processing':
      return {
        icon: AlertCircle,
        color: 'text-blue-400',
        bgColor: 'bg-blue-900/20',
        borderColor: 'border-blue-700',
        label: 'Processing',
      };
    case 'completed':
      return {
        icon: CheckCircle,
        color: 'text-green-400',
        bgColor: 'bg-green-900/20',
        borderColor: 'border-green-700',
        label: 'Completed',
      };
    case 'failed':
      return {
        icon: XCircle,
        color: 'text-red-400',
        bgColor: 'bg-red-900/20',
        borderColor: 'border-red-700',
        label: 'Failed',
      };
    case 'cancelled':
      return {
        icon: X,
        color: 'text-gray-400',
        bgColor: 'bg-gray-900/20',
        borderColor: 'border-gray-700',
        label: 'Cancelled',
      };
    default:
      return {
        icon: AlertCircle,
        color: 'text-gray-400',
        bgColor: 'bg-gray-900/20',
        borderColor: 'border-gray-700',
        label: status,
      };
  }
};

interface CashBonusBulkAssignmentColumnsProps {
  onView: (job: BonusBulkAssignmentJob) => void;
  onCancel: (job: BonusBulkAssignmentJob) => void;
  onRetry: (job: BonusBulkAssignmentJob) => void;
}

export const createCashBonusBulkAssignmentColumns = ({
  onView,
  onCancel,
  onRetry,
}: CashBonusBulkAssignmentColumnsProps): TableColumn<BonusBulkAssignmentJob>[] => [
  {
    key: 'id',
    label: 'Job ID',
    width: '100px',
    sortable: true,
    render: (job) => <div className="font-mono text-gray-300">#{job.id}</div>,
  },
  {
    key: 'bonus',
    label: 'Bonus',
    width: '200px',
    sortable: false,
    render: (job) => {
      const bonus = job.bonus;
      if (!bonus) {
        return <span className="text-gray-400">No bonus data</span>;
      }
      return (
        <div>
          <div className="font-medium text-gray-300">{bonus.name}</div>
          <div className="text-sm text-gray-400">ID: {bonus.id}</div>
        </div>
      );
    },
  },
  {
    key: 'targets',
    label: 'Targets',
    width: '180px',
    sortable: false,
    render: (job) => {
      const targets = job.targets;
      const completed = targets.filter(
        (t: any) => t.status === 'completed'
      ).length;
      const failed = targets.filter((t: any) => t.status === 'failed').length;
      const processing = targets.filter(
        (t: any) => t.status === 'processing'
      ).length;
      const pending = targets.filter((t: any) => t.status === 'pending').length;

      return (
        <div>
          <div className="text-gray-300 font-medium">
            {targets.length} total
          </div>
          <div className="text-xs text-gray-400 space-x-2">
            {completed > 0 && (
              <span className="text-green-400">{completed} done</span>
            )}
            {failed > 0 && (
              <span className="text-red-400">{failed} failed</span>
            )}
            {processing > 0 && (
              <span className="text-blue-400">{processing} processing</span>
            )}
            {pending > 0 && (
              <span className="text-yellow-400">{pending} pending</span>
            )}
          </div>
        </div>
      );
    },
  },
  {
    key: 'status',
    label: 'Status',
    width: '140px',
    sortable: true,
    render: (job) => {
      const config = getStatusConfig(job.status);
      const StatusIcon = config.icon;

      return (
        <div
          className={`flex items-center gap-2 px-2 py-1 rounded-full border ${config.bgColor} ${config.borderColor}`}
        >
          <StatusIcon className={`h-3 w-3 ${config.color}`} />
          <span className={`text-xs font-medium ${config.color}`}>
            {config.label}
          </span>
        </div>
      );
    },
  },
  {
    key: 'bonusValuesVersion',
    label: 'Version',
    width: '80px',
    sortable: true,
    render: (job) => (
      <div className="font-mono text-gray-300">v{job.bonusValuesVersion}</div>
    ),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '140px',
    sortable: true,
    render: (job) => (
      <div className="text-sm text-gray-300">{formatDate(job.createdAt)}</div>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '120px',
    sortable: false,
    render: (job) => {
      const canCancel = ['pending', 'processing'].includes(job.status);
      const canRetry = job.status === 'failed';

      return (
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onView(job)}
            className="p-1 text-blue-400 hover:text-blue-300"
            title="View Details"
          >
            <Eye className="h-4 w-4" />
          </Button>

          {canRetry && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onRetry(job)}
              className="p-1 text-green-400 hover:text-green-300"
              title="Retry Job"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          )}

          {canCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onCancel(job)}
              className="p-1 text-red-400 hover:text-red-300"
              title="Cancel Job"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      );
    },
  },
];
