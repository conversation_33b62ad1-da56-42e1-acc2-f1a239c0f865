import { useState, useEffect, useRef } from 'react';
import { Plus, X } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { useApi } from '../../../features/api/useApi';
import {
  BonusBulkAssignmentJobSearchRequest,
  BonusBulkAssignmentJobGetRequest,
  BonusBulkAssignmentJobCancelRequest,
  BonusBulkAssignmentJobRetryRequest,
  CashBonusBulkAssignmentJobCreateRequest,
  CashBonusSearchRequest,
  type BonusBulkAssignmentJob,
  type CashBonus,
  type BonusBulkAssignmentJobSearchRequestOptions,
  type CashBonusBulkAssignmentRequestCreateOptions,
} from '@panels/api';
import { createCashBonusBulkAssignmentColumns } from './columns';
import { createCashBonusBulkAssignmentFilters } from './filters';

interface FormData {
  bonusId: number | null;
  customerIds: string;
}

interface FormErrors {
  [key: string]: string | undefined;
}

export const CashBonusBulkAssignmentPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: 'createdAt',
    direction: 'desc',
  });
  const [filters, setFilters] = useState({});

  // Data state
  const [jobs, setJobs] = useState<BonusBulkAssignmentJob[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Long polling state
  const [isPolling, setIsPolling] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const POLLING_INTERVAL = 3000; // 3 seconds

  // Modal states
  const [isCreateFormOpen, setIsCreateFormOpen] = useState(false);
  const [isJobDetailsOpen, setIsJobDetailsOpen] = useState(false);
  const [jobDetails, setJobDetails] = useState<BonusBulkAssignmentJob | null>(
    null
  );
  const [jobDetailsError, setJobDetailsError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<FormData>({
    bonusId: null,
    customerIds: '',
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bonuses, setBonuses] = useState<CashBonus[]>([]);
  const [isLoadingBonuses, setIsLoadingBonuses] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    variant: 'danger' as const,
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Load jobs function
  const loadJobs = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const searchOptions: BonusBulkAssignmentJobSearchRequestOptions = {
        page: currentPage,
        limit: itemsPerPage,
        bonusType: 'cash',
        ...filters,
      };

      const request = new BonusBulkAssignmentJobSearchRequest(searchOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setJobs(response.data.items);
        setTotal(response.data.total);
      } else {
        setError(response.error || 'Failed to load jobs');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Polling functions
  const startPolling = () => {
    if (pollingIntervalRef.current) return;

    setIsPolling(true);
    pollingIntervalRef.current = setInterval(() => {
      loadJobs();
    }, POLLING_INTERVAL);
  };

  const stopPolling = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    setIsPolling(false);
  };

  // Check if any jobs are in progress
  const hasActiveJobs = jobs.some((job) =>
    ['pending', 'processing'].includes(job.status)
  );

  // Auto-start/stop polling based on active jobs
  useEffect(() => {
    if (hasActiveJobs && !isPolling) {
      startPolling();
    } else if (!hasActiveJobs && isPolling) {
      stopPolling();
    }
  }, [hasActiveJobs, isPolling]);

  // Load jobs when dependencies change
  useEffect(() => {
    loadJobs();
  }, [currentPage, itemsPerPage, sortState, filters]);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  // Load bonuses function
  const loadBonuses = async () => {
    setIsLoadingBonuses(true);
    try {
      const request = new CashBonusSearchRequest({
        page: 1,
        limit: 100,
        isActive: true,
      });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setBonuses(response.data.items);
      }
    } catch (error) {
      console.error('Failed to load bonuses:', error);
    } finally {
      setIsLoadingBonuses(false);
    }
  };

  // Form handlers
  const handleCreateFormOpen = () => {
    setIsCreateFormOpen(true);
    loadBonuses();
  };

  const handleCreateFormClose = () => {
    setIsCreateFormOpen(false);
    setFormData({ bonusId: null, customerIds: '' });
    setFormErrors({});
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFormSubmit = async () => {
    setFormErrors({});

    // Validation
    if (!formData.bonusId) {
      setFormErrors({ bonusId: 'Please select a bonus' });
      return;
    }

    if (!formData.customerIds.trim()) {
      setFormErrors({ customerIds: 'Please enter customer IDs' });
      return;
    }

    setIsSubmitting(true);
    try {
      const customerIds = formData.customerIds
        .split(/[,\n]/)
        .map((id) => id.trim())
        .filter((id) => id)
        .map((id) => parseInt(id));

      const requestData: CashBonusBulkAssignmentRequestCreateOptions = {
        cashBonusId: formData.bonusId!,
        externalCustomerIds: customerIds,
      };

      const request = new CashBonusBulkAssignmentJobCreateRequest(requestData);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        handleCreateFormClose();
        loadJobs(); // Refresh the list
      } else {
        setFormErrors({
          submit: response.error || 'Failed to create bulk assignment job',
        });
      }
    } catch (error) {
      setFormErrors({
        submit: error instanceof Error ? error.message : 'An error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewJob = async (job: BonusBulkAssignmentJob) => {
    try {
      const request = new BonusBulkAssignmentJobGetRequest({ id: job.id });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setJobDetails(response.data);
        setIsJobDetailsOpen(true);
        setJobDetailsError(null);
      } else {
        setJobDetailsError(response.error || 'Failed to load job details');
        setIsJobDetailsOpen(true);
      }
    } catch (error) {
      console.error('Error loading job details:', error);
      setJobDetailsError('Failed to load job details');
      setIsJobDetailsOpen(true);
    }
  };

  const handleCloseJobDetails = () => {
    setIsJobDetailsOpen(false);
    setJobDetails(null);
    setJobDetailsError(null);
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    (window as any).confirmHandler = null;
  };

  const handleConfirmationConfirm = () => {
    if ((window as any).confirmHandler) {
      (window as any).confirmHandler();
    }
  };

  const handleCancelJob = async (job: BonusBulkAssignmentJob) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Cancel Bulk Assignment Job',
      message: `Are you sure you want to cancel job #${job.id}? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Cancel Job',
    });

    const handleConfirm = async () => {
      setIsConfirmationLoading(true);
      try {
        const request = new BonusBulkAssignmentJobCancelRequest({ id: job.id });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          loadJobs();
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
        } else {
          console.error('Failed to cancel job:', response.error);
        }
      } catch (error) {
        console.error('Error canceling job:', error);
      } finally {
        setIsConfirmationLoading(false);
      }
    };

    (window as any).confirmHandler = handleConfirm;
  };

  const handleRetryJob = async (job: BonusBulkAssignmentJob) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Retry Bulk Assignment Job',
      message: `Are you sure you want to retry job #${job.id}?`,
      variant: 'primary',
      confirmText: 'Retry Job',
    });

    const handleConfirm = async () => {
      setIsConfirmationLoading(true);
      try {
        const request = new BonusBulkAssignmentJobRetryRequest({ id: job.id });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          loadJobs();
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
        } else {
          console.error('Failed to retry job:', response.error);
        }
      } catch (error) {
        console.error('Error retrying job:', error);
      } finally {
        setIsConfirmationLoading(false);
      }
    };

    (window as any).confirmHandler = handleConfirm;
  };

  // Table configuration
  const columns = createCashBonusBulkAssignmentColumns({
    onView: handleViewJob,
    onCancel: handleCancelJob,
    onRetry: handleRetryJob,
  });

  const filterConfig = createCashBonusBulkAssignmentFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No bulk assignment jobs found',
    description: 'Get started by creating your first bulk assignment job.',
    action: (
      <Button
        onClick={handleCreateFormOpen}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Bulk Assignment
      </Button>
    ),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-300">
            Cash Bonus Bulk Assignment
          </h1>
          <p className="text-gray-400">
            Manage bulk assignment jobs for cash bonuses
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {isPolling && (
            <div className="flex items-center text-sm text-blue-400">
              <div className="animate-pulse w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Auto-refreshing...
            </div>
          )}
          <Button
            onClick={handleCreateFormOpen}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create Bulk Assignment
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={jobs}
        columns={columns}
        total={total}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        error={error}
        sortState={sortState}
        onPageChange={setCurrentPage}
        onItemsPerPageChange={setItemsPerPage}
        onSortChange={setSortState}
        filters={filterConfig}
        onFiltersChange={setFilters}
        emptyState={emptyState}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />

      {/* Create Form Modal */}
      {isCreateFormOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-dark-800 rounded-lg border border-dark-600 p-6 w-full max-w-2xl mx-4">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-300">
                Create Bulk Assignment Job
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCreateFormClose}
                className="p-2"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-4">
              {/* Bonus Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Bonus *
                </label>
                {isLoadingBonuses ? (
                  <div className="text-sm text-gray-400">
                    Loading bonuses...
                  </div>
                ) : (
                  <select
                    value={formData.bonusId || ''}
                    onChange={(e) =>
                      handleInputChange(
                        'bonusId',
                        e.target.value ? parseInt(e.target.value) : null
                      )
                    }
                    className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.bonusId ? 'border-red-500' : 'border-dark-600'
                    }`}
                  >
                    <option value="">Select a bonus...</option>
                    {bonuses.map((bonus) => (
                      <option key={bonus.id} value={bonus.id}>
                        {bonus.bonus.name} - ₺{bonus.cashAmount}
                      </option>
                    ))}
                  </select>
                )}
                {formErrors.bonusId && (
                  <p className="mt-1 text-sm text-red-400">
                    {formErrors.bonusId}
                  </p>
                )}
              </div>

              {/* Customer IDs */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Customer IDs *
                </label>
                <textarea
                  value={formData.customerIds}
                  onChange={(e) =>
                    handleInputChange('customerIds', e.target.value)
                  }
                  placeholder="Enter customer IDs separated by commas or new lines..."
                  rows={6}
                  className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
                    formErrors.customerIds
                      ? 'border-red-500'
                      : 'border-dark-600'
                  }`}
                />
                {formErrors.customerIds && (
                  <p className="mt-1 text-sm text-red-400">
                    {formErrors.customerIds}
                  </p>
                )}
                <p className="mt-1 text-xs text-gray-400">
                  Enter customer IDs separated by commas or new lines. Example:
                  123, 456, 789
                </p>
              </div>

              {/* Submit Error */}
              {formErrors.submit && (
                <div className="p-3 bg-red-900/20 border border-red-700 rounded-md">
                  <p className="text-sm text-red-400">{formErrors.submit}</p>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="mt-6 flex justify-end space-x-3">
              <Button variant="outline" onClick={handleCreateFormClose}>
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleFormSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating...' : 'Create Job'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Job Details Modal */}
      {isJobDetailsOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-dark-800 rounded-lg border border-dark-600 p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-300">
                Bulk Assignment Job Details
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseJobDetails}
                className="p-2"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {jobDetailsError ? (
              <div className="p-4 bg-red-900/20 border border-red-700 rounded-md">
                <p className="text-red-400">{jobDetailsError}</p>
              </div>
            ) : jobDetails ? (
              <div className="space-y-6">
                {/* Job Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-400 mb-3">
                      Job Information
                    </h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Job ID:</span>
                        <span className="text-gray-300">#{jobDetails.id}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Status:</span>
                        <span className="text-gray-300">
                          {jobDetails.status}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Bonus ID:</span>
                        <span className="text-gray-300">
                          #{jobDetails.bonusId}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total Targets:</span>
                        <span className="text-gray-300">
                          {jobDetails.targets.length}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-400 mb-3">
                      Progress Statistics
                    </h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Completed:</span>
                        <span className="text-green-300">
                          {
                            jobDetails.targets.filter(
                              (t) => t.status === 'completed'
                            ).length
                          }
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Failed:</span>
                        <span className="text-red-300">
                          {
                            jobDetails.targets.filter(
                              (t) => t.status === 'failed'
                            ).length
                          }
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Processing:</span>
                        <span className="text-blue-300">
                          {
                            jobDetails.targets.filter(
                              (t) => t.status === 'processing'
                            ).length
                          }
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Pending:</span>
                        <span className="text-yellow-300">
                          {
                            jobDetails.targets.filter(
                              (t) => t.status === 'pending'
                            ).length
                          }
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Bonus Information */}
                {jobDetails.bonus && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-400 mb-3">
                      Associated Bonus
                    </h3>
                    <div className="p-4 bg-dark-700 border border-dark-600 rounded-lg">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-400">Name:</span>
                          <span className="ml-2 text-gray-300">
                            {jobDetails.bonus.name}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-400">Type:</span>
                          <span className="ml-2 text-gray-300">
                            {jobDetails.bonus.type}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-400">Active:</span>
                          <span className="ml-2 text-gray-300">
                            {jobDetails.bonus.isActive ? 'Yes' : 'No'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-400">Values Version:</span>
                          <span className="ml-2 text-gray-300">
                            v{jobDetails.bonusValuesVersion}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Timestamps */}
                <div>
                  <h3 className="text-sm font-medium text-gray-400 mb-3">
                    Timestamps
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Created:</span>
                      <span className="ml-2 text-gray-300">
                        {new Intl.DateTimeFormat('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                        }).format(jobDetails.createdAt)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-400">Updated:</span>
                      <span className="ml-2 text-gray-300">
                        {new Intl.DateTimeFormat('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                        }).format(jobDetails.updatedAt)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="text-gray-400 mt-2">Loading job details...</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
