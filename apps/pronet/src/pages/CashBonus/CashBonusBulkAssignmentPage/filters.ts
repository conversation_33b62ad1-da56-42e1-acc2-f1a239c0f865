import { FilterField } from '@panels/ui';

export const createCashBonusBulkAssignmentFilters = (): FilterField[] => [
  {
    key: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { label: 'All', value: '' },
      { label: 'Pending', value: 'pending' },
      { label: 'Processing', value: 'processing' },
      { label: 'Completed', value: 'completed' },
      { label: 'Failed', value: 'failed' },
      { label: 'Cancelled', value: 'cancelled' },
    ],
  },
  {
    key: 'bonusId',
    label: 'Bonus ID',
    type: 'text',
    placeholder: 'Search by bonus ID...',
  },
];
