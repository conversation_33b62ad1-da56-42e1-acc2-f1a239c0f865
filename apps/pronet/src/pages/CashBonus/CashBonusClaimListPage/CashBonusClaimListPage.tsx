import { useState, useMemo } from 'react';
import { Eye } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useBonusClaims } from '../../../hooks/useBonusClaims';
import { CashBonusClaimDetailsModal } from '../../../components/modals/CashBonusClaimDetailsModal';
import { type BonusClaim } from '@panels/api';
import { createCashBonusClaimColumns } from './columns';
import { createCashBonusClaimFilters } from './filters';

export const CashBonusClaimListPage = () => {
  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: 'createdAt',
    direction: 'desc',
  });
  const [filters, setFilters] = useState({});

  // Data fetching
  const fetchFilters = useMemo(
    () => ({ ...filters, bonusType: 'cash' }),
    [filters]
  );

  const {
    data: claims,
    total,
    isLoading,
  } = useBonusClaims({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Modal state
  const [selectedClaim, setSelectedClaim] = useState<BonusClaim | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Action handlers
  const handleViewClaim = (claim: BonusClaim) => {
    setSelectedClaim(claim);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedClaim(null);
  };

  // Table configuration
  const columns = useMemo(
    () => createCashBonusClaimColumns({ onView: handleViewClaim }),
    []
  );

  const filterConfig = useMemo(() => createCashBonusClaimFilters(), []);

  // Empty state configuration
  const emptyState = {
    icon: <Eye className="h-12 w-12 text-gray-400" />,
    title: 'No cash bonus claims found',
    description: 'Claims will appear here when customers claim cash bonuses.',
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-300">
            Cash Bonus Claims
          </h1>
          <p className="text-gray-400">
            View and manage cash bonus claims from customers
          </p>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={claims}
        columns={columns}
        total={total}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        onPageChange={setCurrentPage}
        onItemsPerPageChange={setItemsPerPage}
        onSort={(field) => {
          setSortState((prev) => ({
            field,
            direction:
              prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
          }));
        }}
        filterFields={filterConfig}
        filters={filters}
        onFilterChange={setFilters}
        emptyState={emptyState}
      />

      {/* Claim Details Modal */}
      <CashBonusClaimDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        claim={selectedClaim}
      />
    </div>
  );
};
