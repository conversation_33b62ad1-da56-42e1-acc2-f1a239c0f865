import { Eye, User } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusClaim } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

interface CashBonusClaimColumnsProps {
  onView: (claim: BonusClaim) => void;
}

export const createCashBonusClaimColumns = ({
  onView,
}: CashBonusClaimColumnsProps): TableColumn<BonusClaim>[] => [
  {
    key: 'id',
    label: 'Claim ID',
    width: '100px',
    sortable: true,
    render: (claim) => (
      <div className="font-mono text-gray-300">#{claim.id}</div>
    ),
  },
  {
    key: 'customer',
    label: 'Customer',
    width: '200px',
    sortable: true,
    render: (claim) => (
      <div className="flex items-center gap-2">
        <User className="h-4 w-4 text-gray-400" />
        <div>
          <div className="font-medium text-gray-300">
            {claim.customer?.username || `Customer #${claim.customerId}`}
          </div>
          <div className="text-xs text-gray-400">
            External ID: {claim.customer?.externalId || 'N/A'}
          </div>
        </div>
      </div>
    ),
  },
  {
    key: 'bonus',
    label: 'Bonus',
    width: '200px',
    sortable: false,
    render: (claim) => (
      <div>
        {claim.bonus ? (
          <>
            <div className="font-medium text-gray-300">{claim.bonus.name}</div>
            <div className="text-sm text-gray-400">ID: {claim.bonus.id}</div>
          </>
        ) : (
          <div className="text-gray-400">No bonus data</div>
        )}
      </div>
    ),
  },
  {
    key: 'source',
    label: 'Source',
    width: '120px',
    sortable: true,
    render: (claim) => (
      <div className="text-gray-300 capitalize">{claim.source}</div>
    ),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '140px',
    sortable: true,
    render: (claim) => (
      <div className="text-sm text-gray-300">{formatDate(claim.createdAt)}</div>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '80px',
    sortable: false,
    render: (claim) => (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onView(claim)}
        className="p-1 text-blue-400 hover:text-blue-300"
        title="View Details"
      >
        <Eye className="h-4 w-4" />
      </Button>
    ),
  },
];
