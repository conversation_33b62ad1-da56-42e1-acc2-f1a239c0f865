import { FilterField } from '@panels/ui';

export const createCashBonusClaimFilters = (): FilterField[] => [
  {
    key: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { label: 'All', value: '' },
      { label: 'Pending', value: 'pending' },
      { label: 'Approved', value: 'approved' },
      { label: 'Completed', value: 'completed' },
      { label: 'Rejected', value: 'rejected' },
      { label: 'Failed', value: 'failed' },
      { label: 'Processing', value: 'processing' },
    ],
  },
  {
    key: 'externalCustomerId',
    label: 'Customer ID',
    type: 'text',
    placeholder: 'Search by customer ID...',
  },
  {
    key: 'search',
    label: 'Search',
    type: 'text',
    placeholder: 'Search by bonus name...',
  },
];
