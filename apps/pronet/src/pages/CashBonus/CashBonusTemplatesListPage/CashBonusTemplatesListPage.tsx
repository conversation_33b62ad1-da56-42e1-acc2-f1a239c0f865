import { useState, useMemo, useCallback } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useCashBonusTemplates } from '../../../hooks/useCashBonusTemplates';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { CashBonusTemplateCreateModal } from '../../../components/modals/CashBonusTemplateCreateModal';
import { useApi } from '../../../features/api/useApi';
import {
  BonusTemplateSoftDeleteRequest,
  type CashBonusTemplate,
  type BonusTemplateSoftDeleteRequestOptions,
} from '@panels/api';
import { createCashBonusTemplateColumns } from './columns';
import { createCashBonusTemplateFilters } from './filters';

export const CashBonusTemplatesListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: 'createdAt',
    direction: 'desc',
  });
  const [filters, setFilters] = useState({});

  // Data fetching
  const fetchFilters = useMemo(
    () => ({ ...filters, isActive: true }),
    [filters]
  );

  const {
    data: templates,
    total,
    isLoading,
    refetch,
  } = useCashBonusTemplates({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    variant: 'danger' as const,
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Action handlers
  const handleCreateTemplate = () => {
    setIsCreateModalOpen(true);
  };

  const handleCreateSuccess = () => {
    refetch(); // Refresh the list
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleDeleteTemplate = async (template: CashBonusTemplate) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Cash Bonus Template',
      message: `Are you sure you want to delete "${template.bonusTemplate.name}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
    });

    const handleConfirm = async () => {
      setIsConfirmationLoading(true);
      try {
        const requestOptions: BonusTemplateSoftDeleteRequestOptions = {
          id: template.bonusTemplate.id,
        };
        const request = new BonusTemplateSoftDeleteRequest(requestOptions);
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch();
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
        } else {
          console.error('Failed to delete template:', response.error);
        }
      } catch (error) {
        console.error('Error deleting template:', error);
      } finally {
        setIsConfirmationLoading(false);
      }
    };

    // Store the confirm handler for the dialog
    (window as any).confirmHandler = handleConfirm;
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    (window as any).confirmHandler = null;
  };

  const handleConfirmationConfirm = () => {
    if ((window as any).confirmHandler) {
      (window as any).confirmHandler();
    }
  };

  // Sort handler
  const handleSort = useCallback((field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  }, []);

  // Table configuration
  const columns = useMemo(
    () =>
      createCashBonusTemplateColumns({
        onDelete: handleDeleteTemplate,
        // TODO: Add handlers for view, edit, and copy when implemented
        // onView: handleViewTemplate,
        // onEdit: handleEditTemplate,
        // onCopy: handleCopyTemplate,
      }),
    []
  );

  const filterGroups = useMemo(
    () => [
      {
        name: 'general',
        displayName: 'General',
        fields: createCashBonusTemplateFilters(),
      },
    ],
    []
  );

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No cash bonus templates found',
    description: 'Get started by creating your first cash bonus template.',
    action: (
      <Button
        onClick={handleCreateTemplate}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Template
      </Button>
    ),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-300">
            Cash Bonus Templates
          </h1>
          <p className="text-gray-400">Manage reusable cash bonus templates</p>
        </div>
        <Button
          onClick={handleCreateTemplate}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Template
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={templates}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          sortState={sortState}
        />
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />

      {/* Create Cash Bonus Template Modal */}
      <CashBonusTemplateCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
};
