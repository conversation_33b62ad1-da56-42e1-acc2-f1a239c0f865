import { Eye, Edit, Trash2, Copy } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type CashBonusTemplate } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format template status
const formatTemplateStatus = (template: CashBonusTemplate) => {
  const isActive = template.bonusTemplate.isActive;
  const isDeleted = template.bonusTemplate.deletedAt !== null;

  let status: string;
  let colorClass: string;

  if (isDeleted) {
    status = 'deleted';
    colorClass = 'bg-red-900 text-red-300 border-red-700';
  } else if (isActive) {
    status = 'active';
    colorClass = 'bg-green-900 text-green-300 border-green-700';
  } else {
    status = 'inactive';
    colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
  }

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

interface CashBonusTemplateColumnsProps {
  onView?: (template: CashBonusTemplate) => void;
  onEdit?: (template: CashBonusTemplate) => void;
  onCopy?: (template: CashBonusTemplate) => void;
  onDelete: (template: CashBonusTemplate) => void;
}

export const createCashBonusTemplateColumns = ({
  onView,
  onEdit,
  onCopy,
  onDelete,
}: CashBonusTemplateColumnsProps): TableColumn<CashBonusTemplate>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (template) => (
      <span className="font-mono text-sm text-gray-300">#{template.id}</span>
    ),
  },
  {
    key: 'name',
    label: 'Name',
    width: '200px',
    sortable: true,
    render: (template) => (
      <div>
        <div className="font-medium text-gray-300">
          {template.bonusTemplate.name}
        </div>
        <div className="text-sm text-gray-400">ID: {template.id}</div>
      </div>
    ),
  },
  {
    key: 'cashAmount',
    label: 'Cash Amount',
    width: '120px',
    sortable: true,
    render: (template) => (
      <div className="font-mono text-gray-300">₺{template.cashAmount}</div>
    ),
  },
  {
    key: 'rules',
    label: 'Rules',
    width: '80px',
    sortable: false,
    render: (template) => (
      <div className="text-sm text-gray-400">
        {template.bonusTemplate.rules.length} rule
        {template.bonusTemplate.rules.length !== 1 ? 's' : ''}
      </div>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '100px',
    sortable: true,
    render: (template) => formatTemplateStatus(template),
  },
  {
    key: 'expiresAt',
    label: 'Expires',
    width: '120px',
    sortable: true,
    render: (template) => {
      const expiresAt = template.bonusTemplate.expiresAt;
      if (!expiresAt) {
        return <span className="text-gray-400">Never</span>;
      }
      return (
        <div className="text-sm text-gray-300">
          {new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          }).format(new Date(expiresAt))}
        </div>
      );
    },
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '140px',
    sortable: true,
    render: (template) => (
      <div className="text-sm text-gray-300">
        {formatDate(template.createdAt)}
      </div>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '120px',
    sortable: false,
    render: (template) => {
      const isDeleted = template.bonusTemplate.deletedAt !== null;

      return (
        <div className="flex items-center gap-1">
          {onView && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onView(template)}
              className="p-1 text-blue-400 hover:text-blue-300"
              title="View Details"
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}

          {!isDeleted && onEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(template)}
              className="p-1 text-green-400 hover:text-green-300"
              title="Edit Template"
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}

          {!isDeleted && onCopy && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onCopy(template)}
              className="p-1 text-purple-400 hover:text-purple-300"
              title="Create Bonus from Template"
            >
              <Copy className="h-4 w-4" />
            </Button>
          )}

          {!isDeleted && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(template)}
              className="p-1 text-red-400 hover:text-red-300"
              title="Delete Template"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      );
    },
  },
];
