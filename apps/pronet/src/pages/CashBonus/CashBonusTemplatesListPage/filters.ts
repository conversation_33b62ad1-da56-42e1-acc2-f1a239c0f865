import { FilterField } from '@panels/ui';

export const createCashBonusTemplateFilters = (): FilterField[] => [
  {
    key: 'isActive',
    label: 'Status',
    type: 'select',
    options: [
      { label: 'All', value: '' },
      { label: 'Active', value: 'true' },
      { label: 'Inactive', value: 'false' },
    ],
  },
  {
    key: 'search',
    label: 'Search',
    type: 'text',
    placeholder: 'Search by name...',
  },
];
