import { FilterField } from '@panels/ui';

export const createCashBonusTemplateFilters = (): FilterField[] => [
  {
    key: 'search',
    type: 'text',
    label: 'Search',
    placeholder: 'Search by name or ID...',
  },
  {
    key: 'isActive',
    type: 'select',
    label: 'Status',
    placeholder: 'All statuses',
    options: [
      { value: 'true', label: 'Active' },
      { value: 'false', label: 'Inactive' },
    ],
  },
  {
    key: 'minAmount',
    type: 'number',
    label: 'Min Amount',
    placeholder: 'Minimum cash amount',
  },
  {
    key: 'maxAmount',
    type: 'number',
    label: 'Max Amount',
    placeholder: 'Maximum cash amount',
  },
];
