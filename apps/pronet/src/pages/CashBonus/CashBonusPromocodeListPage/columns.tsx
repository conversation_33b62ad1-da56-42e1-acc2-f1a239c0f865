import { Eye, Edit, Trash2, Toggle<PERSON>eft, ToggleRight, Copy } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusPromocode } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format promocode status
const formatPromocodeStatus = (promocode: BonusPromocode) => {
  const isActive = promocode.isActive;
  const isDeleted = promocode.deletedAt !== null;

  let status: string;
  let colorClass: string;

  if (isDeleted) {
    status = 'deleted';
    colorClass = 'bg-red-900 text-red-300 border-red-700';
  } else if (isActive) {
    status = 'active';
    colorClass = 'bg-green-900 text-green-300 border-green-700';
  } else {
    status = 'inactive';
    colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
  }

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

interface CashBonusPromocodeColumnsProps {
  onView: (promocode: BonusPromocode) => void;
  onToggle: (promocode: BonusPromocode) => void;
  onDelete: (promocode: BonusPromocode) => void;
}

export const createCashBonusPromocodeColumns = ({
  onView,
  onToggle,
  onDelete,
}: CashBonusPromocodeColumnsProps): TableColumn<BonusPromocode>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (promocode) => (
      <span className="font-mono text-sm text-gray-300">#{promocode.id}</span>
    ),
  },
  {
    key: 'code',
    label: 'Promocode',
    width: '150px',
    sortable: true,
    render: (promocode) => (
      <div>
        <div className="font-medium text-gray-300 font-mono">
          {promocode.code}
        </div>
        <div className="text-sm text-gray-400">ID: {promocode.id}</div>
      </div>
    ),
  },
  {
    key: 'bonus',
    label: 'Bonus',
    width: '200px',
    sortable: false,
    render: (promocode) => {
      const bonus = promocode.bonus;
      if (!bonus) {
        return <span className="text-gray-400">No bonus data</span>;
      }
      return (
        <div>
          <div className="font-medium text-gray-300">{bonus.name}</div>
          <div className="text-sm text-gray-400">ID: {bonus.id}</div>
        </div>
      );
    },
  },
  {
    key: 'usage',
    label: 'Usage',
    width: '120px',
    sortable: false,
    render: (promocode) => {
      const activations = promocode.activations;
      const maxActivations = promocode.maxActivations;

      return (
        <div>
          <div className="text-gray-300">
            {activations.toLocaleString()}
            {maxActivations && ` / ${maxActivations.toLocaleString()}`}
          </div>
          {maxActivations && (
            <div className="text-xs text-gray-400">
              {Math.round((activations / maxActivations) * 100)}% used
            </div>
          )}
        </div>
      );
    },
  },
  {
    key: 'status',
    label: 'Status',
    width: '100px',
    sortable: true,
    render: (promocode) => formatPromocodeStatus(promocode),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '140px',
    sortable: true,
    render: (promocode) => (
      <div className="text-sm text-gray-300">
        {formatDate(promocode.createdAt)}
      </div>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '140px',
    sortable: false,
    render: (promocode) => {
      const isDeleted = promocode.deletedAt !== null;

      return (
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onView(promocode)}
            className="p-1 text-blue-400 hover:text-blue-300"
            title="View Details"
          >
            <Eye className="h-4 w-4" />
          </Button>

          {!isDeleted && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigator.clipboard.writeText(promocode.code)}
                className="p-1 text-purple-400 hover:text-purple-300"
                title="Copy Code"
              >
                <Copy className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onToggle(promocode)}
                className={`p-1 ${
                  promocode.isActive
                    ? 'text-yellow-400 hover:text-yellow-300'
                    : 'text-green-400 hover:text-green-300'
                }`}
                title={promocode.isActive ? 'Deactivate' : 'Activate'}
              >
                {promocode.isActive ? (
                  <ToggleLeft className="h-4 w-4" />
                ) : (
                  <ToggleRight className="h-4 w-4" />
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(promocode)}
                className="p-1 text-red-400 hover:text-red-300"
                title="Delete Promocode"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      );
    },
  },
];
