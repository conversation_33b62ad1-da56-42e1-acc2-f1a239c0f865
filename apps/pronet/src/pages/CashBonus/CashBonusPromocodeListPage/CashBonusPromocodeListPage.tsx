import { useState, useMemo } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useBonusPromocodes } from '../../../hooks/useBonusPromocodes';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { CashBonusPromocodeCreateModal } from '../../../components/modals/CashBonusPromocodeCreateModal';
import { CashBonusPromocodeDetailsModal } from '../../../components/modals/CashBonusPromocodeDetailsModal';
import { useApi } from '../../../features/api/useApi';
import {
  BonusPromocodeToggleRequest,
  BonusPromocodeSoftDeleteRequest,
  type BonusPromocode,
  type BonusPromocodeToggleRequestOptions,
  type BonusPromocodeSoftDeleteRequestOptions,
} from '@panels/api';
import { createCashBonusPromocodeColumns } from './columns';
import { createCashBonusPromocodeFilters } from './filters';

export const CashBonusPromocodeListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: 'createdAt',
    direction: 'desc',
  });
  const [filters, setFilters] = useState({});

  // Data fetching
  const fetchFilters = useMemo(
    () => ({ ...filters, bonusType: 'cash' }),
    [filters]
  );

  const {
    data: promocodes,
    total,
    isLoading,
    refetch,
  } = useBonusPromocodes({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedPromocode, setSelectedPromocode] =
    useState<BonusPromocode | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    variant: 'danger' as const,
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Action handlers
  const handleCreatePromocode = () => {
    setIsCreateModalOpen(true);
  };

  const handleCreateSuccess = () => {
    refetch(); // Refresh the list
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleViewPromocode = (promocode: BonusPromocode) => {
    setSelectedPromocode(promocode);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPromocode(null);
  };

  const handleTogglePromocode = async (promocode: BonusPromocode) => {
    const action = promocode.isActive ? 'deactivate' : 'activate';
    setConfirmationDialog({
      isOpen: true,
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Promocode`,
      message: `Are you sure you want to ${action} promocode "${promocode.code}"?`,
      variant: 'danger' as const,
      confirmText: action.charAt(0).toUpperCase() + action.slice(1),
    });

    const handleConfirm = async () => {
      setIsConfirmationLoading(true);
      try {
        const requestOptions: BonusPromocodeToggleRequestOptions = {
          id: promocode.id,
          isActive: !promocode.isActive,
        };
        const request = new BonusPromocodeToggleRequest(requestOptions);
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch();
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
        } else {
          console.error('Failed to toggle promocode:', response.error);
        }
      } catch (error) {
        console.error('Error toggling promocode:', error);
      } finally {
        setIsConfirmationLoading(false);
      }
    };

    // Store the confirm handler for the dialog
    (window as any).confirmHandler = handleConfirm;
  };

  const handleDeletePromocode = async (promocode: BonusPromocode) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Promocode',
      message: `Are you sure you want to delete promocode "${promocode.code}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
    });

    const handleConfirm = async () => {
      setIsConfirmationLoading(true);
      try {
        const requestOptions: BonusPromocodeSoftDeleteRequestOptions = {
          id: promocode.id,
        };
        const request = new BonusPromocodeSoftDeleteRequest(requestOptions);
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch();
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
        } else {
          console.error('Failed to delete promocode:', response.error);
        }
      } catch (error) {
        console.error('Error deleting promocode:', error);
      } finally {
        setIsConfirmationLoading(false);
      }
    };

    // Store the confirm handler for the dialog
    (window as any).confirmHandler = handleConfirm;
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    (window as any).confirmHandler = null;
  };

  const handleConfirmationConfirm = () => {
    if ((window as any).confirmHandler) {
      (window as any).confirmHandler();
    }
  };

  // Table configuration
  const columns = useMemo(
    () =>
      createCashBonusPromocodeColumns({
        onView: handleViewPromocode,
        onToggle: handleTogglePromocode,
        onDelete: handleDeletePromocode,
      }),
    []
  );

  const filterConfig = useMemo(() => createCashBonusPromocodeFilters(), []);

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No cash bonus promocodes found',
    description: 'Get started by creating your first cash bonus promocode.',
    action: (
      <Button
        onClick={handleCreatePromocode}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Promocode
      </Button>
    ),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-300">
            Cash Bonus Promocodes
          </h1>
          <p className="text-gray-400">
            Manage cash bonus promocodes and activation codes
          </p>
        </div>
        <Button
          onClick={handleCreatePromocode}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Promocode
        </Button>
      </div>

      {/* Data Table */}
      <DataTable
        data={promocodes}
        columns={columns}
        total={total}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        isLoading={isLoading}
        sortState={sortState}
        onPageChange={setCurrentPage}
        onItemsPerPageChange={setItemsPerPage}
        onSort={(field) => {
          setSortState((prev) => ({
            field,
            direction:
              prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
          }));
        }}
        filterFields={filterConfig}
        filters={filters}
        onFilterChange={setFilters}
        emptyState={emptyState}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />

      {/* Create Promocode Modal */}
      <CashBonusPromocodeCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {/* Promocode Details Modal */}
      <CashBonusPromocodeDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        promocode={selectedPromocode}
      />
    </div>
  );
};
