import { FilterField } from '@panels/ui';

export const createCashBonusPromocodeFilters = (): FilterField[] => [
  {
    key: 'search',
    type: 'text',
    label: 'Search',
    placeholder: 'Search by code or bonus name...',
  },
  {
    key: 'isActive',
    type: 'select',
    label: 'Status',
    placeholder: 'All statuses',
    options: [
      { value: 'true', label: 'Active' },
      { value: 'false', label: 'Inactive' },
    ],
  },
  {
    key: 'bonusId',
    type: 'number',
    label: 'Bonus ID',
    placeholder: 'Filter by bonus ID',
  },
  {
    key: 'minActivations',
    type: 'number',
    label: 'Min Activations',
    placeholder: 'Minimum activations',
  },
  {
    key: 'maxActivations',
    type: 'number',
    label: 'Max Activations',
    placeholder: 'Maximum activations',
  },
];
