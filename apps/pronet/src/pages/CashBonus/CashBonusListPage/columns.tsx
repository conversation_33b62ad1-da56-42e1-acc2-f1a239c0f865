import { Eye, Pause, Play, Trash2 } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type CashBonus } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format bonus status
const formatBonusStatus = (bonus: CashBonus) => {
  const isActive = bonus.bonus.isActive;
  const isDeleted = bonus.bonus.deletedAt !== null;

  let status: string;
  let colorClass: string;

  if (isDeleted) {
    status = 'deleted';
    colorClass = 'bg-red-900 text-red-300 border-red-700';
  } else if (isActive) {
    status = 'active';
    colorClass = 'bg-green-900 text-green-300 border-green-700';
  } else {
    status = 'inactive';
    colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
  }

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

interface CashBonusColumnsProps {
  onView: (bonus: CashBonus) => void;
  onToggle: (bonus: CashBonus) => void;
  onDelete: (bonus: CashBonus) => void;
}

export const createCashBonusColumns = ({
  onView,
  onToggle,
  onDelete,
}: CashBonusColumnsProps): TableColumn<CashBonus>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (bonus) => (
      <span className="font-mono text-sm text-gray-300">#{bonus.id}</span>
    ),
  },
  {
    key: 'name',
    label: 'Name',
    width: '200px',
    sortable: true,
    render: (bonus) => (
      <div>
        <div className="font-medium text-gray-300">{bonus.bonus.name}</div>
        <div className="text-sm text-gray-400">ID: {bonus.id}</div>
      </div>
    ),
  },
  {
    key: 'cashAmount',
    label: 'Cash Amount',
    width: '120px',
    sortable: true,
    render: (bonus) => (
      <div className="font-mono text-gray-300">₺{bonus.cashAmount}</div>
    ),
  },
  {
    key: 'rules',
    label: 'Rules',
    width: '80px',
    sortable: false,
    render: (bonus) => (
      <div className="text-sm text-gray-400">
        {bonus.bonus.rules.length} rule
        {bonus.bonus.rules.length !== 1 ? 's' : ''}
      </div>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '100px',
    sortable: true,
    render: (bonus) => formatBonusStatus(bonus),
  },
  {
    key: 'expiresAt',
    label: 'Expires',
    width: '120px',
    sortable: true,
    render: (bonus) => {
      const expiresAt = bonus.bonus.expiresAt;
      if (!expiresAt) {
        return <span className="text-gray-400">Never</span>;
      }
      return (
        <div className="text-sm text-gray-300">
          {new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          }).format(new Date(expiresAt))}
        </div>
      );
    },
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '140px',
    sortable: true,
    render: (bonus) => (
      <div className="text-sm text-gray-300">{formatDate(bonus.createdAt)}</div>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '120px',
    sortable: false,
    render: (bonus) => {
      const isDeleted = bonus.bonus.deletedAt !== null;

      return (
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onView(bonus)}
            className="p-1 text-blue-400 hover:text-blue-300"
            title="View Details"
          >
            <Eye className="h-4 w-4" />
          </Button>

          {!isDeleted && (
            <>
              {bonus.bonus.isActive ? (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggle(bonus)}
                  className="p-1 text-yellow-400 hover:text-yellow-300"
                  title="Deactivate Bonus"
                >
                  <Pause className="h-4 w-4" />
                </Button>
              ) : (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggle(bonus)}
                  className="p-1 text-green-400 hover:text-green-300"
                  title="Activate Bonus"
                >
                  <Play className="h-4 w-4" />
                </Button>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(bonus)}
                className="p-1 text-red-400 hover:text-red-300"
                title="Delete Bonus"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      );
    },
  },
];
