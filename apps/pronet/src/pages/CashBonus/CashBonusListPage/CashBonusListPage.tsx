import { useState, useMemo, useCallback } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useCashBonuses } from '../../../hooks/useCashBonuses';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { CashBonusCreateModal } from '../../../components/modals/CashBonusCreateModal';
import { useApi } from '../../../features/api/useApi';
import {
  BonusSoftDeleteRequest,
  BonusToggleRequest,
  type CashBonus,
  type BonusSoftDeleteRequestOptions,
  type BonusToggleRequestOptions,
} from '@panels/api';
import { createCashBonusColumns } from './columns';
import { createCashBonusFilters } from './filters';

export const CashBonusListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: 'createdAt',
    direction: 'desc',
  });
  const [filters, setFilters] = useState({});

  // Data fetching
  const fetchFilters = useMemo(() => ({ ...filters }), [filters]);

  const {
    data: bonuses,
    total,
    isLoading,
    refetch,
  } = useCashBonuses({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field || undefined,
    sortDirection: sortState.direction,
  });

  // Modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    variant: 'danger' as const,
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Action handlers
  const handleCreateBonus = () => {
    setIsCreateModalOpen(true);
  };

  const handleCreateSuccess = () => {
    refetch(); // Refresh the list
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleToggleBonus = async (bonus: CashBonus) => {
    const action = bonus.bonus.isActive ? 'deactivate' : 'activate';
    setConfirmationDialog({
      isOpen: true,
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Cash Bonus`,
      message: `Are you sure you want to ${action} "${bonus.bonus.name}"?`,
      variant: 'danger' as const,
      confirmText: action.charAt(0).toUpperCase() + action.slice(1),
    });

    const handleConfirm = async () => {
      setIsConfirmationLoading(true);
      try {
        const requestOptions: BonusToggleRequestOptions = {
          id: bonus.bonus.id,
          isActive: !bonus.bonus.isActive,
        };
        const request = new BonusToggleRequest(requestOptions);
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch();
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
        } else {
          console.error('Failed to toggle bonus:', response.error);
        }
      } catch (error) {
        console.error('Error toggling bonus:', error);
      } finally {
        setIsConfirmationLoading(false);
      }
    };

    // Store the confirm handler for the dialog
    (window as any).confirmHandler = handleConfirm;
  };

  const handleViewBonus = (bonus: CashBonus) => {
    // TODO: Implement view bonus details modal
    console.log('View bonus:', bonus);
  };

  const handleDeleteBonus = async (bonus: CashBonus) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Cash Bonus',
      message: `Are you sure you want to delete "${bonus.bonus.name}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
    });

    const handleConfirm = async () => {
      setIsConfirmationLoading(true);
      try {
        const requestOptions: BonusSoftDeleteRequestOptions = {
          id: bonus.bonus.id,
        };
        const request = new BonusSoftDeleteRequest(requestOptions);
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch();
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
        } else {
          console.error('Failed to delete bonus:', response.error);
        }
      } catch (error) {
        console.error('Error deleting bonus:', error);
      } finally {
        setIsConfirmationLoading(false);
      }
    };

    // Store the confirm handler for the dialog
    (window as any).confirmHandler = handleConfirm;
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    (window as any).confirmHandler = null;
  };

  const handleConfirmationConfirm = () => {
    if ((window as any).confirmHandler) {
      (window as any).confirmHandler();
    }
  };

  // Sort handler
  const handleSort = useCallback((field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  }, []);

  // Table configuration
  const columns = useMemo(
    () =>
      createCashBonusColumns({
        onView: handleViewBonus,
        onToggle: handleToggleBonus,
        onDelete: handleDeleteBonus,
      }),
    []
  );

  const filterGroups = useMemo(
    () => [
      {
        name: 'general',
        displayName: 'General',
        fields: createCashBonusFilters(),
      },
    ],
    []
  );

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No cash bonuses found',
    description: 'Get started by creating your first cash bonus.',
    action: (
      <Button onClick={handleCreateBonus} className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        Create Cash Bonus
      </Button>
    ),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">Cash Bonuses</h1>
          <p className="text-gray-400">
            Manage and monitor your cash bonus campaigns
          </p>
        </div>
        <Button onClick={handleCreateBonus} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Cash Bonus
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={bonuses}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          sortState={sortState}
        />
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />

      {/* Create Cash Bonus Modal */}
      <CashBonusCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
};
