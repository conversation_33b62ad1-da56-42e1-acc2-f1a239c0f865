import { useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useBonusPromocodes } from '../../../hooks/useBonusPromocodes';
import Button from '../../../components/ui/Button';
import { BonusPromocodeDetailsModal } from '../../../components/modals/BonusPromocodeDetailsModal';
import { BonusPromocodeCreateModal } from '../../../components/modals/BonusPromocodeCreateModal';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import {
  BonusPromocodeToggleRequest,
  BonusPromocodeSoftDeleteRequest,
} from '@panels/api';
import { useApi } from '../../../features/api/useApi';
import type { BonusPromocode } from '@panels/api';
import { createWeeklyLossbackBonusPromocodeColumns } from './columns';
import { createWeeklyLossbackBonusPromocodeFilters } from './filters';

export const WeeklyLossbackBonusPromocodeListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({
    bonusType: 'weekly_lossback', // Filter for weekly lossback bonus promocodes only
  });

  const fetchFilters = useMemo(
    () => ({ ...filters, isActive: true }),
    [filters]
  );

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPromocode, setSelectedPromocode] =
    useState<BonusPromocode | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    variant: 'info' | 'warning' | 'danger';
    confirmText: string;
    action: () => Promise<void>;
  }>({
    isOpen: false,
    title: '',
    message: '',
    variant: 'info',
    confirmText: '',
    action: async () => {},
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Fetch data
  const { data, total, isLoading, refetch } = useBonusPromocodes({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleCreatePromocode = () => {
    setIsCreateModalOpen(true);
  };

  const handleViewPromocode = (promocode: BonusPromocode) => {
    setSelectedPromocode(promocode);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPromocode(null);
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleCreateSuccess = () => {
    refetch(); // Refresh the list after successful creation
  };

  const handleTogglePromocode = async (promocode: BonusPromocode) => {
    const newStatus = !promocode.isActive;
    const action = newStatus ? 'activate' : 'deactivate';

    setConfirmationDialog({
      isOpen: true,
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Promocode`,
      message: `Are you sure you want to ${action} the promocode "${
        promocode.code
      }"? This will ${newStatus ? 'enable' : 'disable'} it for customer use.`,
      variant: newStatus ? 'info' : 'warning',
      confirmText: action.charAt(0).toUpperCase() + action.slice(1),
      action: async () => {
        const request = new BonusPromocodeToggleRequest({
          id: promocode.id,
          isActive: newStatus,
        });
        const response = await pronet.makeRequest(request);
        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || `Failed to ${action} promocode`);
        }
      },
    });
  };

  const handleDeletePromocode = async (promocode: BonusPromocode) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Promocode',
      message: `Are you sure you want to delete the promocode "${promocode.code}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        const request = new BonusPromocodeSoftDeleteRequest({
          id: promocode.id,
        });
        const response = await pronet.makeRequest(request);
        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to delete promocode');
        }
      },
    });
  };

  const handleConfirmAction = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with handlers
  const columns = createWeeklyLossbackBonusPromocodeColumns({
    onViewPromocode: handleViewPromocode,
    onTogglePromocode: handleTogglePromocode,
    onDeletePromocode: handleDeletePromocode,
  });

  // Create filter groups
  const filterGroups = createWeeklyLossbackBonusPromocodeFilters();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Weekly Lossback Bonus Promocodes
          </h1>
          <p className="text-gray-400 mt-1">
            Manage promocodes for weekly lossback bonuses
          </p>
        </div>
        <Button
          onClick={handleCreatePromocode}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Promocode
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data || []}
          columns={columns}
          filterGroups={filterGroups}
          total={total || 0}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          title={`Weekly Lossback Bonus Promocodes (${(
            total || 0
          ).toLocaleString()})`}
        />
      </div>

      {/* Modals */}
      {selectedPromocode && (
        <BonusPromocodeDetailsModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          promocode={selectedPromocode}
        />
      )}

      <BonusPromocodeCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
        bonusType="weekly_lossback"
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={() =>
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }))
        }
        onConfirm={handleConfirmAction}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />
    </div>
  );
};
