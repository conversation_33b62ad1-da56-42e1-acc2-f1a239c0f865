import { useMemo, useState } from 'react';
import { DataTable, SortState } from '@panels/ui';
import { useBonusClaims } from '../../../hooks/useBonusClaims';
import { BonusClaimDetailsModal } from '../../../components/modals/BonusClaimDetailsModal';
import type { BonusClaim } from '@panels/api';
import { createWeeklyLossbackBonusClaimColumns } from './columns';
import { createWeeklyLossbackBonusClaimFilters } from './filters';

export const WeeklyLossbackBonusClaimListPage = () => {
  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({
    bonusType: 'weekly_lossback', // Filter for weekly lossback bonus claims only
  });

  const fetchFilters = useMemo(
    () => ({ ...filters }),
    [filters]
  );

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedClaim, setSelectedClaim] = useState<BonusClaim | null>(null);

  // Fetch data
  const { data, total, isLoading, error, refetch } = useBonusClaims({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleViewClaim = (claim: BonusClaim) => {
    setSelectedClaim(claim);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedClaim(null);
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with handlers
  const columns = createWeeklyLossbackBonusClaimColumns({
    onViewClaim: handleViewClaim,
  });

  // Create filter groups
  const filterGroups = createWeeklyLossbackBonusClaimFilters();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">Weekly Lossback Bonus Claims</h1>
          <p className="text-gray-400 mt-1">
            Monitor customer claims for weekly lossback bonuses
          </p>
        </div>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data || []}
          columns={columns}
          filterGroups={filterGroups}
          total={total || 0}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          title={`Weekly Lossback Bonus Claims (${(total || 0).toLocaleString()})`}
        />
      </div>

      {/* Details Modal */}
      <BonusClaimDetailsModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        claim={selectedClaim}
      />
    </div>
  );
};
