import { useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useWeeklyLossbackBonusTemplates } from '../../../hooks/useWeeklyLossbackBonusTemplates';
import Button from '../../../components/ui/Button';
import { WeeklyLossbackBonusTemplateDetailsModal } from '../../../components/modals/WeeklyLossbackBonusTemplateDetailsModal';
import { WeeklyLossbackBonusTemplateCreateModal } from '../../../components/modals/WeeklyLossbackBonusTemplateCreateModal';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { BonusTemplateSoftDeleteRequest } from '@panels/api';
import { useApi } from '../../../features/api/useApi';
import type { WeeklyLossbackBonusTemplate } from '@panels/api';
import { createWeeklyLossbackBonusTemplateColumns } from './columns';
import { createWeeklyLossbackBonusTemplateFilters } from './filters';

export const WeeklyLossbackBonusTemplatesListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(
    () => ({ ...filters, isActive: true }),
    [filters]
  );

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<WeeklyLossbackBonusTemplate | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    variant: 'info' | 'warning' | 'danger';
    confirmText: string;
    action: () => Promise<void>;
  }>({
    isOpen: false,
    title: '',
    message: '',
    variant: 'info',
    confirmText: '',
    action: async () => {},
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Data fetching
  const { data, total, isLoading, refetch } = useWeeklyLossbackBonusTemplates({
    page: currentPage,
    limit: itemsPerPage,
    sortField: sortState.field || undefined,
    sortDirection: sortState.direction,
    filters: fetchFilters,
  });

  // Action handlers
  const handleCreateTemplate = () => {
    setIsCreateModalOpen(true);
  };

  const handleViewTemplate = (template: WeeklyLossbackBonusTemplate) => {
    setSelectedTemplate(template);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedTemplate(null);
  };

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    refetch();
  };

  const handleDeleteTemplate = (template: WeeklyLossbackBonusTemplate) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Template',
      message: `Are you sure you want to delete the template "${template.bonusTemplate.name}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        const request = new BonusTemplateSoftDeleteRequest({
          id: template.bonusTemplate.id,
        });
        const response = await pronet.makeRequest(request);
        if (response.success) {
          refetch();
        } else {
          throw new Error(response.error || 'Failed to delete template');
        }
      },
    });
  };

  const handleConfirmAction = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with handlers
  const columns = createWeeklyLossbackBonusTemplateColumns({
    onViewTemplate: handleViewTemplate,
    onDeleteTemplate: handleDeleteTemplate,
  });

  // Create filter groups
  const filterGroups = createWeeklyLossbackBonusTemplateFilters();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Weekly Lossback Bonus Templates
          </h1>
          <p className="text-gray-400 mt-1">
            Manage and monitor weekly lossback bonus templates
          </p>
        </div>
        <Button
          onClick={handleCreateTemplate}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Template
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data || []}
          columns={columns}
          filterGroups={filterGroups}
          total={total || 0}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          title={`Weekly Lossback Bonus Templates (${(
            total || 0
          ).toLocaleString()})`}
        />
      </div>

      {/* Modals */}
      {selectedTemplate && (
        <WeeklyLossbackBonusTemplateDetailsModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          template={selectedTemplate}
        />
      )}

      <WeeklyLossbackBonusTemplateCreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={() =>
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }))
        }
        onConfirm={handleConfirmAction}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />
    </div>
  );
};
