import { useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useWeeklyLossbackBonuses } from '../../../hooks/useWeeklyLossbackBonuses';
import Button from '../../../components/ui/Button';
import { WeeklyLossbackBonusDetailsModal } from '../../../components/modals/WeeklyLossbackBonusDetailsModal';
import { WeeklyLossbackBonusCreateModal } from '../../../components/modals/WeeklyLossbackBonusCreateModal';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { BonusToggleRequest, BonusSoftDeleteRequest } from '@panels/api';
import { useApi } from '../../../features/api/useApi';
import type { WeeklyLossbackBonus } from '@panels/api';
import { createWeeklyLossbackBonusColumns } from './columns';
import { createWeeklyLossbackBonusFilters } from './filters';

export const WeeklyLossbackBonusListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(
    () => ({ ...filters, isActive: true }),
    [filters]
  );

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedBonus, setSelectedBonus] =
    useState<WeeklyLossbackBonus | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    variant: 'info' | 'warning' | 'danger';
    confirmText: string;
    action: () => Promise<void>;
  }>({
    isOpen: false,
    title: '',
    message: '',
    variant: 'info',
    confirmText: '',
    action: async () => {},
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Data fetching
  const { data, total, isLoading, refetch } = useWeeklyLossbackBonuses({
    page: currentPage,
    limit: itemsPerPage,
    sortField: sortState.field || undefined,
    sortDirection: sortState.direction,
    filters: fetchFilters,
  });

  // Action handlers
  const handleViewBonus = (bonus: WeeklyLossbackBonus) => {
    setSelectedBonus(bonus);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedBonus(null);
  };

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    refetch();
  };

  const handleToggleBonus = (bonus: WeeklyLossbackBonus) => {
    const isActive = bonus.bonus?.isActive;
    setConfirmationDialog({
      isOpen: true,
      title: isActive ? 'Deactivate Bonus' : 'Activate Bonus',
      message: `Are you sure you want to ${
        isActive ? 'deactivate' : 'activate'
      } the bonus "${bonus.bonus?.name || 'Unknown'}"?`,
      variant: isActive ? 'warning' : 'info',
      confirmText: isActive ? 'Deactivate' : 'Activate',
      action: async () => {
        const request = new BonusToggleRequest({
          id: bonus.bonus?.id || 0,
          isActive: !isActive,
        });
        const response = await pronet.makeRequest(request);
        if (response.success) {
          refetch();
        } else {
          throw new Error(response.error || 'Failed to toggle bonus');
        }
      },
    });
  };

  const handleDeleteBonus = (bonus: WeeklyLossbackBonus) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Bonus',
      message: `Are you sure you want to delete the bonus "${
        bonus.bonus?.name || 'Unknown'
      }"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        const request = new BonusSoftDeleteRequest({
          id: bonus.bonus?.id || 0,
        });
        const response = await pronet.makeRequest(request);
        if (response.success) {
          refetch();
        } else {
          throw new Error(response.error || 'Failed to delete bonus');
        }
      },
    });
  };

  const handleConfirmAction = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with handlers
  const columns = createWeeklyLossbackBonusColumns({
    onViewBonus: handleViewBonus,
    onToggleBonus: handleToggleBonus,
    onDeleteBonus: handleDeleteBonus,
  });

  // Create filter groups
  const filterGroups = createWeeklyLossbackBonusFilters();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Weekly Lossback Bonuses
          </h1>
          <p className="text-gray-400">
            Manage weekly lossback bonus configurations
          </p>
        </div>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Bonus
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data || []}
          columns={columns}
          filterGroups={filterGroups}
          total={total || 0}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          title={`Weekly Lossback Bonuses (${(total || 0).toLocaleString()})`}
        />
      </div>

      {/* Modals */}
      {selectedBonus && (
        <WeeklyLossbackBonusDetailsModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          bonus={selectedBonus}
        />
      )}

      <WeeklyLossbackBonusCreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={() =>
          setConfirmationDialog((prev) => ({ ...prev, isOpen: false }))
        }
        onConfirm={handleConfirmAction}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />
    </div>
  );
};
