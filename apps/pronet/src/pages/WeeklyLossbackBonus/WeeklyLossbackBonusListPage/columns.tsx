import { Eye, Pause, Play, Trash2 } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type WeeklyLossbackBonus } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format bonus status
const formatBonusStatus = (bonus: WeeklyLossbackBonus) => {
  if (!bonus.bonus) return null;

  const isActive = bonus.bonus.isActive;
  const isDeleted = bonus.bonus.deletedAt !== null;

  let status: string;
  let colorClass: string;

  if (isDeleted) {
    status = 'deleted';
    colorClass = 'bg-red-900 text-red-300 border-red-700';
  } else if (isActive) {
    status = 'active';
    colorClass = 'bg-green-900 text-green-300 border-green-700';
  } else {
    status = 'inactive';
    colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

// Helper function to format request window
const formatRequestWindow = (bonus: WeeklyLossbackBonus) => {
  const startHours = Math.floor(bonus.requestWindowStartSeconds / 3600);
  const endHours = Math.floor(bonus.requestWindowEndSeconds / 3600);
  return `${startHours}h - ${endHours}h`;
};

interface ColumnHandlers {
  onViewBonus: (bonus: WeeklyLossbackBonus) => void;
  onToggleBonus: (bonus: WeeklyLossbackBonus) => void;
  onDeleteBonus: (bonus: WeeklyLossbackBonus) => void;
}

export const createWeeklyLossbackBonusColumns = (handlers: ColumnHandlers): TableColumn<WeeklyLossbackBonus>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (bonus) => (
      <span className="font-mono text-sm text-gray-300">#{bonus.id}</span>
    ),
  },
  {
    key: 'name',
    label: 'Name',
    width: '200px',
    sortable: true,
    render: (bonus) => (
      <span className="font-medium text-gray-300">
        {bonus.bonus?.name || 'Unknown'}
      </span>
    ),
  },
  {
    key: 'maxBalance',
    label: 'Max Balance',
    width: '120px',
    sortable: true,
    render: (bonus) => (
      <span className="text-gray-300">
        ₺{bonus.maxBalance.toLocaleString()}
      </span>
    ),
  },
  {
    key: 'lossbackPercentage',
    label: 'Lossback %',
    width: '120px',
    sortable: true,
    render: (bonus) => (
      <span className="text-gray-300">{bonus.lossbackPercentage}%</span>
    ),
  },
  {
    key: 'requestWindow',
    label: 'Request Window',
    width: '150px',
    sortable: false,
    render: (bonus) => (
      <span className="text-gray-300">
        {formatRequestWindow(bonus)}
      </span>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (bonus) => formatBonusStatus(bonus),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (bonus) => (
      <span className="text-sm text-gray-400">
        {formatDate(bonus.createdAt)}
      </span>
    ),
  },
  {
    key: 'updatedAt',
    label: 'Updated',
    width: '160px',
    sortable: true,
    render: (bonus) => (
      <span className="text-sm text-gray-400">
        {formatDate(bonus.updatedAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '150px',
    sortable: false,
    sticky: 'right',
    render: (bonus) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onViewBonus(bonus)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onToggleBonus(bonus)}
          className={`p-1 ${
            bonus.bonus?.isActive
              ? 'text-yellow-400 hover:text-yellow-300'
              : 'text-green-400 hover:text-green-300'
          }`}
          title={
            bonus.bonus?.isActive ? 'Deactivate Bonus' : 'Activate Bonus'
          }
        >
          {bonus.bonus?.isActive ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlers.onDeleteBonus(bonus)}
          className="p-1 text-red-400 hover:text-red-300"
          title="Delete Bonus"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];

// Export helper functions for use in other components
export { formatDate, formatBonusStatus, formatRequestWindow };
