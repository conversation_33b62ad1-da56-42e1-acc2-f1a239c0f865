import React, { useState, useEffect } from 'react';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import Select from '../components/ui/Select';
import Modal from '../components/ui/Modal';
import {
  getFreespinProviders,
  getProviderGames,
  getBetAmounts,
  createFreespinBonus,
} from '../utils/api';

interface Provider {
  providerName: string;
  providerId: number;
  steps: Array<{
    schema: Record<string, any>;
  }>;
}

interface Game {
  id: number;
  providerGameId: string;
  name: string;
  gameType: string;
}

interface FormData {
  providerId: number | null;
  gameIds: number[];
  [key: string]: any; // Dynamic fields based on schema
}

const Bonuses: React.FC = () => {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(
    null
  );
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form data
  const [formData, setFormData] = useState<FormData>({
    providerId: null,
    gameIds: [],
  });

  // Games data
  const [games, setGames] = useState<Game[]>([]);
  const [gameSearch, setGameSearch] = useState('');
  const [selectedGames, setSelectedGames] = useState<Game[]>([]);
  const [showGameModal, setShowGameModal] = useState(false);

  // Bet amounts
  const [betAmounts, setBetAmounts] = useState<number[]>([]);

  useEffect(() => {
    fetchProviders();
  }, []);

  const fetchProviders = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getFreespinProviders();
      if (response.success) {
        setProviders(response.data);
      } else {
        setError(response.error || 'Failed to fetch providers');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleProviderSelect = (providerId: string) => {
    const provider = providers.find(
      (p) => p.providerId === parseInt(providerId)
    );
    if (provider) {
      setSelectedProvider(provider);
      setFormData({ providerId: provider.providerId, gameIds: [] });
      setCurrentStep(1);
    }
  };

  const fetchGames = async (searchTerm = '') => {
    if (!selectedProvider) return;

    setLoading(true);
    try {
      const response = await getProviderGames(selectedProvider.providerId, {
        page: 1,
        limit: 200,
        name: searchTerm,
      });
      if (response.success) {
        setGames(response.data);
      } else {
        setError(response.error || 'Failed to fetch games');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleGameSearch = (searchTerm: string) => {
    setGameSearch(searchTerm);
    fetchGames(searchTerm);
  };

  const toggleGameSelection = (game: Game) => {
    const isSelected = selectedGames.find((g) => g.id === game.id);
    if (isSelected) {
      setSelectedGames((prev) => prev.filter((g) => g.id !== game.id));
    } else {
      setSelectedGames((prev) => [...prev, game]);
    }
  };

  const confirmGameSelection = () => {
    const newFormData: any = {
      ...formData,
      gameIds: selectedGames.map((g) => g.id),
    };

    // Initialize form fields based on schema
    if (selectedProvider) {
      const fields = getSchemaFields();
      fields.forEach((field) => {
        if (field.name !== 'gameIds' && newFormData[field.name] === undefined) {
          // Set default values based on field type
          if (field.config.inputOptions?.defaultValue !== undefined) {
            newFormData[field.name] = field.config.inputOptions.defaultValue;
          } else if (
            field.config.type === 'integer' ||
            field.config.type === 'float'
          ) {
            newFormData[field.name] = 0;
          } else if (field.config.type === 'array') {
            newFormData[field.name] = '';
          } else {
            newFormData[field.name] = '';
          }
        }
      });
    }

    setFormData(newFormData);
    setShowGameModal(false);
    setCurrentStep(2);
  };

  // Helper functions for schema parsing
  const getSchemaFields = () => {
    if (!selectedProvider) return [];

    const fields: any[] = [];
    selectedProvider.steps.forEach((step, stepIndex) => {
      Object.entries(step.schema).forEach(
        ([fieldName, fieldConfig]: [string, any]) => {
          fields.push({
            name: fieldName,
            config: fieldConfig,
            stepIndex,
          });
        }
      );
    });

    // Sort by order if specified
    return fields.sort((a, b) => {
      const orderA = a.config.inputOptions?.order || 999;
      const orderB = b.config.inputOptions?.order || 999;
      return orderA - orderB;
    });
  };

  const getFieldsForCurrentStep = () => {
    const allFields = getSchemaFields();
    // For step 2 (bonus configuration), show all fields except gameIds
    return allFields.filter((field) => field.name !== 'gameIds');
  };

  const isFieldSelect = (fieldName: string) => {
    const fields = getSchemaFields();
    const field = fields.find((f) => f.name === fieldName);
    return field?.config.inputType === 'select';
  };

  const initializeFormData = () => {
    if (!selectedProvider) return;

    const fields = getSchemaFields();
    const newFormData: FormData = {
      providerId: selectedProvider.providerId,
      gameIds: formData.gameIds,
    };

    fields.forEach((field) => {
      if (field.name !== 'gameIds') {
        // Set default values based on field type
        if (field.config.inputOptions?.defaultValue !== undefined) {
          newFormData[field.name] = field.config.inputOptions.defaultValue;
        } else if (
          field.config.type === 'integer' ||
          field.config.type === 'float'
        ) {
          newFormData[field.name] = 0;
        } else if (field.config.type === 'array') {
          newFormData[field.name] = '';
        } else {
          newFormData[field.name] = '';
        }
      }
    });

    setFormData(newFormData);
  };

  const fetchBetAmounts = async (fieldName: string) => {
    if (
      !selectedProvider ||
      formData.gameIds.length === 0 ||
      !isFieldSelect(fieldName)
    )
      return;

    setLoading(true);
    try {
      const response = await getBetAmounts({
        vendorId: selectedProvider.providerId,
        gameId: formData.gameIds,
        currencyId: formData.currencyId || 1,
      });
      if (response.success) {
        setBetAmounts(response.data);
      } else {
        setError(response.error || 'Failed to fetch bet amounts');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentStep === 2 && formData.gameIds.length > 0) {
      // Check if any field needs bet amounts (has inputType: 'select')
      const fields = getSchemaFields();
      const selectField = fields.find((f) => f.config.inputType === 'select');
      if (selectField) {
        fetchBetAmounts(selectField.name);
      }
    }
  }, [currentStep, formData.gameIds, formData.currencyId]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!selectedProvider || !formData.customerIds?.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    const customerIdList = formData.customerIds
      .split(',')
      .map((id: string) => id.trim())
      .filter((id: string) => id)
      .map((id: string) => parseInt(id))
      .filter((id: number) => !isNaN(id));

    if (customerIdList.length === 0) {
      setError('Please provide valid customer IDs');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const results = [];
      for (const customerId of customerIdList) {
        const bonusData: any = {
          providerId: selectedProvider.providerId,
          customerId,
          gameIds: formData.gameIds,
        };

        // Add all schema fields except gameIds and customerIds
        const fields = getSchemaFields();
        fields.forEach((field) => {
          if (field.name !== 'gameIds' && field.name !== 'customerIds') {
            let value = formData[field.name];

            // Format date fields to ISO string with milliseconds and Z timezone
            if (field.config.type === 'date' && value) {
              // Convert datetime-local format to ISO string
              const date = new Date(value);
              value = date.toISOString();
            }

            bonusData[field.name] = value;
          }
        });

        const response = await createFreespinBonus(bonusData);
        results.push({
          customerId,
          success: response.success,
          error: response.error,
        });
      }

      const successCount = results.filter((r) => r.success).length;
      const failureCount = results.length - successCount;

      if (successCount > 0) {
        setSuccess(
          `Successfully created ${successCount} freespin bonuses${
            failureCount > 0 ? ` (${failureCount} failed)` : ''
          }`
        );
      }

      if (failureCount > 0) {
        const failedIds = results
          .filter((r) => !r.success)
          .map((r) => r.customerId);
        setError(
          `Failed to create bonuses for customer IDs: ${failedIds.join(', ')}`
        );
      }

      // Reset form on success
      if (successCount === results.length) {
        resetForm();
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSelectedProvider(null);
    setCurrentStep(0);
    setFormData({
      providerId: null,
      gameIds: [],
    });
    setSelectedGames([]);
    setBetAmounts([]);
    setGameSearch('');
  };

  const renderFormField = (field: any) => {
    const { name, config } = field;
    const label = config.inputOptions?.name || name;
    const isRequired = config.required;
    const value = formData[name] || '';

    if (name === 'gameIds') return null; // Skip gameIds as it's handled separately

    if (config.inputType === 'select') {
      // For select fields, use bet amounts if available
      if (betAmounts.length > 0) {
        return (
          <Select
            key={name}
            label={`${label}${isRequired ? ' *' : ''}`}
            placeholder={`Select ${label.toLowerCase()}`}
            options={betAmounts.map((amount) => ({
              value: amount.toString(),
              label: amount.toString(),
            }))}
            value={value.toString()}
            onChange={(e) =>
              handleInputChange(name, parseFloat(e.target.value))
            }
          />
        );
      }
      return null; // Don't render select until options are loaded
    }

    if (config.type === 'date') {
      return (
        <Input
          key={name}
          label={`${label}${isRequired ? ' *' : ''}`}
          type="datetime-local"
          value={value}
          onChange={(e) => handleInputChange(name, e.target.value)}
          required={isRequired}
        />
      );
    }

    if (config.type === 'integer') {
      return (
        <Input
          key={name}
          label={`${label}${isRequired ? ' *' : ''}`}
          type="number"
          value={value}
          onChange={(e) =>
            handleInputChange(name, parseInt(e.target.value) || 0)
          }
          required={isRequired}
        />
      );
    }

    if (config.type === 'float') {
      return (
        <Input
          key={name}
          label={`${label}${isRequired ? ' *' : ''}`}
          type="number"
          step="0.01"
          value={value}
          onChange={(e) =>
            handleInputChange(name, parseFloat(e.target.value) || 0)
          }
          required={isRequired}
        />
      );
    }

    if (config.type === 'array' && config.inputType === 'text') {
      return (
        <Input
          key={name}
          label={`${label}${isRequired ? ' *' : ''}`}
          placeholder="Enter values separated by commas"
          value={value}
          onChange={(e) => handleInputChange(name, e.target.value)}
          helperText={
            name === 'customerIds'
              ? 'Separate multiple customer IDs with commas'
              : undefined
          }
          required={isRequired}
        />
      );
    }

    // Default to text input
    return (
      <Input
        key={name}
        label={`${label}${isRequired ? ' *' : ''}`}
        value={value}
        onChange={(e) => handleInputChange(name, e.target.value)}
        required={isRequired}
      />
    );
  };

  return (
    <div className="space-y-6">
      <div className="bg-dark-800 shadow rounded-lg border border-dark-600">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-300 mb-6">
            Bulk Freespin Assignment
          </h3>

          {error && (
            <div className="mb-4 rounded-md bg-red-900 border border-red-700 p-4">
              <div className="text-sm text-red-300">{error}</div>
            </div>
          )}

          {success && (
            <div className="mb-4 rounded-md bg-green-900 border border-green-700 p-4">
              <div className="text-sm text-green-300">{success}</div>
            </div>
          )}

          {/* Step 0: Provider Selection */}
          {currentStep === 0 && (
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-300">
                Step 1: Select Provider
              </h4>
              {loading ? (
                <div className="text-center py-4 text-gray-400">
                  Loading providers...
                </div>
              ) : (
                <Select
                  label="Provider"
                  placeholder="Select a provider"
                  options={providers.map((p) => ({
                    value: p.providerId.toString(),
                    label: p.providerName,
                  }))}
                  onChange={(e) => handleProviderSelect(e.target.value)}
                />
              )}
            </div>
          )}

          {/* Step 1: Game Selection */}
          {currentStep === 1 && selectedProvider && (
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-300">
                Step 2: Select Games
              </h4>
              <p className="text-sm text-gray-400">
                Provider: {selectedProvider.providerName}
              </p>

              <Button
                onClick={() => {
                  setShowGameModal(true);
                  fetchGames();
                }}
              >
                Select Games ({selectedGames.length} selected)
              </Button>

              {selectedGames.length > 0 && (
                <div className="mt-4">
                  <h5 className="text-sm font-medium text-gray-300 mb-2">
                    Selected Games:
                  </h5>
                  <div className="space-y-1">
                    {selectedGames.map((game) => (
                      <div key={game.id} className="text-sm text-gray-400">
                        {game.name} (ID: {game.id})
                      </div>
                    ))}
                  </div>
                  <Button onClick={confirmGameSelection} className="mt-4">
                    Continue with Selected Games
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Game Selection Modal */}
          <Modal
            isOpen={showGameModal}
            onClose={() => setShowGameModal(false)}
            title="Select Games"
            size="lg"
          >
            <div className="space-y-4">
              <Input
                label="Search Games"
                placeholder="Search by game name..."
                value={gameSearch}
                onChange={(e) => handleGameSearch(e.target.value)}
              />

              {loading ? (
                <div className="text-center py-4 text-gray-400">
                  Loading games...
                </div>
              ) : (
                <div className="max-h-96 overflow-y-auto space-y-2">
                  {games.map((game) => (
                    <div
                      key={game.id}
                      className={`p-3 border rounded cursor-pointer transition-colors ${
                        selectedGames.find((g) => g.id === game.id)
                          ? 'border-primary-500 bg-primary-900 bg-opacity-20'
                          : 'border-dark-600 hover:border-dark-500 bg-dark-700'
                      }`}
                      onClick={() => toggleGameSelection(game)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium text-gray-300">
                            {game.name}
                          </div>
                          <div className="text-sm text-gray-400">
                            ID: {game.id} | Type: {game.gameType}
                          </div>
                        </div>
                        <div
                          className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                            selectedGames.find((g) => g.id === game.id)
                              ? 'border-primary-500 bg-primary-500'
                              : 'border-gray-500'
                          }`}
                        >
                          {selectedGames.find((g) => g.id === game.id) && (
                            <svg
                              className="w-3 h-3 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex justify-end space-x-2 pt-4 border-t border-dark-600">
                <Button
                  variant="outline"
                  onClick={() => setShowGameModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={confirmGameSelection}
                  disabled={selectedGames.length === 0}
                >
                  Confirm Selection ({selectedGames.length})
                </Button>
              </div>
            </div>
          </Modal>

          {/* Step 2: Bonus Configuration */}
          {currentStep === 2 && selectedProvider && (
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-300">
                Step 3: Configure Bonus
              </h4>
              <p className="text-sm text-gray-400">
                Provider: {selectedProvider.providerName} | Games:{' '}
                {selectedGames.length} selected
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {getSchemaFields()
                  .filter((field) => field.name !== 'gameIds')
                  .map((field) => (
                    <div
                      key={field.name}
                      className={
                        field.name === 'customerIds' ? 'md:col-span-2' : ''
                      }
                    >
                      {renderFormField(field)}
                    </div>
                  ))}
              </div>

              <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={() => setCurrentStep(1)}>
                  Back to Games
                </Button>
                <Button
                  onClick={handleSubmit}
                  loading={loading}
                  disabled={(() => {
                    const fields = getSchemaFields();
                    const requiredFields = fields.filter(
                      (f) => f.config.required && f.name !== 'gameIds'
                    );

                    // Check if all required fields are filled
                    for (const field of requiredFields) {
                      const value = formData[field.name];
                      if (
                        !value ||
                        (typeof value === 'string' && !value.trim())
                      ) {
                        return true;
                      }
                    }

                    // Check if select fields have loaded options
                    const selectFields = fields.filter(
                      (f) => f.config.inputType === 'select'
                    );
                    if (selectFields.length > 0 && betAmounts.length === 0) {
                      return true;
                    }

                    return false;
                  })()}
                >
                  Create Freespin Bonuses
                </Button>
              </div>
            </div>
          )}

          {/* Reset button */}
          {currentStep > 0 && (
            <div className="pt-4 border-t border-dark-600 mt-6">
              <Button variant="outline" onClick={resetForm}>
                Start Over
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Bonuses;
