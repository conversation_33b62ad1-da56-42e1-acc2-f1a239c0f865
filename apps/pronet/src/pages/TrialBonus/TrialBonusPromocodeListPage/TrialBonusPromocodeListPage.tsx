import { useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useBonusPromocodes } from '../../../hooks/useBonusPromocodes';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { TrialBonusPromocodeCreateModal } from '../../../components/modals/TrialBonusPromocodeCreateModal';
import { TrialBonusPromocodeDetailsModal } from '../../../components/modals/TrialBonusPromocodeDetailsModal';
import {
  BonusPromocodeToggleRequest,
  BonusPromocodeSoftDeleteRequest,
} from '@panels/api';
import { useApi } from '../../../features/api/useApi';
import type { BonusPromocode } from '@panels/api';
import { createTrialBonusPromocodeColumns } from './columns';
import { createTrialBonusPromocodeFilters } from './filters';

export const TrialBonusPromocodeListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(
    () => ({ ...filters, bonusType: 'trial', isActive: true }),
    [filters]
  );

  // Modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedPromocode, setSelectedPromocode] =
    useState<BonusPromocode | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Fetch data
  const { data, total, isLoading, error, refetch } = useBonusPromocodes({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleCreatePromocode = () => {
    setIsCreateModalOpen(true);
  };

  const handleCreateSuccess = () => {
    refetch(); // Refresh the list
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleViewPromocode = (promocode: BonusPromocode) => {
    setSelectedPromocode(promocode);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPromocode(null);
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  // Toggle promocode
  const handleTogglePromocode = (promocode: BonusPromocode) => {
    const isActive = promocode.isActive;
    const action = isActive ? 'deactivate' : 'activate';
    const actionText = isActive ? 'Deactivate' : 'Activate';

    setConfirmationDialog({
      isOpen: true,
      title: `${actionText} Promocode`,
      message: `Are you sure you want to ${action} the promocode "${promocode.code}"?`,
      variant: isActive ? 'warning' : 'info',
      confirmText: actionText,
      action: async () => {
        const request = new BonusPromocodeToggleRequest({
          id: promocode.id,
          isActive: !isActive,
        });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || `Failed to ${action} promocode`);
        }
      },
    });
  };

  // Delete promocode
  const handleDeletePromocode = (promocode: BonusPromocode) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Promocode',
      message: `Are you sure you want to delete the promocode "${promocode.code}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        const request = new BonusPromocodeSoftDeleteRequest({
          id: promocode.id,
        });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to delete promocode');
        }
      },
    });
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with action handlers
  const columns = createTrialBonusPromocodeColumns({
    onView: handleViewPromocode,
    onToggle: handleTogglePromocode,
    onDelete: handleDeletePromocode,
  });

  // Get filter groups
  const filterGroups = createTrialBonusPromocodeFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No trial bonus promocodes found',
    description: 'Get started by creating your first trial bonus promocode.',
    action: (
      <Button
        onClick={handleCreatePromocode}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Promocode
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">
            Error loading trial bonus promocodes
          </div>
          <div className="text-gray-400 mb-4">{error}</div>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Trial Bonus Promocodes
          </h1>
          <p className="text-gray-400 mt-1">
            Manage trial bonus promocodes and their usage
          </p>
        </div>
        <Button
          onClick={handleCreatePromocode}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Promocode
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          title={`Trial Bonus Promocodes (${(total || 0).toLocaleString()})`}
        />
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />

      {/* Create Promocode Modal */}
      <TrialBonusPromocodeCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {/* Promocode Details Modal */}
      <TrialBonusPromocodeDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        promocode={selectedPromocode}
      />
    </div>
  );
};
