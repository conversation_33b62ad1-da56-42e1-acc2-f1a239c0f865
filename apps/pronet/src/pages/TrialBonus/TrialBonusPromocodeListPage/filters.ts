import { FilterGroup } from '@panels/ui';

export const createTrialBonusPromocodeFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'code',
        label: 'Promocode',
        type: 'text',
        placeholder: 'Search by promocode...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Inactive' },
        ],
      },
    ],
  },
  {
    name: 'usage',
    displayName: 'Usage Filters',
    fields: [
      {
        key: 'hasMaxActivations',
        label: 'Has Max Activations',
        type: 'select',
        options: [
          { value: '', label: 'All' },
          { value: 'true', label: 'Limited' },
          { value: 'false', label: 'Unlimited' },
        ],
      },
      {
        key: 'minActivations',
        label: 'Min Activations',
        type: 'number',
        placeholder: 'Minimum activations...',
        min: 0,
      },
      {
        key: 'maxActivations',
        label: 'Max Activations',
        type: 'number',
        placeholder: 'Maximum activations...',
        min: 0,
      },
    ],
  },
  {
    name: 'dates',
    displayName: 'Date Filters',
    fields: [
      {
        key: 'createdAfter',
        label: 'Created After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Created Before',
        type: 'date',
      },
    ],
  },
];
