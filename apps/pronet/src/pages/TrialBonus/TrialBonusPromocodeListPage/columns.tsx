import { Copy, Eye, Pause, Play, Trash2 } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusPromocode } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format promocode status
const formatPromocodeStatus = (promocode: BonusPromocode) => {
  const isActive = promocode.isActive;
  const isDeleted = promocode.deletedAt !== null;

  let status: string;
  let colorClass: string;

  if (isDeleted) {
    status = 'deleted';
    colorClass = 'bg-red-900 text-red-300 border-red-700';
  } else if (isActive) {
    status = 'active';
    colorClass = 'bg-green-900 text-green-300 border-green-700';
  } else {
    status = 'inactive';
    colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

// Helper function to copy to clipboard
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    // TODO: Show success toast
  } catch (err) {
    console.error('Failed to copy text: ', err);
    // TODO: Show error toast
  }
};

interface TrialBonusPromocodeColumnsProps {
  onView: (promocode: BonusPromocode) => void;
  onToggle: (promocode: BonusPromocode) => void;
  onDelete: (promocode: BonusPromocode) => void;
}

export const createTrialBonusPromocodeColumns = ({
  onView,
  onToggle,
  onDelete,
}: TrialBonusPromocodeColumnsProps): TableColumn<BonusPromocode>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (promocode) => (
      <span className="font-mono text-sm text-gray-300">#{promocode.id}</span>
    ),
  },
  {
    key: 'code',
    label: 'Code',
    width: '150px',
    sortable: true,
    render: (promocode) => (
      <div className="flex items-center space-x-2">
        <span className="font-mono text-sm text-gray-300">
          {promocode.code}
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => copyToClipboard(promocode.code)}
          className="p-1 text-gray-400 hover:text-gray-300"
          title="Copy Code"
        >
          <Copy className="h-3 w-3" />
        </Button>
      </div>
    ),
  },
  {
    key: 'bonusId',
    label: 'Bonus ID',
    width: '100px',
    sortable: true,
    render: (promocode) => (
      <span className="font-mono text-sm text-gray-300">
        #{promocode.bonusId}
      </span>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (promocode) => formatPromocodeStatus(promocode),
  },
  {
    key: 'activations',
    label: 'Activations',
    width: '120px',
    sortable: true,
    render: (promocode) => (
      <span className="text-gray-300">
        {promocode.activations}
        {promocode.maxActivations && ` / ${promocode.maxActivations}`}
      </span>
    ),
  },
  {
    key: 'maxActivations',
    label: 'Max Uses',
    width: '100px',
    sortable: true,
    render: (promocode) => (
      <span className="text-gray-300">
        {promocode.maxActivations || 'Unlimited'}
      </span>
    ),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (promocode) => (
      <span className="text-sm text-gray-400">
        {formatDate(promocode.createdAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '150px',
    sortable: false,
    sticky: 'right',
    render: (promocode) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onView(promocode)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>

        {promocode.isActive ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggle(promocode)}
            className="p-1 text-yellow-400 hover:text-yellow-300"
            title="Deactivate Promocode"
          >
            <Pause className="h-4 w-4" />
          </Button>
        ) : (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggle(promocode)}
            className="p-1 text-green-400 hover:text-green-300"
            title="Activate Promocode"
          >
            <Play className="h-4 w-4" />
          </Button>
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete(promocode)}
          className="p-1 text-red-400 hover:text-red-300"
          title="Delete Promocode"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];
