import { FilterGroup } from '@panels/ui';

export const createTrialBonusFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'externalBonusName',
        label: 'External Name',
        type: 'text',
        placeholder: 'Search by external bonus name...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'externalBonusId',
        label: 'External Bonus ID',
        type: 'number',
        placeholder: 'Enter external bonus ID...',
      },
    ],
  },
  {
    name: 'status',
    displayName: 'Status Filters',
    fields: [
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: '', label: 'All' },
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Inactive' },
        ],
      },
    ],
  },
];
