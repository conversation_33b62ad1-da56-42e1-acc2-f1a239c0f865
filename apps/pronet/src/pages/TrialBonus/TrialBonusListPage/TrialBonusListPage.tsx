import { useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useTrialBonuses } from '../../../hooks/useTrialBonuses';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { TrialBonusCreateModal } from '../../../components/modals/TrialBonusCreateModal';
import { TrialBonusDetailsModal } from '../../../components/modals/TrialBonusDetailsModal';
import { useApi } from '../../../features/api/useApi';
import {
  BonusToggleRequest,
  BonusSoftDeleteRequest,
  type TrialBonus,
} from '@panels/api';
import { createTrialBonusColumns } from './columns';
import { createTrialBonusFilters } from './filters';

export const TrialBonusListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(() => ({ ...filters }), [filters]);

  // Modal state
  const [selectedBonus, setSelectedBonus] = useState<TrialBonus | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Fetch data
  const { data, total, isLoading, error, refetch } = useTrialBonuses({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleCreateBonus = () => {
    setIsCreateModalOpen(true);
  };

  const handleViewBonus = (bonus: TrialBonus) => {
    setSelectedBonus(bonus);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedBonus(null);
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleCreateSuccess = () => {
    refetch(); // Refresh the list after successful creation
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      // Ensure the modal closes after successful action
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  // Pause/Activate bonus
  const handlePauseBonus = (bonus: TrialBonus) => {
    const isActive = bonus.bonus.isActive;
    const action = isActive ? 'pause' : 'activate';
    const actionText = isActive ? 'Pause' : 'Activate';

    setConfirmationDialog({
      isOpen: true,
      title: `${actionText} Trial Bonus`,
      message: `Are you sure you want to ${action} the trial bonus "${bonus.bonus.name}"?`,
      variant: isActive ? 'warning' : 'info',
      confirmText: actionText,
      action: async () => {
        const request = new BonusToggleRequest({
          id: bonus.bonus.id,
          isActive: !isActive,
        });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || `Failed to ${action} bonus`);
        }
      },
    });
  };

  // Delete bonus
  const handleDeleteBonus = (bonus: TrialBonus) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Trial Bonus',
      message: `Are you sure you want to delete the trial bonus "${bonus.bonus.name}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        const request = new BonusSoftDeleteRequest({ id: bonus.bonus.id });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to delete bonus');
        }
      },
    });
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with action handlers
  const columns = createTrialBonusColumns({
    onView: handleViewBonus,
    onToggle: handlePauseBonus,
    onDelete: handleDeleteBonus,
  });

  // Get filter groups
  const filterGroups = createTrialBonusFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No trial bonuses found',
    description: 'Get started by creating your first trial bonus.',
    action: (
      <Button onClick={handleCreateBonus} className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        Create Trial Bonus
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">
            Error loading trial bonuses
          </div>
          <div className="text-gray-400 mb-4">{error}</div>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">Trial Bonuses</h1>
          <p className="text-gray-400 mt-1">
            Manage trial bonuses and their configurations
          </p>
        </div>
        <Button onClick={handleCreateBonus} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Trial Bonus
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          title={`Trial Bonuses (${(total || 0).toLocaleString()})`}
        />
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />

      {/* Create Modal */}
      <TrialBonusCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {/* Details Modal */}
      <TrialBonusDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        bonus={selectedBonus}
      />
    </div>
  );
};
