import { Eye, Pause, Play, Trash2 } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type TrialBonus } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format bonus status
const formatBonusStatus = (bonus: TrialBonus) => {
  const now = new Date();
  const isExpired = bonus.bonus.expiresAt && bonus.bonus.expiresAt < now;
  const isActive = bonus.bonus.isActive;

  let status: string;
  let colorClass: string;

  if (isExpired) {
    status = 'expired';
    colorClass = 'bg-red-900 text-red-300 border-red-700';
  } else if (isActive) {
    status = 'active';
    colorClass = 'bg-green-900 text-green-300 border-green-700';
  } else {
    status = 'inactive';
    colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

interface TrialBonusColumnsProps {
  onView: (bonus: TrialBonus) => void;
  onToggle: (bonus: TrialBonus) => void;
  onDelete: (bonus: TrialBonus) => void;
}

export const createTrialBonusColumns = ({
  onView,
  onToggle,
  onDelete,
}: TrialBonusColumnsProps): TableColumn<TrialBonus>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (bonus) => (
      <span className="font-mono text-sm text-gray-300">#{bonus.id}</span>
    ),
  },
  {
    key: 'name',
    label: 'Name',
    width: '200px',
    sortable: true,
    render: (bonus) => (
      <span className="font-medium text-gray-300">{bonus.bonus.name}</span>
    ),
  },
  {
    key: 'externalBonusName',
    label: 'External Name',
    width: '180px',
    sortable: true,
    render: (bonus) => (
      <span className="font-medium text-gray-300">
        {bonus.externalBonusName}
      </span>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (bonus) => formatBonusStatus(bonus),
  },
  {
    key: 'amount',
    label: 'Amount',
    width: '120px',
    sortable: true,
    render: (bonus) => (
      <span className="text-gray-300">₺{bonus.amount.toFixed(2)}</span>
    ),
  },
  {
    key: 'externalBonusId',
    label: 'External ID',
    width: '120px',
    sortable: true,
    render: (bonus) => (
      <span className="font-mono text-sm text-gray-300">
        #{bonus.externalBonusId}
      </span>
    ),
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (bonus) => (
      <span className="text-sm text-gray-400">
        {formatDate(bonus.createdAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '150px',
    sortable: false,
    sticky: 'right',
    render: (bonus) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onView(bonus)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>

        {bonus.bonus.isActive ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggle(bonus)}
            className="p-1 text-yellow-400 hover:text-yellow-300"
            title="Pause Bonus"
          >
            <Pause className="h-4 w-4" />
          </Button>
        ) : (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggle(bonus)}
            className="p-1 text-green-400 hover:text-green-300"
            title="Activate Bonus"
          >
            <Play className="h-4 w-4" />
          </Button>
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete(bonus)}
          className="p-1 text-red-400 hover:text-red-300"
          title="Delete Bonus"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];
