import { useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useTrialBonusTemplates } from '../../../hooks/useTrialBonusTemplates';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { TrialBonusTemplateCreateModal } from '../../../components/modals/TrialBonusTemplateCreateModal';
import { TrialBonusTemplateDetailsModal } from '../../../components/modals/TrialBonusTemplateDetailsModal';
import { useApi } from '../../../features/api/useApi';
import {
  BonusTemplateSoftDeleteRequest,
  type TrialBonusTemplate,
} from '@panels/api';
import { createTrialBonusTemplateColumns } from './columns';
import { createTrialBonusTemplateFilters } from './filters';

export const TrialBonusTemplatesListPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(
    () => ({ ...filters, isActive: true }),
    [filters]
  );

  // Modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<TrialBonusTemplate | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Fetch data
  const { data, total, isLoading, error, refetch } = useTrialBonusTemplates({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleCreateTemplate = () => {
    setIsCreateModalOpen(true);
  };

  const handleCreateSuccess = () => {
    refetch(); // Refresh the list
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleViewTemplate = (template: TrialBonusTemplate) => {
    setSelectedTemplate(template);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedTemplate(null);
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  // Delete template
  const handleDeleteTemplate = (template: TrialBonusTemplate) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Delete Trial Bonus Template',
      message: `Are you sure you want to delete the template "${template.bonusTemplate.name}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      action: async () => {
        const request = new BonusTemplateSoftDeleteRequest({
          id: template.bonusTemplate.id,
        });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          refetch(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to delete template');
        }
      },
    });
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with action handlers
  const columns = createTrialBonusTemplateColumns({
    onView: handleViewTemplate,
    onDelete: handleDeleteTemplate,
  });

  // Get filter groups
  const filterGroups = createTrialBonusTemplateFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No trial bonus templates found',
    description: 'Get started by creating your first trial bonus template.',
    action: (
      <Button
        onClick={handleCreateTemplate}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Template
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">
            Error loading trial bonus templates
          </div>
          <div className="text-gray-400 mb-4">{error}</div>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Trial Bonus Templates
          </h1>
          <p className="text-gray-400 mt-1">
            Manage trial bonus templates and their configurations
          </p>
        </div>
        <Button
          onClick={handleCreateTemplate}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Template
        </Button>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          title={`Trial Bonus Templates (${(total || 0).toLocaleString()})`}
        />
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />

      {/* Create Template Modal */}
      <TrialBonusTemplateCreateModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {/* Template Details Modal */}
      <TrialBonusTemplateDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        template={selectedTemplate}
      />
    </div>
  );
};
