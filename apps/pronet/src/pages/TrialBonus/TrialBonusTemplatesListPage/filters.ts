import { FilterGroup } from '@panels/ui';

export const createTrialBonusTemplateFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'name',
        label: 'Template Name',
        type: 'text',
        placeholder: 'Search by template name...',
      },
      {
        key: 'isActive',
        label: 'Status',
        type: 'select',
        options: [
          { value: 'true', label: 'Active' },
          { value: 'false', label: 'Inactive' },
        ],
      },
      {
        key: 'minAmount',
        label: 'Min Amount',
        type: 'number',
        placeholder: 'Minimum amount...',
        min: 0,
      },
      {
        key: 'maxAmount',
        label: 'Max Amount',
        type: 'number',
        placeholder: 'Maximum amount...',
        min: 0,
      },
    ],
  },
  {
    name: 'advanced',
    displayName: 'Advanced Filters',
    fields: [
      {
        key: 'createdAfter',
        label: 'Created After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Created Before',
        type: 'date',
      },
      {
        key: 'validForDays',
        label: 'Valid For Days',
        type: 'number',
        placeholder: 'Valid for days...',
        min: 1,
      },
    ],
  },
];
