import { useMemo, useState } from 'react';
import { FileText } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import { useBonusClaims } from '../../../hooks/useBonusClaims';
import Button from '../../../components/ui/Button';
import { BonusClaimDetailsModal } from '../../../components/modals/BonusClaimDetailsModal';
import type { BonusClaim } from '@panels/api';
import { createTrialBonusClaimColumns } from './columns';
import { createTrialBonusClaimFilters } from './filters';

export const TrialBonusClaimListPage = () => {
  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const fetchFilters = useMemo(
    () => ({ ...filters, bonusType: 'trial' }),
    [filters]
  );

  // Modal state
  const [selectedClaim, setSelectedClaim] = useState<BonusClaim | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Fetch data
  const { data, total, isLoading, error, refetch } = useBonusClaims({
    page: currentPage,
    limit: itemsPerPage,
    filters: fetchFilters,
    sortField: sortState.field,
    sortDirection: sortState.direction,
  });

  // Action handlers
  const handleViewClaim = (claim: BonusClaim) => {
    setSelectedClaim(claim);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedClaim(null);
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with action handlers
  const columns = createTrialBonusClaimColumns({
    onView: handleViewClaim,
  });

  // Get filter groups
  const filterGroups = createTrialBonusClaimFilters();

  // Empty state configuration
  const emptyState = {
    icon: <FileText className="h-12 w-12 text-gray-400" />,
    title: 'No trial bonus claims found',
    description: 'Claims will appear here when users request trial bonuses.',
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">
            Error loading trial bonus claims
          </div>
          <div className="text-gray-400 mb-4">{error}</div>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Trial Bonus Claims
          </h1>
          <p className="text-gray-400 mt-1">
            Review and manage trial bonus claims from users
          </p>
        </div>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={data}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          title={`Trial Bonus Claims (${(total || 0).toLocaleString()})`}
        />
      </div>

      {/* Claim Details Modal */}
      {selectedClaim && (
        <BonusClaimDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={handleCloseDetailsModal}
          claim={selectedClaim}
        />
      )}
    </div>
  );
};
