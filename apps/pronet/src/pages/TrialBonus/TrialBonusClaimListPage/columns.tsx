import { Eye, User } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusClaim } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

interface TrialBonusClaimColumnsProps {
  onView: (claim: BonusClaim) => void;
}

export const createTrialBonusClaimColumns = ({
  onView,
}: TrialBonusClaimColumnsProps): TableColumn<BonusClaim>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (claim) => (
      <span className="font-mono text-sm text-gray-300">#{claim.id}</span>
    ),
  },
  {
    key: 'customer',
    label: 'Customer',
    width: '200px',
    sortable: true,
    render: (claim) => (
      <div className="flex items-center gap-2">
        <User className="h-4 w-4 text-gray-400" />
        <div>
          <div className="font-medium text-gray-300">
            {claim.customer?.username || `Customer #${claim.customerId}`}
          </div>
          <div className="text-xs text-gray-400">
            External ID: {claim.customer?.externalId || 'N/A'}
          </div>
        </div>
      </div>
    ),
  },
  {
    key: 'bonusId',
    label: 'Bonus ID',
    width: '100px',
    sortable: true,
    render: (claim) => (
      <span className="font-mono text-sm text-gray-300">#{claim.bonusId}</span>
    ),
  },
  {
    key: 'source',
    label: 'Source',
    width: '120px',
    sortable: true,
    render: (claim) => <span className="text-gray-300">{claim.source}</span>,
  },
  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (claim) => (
      <span className="text-sm text-gray-400">
        {formatDate(claim.createdAt)}
      </span>
    ),
  },
  {
    key: 'updatedAt',
    label: 'Updated',
    width: '160px',
    sortable: true,
    render: (claim) => (
      <span className="text-sm text-gray-400">
        {formatDate(claim.updatedAt)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '150px',
    sortable: false,
    sticky: 'right',
    render: (claim) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onView(claim)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];
