import { FilterGroup } from '@panels/ui';

export const createTrialBonusClaimFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'userId',
        label: 'User ID',
        type: 'number',
        placeholder: 'Enter user ID...',
      },
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'status',
        label: 'Status',
        type: 'select',
        options: [
          { value: '', label: 'All' },
          { value: 'pending', label: 'Pending' },
          { value: 'approved', label: 'Approved' },
          { value: 'rejected', label: 'Rejected' },
          { value: 'expired', label: 'Expired' },
        ],
      },
    ],
  },
  {
    name: 'amounts',
    displayName: 'Amount Filters',
    fields: [
      {
        key: 'minAmount',
        label: 'Min Amount',
        type: 'number',
        placeholder: 'Minimum amount...',
        min: 0,
      },
      {
        key: 'maxAmount',
        label: 'Max Amount',
        type: 'number',
        placeholder: 'Maximum amount...',
        min: 0,
      },
    ],
  },
  {
    name: 'dates',
    displayName: 'Date Filters',
    fields: [
      {
        key: 'claimedAfter',
        label: 'Claimed After',
        type: 'date',
      },
      {
        key: 'claimedBefore',
        label: 'Claimed Before',
        type: 'date',
      },
      {
        key: 'processedAfter',
        label: 'Processed After',
        type: 'date',
      },
      {
        key: 'processedBefore',
        label: 'Processed Before',
        type: 'date',
      },
    ],
  },
];
