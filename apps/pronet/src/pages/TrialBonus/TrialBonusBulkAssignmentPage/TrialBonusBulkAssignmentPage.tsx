import { useState, useEffect, useRef } from 'react';
import { Plus, X } from 'lucide-react';
import { DataTable, SortState } from '@panels/ui';
import Button from '../../../components/ui/Button';
import { ConfirmationDialog } from '../../../components/ui/ConfirmationDialog';
import { TrialBonusBulkAssignmentDetailsModal } from '../../../components/modals/TrialBonusBulkAssignmentDetailsModal/TrialBonusBulkAssignmentDetailsModal';
import { useApi } from '../../../features/api/useApi';
import {
  BonusBulkAssignmentJobSearchRequest,
  BonusBulkAssignmentJobGetRequest,
  BonusBulkAssignmentJobCancelRequest,
  BonusBulkAssignmentJobRetryRequest,
  TrialBonusBulkAssignmentJobCreateRequest,
  TrialBonusSearchRequest,
  type BonusBulkAssignmentJob,
  type TrialBonus,
  type BonusBulkAssignmentJobSearchRequestOptions,
  type TrialBonusBulkAssignmentRequestCreateOptions,
} from '@panels/api';
import { createTrialBonusBulkAssignmentColumns } from './columns';
import { createTrialBonusBulkAssignmentFilters } from './filters';

interface FormData {
  bonusId: number | null;
  customerIds: string;
}

interface FormErrors {
  [key: string]: string | undefined;
}

export const TrialBonusBulkAssignmentPage = () => {
  const { pronet } = useApi();

  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortState, setSortState] = useState<SortState>({
    field: null,
    direction: 'asc',
  });
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  // Data state
  const [jobs, setJobs] = useState<BonusBulkAssignmentJob[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Long polling state
  const [isPolling, setIsPolling] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const POLLING_INTERVAL = 3000; // 3 seconds

  // Modal states
  const [isCreateFormOpen, setIsCreateFormOpen] = useState(false);
  const [isJobDetailsOpen, setIsJobDetailsOpen] = useState(false);
  const [jobDetails, setJobDetails] = useState<BonusBulkAssignmentJob | null>(
    null
  );

  // Form state
  const [formData, setFormData] = useState<FormData>({
    bonusId: null,
    customerIds: '',
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bonuses, setBonuses] = useState<TrialBonus[]>([]);
  const [isLoadingBonuses, setIsLoadingBonuses] = useState(false);

  // Confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
    variant: 'danger' | 'warning' | 'info';
    confirmText: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    action: async () => {},
    variant: 'warning',
    confirmText: 'Confirm',
  });
  const [isConfirmationLoading, setIsConfirmationLoading] = useState(false);

  // Load jobs function
  const loadJobs = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const searchOptions: BonusBulkAssignmentJobSearchRequestOptions = {
        page: currentPage,
        limit: itemsPerPage,
        bonusType: 'trial',
        ...filters,
      };

      const request = new BonusBulkAssignmentJobSearchRequest(searchOptions);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setJobs(response.data.items);
        setTotal(response.data.total);
      } else {
        setError(response.error || 'Failed to load jobs');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Polling functions
  const startPolling = () => {
    if (pollingIntervalRef.current) return;

    setIsPolling(true);
    pollingIntervalRef.current = setInterval(() => {
      loadJobs();
    }, POLLING_INTERVAL);
  };

  const stopPolling = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    setIsPolling(false);
  };

  // Check if any jobs are in progress
  const hasActiveJobs = jobs.some((job) =>
    ['pending', 'processing'].includes(job.status)
  );

  // Auto-start/stop polling based on active jobs
  useEffect(() => {
    if (hasActiveJobs && !isPolling) {
      startPolling();
    } else if (!hasActiveJobs && isPolling) {
      stopPolling();
    }
  }, [hasActiveJobs, isPolling]);

  // Load jobs when dependencies change
  useEffect(() => {
    loadJobs();
  }, [currentPage, itemsPerPage, sortState, filters]);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  // Load bonuses function
  const loadBonuses = async () => {
    setIsLoadingBonuses(true);
    try {
      const request = new TrialBonusSearchRequest({
        page: 1,
        limit: 100,
        isActive: true,
      });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setBonuses(response.data.items);
      }
    } catch (error) {
      console.error('Failed to load bonuses:', error);
    } finally {
      setIsLoadingBonuses(false);
    }
  };

  // Form handlers
  const handleCreateFormOpen = () => {
    setIsCreateFormOpen(true);
    loadBonuses();
  };

  const handleCreateFormClose = () => {
    setIsCreateFormOpen(false);
    setFormData({ bonusId: null, customerIds: '' });
    setFormErrors({});
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFormSubmit = async () => {
    setFormErrors({});

    // Validation
    if (!formData.bonusId) {
      setFormErrors({ bonusId: 'Please select a bonus' });
      return;
    }

    if (!formData.customerIds.trim()) {
      setFormErrors({ customerIds: 'Please enter customer IDs' });
      return;
    }

    setIsSubmitting(true);
    try {
      const customerIds = formData.customerIds
        .split(/[,\n]/)
        .map((id) => id.trim())
        .filter((id) => id)
        .map((id) => parseInt(id));

      const requestData: TrialBonusBulkAssignmentRequestCreateOptions = {
        trialBonusId: formData.bonusId!,
        externalCustomerIds: customerIds,
      };

      const request = new TrialBonusBulkAssignmentJobCreateRequest(requestData);
      const response = await pronet.makeRequest(request);

      if (response.success) {
        handleCreateFormClose();
        loadJobs(); // Refresh the list
      } else {
        setFormErrors({
          submit: response.error || 'Failed to create bulk assignment job',
        });
      }
    } catch (error) {
      setFormErrors({
        submit: error instanceof Error ? error.message : 'An error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewJob = async (job: BonusBulkAssignmentJob) => {
    try {
      const request = new BonusBulkAssignmentJobGetRequest({ id: job.id });
      const response = await pronet.makeRequest(request);

      if (response.success) {
        setJobDetails(response.data);
        setIsJobDetailsOpen(true);
      } else {
        console.error('Failed to load job details:', response.error);
        // The modal will handle the error state if jobDetails is null
        setJobDetails(null);
        setIsJobDetailsOpen(true);
      }
    } catch (error) {
      console.error('Error loading job details:', error);
      // The modal will handle the error state if jobDetails is null
      setJobDetails(null);
      setIsJobDetailsOpen(true);
    }
  };

  const handleCloseJobDetails = () => {
    setIsJobDetailsOpen(false);
    setJobDetails(null);
  };

  // Confirmation dialog handlers
  const handleConfirmationClose = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  const handleConfirmationConfirm = async () => {
    setIsConfirmationLoading(true);
    try {
      await confirmationDialog.action();
      setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
    } catch (error) {
      console.error('Action failed:', error);
      // TODO: Show error toast
    } finally {
      setIsConfirmationLoading(false);
    }
  };

  // Cancel job
  const handleCancelJob = (job: BonusBulkAssignmentJob) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Cancel Bulk Assignment Job',
      message: `Are you sure you want to cancel the bulk assignment job #${job.id}? This will stop the job from processing any remaining users.`,
      variant: 'warning',
      confirmText: 'Cancel Job',
      action: async () => {
        const request = new BonusBulkAssignmentJobCancelRequest({
          id: job.id,
        });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          loadJobs(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to cancel job');
        }
      },
    });
  };

  // Retry job
  const handleRetryJob = (job: BonusBulkAssignmentJob) => {
    setConfirmationDialog({
      isOpen: true,
      title: 'Retry Bulk Assignment Job',
      message: `Are you sure you want to retry the bulk assignment job #${job.id}? This will restart the job from where it failed.`,
      variant: 'info',
      confirmText: 'Retry Job',
      action: async () => {
        const request = new BonusBulkAssignmentJobRetryRequest({
          id: job.id,
        });
        const response = await pronet.makeRequest(request);

        if (response.success) {
          loadJobs(); // Refresh the list
        } else {
          throw new Error(response.error || 'Failed to retry job');
        }
      },
    });
  };

  // Sort handler
  const handleSort = (field: string) => {
    setSortState((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Create columns with action handlers
  const columns = createTrialBonusBulkAssignmentColumns({
    onView: handleViewJob,
    onCancel: handleCancelJob,
    onRetry: handleRetryJob,
  });

  // Get filter groups
  const filterGroups = createTrialBonusBulkAssignmentFilters();

  // Empty state configuration
  const emptyState = {
    icon: <Plus className="h-12 w-12 text-gray-400" />,
    title: 'No bulk assignment jobs found',
    description: 'Get started by creating your first bulk assignment job.',
    action: (
      <Button
        onClick={handleCreateFormOpen}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Bulk Assignment
      </Button>
    ),
  };

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">
            Error loading bulk assignment jobs
          </div>
          <div className="text-gray-400 mb-4">{error}</div>
          <Button onClick={() => loadJobs()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">
            Trial Bonus Bulk Assignments
          </h1>
          <p className="text-gray-400 mt-1">
            Manage bulk assignment jobs for trial bonuses
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {isPolling && (
            <div className="flex items-center text-sm text-blue-400">
              <div className="animate-pulse w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Auto-refreshing...
            </div>
          )}
          <Button
            onClick={handleCreateFormOpen}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create Bulk Assignment
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <div className="min-h-[600px] grid">
        <DataTable
          data={jobs}
          columns={columns}
          filterGroups={filterGroups}
          total={total}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          isLoading={isLoading}
          sortState={sortState}
          filters={filters}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          onSort={handleSort}
          onFilterChange={setFilters}
          emptyState={emptyState}
          title={`Trial Bonus Bulk Assignments (${(
            total || 0
          ).toLocaleString()})`}
        />
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={handleConfirmationClose}
        onConfirm={handleConfirmationConfirm}
        title={confirmationDialog.title}
        message={confirmationDialog.message}
        variant={confirmationDialog.variant}
        confirmText={confirmationDialog.confirmText}
        isLoading={isConfirmationLoading}
      />

      {/* Create Form Modal */}
      {isCreateFormOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-dark-800 rounded-lg border border-dark-600 p-6 w-full max-w-2xl mx-4">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-300">
                Create Bulk Assignment Job
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCreateFormClose}
                className="p-2"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-4">
              {/* Bonus Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Bonus *
                </label>
                {isLoadingBonuses ? (
                  <div className="text-sm text-gray-400">
                    Loading bonuses...
                  </div>
                ) : (
                  <select
                    value={formData.bonusId || ''}
                    onChange={(e) =>
                      handleInputChange(
                        'bonusId',
                        e.target.value ? parseInt(e.target.value) : null
                      )
                    }
                    className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.bonusId ? 'border-red-500' : 'border-dark-600'
                    }`}
                  >
                    <option value="">Select a bonus...</option>
                    {bonuses.map((bonus) => (
                      <option key={bonus.id} value={bonus.id}>
                        {bonus.bonus.name} - ₺{bonus.amount.toFixed(2)} (
                        {bonus.externalBonusName})
                      </option>
                    ))}
                  </select>
                )}
                {formErrors.bonusId && (
                  <p className="mt-1 text-sm text-red-400">
                    {formErrors.bonusId}
                  </p>
                )}
              </div>

              {/* Customer IDs */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Customer IDs *
                </label>
                <textarea
                  value={formData.customerIds}
                  onChange={(e) =>
                    handleInputChange('customerIds', e.target.value)
                  }
                  placeholder="Enter customer IDs separated by commas or new lines..."
                  rows={6}
                  className={`w-full px-3 py-2 bg-dark-700 border rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
                    formErrors.customerIds
                      ? 'border-red-500'
                      : 'border-dark-600'
                  }`}
                />
                {formErrors.customerIds && (
                  <p className="mt-1 text-sm text-red-400">
                    {formErrors.customerIds}
                  </p>
                )}
                <p className="mt-1 text-xs text-gray-400">
                  Enter customer IDs separated by commas or new lines. Example:
                  123, 456, 789
                </p>
              </div>

              {/* Submit Error */}
              {formErrors.submit && (
                <div className="p-3 bg-red-900/20 border border-red-700 rounded-md">
                  <p className="text-sm text-red-400">{formErrors.submit}</p>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="mt-6 flex justify-end space-x-3">
              <Button variant="outline" onClick={handleCreateFormClose}>
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleFormSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating...' : 'Create Job'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Job Details Modal */}
      <TrialBonusBulkAssignmentDetailsModal
        isOpen={isJobDetailsOpen}
        onClose={handleCloseJobDetails}
        job={jobDetails}
      />
    </div>
  );
};
