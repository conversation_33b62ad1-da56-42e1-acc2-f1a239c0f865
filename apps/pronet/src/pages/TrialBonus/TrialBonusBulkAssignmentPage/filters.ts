import { FilterGroup } from '@panels/ui';

export const createTrialBonusBulkAssignmentFilters = (): FilterGroup[] => [
  {
    name: 'basic',
    displayName: 'Basic Filters',
    fields: [
      {
        key: 'bonusId',
        label: 'Bonus ID',
        type: 'number',
        placeholder: 'Enter bonus ID...',
      },
      {
        key: 'status',
        label: 'Status',
        type: 'select',
        options: [
          { value: '', label: 'All' },
          { value: 'pending', label: 'Pending' },
          { value: 'processing', label: 'Processing' },
          { value: 'completed', label: 'Completed' },
          { value: 'failed', label: 'Failed' },
          { value: 'cancelled', label: 'Cancelled' },
        ],
      },
      {
        key: 'createdBy',
        label: 'Created By',
        type: 'text',
        placeholder: 'Enter creator name...',
      },
    ],
  },
  {
    name: 'progress',
    displayName: 'Progress Filters',
    fields: [
      {
        key: 'minTotalUsers',
        label: 'Min Total Users',
        type: 'number',
        placeholder: 'Minimum total users...',
        min: 0,
      },
      {
        key: 'maxTotalUsers',
        label: 'Max Total Users',
        type: 'number',
        placeholder: 'Maximum total users...',
        min: 0,
      },
      {
        key: 'minProcessedUsers',
        label: 'Min Processed',
        type: 'number',
        placeholder: 'Minimum processed users...',
        min: 0,
      },
    ],
  },
  {
    name: 'dates',
    displayName: 'Date Filters',
    fields: [
      {
        key: 'createdAfter',
        label: 'Created After',
        type: 'date',
      },
      {
        key: 'createdBefore',
        label: 'Created Before',
        type: 'date',
      },
      {
        key: 'completedAfter',
        label: 'Completed After',
        type: 'date',
      },
      {
        key: 'completedBefore',
        label: 'Completed Before',
        type: 'date',
      },
    ],
  },
];
