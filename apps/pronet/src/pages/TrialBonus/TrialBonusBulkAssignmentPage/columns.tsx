import { Eye, XCircle, Play } from 'lucide-react';
import { TableColumn } from '@panels/ui';
import { type BonusBulkAssignmentJob } from '@panels/api';
import Button from '../../../components/ui/Button';

// Helper function to format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Helper function to format job status
const formatJobStatus = (job: BonusBulkAssignmentJob) => {
  const status = job.status;
  let colorClass: string;

  switch (status) {
    case 'pending':
      colorClass = 'bg-yellow-900 text-yellow-300 border-yellow-700';
      break;
    case 'processing':
      colorClass = 'bg-blue-900 text-blue-300 border-blue-700';
      break;
    case 'completed':
      colorClass = 'bg-green-900 text-green-300 border-green-700';
      break;
    case 'failed':
      colorClass = 'bg-red-900 text-red-300 border-red-700';
      break;
    case 'cancelled':
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
      break;
    default:
      colorClass = 'bg-gray-900 text-gray-300 border-gray-700';
  }

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium border ${colorClass}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

// Helper function to format progress
const formatProgress = (job: BonusBulkAssignmentJob) => {
  const processed = job.targets.filter(
    (t) => t.status === 'processed' || t.status === 'completed'
  ).length;
  const total = job.targets.length;
  const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

  return (
    <div className="flex items-center space-x-2">
      <div className="flex-1 bg-gray-700 rounded-full h-2">
        <div
          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${percentage}%` }}
        />
      </div>
      <span className="text-xs text-gray-400 min-w-[3rem]">{percentage}%</span>
    </div>
  );
};

interface TrialBonusBulkAssignmentColumnsProps {
  onView: (job: BonusBulkAssignmentJob) => void;
  onCancel: (job: BonusBulkAssignmentJob) => void;
  onRetry: (job: BonusBulkAssignmentJob) => void;
}

export const createTrialBonusBulkAssignmentColumns = ({
  onView,
  onCancel,
  onRetry,
}: TrialBonusBulkAssignmentColumnsProps): TableColumn<BonusBulkAssignmentJob>[] => [
  {
    key: 'id',
    label: 'ID',
    width: '80px',
    sortable: true,
    render: (job) => (
      <span className="font-mono text-sm text-gray-300">#{job.id}</span>
    ),
  },
  {
    key: 'bonusId',
    label: 'Bonus ID',
    width: '100px',
    sortable: true,
    render: (job) => (
      <span className="font-mono text-sm text-gray-300">#{job.bonusId}</span>
    ),
  },
  {
    key: 'status',
    label: 'Status',
    width: '120px',
    sortable: true,
    render: (job) => formatJobStatus(job),
  },
  {
    key: 'progress',
    label: 'Progress',
    width: '200px',
    sortable: false,
    render: (job) => {
      const processed = job.targets.filter(
        (t) => t.status === 'processed' || t.status === 'completed'
      ).length;
      const total = job.targets.length;

      return (
        <div className="space-y-1">
          {formatProgress(job)}
          <div className="text-xs text-gray-400">
            {processed} / {total} users
          </div>
        </div>
      );
    },
  },

  {
    key: 'createdAt',
    label: 'Created',
    width: '160px',
    sortable: true,
    render: (job) => (
      <span className="text-sm text-gray-400">{formatDate(job.createdAt)}</span>
    ),
  },
  {
    key: 'updatedAt',
    label: 'Updated',
    width: '160px',
    sortable: true,
    render: (job) => (
      <span className="text-sm text-gray-400">{formatDate(job.updatedAt)}</span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    width: '150px',
    sortable: false,
    sticky: 'right',
    render: (job) => (
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onView(job)}
          className="p-1"
          title="View Details"
        >
          <Eye className="h-4 w-4" />
        </Button>

        {job.status === 'pending' || job.status === 'processing' ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCancel(job)}
            className="p-1 text-red-400 hover:text-red-300"
            title="Cancel Job"
          >
            <XCircle className="h-4 w-4" />
          </Button>
        ) : null}

        {job.status === 'failed' && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRetry(job)}
            className="p-1 text-blue-400 hover:text-blue-300"
            title="Retry Job"
          >
            <Play className="h-4 w-4" />
          </Button>
        )}
      </div>
    ),
  },
];
