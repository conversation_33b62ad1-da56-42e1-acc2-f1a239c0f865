import React, { useState, useEffect } from 'react';
import { useSiteClaimableBonuses } from '../../hooks/useSiteClaimableBonuses';
import { useClaimableBonuses } from '../../hooks/useClaimableBonuses';
import { BonusSlotCard } from '../../components/BonusSlotCard';
import { BonusSelectionModal } from '../../components/BonusSelectionModal';
import { RewardsPagePreview } from '../../components/RewardsPagePreview';
import { ErrorBoundary } from '../../components/ErrorBoundary';
import { useNotifications } from '../../contexts/NotificationContext';
import { validateRewardsPage, getValidationRecommendations } from '../../utils/rewardsPageValidation';
import Button from '../../components/ui/Button';
import { RefreshCw, Eye, AlertCircle, CheckCircle, Shield, Plus, Gift } from 'lucide-react';
import type { SiteClaimableBonus } from '@panels/api';

export const RewardsPageConfigurationPage: React.FC = () => {
  const { data: siteClaimableBonuses, isLoading, error, refetch, updateBonus, toggleBonus, createBonus, deleteBonus } = useSiteClaimableBonuses();
  const { data: claimableBonuses, refetch: refetchClaimable } = useClaimableBonuses();
  const { success, error: showError, warning, info } = useNotifications();

  const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
  const [isSelectionModalOpen, setIsSelectionModalOpen] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [operationLoading, setOperationLoading] = useState<string | null>(null);
  const [validationResults, setValidationResults] = useState<ReturnType<typeof validateRewardsPage> | null>(null);

  const handleSlotSelect = (slotName: string) => {
    setSelectedSlot(slotName);
    setIsSelectionModalOpen(true);
  };

  const handleBonusAssign = async (bonusId: number | null) => {
    if (!selectedSlot) {
      showError('Invalid Operation', 'No slot selected for bonus assignment.');
      return;
    }

    // Special handling for bonus removal
    if (bonusId === null) {
      warning(
        'Bonus Removal',
        'To remove a bonus assignment, please deactivate the slot instead. Bonus removal via API is currently not supported.'
      );
      setIsSelectionModalOpen(false);
      setSelectedSlot(null);
      return;
    }

    setOperationLoading(selectedSlot);
    try {
      const result = await updateBonus({
        slotName: selectedSlot,
        bonusId: bonusId!, // We know it's not null at this point
      });

      if (result.success) {
        setIsSelectionModalOpen(false);
        setSelectedSlot(null);
        // Refresh claimable bonuses to update preview
        refetchClaimable();

        success('Bonus Assigned', `Successfully assigned bonus to ${selectedSlot} slot.`);
      } else {
        const errorMessage = result.error || 'Failed to assign bonus to slot.';
        showError('Assignment Failed', errorMessage);
        console.error('Bonus assignment failed:', {
          slotName: selectedSlot,
          bonusId,
          error: result.error
        });
      }
    } catch (error) {
      showError('Unexpected Error', 'An unexpected error occurred while assigning the bonus.');
      console.error('Bonus assignment error:', {
        slotName: selectedSlot,
        bonusId,
        error
      });
    } finally {
      setOperationLoading(null);
    }
  };

  const handleSlotToggle = async (slotName: string, isActive: boolean) => {
    // Validation before toggle
    if (isActive) {
      const slot = siteClaimableBonuses.find(s => s.slotName === slotName);
      if (!slot?.bonusId || !slot?.bonus) {
        warning('Cannot Activate Slot', 'Please assign a bonus to this slot before activating it.');
        return;
      }
      if (!slot.bonus.isActive) {
        warning('Inactive Bonus', 'The assigned bonus is inactive. Users won\'t be able to claim it.');
      }
    }

    setOperationLoading(slotName);
    try {
      const result = await toggleBonus({
        slotName,
        isActive,
      });

      if (result.success) {
        // Refresh claimable bonuses to update preview
        refetchClaimable();

        success(
          `Slot ${isActive ? 'Activated' : 'Deactivated'}`,
          `Successfully ${isActive ? 'activated' : 'deactivated'} the ${slotName} slot.`
        );
      } else {
        showError('Toggle Failed', result.error || `Failed to ${isActive ? 'activate' : 'deactivate'} slot.`);
      }
    } catch (error) {
      showError('Unexpected Error', 'An unexpected error occurred while toggling the slot.');
      console.error('Slot toggle error:', error);
    } finally {
      setOperationLoading(null);
    }
  };

  const handleCreateHappySlot = async () => {
    // Find the highest existing happy slot number
    const happySlots = siteClaimableBonuses.filter(slot => slot.slotName.startsWith('happy_'));
    const highestNumber = happySlots.reduce((max, slot) => {
      const match = slot.slotName.match(/happy_(\d+)/);
      if (match) {
        const num = parseInt(match[1], 10);
        return num > max ? num : max;
      }
      return max;
    }, 0);

    const newSlotName = `happy_${highestNumber + 1}`;

    setOperationLoading(newSlotName);
    try {
      const result = await createBonus({
        slotName: newSlotName,
        bonusId: 2, // Default bonusId as specified in requirements
        isActive: true,
      });

      if (result.success) {
        // Refresh claimable bonuses to update preview
        refetchClaimable();

        success('Happy Slot Created', `Successfully created ${newSlotName} slot.`);
      } else {
        showError('Creation Failed', result.error || 'Failed to create happy bonus slot.');
      }
    } catch (error) {
      showError('Unexpected Error', 'An unexpected error occurred while creating the slot.');
      console.error('Slot creation error:', error);
    } finally {
      setOperationLoading(null);
    }
  };

  const handleDeleteHappySlot = async (slotName: string) => {
    if (!slotName.startsWith('happy_')) {
      showError('Invalid Operation', 'Only happy bonus slots can be deleted.');
      return;
    }

    setOperationLoading(slotName);
    try {
      const result = await deleteBonus({
        slotName,
      });

      if (result.success) {
        // Refresh claimable bonuses to update preview
        refetchClaimable();

        success('Happy Slot Deleted', `Successfully deleted ${slotName} slot.`);
      } else {
        showError('Deletion Failed', result.error || 'Failed to delete happy bonus slot.');
      }
    } catch (error) {
      showError('Unexpected Error', 'An unexpected error occurred while deleting the slot.');
      console.error('Slot deletion error:', error);
    } finally {
      setOperationLoading(null);
    }
  };

  // Run validation when data changes
  useEffect(() => {
    if (siteClaimableBonuses.length > 0) {
      const validation = validateRewardsPage(siteClaimableBonuses, claimableBonuses);
      setValidationResults(validation);

      // Show recommendations if there are issues
      if (!validation.overall.isValid && validation.overall.level === 'error') {
        const recommendations = getValidationRecommendations(validation);
        if (recommendations.length > 0) {
          info('Configuration Tips', recommendations[0]);
        }
      }
    }
  }, [siteClaimableBonuses, claimableBonuses, info]);

  const handleRefresh = async () => {
    try {
      await Promise.all([refetch(), refetchClaimable()]);
      success('Data Refreshed', 'Successfully refreshed rewards page configuration.');
    } catch (error) {
      showError('Refresh Failed', 'Failed to refresh data. Please try again.');
      console.error('Refresh error:', error);
    }
  };

  const togglePreviewMode = () => {
    setIsPreviewMode(!isPreviewMode);
    if (!isPreviewMode) {
      info('Preview Mode', 'Now showing how the rewards page will appear to users.');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading rewards page configuration...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-400 mb-4">Failed to load configuration</p>
          <p className="text-red-400 text-sm mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="primary">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // Sort slots by position for consistent display
  const sortedSlots = [...siteClaimableBonuses].sort((a, b) => a.position - b.position);

  // Categorize slots by type for organized display
  const timeSlots = sortedSlots.filter(slot => slot.slotName.startsWith('time_'));
  const welcomeSlots = sortedSlots.filter(slot => slot.slotName === 'welcome');
  const happySlots = sortedSlots.filter(slot => slot.slotName.startsWith('happy_'));

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        showError('Application Error', 'An unexpected error occurred in the rewards page configuration.');
        console.error('RewardsPageConfiguration error:', error, errorInfo);
      }}
    >
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-300">Rewards Page Configuration</h1>
          <p className="text-gray-400 mt-1">
            Configure which bonuses appear on the frontend rewards page and control their visibility
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={togglePreviewMode}
            variant={isPreviewMode ? 'primary' : 'secondary'}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            {isPreviewMode ? 'Exit Preview' : 'Preview Mode'}
          </Button>
          <Button onClick={handleRefresh} variant="secondary" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Status Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <CheckCircle className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Total Slots</p>
              <p className="text-xl font-semibold text-gray-300">{sortedSlots.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-500/20 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Active Slots</p>
              <p className="text-xl font-semibold text-gray-300">
                {sortedSlots.filter(slot => slot.isActive).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-dark-700 rounded-lg p-4 border border-dark-600">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <CheckCircle className="h-5 w-5 text-purple-400" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Claimable Now</p>
              <p className="text-xl font-semibold text-gray-300">{claimableBonuses.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Validation Summary */}
      {validationResults && !isPreviewMode && (
        <div className={`rounded-lg p-4 border ${
          validationResults.overall.level === 'success'
            ? 'bg-green-500/10 border-green-500/20'
            : validationResults.overall.level === 'warning'
            ? 'bg-yellow-500/10 border-yellow-500/20'
            : 'bg-red-500/10 border-red-500/20'
        }`}>
          <div className="flex items-start gap-3">
            <Shield className={`h-5 w-5 mt-0.5 ${
              validationResults.overall.level === 'success'
                ? 'text-green-400'
                : validationResults.overall.level === 'warning'
                ? 'text-yellow-400'
                : 'text-red-400'
            }`} />
            <div className="flex-1">
              <p className={`font-medium ${
                validationResults.overall.level === 'success'
                  ? 'text-green-300'
                  : validationResults.overall.level === 'warning'
                  ? 'text-yellow-300'
                  : 'text-red-300'
              }`}>
                {validationResults.overall.message}
              </p>
              {validationResults.overall.details && (
                <p className={`text-sm mt-1 ${
                  validationResults.overall.level === 'success'
                    ? 'text-green-400'
                    : validationResults.overall.level === 'warning'
                    ? 'text-yellow-400'
                    : 'text-red-400'
                }`}>
                  {validationResults.overall.details}
                </p>
              )}
              {validationResults.summary.issueCount > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-gray-400">
                    Issues found in: {validationResults.slots
                      .filter(s => s.level === 'error')
                      .map(s => s.slotName)
                      .join(', ')}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Preview Mode Banner */}
      {isPreviewMode && (
        <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <Eye className="h-5 w-5 text-blue-400" />
            <div>
              <p className="text-blue-300 font-medium">Preview Mode Active</p>
              <p className="text-blue-400 text-sm">
                Showing how the rewards page will appear to end users. Only active slots with assigned bonuses are visible.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Content based on mode */}
      {isPreviewMode ? (
        <RewardsPagePreview
          slots={sortedSlots}
          claimableBonuses={claimableBonuses}
        />
      ) : (
        <>
          {/* Time-based Bonuses Section */}
          {timeSlots.length > 0 && (
            <div className="space-y-4">
              <div className="border-b border-gray-700 pb-2">
                <h2 className="text-xl font-semibold text-gray-300">Time-based Bonuses</h2>
                <p className="text-sm text-gray-400">Instant and weekly reward slots that appear side by side (50% width each)</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {timeSlots.map((slot) => (
                  <BonusSlotCard
                    key={slot.id}
                    slot={slot}
                    isPreviewMode={false}
                    isLoading={operationLoading === slot.slotName}
                    onSelect={() => handleSlotSelect(slot.slotName)}
                    onToggle={(isActive) => handleSlotToggle(slot.slotName, isActive)}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Happy Bonuses Section */}
          <div className="space-y-4">
            <div className="border-b border-gray-700 pb-2">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-300">Happy Bonuses</h2>
                  <p className="text-sm text-gray-400">Special bonus slots that appear in a row (33.33% width each)</p>
                </div>
                <Button
                  onClick={handleCreateHappySlot}
                  variant="primary"
                  size="sm"
                  className="flex items-center gap-2"
                  disabled={operationLoading !== null}
                >
                  <Plus className="h-4 w-4" />
                  Create Happy Slot
                </Button>
              </div>
            </div>
            {happySlots.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {happySlots.map((slot) => (
                  <BonusSlotCard
                    key={slot.id}
                    slot={slot}
                    isPreviewMode={false}
                    isLoading={operationLoading === slot.slotName}
                    onSelect={() => handleSlotSelect(slot.slotName)}
                    onToggle={(isActive) => handleSlotToggle(slot.slotName, isActive)}
                    onDelete={() => handleDeleteHappySlot(slot.slotName)}
                  />
                ))}
              </div>
            ) : (
              <div className="bg-dark-600 rounded-lg p-8 text-center">
                <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 mb-2">No happy bonus slots created yet</p>
                <p className="text-gray-500 text-sm mb-4">Create your first happy bonus slot to get started</p>
                <Button
                  onClick={handleCreateHappySlot}
                  variant="primary"
                  className="flex items-center gap-2"
                  disabled={operationLoading !== null}
                >
                  <Plus className="h-4 w-4" />
                  Create First Happy Slot
                </Button>
              </div>
            )}
          </div>

          {/* Welcome Bonus Section */}
          {welcomeSlots.length > 0 && (
            <div className="space-y-4">
              <div className="border-b border-gray-700 pb-2">
                <h2 className="text-xl font-semibold text-gray-300">Welcome Bonus</h2>
                <p className="text-sm text-gray-400">Full-width welcome bonus slot for new players</p>
              </div>
              <div className="grid grid-cols-1 gap-6">
                {welcomeSlots.map((slot) => (
                  <BonusSlotCard
                    key={slot.id}
                    slot={slot}
                    isPreviewMode={false}
                    isLoading={operationLoading === slot.slotName}
                    onSelect={() => handleSlotSelect(slot.slotName)}
                    onToggle={(isActive) => handleSlotToggle(slot.slotName, isActive)}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Fallback for any uncategorized slots */}
          {sortedSlots.filter(slot =>
            !slot.slotName.startsWith('time_') &&
            slot.slotName !== 'welcome' &&
            !slot.slotName.startsWith('happy_')
          ).length > 0 && (
            <div className="space-y-4">
              <div className="border-b border-gray-700 pb-2">
                <h2 className="text-xl font-semibold text-gray-300">Other Slots</h2>
                <p className="text-sm text-gray-400">Additional bonus slots</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {sortedSlots
                  .filter(slot =>
                    !slot.slotName.startsWith('time_') &&
                    slot.slotName !== 'welcome' &&
                    !slot.slotName.startsWith('happy_')
                  )
                  .map((slot) => (
                    <BonusSlotCard
                      key={slot.id}
                      slot={slot}
                      isPreviewMode={false}
                      isLoading={operationLoading === slot.slotName}
                      onSelect={() => handleSlotSelect(slot.slotName)}
                      onToggle={(isActive) => handleSlotToggle(slot.slotName, isActive)}
                    />
                  ))}
              </div>
            </div>
          )}
        </>
      )}

      {/* Bonus Selection Modal */}
      <BonusSelectionModal
        isOpen={isSelectionModalOpen}
        onClose={() => {
          setIsSelectionModalOpen(false);
          setSelectedSlot(null);
        }}
        onSelect={handleBonusAssign}
        currentBonusId={selectedSlot ? sortedSlots.find(s => s.slotName === selectedSlot)?.bonusId ?? null : null}
        isLoading={operationLoading !== null}
      />
      </div>
    </ErrorBoundary>
  );
};
